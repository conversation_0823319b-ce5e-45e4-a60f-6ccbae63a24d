# Image Generator Maintenance Guide

This guide explains how to temporarily disable and re-enable the Image Generator feature at `/image-generator`.

## 📋 Overview

The Image Generator can be temporarily disabled by redirecting users to a maintenance page instead of showing the actual feature. This approach:

- ✅ Keeps the original code intact
- ✅ Shows users a professional maintenance page
- ✅ Easy to re-enable later
- ✅ Prevents access without breaking the site

## 🔧 Files Modified

When disabling the Image Generator, the following files are affected:

1. **`middleware.ts`** - Handles the redirect logic
2. **`app/maintenance/page.tsx`** - The maintenance page users see
3. **This guide** - For future reference

## 🚫 How to Disable the Image Generator

### Step 1: Update Middleware

Edit `middleware.ts` and add the redirect logic:

```typescript
// Add this BEFORE the existing protected routes check
// Temporarily disable image-generator - redirect to maintenance page
if (request.nextUrl.pathname.startsWith("/image-generator")) {
  const maintenanceUrl = new URL("/maintenance", request.url);
  return NextResponse.redirect(maintenanceUrl);
}

// Update the protected routes check to REMOVE image-generator
if (
  request.nextUrl.pathname.startsWith("/qr-code/analytics") ||
  request.nextUrl.pathname.startsWith("/api/generate-image")
  // Note: removed "/image-generator" from here
) {
  // ... existing auth logic
}
```

### Step 2: Update Middleware Matcher

Add `/maintenance` to the matcher array:

```typescript
export const config = {
  matcher: [
    "/qr-code/analytics",
    "/api/generate-image",
    "/markdown-converter",
    "/image-generator",
    "/maintenance", // Add this line
  ],
};
```

### Step 3: Create Maintenance Page

Create `app/maintenance/page.tsx` with a professional maintenance message.
(The maintenance page code is already created and styled to match your site's design)

## ✅ How to Re-enable the Image Generator

### Step 1: Remove Redirect from Middleware

In `middleware.ts`, **remove** or **comment out** these lines:

```typescript
// DELETE THESE LINES:
// Temporarily disable image-generator - redirect to maintenance page
if (request.nextUrl.pathname.startsWith("/image-generator")) {
  const maintenanceUrl = new URL("/maintenance", request.url);
  return NextResponse.redirect(maintenanceUrl);
}
```

### Step 2: Add Back to Protected Routes (if needed)

If the image generator requires authentication, add it back to the protected routes:

```typescript
if (
  request.nextUrl.pathname.startsWith("/qr-code/analytics") ||
  request.nextUrl.pathname.startsWith("/api/generate-image") ||
  request.nextUrl.pathname.startsWith("/image-generator") // Add this back
) {
  // ... auth logic
}
```

### Step 3: Optional Cleanup

You can optionally remove the maintenance page if no longer needed:

```bash
# Remove the maintenance page directory
rm -rf app/maintenance/
```

And remove `/maintenance` from the middleware matcher if desired.

## 🧪 Testing

### Test Disable

1. Visit `http://localhost:3000/image-generator`
2. Should redirect to `http://localhost:3000/maintenance`
3. Maintenance page should display properly
4. Links should work (Back to Home, Explore Other Tools)

### Test Re-enable

1. Follow re-enable steps above
2. Visit `http://localhost:3000/image-generator`
3. Should show the actual Image Generator page
4. Authentication should work (if applicable)

## 📁 File Locations

```
project-root/
├── middleware.ts                    # Main redirect logic
├── app/
│   ├── image-generator/
│   │   └── page.tsx                # Original image generator (untouched)
│   └── maintenance/
│       └── page.tsx                # Maintenance page
└── IMAGE_GENERATOR_MAINTENANCE_GUIDE.md  # This guide
```

## 🎨 Maintenance Page Features

The maintenance page includes:

- 🎨 Professional design matching your site's theme
- 📱 Responsive layout for all devices
- ✨ Smooth animations and floating icons
- 🔗 Navigation links (Back to Home, Explore Other Tools)
- 📋 "What's Coming?" section with upcoming features
- 🌙 Dark/light mode support

## 🔄 Quick Commands

### Disable (one-liner approach)

```bash
# Add redirect before protected routes in middleware.ts
# Create maintenance page (already done)
```

### Re-enable (one-liner approach)

```bash
# Remove redirect from middleware.ts
# Optionally remove maintenance page
```

## 💡 Tips

1. **Always test** in development before deploying
2. **Keep the original page intact** - never delete `app/image-generator/page.tsx`
3. **Update this guide** if you make changes to the process
4. **Consider a feature flag** for more advanced toggle functionality
5. **Monitor logs** to see if users are hitting the maintenance page

## 🚀 Deployment

After making changes:

1. **Commit changes**:

   ```bash
   git add .
   git commit -m "Temporarily disable image generator"
   ```

2. **Deploy to production**:

   ```bash
   # Your deployment command (e.g., Vercel)
   npm run deploy
   # or
   vercel --prod
   ```

3. **Verify** the redirect works in production

---

**Created**: $(date)
**Last Updated**: $(date)
**Status**: Image Generator Currently Disabled ❌

*Remember to update the status above when you re-enable the feature!*
