'use strict'

exports.config = {
  app_name: [process.env.NEW_RELIC_APP_NAME || 'TextConverter'],
  license_key: process.env.NEW_RELIC_LICENSE_KEY,
  logging: {
    level: 'debug',
    enabled: true
  },
  allow_all_headers: true,
  attributes: {
    exclude: [
      'request.headers.cookie',
      'request.headers.authorization',
      'request.headers.proxyAuthorization',
      'request.headers.setCookie*',
      'request.headers.x*',
      'response.headers.cookie',
      'response.headers.authorization',
      'response.headers.proxyAuthorization',
      'response.headers.setCookie*',
      'response.headers.x*'
    ]
  },
  distributed_tracing: {
    enabled: true
  },
  transaction_tracer: {
    enabled: true,
    record_sql: 'obfuscated'
  },
  error_collector: {
    enabled: true,
    ignore_status_codes: [404]
  },
  browser_monitoring: {
    enable: true
  },
  application_logging: {
    forwarding: {
      enabled: true
    }
  }
}