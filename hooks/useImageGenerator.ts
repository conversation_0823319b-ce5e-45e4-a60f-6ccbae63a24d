import { useState, useEffect, useCallback, useRef } from "react";
import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  serverTimestamp,
  query,
  orderBy,
  limit,
  startAfter,
  deleteDoc,
  doc,
  setDoc,
  increment,
  onSnapshot,
  endBefore,
  limitToLast,
  deleteField,
} from "firebase/firestore";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

interface SavedImage {
  id: string;
  prompt: string;
  imageUrl: string;
  backblazeUrl?: string;
  style: string;
  aspectRatio?: string;
  width?: number;
  height?: number;
  createdAt: any;
}
interface ProcessEditedImageParams {
  userId: string;
  editedImageBackblazeUrl: string; // This should be the persistent URL from /api/edit-image
  originalImageFileName?: string; // e.g., editingImage.name from the component
  prompt: string; // The edit prompt/instructions
  editType: string; // The selected edit type
  editStrength: number; // The selected edit strength
  aspectRatio: string; // Aspect ratio of the edited image
  width: number; // Width of the edited image, from /api/edit-image response
  height: number; // Height of the edited image, from /api/edit-image response
  originalImageUrl?: string; // URL of the original image being edited
  // Consider if 'style' or other metadata is needed for Firestore record
}

export const useImageGenerator = () => {
  const router = useRouter();
  const { user, loading: authLoading, signOut } = useAuth();
  const [prompt, setPrompt] = useState("");
  const [style, setStyle] = useState("vivid");
  const [aspectRatio, setAspectRatio] = useState("1:1");
  const [width, setWidth] = useState(512);
  const [height, setHeight] = useState(512);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [backblazeUrl, setBackblazeUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [savedImages, setSavedImages] = useState<SavedImage[]>([]);
  const [credits, setCredits] = useState<number | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [firstVisible, setFirstVisible] = useState<any>(null);
  const [lastVisible, setLastVisible] = useState<any>(null);
  const [hasMore, setHasMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  const unsubscribeRef = useRef<(() => void) | null>(null);

  const loadImages = useCallback(
    async (direction: "next" | "prev" | "initial" = "initial") => {
      if (!user?.uid) return;

      setIsLoading(true);
      const imagesRef = collection(db, "users", user.uid, "image-generator");
      let q;

      if (direction === "next" && lastVisible) {
        q = query(
          imagesRef,
          orderBy("createdAt", "desc"),
          startAfter(lastVisible),
          limit(itemsPerPage)
        );
      } else if (direction === "prev" && firstVisible) {
        q = query(
          imagesRef,
          orderBy("createdAt", "desc"),
          endBefore(firstVisible),
          limitToLast(itemsPerPage)
        );
      } else {
        q = query(imagesRef, orderBy("createdAt", "desc"), limit(itemsPerPage));
      }

      const unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          if (!snapshot.empty) {
            const images = snapshot.docs.map(
              (doc) => ({ id: doc.id, ...doc.data() } as SavedImage)
            );
            setSavedImages(images);
            setFirstVisible(snapshot.docs[0]);
            setLastVisible(snapshot.docs[snapshot.docs.length - 1]);
            setHasMore(snapshot.docs.length === itemsPerPage);
            if (direction === "next") setCurrentPage((prev) => prev + 1);
            if (direction === "prev")
              setCurrentPage((prev) => Math.max(1, prev - 1));
          } else {
            if (direction !== "initial") toast.info("No more images to load.");
            setHasMore(false);
          }
          setIsLoading(false);
        },
        (error) => {
          console.error("Error loading images: ", error);
          toast.error("Failed to load images.");
          setIsLoading(false);
        }
      );

      unsubscribeRef.current = unsubscribe;
    },
    [user, lastVisible, firstVisible]
  );

  useEffect(() => {
    if (authLoading) {
      console.log("Auth is loading...");
      return;
    }

    console.log("Auth loaded. User:", user);

    if (user) {
      const userRef = doc(db, "users", user.uid);
      const creditsRef = doc(
        db,
        "users",
        user.uid,
        "credits",
        "imageGenerator"
      );

      console.log("Setting up Firestore listener for user:", user.uid);

      const unsubscribe = onSnapshot(userRef, async (userDoc) => {
        console.log("User document snapshot received:", userDoc.exists());
        if (userDoc.exists() && userDoc.data()?.credits) {
          console.log("Old credits structure found. Migrating...");
          // Migrate old credits structure
          const oldCredits =
            userDoc.data().credits.imageGenerator?.remaining ?? 20;
          await setDoc(creditsRef, {
            balance: oldCredits,
            lastUpdated: serverTimestamp(),
          });
          await setDoc(userRef, { credits: deleteField() }, { merge: true });
          setCredits(oldCredits);
          console.log("Migration complete. Credits set to:", oldCredits);
        } else {
          console.log(
            "No old credits structure. Setting up listener for new structure."
          );
          const unsubscribeCredits = onSnapshot(creditsRef, (doc) => {
            console.log("Credits document snapshot received:", doc.exists());
            if (doc.exists()) {
              const newCredits = doc.data()?.balance ?? 0;
              setCredits(newCredits);
              console.log("Credits updated to:", newCredits);
            } else {
              console.log(
                "Credits document does not exist. Initializing credits."
              );
              // Initialize credits for new user
              setDoc(
                creditsRef,
                { balance: 20, lastUpdated: serverTimestamp() },
                { merge: true }
              );
              setCredits(20);
              console.log("Initialized credits to 20.");
            }
          });
          if (unsubscribeRef.current) {
            unsubscribeRef.current();
          }
          unsubscribeRef.current = unsubscribeCredits;
        }
      });

      setIsInitializing(false);
      console.log("Initialization complete.");

      return () => {
        console.log("Cleaning up listeners.");
        unsubscribe();
        if (unsubscribeRef.current) {
          unsubscribeRef.current();
        }
      };
    } else {
      setIsInitializing(false);
      setCredits(null);
      setSavedImages([]);
    }
  }, [user, authLoading]);

  // Separate effect for loading images to avoid infinite loop
  useEffect(() => {
    if (user && !authLoading) {
      // Load images directly without calling loadImages to avoid dependency issues
      const imagesRef = collection(db, "users", user.uid, "image-generator");
      const q = query(
        imagesRef,
        orderBy("createdAt", "desc"),
        limit(itemsPerPage)
      );

      const unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          if (!snapshot.empty) {
            const images = snapshot.docs.map(
              (doc) => ({ id: doc.id, ...doc.data() } as SavedImage)
            );
            setSavedImages(images);
            setFirstVisible(snapshot.docs[0]);
            setLastVisible(snapshot.docs[snapshot.docs.length - 1]);
            setHasMore(snapshot.docs.length === itemsPerPage);
            setCurrentPage(1);
          } else {
            setSavedImages([]);
            setHasMore(false);
          }
        },
        (error) => {
          console.error("Error loading images: ", error);
          setSavedImages([]);
        }
      );

      return () => unsubscribe();
    }
  }, [user, authLoading]);

  const generateImage = useCallback(async () => {
    if (!user) {
      router.push("/auth/signin?redirect=/image-generator");
      return;
    }
    if (credits === null || credits <= 0) {
      toast.error("You have no credits left.");
      return;
    }
    if (!prompt.trim()) {
      toast.error("Please enter a prompt.");
      return;
    }

    console.log("Starting image generation...", {
      prompt,
      style,
      aspectRatio,
      width,
      height,
    });

    // Check if we have the auth token
    const token = document.cookie
      .split(";")
      .find((c) => c.trim().startsWith("firebaseToken="));
    console.log("Auth token present:", !!token);

    setIsLoading(true);

    try {
      const response = await fetch("/api/generate-image", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt,
          style,
          aspectRatio,
          width,
          height,
          userId: user.uid // Add userId for API tracking
        }),
      });

      console.log("API response status:", response.status);

      if (!response.ok) {
        let errorMessage = "Failed to generate image";
        try {
          const errorData = await response.json();
          console.error("API error:", errorData);
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          console.error("Failed to parse error response:", parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log("API response data:", data);
      setGeneratedImage(data.imageUrl);
      setBackblazeUrl(data.backblazeUrl); // Assuming data.backblazeUrl is the one to save

      // Call handle-post-generation API
      if (data.backblazeUrl && user?.uid) {
        console.log("📞 Calling /api/handle-post-generation...");
        try {
          const postGenResponse = await fetch("/api/handle-post-generation", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              userId: user.uid,
              // imageUrl: data.imageUrl, // This is often a temporary URL from the generation service
              backblazeUrl: data.backblazeUrl, // This is the persistent URL
              prompt,
              style,
              aspectRatio,
              width,
              height,
            }),
          });

          if (!postGenResponse.ok) {
            const postGenErrorData = await postGenResponse.json();
            console.error("Error in /api/handle-post-generation:", postGenErrorData);
            toast.error(`Image generated, but failed to update credits/save: ${postGenErrorData.error || 'Unknown error'}`);
          } else {
            const postGenData = await postGenResponse.json();
            console.log("✅ /api/handle-post-generation successful:", postGenData);
            toast.success("Image generated and saved successfully!");
          }
        } catch (postGenError: any) {
          console.error("Fetch error calling /api/handle-post-generation:", postGenError);
          toast.error(`Image generated, but error in post-processing: ${postGenError.message}`);
        }
      } else {
        console.warn("Skipping /api/handle-post-generation call: missing backblazeUrl or userId");
        toast.warning("Image generated, but post-processing skipped (missing data).");
      }
    } catch (error: any) {
      console.error("Image generation error:", error);
      toast.error(error.message);
    } finally {
      console.log("Setting isLoading to false");
      setIsLoading(false);
    }
  }, [user, credits, prompt, style, aspectRatio, width, height, router]);

  const saveImage = async (imageUrl: string, isEdited = false, editDetails: any = {}) => {
    if (!user) {
      toast.error("You must be logged in to save an image.");
      return;
    }

    // Deduct 1 credit for saving an image
    const creditsRef = doc(db, "users", user.uid, "credits", "imageGenerator");
    await setDoc(creditsRef, { balance: increment(-1) }, { merge: true });

    const imageToSave = {
      userId: user.uid,
      prompt,
      style,
      aspectRatio,
      imageUrl,
      width,
      height,
      createdAt: serverTimestamp(),
      isEdited,
      ...editDetails,
    };
    const imagesRef = collection(db, "users", user.uid, "image-generator");
    await addDoc(imagesRef, imageToSave);

    setGeneratedImage(null); // Clear the temporary generated image
    setBackblazeUrl(null); // Clear the Backblaze URL
    toast.success("Voila! Your masterpiece has been saved.");
  };

  const deleteImage = useCallback(
    async (imageId: string) => {
      if (!user) return;
      try {
        const imageRef = doc(db, "users", user.uid, "image-generator", imageId);
        await deleteDoc(imageRef);
        toast.success("Image deleted.");
      } catch (error) {
        toast.error("Failed to delete image.");
      }
    },
    [user]
  );

  const handleSignOut = async () => {
    await signOut();
    router.push("/");
  };

  const processEditedImage = useCallback(
    async (params: ProcessEditedImageParams) => {
      if (!user) {
        toast.error("You must be logged in to save an edited image.");
        return;
      }

      toast.info("Saving your edited masterpiece...");

      try {
        // 1. Deduct credit for the successful edit.
        const creditsRef = doc(
          db,
          "users",
          user.uid,
          "credits",
          "imageGenerator"
        );
        await setDoc(creditsRef, { balance: increment(-1) }, { merge: true });

        // 2. Save image metadata to Firestore.
        const imageToSave = {
          userId: user.uid,
          prompt: params.prompt,
          style: params.editType, // Using editType as the style for simplicity.
          aspectRatio: params.aspectRatio,
          imageUrl: params.editedImageBackblazeUrl, // The new persistent URL from the API.
          width: params.width,
          height: params.height,
          createdAt: serverTimestamp(),
          isEdited: true, // Flag to show this is an edit.
          editType: params.editType,
          editStrength: params.editStrength,
          originalImageUrl: params.originalImageUrl || null,
        };

        const imagesRef = collection(db, "users", user.uid, "image-generator");
        await addDoc(imagesRef, imageToSave);

        toast.success("Voila! Your edited image has been saved to your gallery.");
      } catch (error) {
        console.error("Error processing edited image:", error);
        toast.error("Oops! Something went wrong while saving your image.");
      }
    },
    [user]
  );

  return {
    prompt,
    setPrompt,
    style,
    setStyle,
    aspectRatio,
    setAspectRatio,
    width,
    setWidth,
    height,
    setHeight,
    generatedImage,
    setGeneratedImage,
    backblazeUrl,
    isLoading,
    user,
    savedImages,
    credits,
    isInitializing,
    generateImage,
    deleteImage,
    handleSignOut,
    loadMoreImages: () => loadImages("next"),
    loadPrevImages: () => loadImages("prev"),
    processEditedImage,
    hasMore,
    currentPage,
  };
};
