# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.env
.env.local

# testing
/coverage



# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Node modules
node_modules/

# Environment variables
.env

# Logs
logs
*.log
npm-debug.log*

#next

# Build directories
dist/
build/

# OS-specific files
.DS_Store
Thumbs.db

# Sentry Config File
.env.sentry-build-plugin

# Firebase
firestore.rules
