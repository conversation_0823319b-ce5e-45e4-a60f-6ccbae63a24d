import { js<PERSON><PERSON> } from "jspdf";
import { <PERSON><PERSON><PERSON>, <PERSON>d<PERSON><PERSON><PERSON><PERSON> } from "@/types/recipe";
import { toast } from "sonner";
import {
  PDFDocument,
  degrees,
  rgb,
  PDFDict,
  PDFName,
  PDFStream,
} from "pdf-lib";
import imageCompression from "browser-image-compression";
import * as pdfjs from "pdfjs-dist";

// Set up PDF.js worker
const initializePdfjs = (): void => {
  if (typeof window !== "undefined" && !pdfjs.GlobalWorkerOptions.workerSrc) {
    // Only set in browser environment and if not already set
    const pdfjsVersion = pdfjs.version || "3.4.120"; // Default to latest if version is not available
    pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsVersion}/pdf.worker.min.js`;
  }
};

// Initialize PDF.js when this module is loaded
initializePdfjs();

/**
 * Checks if the browser environment supports canvas operations needed for PDF compression
 * @returns boolean indicating if the environment supports needed canvas operations
 */
const supportsCanvasCompression = (): boolean => {
  if (typeof window === "undefined" || typeof document === "undefined") {
    return false; // Not in a browser environment
  }

  try {
    const canvas = document.createElement("canvas");
    canvas.width = 100;
    canvas.height = 100;

    const ctx = canvas.getContext("2d");
    if (!ctx) return false;

    // Test if toDataURL with JPEG is supported
    const dataUrl = canvas.toDataURL("image/jpeg", 0.5);
    return dataUrl.indexOf("data:image/jpeg") === 0;
  } catch (e) {
    console.warn("Canvas operations for compression not supported:", e);
    return false;
  }
};

/**
 * Merges multiple PDF files into a single PDF document
 * @param files Array of PDF files to merge
 * @param onProgress Callback function to report progress (0-100)
 * @returns Blob of the merged PDF
 */
export async function mergePDFs(
  files: File[],
  onProgress?: (progress: number) => void
): Promise<Blob> {
  // Create a new PDF document
  const mergedPdf = await PDFDocument.create();
  const totalFiles = files.length;

  if (totalFiles === 0) {
    return new Blob([], { type: "application/pdf" });
  }

  // Process each file
  for (let i = 0; i < totalFiles; i++) {
    const file = files[i];
    if (!(file instanceof File)) {
      throw new Error(
        `Invalid file type at index ${i}. Expected a File object.`
      );
    }
    const fileBuffer = await file.arrayBuffer();

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(fileBuffer);
    const pages = await mergedPdf.copyPages(pdfDoc, pdfDoc.getPageIndices());

    // Add each page to the new document
    pages.forEach((page) => {
      mergedPdf.addPage(page);
    });

    // Update progress
    if (onProgress) {
      onProgress(Math.round(((i + 1) / totalFiles) * 100));
    }
  }

  // Save the merged PDF
  const mergedPdfBytes = await mergedPdf.save();
  return new Blob([mergedPdfBytes], { type: "application/pdf" });
}
/**
 * Organizes pages of a PDF file into a new order.
 * @param pdfBytes The byte array of the PDF file.
 * @param newOrder An array of page indices in the desired new order.
 * @returns A promise that resolves with the byte array of the reorganized PDF.
 */
export async function organizePDF(
  pdfBytes: Uint8Array,
  newOrder: number[]
): Promise<Uint8Array> {
  const pdfDoc = await PDFDocument.load(pdfBytes);
  const newPdfDoc = await PDFDocument.create();

  for (const pageIndex of newOrder) {
    const [copiedPage] = await newPdfDoc.copyPages(pdfDoc, [pageIndex]);
    newPdfDoc.addPage(copiedPage);
  }

  return newPdfDoc.save();
}

/**
 * Extracts pages from a PDF file
 * @param file PDF file to split
 * @param pageIndices Array of page indices to extract (0-based)
 * @param onProgress Callback function to report progress (0-100)
 * @returns Array of Blobs, one for each extracted page
 */
export async function splitPDF(
  file: File,
  pageIndices: number[],
  onProgress?: (progress: number) => void
): Promise<Blob[]> {
  // Load the PDF document
  const fileBuffer = await file.arrayBuffer();
  const pdfDoc = await PDFDocument.load(fileBuffer);
  const blobs: Blob[] = [];

  // Sort pages in ascending order
  pageIndices.sort((a, b) => a - b);

  // Create a new PDF for each page
  const totalPages = pageIndices.length;

  for (let i = 0; i < totalPages; i++) {
    const pageIndex = pageIndices[i];

    // Create a new PDF document
    const newPdf = await PDFDocument.create();

    // Copy the page from the original document
    const [copiedPage] = await newPdf.copyPages(pdfDoc, [pageIndex]);
    newPdf.addPage(copiedPage);

    // Save the new PDF
    const newPdfBytes = await newPdf.save();
    const blob = new Blob([newPdfBytes], { type: "application/pdf" });
    blobs.push(blob);

    // Update progress
    if (onProgress) {
      onProgress(Math.round(((i + 1) / totalPages) * 100));
    }
  }

  return blobs;
}

/**
 * Compresses a PDF file by re-encoding images at a lower quality.
 *
 * @param file The PDF file to compress.
 * @param quality The image quality (0-1), where 1 is best quality.
 * @param onProgress A callback to report compression progress.
 * @returns A Blob of the compressed PDF.
 */
export async function compressPDF(
  file: File,
  quality: number,
  onProgress?: (progress: number) => void
): Promise<Blob> {
  const fileBuffer = await file.arrayBuffer();
  const pdfDoc = await PDFDocument.load(fileBuffer);

  const pages = pdfDoc.getPages();
  const imageRefs = new Set<any>();

  for (const page of pages) {
    const resources = page.node.Resources();
    if (resources) {
      const xobjects = resources.lookup(PDFName.of("XObject"), PDFDict);
      if (xobjects) {
        for (const [name, ref] of xobjects.entries()) {
          const stream = pdfDoc.context.lookup(ref);
          if (
            stream instanceof PDFStream &&
            stream.dict.get(PDFName.of("Subtype")) === PDFName.of("Image")
          ) {
            imageRefs.add(ref);
          }
        }
      }
    }
  }

  const uniqueImageRefs = Array.from(imageRefs);
  if (uniqueImageRefs.length === 0) {
    toast.info("No images found in this PDF to compress.");
    return new Blob([fileBuffer], { type: "application/pdf" });
  }

  let compressedCount = 0;
  for (const ref of uniqueImageRefs) {
    try {
      const stream = pdfDoc.context.lookup(ref) as PDFStream;
      const imageBytes = stream.getContents();

      const compressedImageBlob = await imageCompression(
        new File([imageBytes], "image.jpg", { type: "image/jpeg" }),
        {
          maxSizeMB: 1,
          maxWidthOrHeight: 1920,
          useWebWorker: true,
          initialQuality: quality,
          fileType: "image/jpeg",
        }
      );

      const newImageBytes = await compressedImageBlob.arrayBuffer();
      const newImage = await pdfDoc.embedJpg(newImageBytes);

      pdfDoc.context.assign(ref, newImage.ref);
    } catch (error) {
      console.warn(`Skipping an image that could not be processed: ${error}`);
    }

    compressedCount++;
    if (onProgress) {
      onProgress(Math.round((compressedCount / uniqueImageRefs.length) * 100));
    }
  }

  const compressedPdfBytes = await pdfDoc.save({ useObjectStreams: true });

  if (compressedPdfBytes.length > fileBuffer.byteLength) {
    toast.info(
      "Compression resulted in a larger file size. Reverting to the original file."
    );
    return new Blob([fileBuffer], { type: "application/pdf" });
  }

  return new Blob([compressedPdfBytes], { type: "application/pdf" });
}

/**
 * Gets the page count of a PDF file
 * @param file PDF file to analyze
 * @returns Number of pages in the PDF
 */
export async function getPDFPageCount(file: File): Promise<number> {
  const fileBuffer = await file.arrayBuffer();
  const pdfDoc = await PDFDocument.load(fileBuffer);
  return pdfDoc.getPageCount();
}

/**
 * Rotates pages in a PDF file
 * @param file PDF file to rotate
 * @param rotation Rotation angle in degrees (90, 180, 270)
 * @param pageIndices Array of page indices to rotate (0-based). If empty, rotates all pages.
 * @param onProgress Callback function to report progress (0-100)
 * @returns Blob of the rotated PDF
 */
export async function rotatePDF(
  file: File,
  rotation: 90 | 180 | 270,
  pageIndices: number[] = [],
  onProgress?: (progress: number) => void
): Promise<Blob> {
  // Load the PDF document
  const fileBuffer = await file.arrayBuffer();
  const pdfDoc = await PDFDocument.load(fileBuffer);

  // If no specific pages are provided, rotate all pages
  const pagesToRotate =
    pageIndices.length > 0
      ? pageIndices
      : Array.from({ length: pdfDoc.getPageCount() }, (_, i) => i);

  // Rotate each page
  const totalPages = pagesToRotate.length;

  for (let i = 0; i < totalPages; i++) {
    const pageIndex = pagesToRotate[i];
    const page = pdfDoc.getPage(pageIndex);

    // Apply rotation
    page.setRotation(degrees(rotation));

    // Update progress
    if (onProgress) {
      onProgress(Math.round(((i + 1) / totalPages) * 100));
    }
  }

  // Save the rotated PDF
  const rotatedPdfBytes = await pdfDoc.save();
  return new Blob([rotatedPdfBytes], { type: "application/pdf" });
}

/**
 * Converts PDF pages to JPG images
 * @param file PDF file to convert
 * @param pageIndices Array of page indices to convert (0-based). If empty, converts all pages.
 * @param scale Scale factor for the images (1.0 = 100%)
 * @param quality JPEG quality (0-1)
 * @param onProgress Callback function to report progress (0-100)
 * @returns Array of Blobs, one for each converted page
 */
export async function pdfToJpg(
  file: File,
  pageIndices: number[] = [],
  scale: number = 2.0,
  quality: number = 0.8,
  onProgress?: (progress: number) => void
): Promise<Blob[]> {
  // Load the PDF document
  const fileBuffer = await file.arrayBuffer();
  const pdfDoc = await PDFDocument.load(fileBuffer);

  // If no specific pages are provided, convert all pages
  const pagesToConvert =
    pageIndices.length > 0
      ? pageIndices
      : Array.from({ length: pdfDoc.getPageCount() }, (_, i) => i);

  const totalPages = pagesToConvert.length;
  const imageBlobs: Blob[] = [];

  for (let i = 0; i < totalPages; i++) {
    const pageIndex = pagesToConvert[i];
    const page = pdfDoc.getPage(pageIndex);

    // Get page dimensions
    const { width, height } = page.getSize();

    // Create a canvas to render the page
    const canvas = document.createElement("canvas");
    const scaledWidth = Math.floor(width * scale);
    const scaledHeight = Math.floor(height * scale);
    canvas.width = scaledWidth;
    canvas.height = scaledHeight;

    const ctx = canvas.getContext("2d");
    if (!ctx) {
      throw new Error("Could not get canvas context");
    }

    // Fill with white background
    ctx.fillStyle = "white";
    ctx.fillRect(0, 0, scaledWidth, scaledHeight);

    // In a real implementation, we would render the PDF page to the canvas
    // Since pdf-lib doesn't have built-in rendering capabilities, we would typically use pdf.js
    // For now, we'll create a placeholder image with the page number

    ctx.fillStyle = "white";
    ctx.fillRect(0, 0, scaledWidth, scaledHeight);

    // Add a border
    ctx.strokeStyle = "#cccccc";
    ctx.lineWidth = 1;
    ctx.strokeRect(5, 5, scaledWidth - 10, scaledHeight - 10);

    // Add page number text
    ctx.font = `${Math.floor(scaledWidth / 20)}px Arial`;
    ctx.fillStyle = "#333333";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillText(`Page ${pageIndex + 1}`, scaledWidth / 2, scaledHeight / 2);

    // Convert canvas to blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            resolve(new Blob([""], { type: "image/jpeg" }));
          }
        },
        "image/jpeg",
        quality
      );
    });

    imageBlobs.push(blob);

    // Update progress
    if (onProgress) {
      onProgress(Math.round(((i + 1) / totalPages) * 100));
    }
  }

  return imageBlobs;
}

/**
 * Converts JPG images to a PDF file
 * @param files Array of image files to convert
 * @param onProgress Callback function to report progress (0-100)
 * @returns Blob of the created PDF
 */
export async function jpgToPdf(
  files: File[],
  onProgress?: (progress: number) => void
): Promise<Blob> {
  // Create a new PDF document
  const pdfDoc = await PDFDocument.create();
  const totalFiles = files.length;

  for (let i = 0; i < totalFiles; i++) {
    const file = files[i];

    // Read the image file
    const imageBytes = await file.arrayBuffer();

    // Determine image type and embed it
    if (file.type === "image/jpeg" || file.type === "image/jpg") {
      const jpgImage = await pdfDoc.embedJpg(imageBytes);
      const { width, height } = jpgImage.size();

      // Add a page with the image dimensions
      const page = pdfDoc.addPage([width, height]);

      // Draw the image on the page
      page.drawImage(jpgImage, {
        x: 0,
        y: 0,
        width: width,
        height: height,
      });
    } else if (file.type === "image/png") {
      const pngImage = await pdfDoc.embedPng(imageBytes);
      const { width, height } = pngImage.size();

      // Add a page with the image dimensions
      const page = pdfDoc.addPage([width, height]);

      // Draw the image on the page
      page.drawImage(pngImage, {
        x: 0,
        y: 0,
        width: width,
        height: height,
      });
    } else {
      // For unsupported image types, create an empty page with error message
      const page = pdfDoc.addPage([600, 800]);
      page.drawText(`Unsupported image format: ${file.type}`, {
        x: 50,
        y: 700,
        size: 20,
        color: rgb(0.95, 0.1, 0.1),
      });
    }

    // Update progress
    if (onProgress) {
      onProgress(Math.round(((i + 1) / totalFiles) * 100));
    }
  }

  // Save the PDF
  const pdfBytes = await pdfDoc.save();
  return new Blob([pdfBytes], { type: "application/pdf" });
}

/**
 * Formats a file size in bytes to a human-readable string
 * @param bytes File size in bytes
 * @returns Formatted string (e.g., "2.5 MB")
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Creates a download link for a blob
 * @param blob Blob to download
 * @param filename Filename for the download
 */
export function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * Converts a PDF to a Word document (DOCX)
 * @param file PDF file to convert
 * @param onProgress Callback function to report progress (0-100)
 * @returns Blob of the Word document
 */
export async function pdfToWord(
  file: File,
  onProgress?: (progress: number) => void
): Promise<Blob> {
  // Initialize PDF.js if needed
  if (!pdfjs.GlobalWorkerOptions.workerSrc) {
    pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
  }

  try {
    // Load the PDF file
    const fileData = await file.arrayBuffer();
    const pdf = await pdfjs.getDocument({ data: fileData }).promise;
    const numPages = pdf.numPages;

    // Extract text from each page
    let extractedText = "";
    let htmlContent =
      "<html><head><style>body{font-family:Arial,sans-serif;line-height:1.5;} p{margin-bottom:14pt;}</style></head><body>";

    for (let i = 1; i <= numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();

      // Process text items
      let lastY;
      let text = "";

      for (const item of textContent.items) {
        const textItem = item as any;

        // Check if this is a new line
        if (
          lastY !== undefined &&
          Math.abs(textItem.transform[5] - lastY) > 5
        ) {
          text += "\n";
          htmlContent += "<p>";
        }

        text += textItem.str;
        htmlContent += textItem.str;

        if (
          lastY !== undefined &&
          Math.abs(textItem.transform[5] - lastY) > 5
        ) {
          htmlContent += "</p>";
        }

        lastY = textItem.transform[5];
      }

      extractedText += text + "\n\n";
      htmlContent += '<div style="page-break-after:always;"></div>';

      // Update progress
      if (onProgress) {
        onProgress(Math.round((i / numPages) * 100));
      }
    }

    htmlContent += "</body></html>";

    // In a real implementation, we would convert the HTML to DOCX format
    // using a library like docx-html or mammoth.js
    // For now, we'll create a simple HTML file that can be opened in Word

    // Create a blob with the HTML content
    const blob = new Blob([htmlContent], {
      type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    });

    return blob;
  } catch (error) {
    console.error("Error converting PDF to Word:", error);
    throw new Error("Failed to convert PDF to Word");
  }
}

/**
 * Converts a Word document (DOCX) to PDF
 * @param file Word file to convert
 * @param onProgress Callback function to report progress (0-100)
 * @returns Blob of the PDF document
 */
export async function wordToPdf(
  file: File,
  onProgress?: (progress: number) => void
): Promise<Blob> {
  try {
    // In a real implementation, we would use a library like mammoth.js to convert DOCX to HTML,
    // and then use a PDF generation library to convert the HTML to PDF
    // For now, we'll simulate the conversion process

    // Simulate reading the Word document
    await new Promise((resolve) => setTimeout(resolve, 1000));
    if (onProgress) onProgress(30);

    // Simulate processing the content
    await new Promise((resolve) => setTimeout(resolve, 1000));
    if (onProgress) onProgress(60);

    // Simulate generating the PDF
    await new Promise((resolve) => setTimeout(resolve, 1000));
    if (onProgress) onProgress(90);

    // Create a simple PDF using jsPDF
    const doc = new jsPDF();

    // Add a title
    doc.setFontSize(16);
    doc.text("Converted from Word Document", 20, 20);

    // Add file information
    doc.setFontSize(12);
    doc.text(`File name: ${file.name}`, 20, 30);
    doc.text(`File size: ${formatFileSize(file.size)}`, 20, 40);
    doc.text(`Conversion date: ${new Date().toLocaleString()}`, 20, 50);

    // Add a note about the conversion
    doc.setFontSize(10);
    doc.text(
      "Note: This is a simulated conversion. In a real implementation, the content",
      20,
      70
    );
    doc.text("of your Word document would be preserved in this PDF.", 20, 80);

    // Get the PDF as a blob
    const pdfBlob = doc.output("blob");

    if (onProgress) onProgress(100);

    return pdfBlob;
  } catch (error) {
    console.error("Error converting Word to PDF:", error);
    throw new Error("Failed to convert Word to PDF");
  }
}

/**
 * Generates a PDF from a recipe
 * @param recipe Recipe to convert to PDF
 */
export async function generatePDF(recipe: Recipe | SavedRecipe) {
  try {
    toast.loading("Generating PDF...");
    const doc = new jsPDF();
    let yPos = 20;
    const margin = 20;
    const pageWidth = doc.internal.pageSize.getWidth();
    const contentWidth = pageWidth - 2 * margin;

    // Helper function to add wrapped text
    const addWrappedText = (
      text: string,
      y: number,
      maxWidth: number = contentWidth
    ) => {
      const lines = doc.splitTextToSize(text, maxWidth);
      doc.text(lines, margin, y);
      return y + lines.length * 7;
    };

    // Title
    doc.setFontSize(24);
    doc.setFont("helvetica", "bold");
    yPos = addWrappedText(recipe.title, yPos);
    yPos += 10;

    // Description
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    yPos = addWrappedText(recipe.description, yPos);
    yPos += 15;

    // Recipe Details
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("Recipe Details", margin, yPos);
    yPos += 7;
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    doc.text(`Cooking Time: ${recipe.cookingTime} minutes`, margin, yPos);
    yPos += 7;
    doc.text(`Servings: ${recipe.servings}`, margin, yPos);
    yPos += 7;
    doc.text(`Cuisine: ${recipe.cuisine}`, margin, yPos);
    yPos += 7;
    doc.text(`Difficulty: ${recipe.difficulty}`, margin, yPos);
    yPos += 15;

    // Ingredients
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("Ingredients", margin, yPos);
    yPos += 10;
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    recipe.ingredients.forEach((ingredient) => {
      const ingredientText =
        typeof ingredient === "string"
          ? ingredient
          : `${ingredient.amount} ${ingredient.unit} ${ingredient.name}${
              ingredient.notes ? ` (${ingredient.notes})` : ""
            }`;
      yPos = addWrappedText(`• ${ingredientText}`, yPos);
      yPos += 7;
    });
    yPos += 5;

    // Instructions
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("Instructions", margin, yPos);
    yPos += 10;
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    recipe.instructions.steps.forEach((step, index) => {
      if (yPos > doc.internal.pageSize.getHeight() - 20) {
        doc.addPage();
        yPos = 20;
      }
      yPos = addWrappedText(`${index + 1}. ${step}`, yPos);
      yPos += 7;
    });

    // Tips (if any)
    if (recipe.instructions.tips.length > 0) {
      yPos += 5;
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text("Tips", margin, yPos);
      yPos += 10;
      doc.setFontSize(12);
      doc.setFont("helvetica", "normal");
      recipe.instructions.tips.forEach((tip) => {
        if (yPos > doc.internal.pageSize.getHeight() - 20) {
          doc.addPage();
          yPos = 20;
        }
        yPos = addWrappedText(`• ${tip}`, yPos);
        yPos += 7;
      });
    }

    // Nutritional Information
    if (recipe.nutritionalInfo) {
      yPos += 5;
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text("Nutritional Information", margin, yPos);
      yPos += 10;
      doc.setFontSize(12);
      doc.setFont("helvetica", "normal");
      Object.entries(recipe.nutritionalInfo).forEach(([key, value]) => {
        if (yPos > doc.internal.pageSize.getHeight() - 20) {
          doc.addPage();
          yPos = 20;
        }
        doc.text(`${key}: ${value}`, margin, yPos);
        yPos += 7;
      });
    }

    // Save the PDF
    const fileName = `${recipe.title
      .toLowerCase()
      .replace(/\s+/g, "-")}-recipe.pdf`;
    doc.save(fileName);
    toast.dismiss();
    toast.success("PDF downloaded successfully!");
  } catch (error) {
    console.error("Error generating PDF:", error);
    toast.dismiss();
    toast.error("Failed to generate PDF");
  }
}
