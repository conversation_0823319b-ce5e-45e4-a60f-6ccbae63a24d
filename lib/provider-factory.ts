interface ProviderConfig {
  apiUrl: string;
  getHeaders: (apiKey: string) => Record<string, string>;
  formatBody: (model: string, messages: any[]) => Record<string, any>;
}

const providerFactory = (provider: string): ProviderConfig => {
  switch (provider.toLowerCase()) {
    case "openai":
      return {
        apiUrl: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1/chat/completions",
        getHeaders: (apiKey) => ({
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
        }),
        formatBody: (model, messages) => ({
          model,
          messages,
        }),
      };
    case "deepseek":
      return {
        apiUrl: process.env.DEEPSEEK_BASE_URL || "https://api.deepseek.com/chat/completions",
        getHeaders: (apiKey) => ({
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
        }),
        formatBody: (model, messages) => ({
          model,
          messages,
        }),
      };
    case "openrouter":
      return {
        apiUrl: process.env.OPENROUTER_BASE_URL || "https://openrouter.ai/api/v1/chat/completions",
        getHeaders: (apiKey) => ({
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
          "HTTP-Referer": "https://freetextconverter.com",
          "X-Title": "Free Text Converter",
        }),
        formatBody: (model, messages) => ({
          model,
          messages,
        }),
      };
    case "moonshot":
      // Moonshot AI provider - currently not assigned to any tools
      return {
        apiUrl: process.env.MOONSHOT_BASE_URL || "https://api.moonshot.ai/v1/chat/completions",
        getHeaders: (apiKey) => ({
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
        }),
        formatBody: (model, messages) => ({
          model,
          messages,
        }),
      };
    case "google":
      return {
        apiUrl: `${process.env.GOOGLE_BASE_URL || "https://generativelanguage.googleapis.com/v1beta/models"}/gemini-1.5-pro-latest:generateContent`,
        getHeaders: (apiKey) => ({
          "Content-Type": "application/json",
        }),
        formatBody: (model, messages) => ({
          contents: messages.map((msg: any) => ({
            role: msg.role,
            parts: [{ text: msg.content }],
          })),
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 2048,
          },
        }),
      };
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
};

export const getProvider = (provider: string, apiKey: string) => {
  if (!apiKey) {
    throw new Error(`API key for provider ${provider} is not configured.`);
  }

  const config = providerFactory(provider);

  let apiUrl = config.apiUrl;
  if (provider.toLowerCase() === "google") {
    apiUrl = `${config.apiUrl}?key=${apiKey}`;
  }

  return {
    apiUrl,
    headers: config.getHeaders(apiKey),
    formatBody: config.formatBody,
  };
};
