import { db } from '@/lib/firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';

export interface FeedbackData {
  toolName: string;
  feedbackType: 'positive' | 'negative';
  timestamp: any;
  userAgent?: string;
  sessionId?: string;
}

/**
 * Submit feedback for a tool
 * @param toolName - Name of the tool (e.g., 'unicode-finder')
 * @param feedbackType - Type of feedback ('positive' or 'negative')
 * @param sessionId - Optional session identifier (can be generated client-side)
 */
export async function submitFeedback(
  toolName: string, 
  feedbackType: 'positive' | 'negative',
  sessionId?: string
): Promise<void> {
  try {
    const feedbackData: FeedbackData = {
      toolName,
      feedbackType,
      timestamp: serverTimestamp(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
      sessionId: sessionId || generateSessionId(),
    };

    await addDoc(collection(db, 'feedback'), feedbackData);
  } catch (error) {
    console.error('Error submitting feedback:', error);
    throw error;
  }
}

/**
 * Generate a simple session ID for tracking (not personally identifiable)
 */
function generateSessionId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}
