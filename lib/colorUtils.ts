import namer from 'color-namer';

export interface ColorValues {
  hex: string;
  rgb: { r: number; g: number; b: number; a?: number };
  hsl: { h: number; s: number; l: number; a?: number };
}

export interface GradientStop {
  color: ColorValues;
  position: number; // 0-100
}

// Convert hex to RGB
export const hexToRgb = (hex: string): { r: number; g: number; b: number; a?: number } | null => {
  // Check if it's a hex with alpha
  if (hex.length === 9) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
          a: Math.round((parseInt(result[4], 16) / 255) * 100) / 100,
        }
      : null;
  }
  
  // Regular hex
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
};

// Convert RGB to hex
export const rgbToHex = (r: number, g: number, b: number, a?: number): string => {
  const toHex = (value: number) => {
    const hex = Math.round(value).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  if (a !== undefined) {
    const alphaHex = toHex(Math.round(a * 255));
    return `#${toHex(r)}${toHex(g)}${toHex(b)}${alphaHex}`;
  }
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
};

// Convert RGB to HSL
export const rgbToHsl = (r: number, g: number, b: number, a?: number): { h: number; s: number; l: number; a?: number } => {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }

  const result = {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100),
  };

  if (a !== undefined) {
    return { ...result, a };
  }

  return result;
};

// Convert HSL to RGB
export const hslToRgb = (h: number, s: number, l: number, a?: number): { r: number; g: number; b: number; a?: number } => {
  h /= 360;
  s /= 100;
  l /= 100;
  
  let r, g, b;

  if (s === 0) {
    r = g = b = l; // achromatic
  } else {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  const result = {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255),
  };

  if (a !== undefined) {
    return { ...result, a };
  }

  return result;
};

// Get color name
export const getColorName = (hex: string): string => {
  try {
    const names = namer(hex);
    return names.ntc[0].name;
  } catch (error) {
    return '';
  }
};

// Generate complementary color
export const getComplementaryColor = (color: ColorValues): ColorValues => {
  const h = (color.hsl.h + 180) % 360;
  const rgb = hslToRgb(h, color.hsl.s, color.hsl.l, color.rgb.a);
  const hex = rgbToHex(rgb.r, rgb.g, rgb.b, rgb.a);
  
  return {
    hex,
    rgb,
    hsl: { h, s: color.hsl.s, l: color.hsl.l, a: color.hsl.a },
  };
};

// Generate analogous colors
export const getAnalogousColors = (color: ColorValues): ColorValues[] => {
  const h1 = (color.hsl.h + 30) % 360;
  const h2 = (color.hsl.h - 30 + 360) % 360;
  
  const rgb1 = hslToRgb(h1, color.hsl.s, color.hsl.l, color.rgb.a);
  const hex1 = rgbToHex(rgb1.r, rgb1.g, rgb1.b, rgb1.a);
  
  const rgb2 = hslToRgb(h2, color.hsl.s, color.hsl.l, color.rgb.a);
  const hex2 = rgbToHex(rgb2.r, rgb2.g, rgb2.b, rgb2.a);
  
  return [
    {
      hex: hex1,
      rgb: rgb1,
      hsl: { h: h1, s: color.hsl.s, l: color.hsl.l, a: color.hsl.a },
    },
    {
      hex: hex2,
      rgb: rgb2,
      hsl: { h: h2, s: color.hsl.s, l: color.hsl.l, a: color.hsl.a },
    },
  ];
};

// Generate triadic colors
export const getTriadicColors = (color: ColorValues): ColorValues[] => {
  const h1 = (color.hsl.h + 120) % 360;
  const h2 = (color.hsl.h + 240) % 360;
  
  const rgb1 = hslToRgb(h1, color.hsl.s, color.hsl.l, color.rgb.a);
  const hex1 = rgbToHex(rgb1.r, rgb1.g, rgb1.b, rgb1.a);
  
  const rgb2 = hslToRgb(h2, color.hsl.s, color.hsl.l, color.rgb.a);
  const hex2 = rgbToHex(rgb2.r, rgb2.g, rgb2.b, rgb2.a);
  
  return [
    {
      hex: hex1,
      rgb: rgb1,
      hsl: { h: h1, s: color.hsl.s, l: color.hsl.l, a: color.hsl.a },
    },
    {
      hex: hex2,
      rgb: rgb2,
      hsl: { h: h2, s: color.hsl.s, l: color.hsl.l, a: color.hsl.a },
    },
  ];
};

// Calculate contrast ratio
export const getContrastRatio = (color1: string, color2: string): number => {
  const getLuminance = (hex: string) => {
    const rgb = hexToRgb(hex);
    if (!rgb) return 0;
    
    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };
  
  const l1 = getLuminance(color1);
  const l2 = getLuminance(color2);
  
  return (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
};

// Gradient presets
export const gradientPresets = [
  {
    name: 'Sunset',
    stops: [
      { color: '#FF5F6D', position: 0 },
      { color: '#FFC371', position: 100 },
    ],
    type: 'linear',
    angle: 45,
  },
  {
    name: 'Ocean',
    stops: [
      { color: '#2E3192', position: 0 },
      { color: '#1BFFFF', position: 100 },
    ],
    type: 'linear',
    angle: 90,
  },
  {
    name: 'Forest',
    stops: [
      { color: '#134E5E', position: 0 },
      { color: '#71B280', position: 100 },
    ],
    type: 'linear',
    angle: 135,
  },
  {
    name: 'Candy',
    stops: [
      { color: '#FC466B', position: 0 },
      { color: '#3F5EFB', position: 100 },
    ],
    type: 'linear',
    angle: 90,
  },
  {
    name: 'Cosmic',
    stops: [
      { color: '#3A1C71', position: 0 },
      { color: '#D76D77', position: 50 },
      { color: '#FFAF7B', position: 100 },
    ],
    type: 'linear',
    angle: 45,
  },
  {
    name: 'Emerald',
    stops: [
      { color: '#43C6AC', position: 0 },
      { color: '#191654', position: 100 },
    ],
    type: 'radial',
    shape: 'circle',
    size: 'farthest-corner',
    position: { x: 50, y: 50 },
  },
];
