import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

// Lightweight user agent detection
export const userAgent = {
  isMobile: () => {
    if (typeof window === 'undefined') return false;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      window.navigator.userAgent
    );
  },
  
  isBrowser: () => {
    return typeof window !== 'undefined';
  },
  
  isBot: () => {
    if (typeof window === 'undefined') return false;
    return /bot|crawler|spider|crawling/i.test(
      window.navigator.userAgent
    );
  },
  
  getBrowser: () => {
    if (typeof window === 'undefined') return 'unknown';
    const ua = window.navigator.userAgent;
    
    if (ua.includes('Chrome')) return 'chrome';
    if (ua.includes('Firefox')) return 'firefox';
    if (ua.includes('Safari')) return 'safari';
    if (ua.includes('Edge')) return 'edge';
    if (ua.includes('MSIE') || ua.includes('Trident/')) return 'ie';
    
    return 'unknown';
  }
};

// Export other utilities
export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
