// storage.ts
interface StorageData {
  textConverter: {
    lastText: string;
    lastConvertedText: string;
    lastSentimentResult: any;
  };
  dateCalculator: {
    startDate: string;
    endDate: string;
    daysToAdd: string;
    workdaysToAdd: string;
  };
  replaceSpaces: {
    lastInput: string;
    lastOutput: string;
  };
}

const STORAGE_KEY = 'textConverterApp';

const defaultData: StorageData = {
  textConverter: {
    lastText: '',
    lastConvertedText: '',
    lastSentimentResult: null,
  },
  dateCalculator: {
    startDate: '',
    endDate: '',
    daysToAdd: '',
    workdaysToAdd: '',
  },
  replaceSpaces: {
    lastInput: '',
    lastOutput: '',
  },
};

export const storage = {
  // Get all data
  getData: (): StorageData => {
    if (typeof window === 'undefined') return defaultData;
    const data = localStorage.getItem(STORAGE_KEY);
    return data ? JSON.parse(data) : defaultData;
  },

  // Save all data
  saveData: (data: Partial<StorageData>) => {
    if (typeof window === 'undefined') return;
    const currentData = storage.getData();
    const newData = { ...currentData, ...data };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(newData));
  },

  // Clear all data
  clearData: () => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(STORAGE_KEY);
  },

  // Get data for specific feature
  getFeatureData: <K extends keyof StorageData>(feature: K): StorageData[K] => {
    const data = storage.getData();
    return data[feature];
  },

  // Save data for specific feature
  saveFeatureData: <K extends keyof StorageData>(feature: K, data: Partial<StorageData[K]>) => {
    const currentData = storage.getData();
    const newData = {
      ...currentData,
      [feature]: {
        ...currentData[feature],
        ...data,
      },
    };
    storage.saveData(newData);
  },
};