import { charSets, zalgoChars } from './text-conversion-maps';

const mapSpecialChars = (char: string, charMap: { [key: string]: string }): string => {
  const normalChar = char.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
  return charMap[normalChar] || char;
};

export const convertText = (text: string, type: string | null): string => {
  if (!type) return text;

  switch (type) {
    case "sentence":
      return text.toLowerCase().replace(/(^\w|\.\s+\w)/gm, letter => letter.toUpperCase());
    case "lower":
      return text.toLowerCase();
    case "upper":
      return text.toUpperCase();
    case "capitalized":
      return text.toLowerCase().replace(/\b\w/g, letter => letter.toUpperCase());
    case "alternating":
      return text.split("").map((char, i) => i % 2 === 0 ? char.toLowerCase() : char.toUpperCase()).join("");
    case "title":
      return text.toLowerCase().replace(/\b\w/g, letter => letter.toUpperCase());
    case "bold":
    case "italic":
    case "script":
    case "gothic":
    case "doublestruck":
    case "monospace":
    case "sansserif":
    case "boldsansserif":
    case "italicsansserif":
    case "bolditalicsansserif":
    case "circlenegative":
    case "circled":
    case "parenthesized":
    case "fullwidth":
    case "squaredtext":
    case "mini":
    case "bent":
    case "squared":
    case "neon":
    case "bubble":
    case "tiny":
    case "aesthetic":
    case "handwriting":
      return text.split('').map(char => mapSpecialChars(char, charSets[type] || {})).join('');
    case "bolditalic":
      return text.split('').map(char => mapSpecialChars(char, charSets.boldItalic || {})).join('');
    case "strikethrough":
      return text.split('').map(char => char + '\u0336').join('');
    case "underline":
      return text.split('').map(char => char + '\u0332').join('');
    case "distorted":
        return text.split('').map(char => char + '\u0337').join('');
    case "overline":
      return text.split('').map(char => char + '\u0305').join('');
    case "doubleunderline":
      return text.split('').map(char => char + '\u0333').join('');
    case "mirror":
    case "backwards":
      return text.split('').reverse().join('');
    case "upsidedown":
      return text.split('').map(char => charSets.upsidedown[char] || char).reverse().join('');
    case "zalgo":
      return text.split('').map(char => {
        if (char.match(/[a-zA-Z]/)) {
          const numMarks = Math.floor(Math.random() * 3) + 1;
          let result = char;
          for (let i = 0; i < numMarks; i++) {
            result += zalgoChars[Math.floor(Math.random() * zalgoChars.length)];
          }
          return result;
        }
        return char;
              }).join('');
    case "regional":
        return text.split('').map(char => {
            if (char >= 'a' && char <= 'z') {
                const index = char.charCodeAt(0) - 'a'.charCodeAt(0);
                return String.fromCodePoint(0x1F1E6 + index);
            }
            if (char >= 'A' && char <= 'Z') {
                const index = char.charCodeAt(0) - 'A'.charCodeAt(0);
                return String.fromCodePoint(0x1F1E6 + index);
            }
            return char;
        }).join('');
    case "reverse":
      return text.split('').reverse().join('');
    case "shuffle":
      const chars = text.split('');
      for (let i = chars.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [chars[i], chars[j]] = [chars[j], chars[i]];
      }
      return chars.join('');
    case "leetspeak":
      return text
        .replace(/[aA]/g, '4')
        .replace(/[eE]/g, '3')
        .replace(/[lL]/g, '1')
        .replace(/[oO]/g, '0')
        .replace(/[sS]/g, '5')
        .replace(/[tT]/g, '7')
        .replace(/[iI]/g, '!')
        .replace(/[gG]/g, '9');
    case "mocking":
      return text.split('').map((char, i) => {
        if (Math.random() > 0.5) {
          return i % 2 === 0 ? char.toLowerCase() : char.toUpperCase();
        } else {
          return i % 2 === 0 ? char.toUpperCase() : char.toLowerCase();
        }
      }).join('');
    case "binary":
      return text.split('').map(char => {
        const binary = char.charCodeAt(0).toString(2);
        return binary.padStart(8, '0');
      }).join(' ');
    case "morse":
      const morseCode: { [key: string]: string } = {
        'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
        'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
        'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
        'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
        'Y': '-.--', 'Z': '--..', '0': '-----', '1': '.----', '2': '..---',
        '3': '...--', '4': '....-', '5': '.....', '6': '-....', '7': '--...',
        '8': '---..', '9': '----.', ' ': '/'
      };
      return text.toUpperCase().split('').map(char => morseCode[char] || char).join(' ');
    default:
      return text;
  }
};
