/**
 * New Relic utility functions for custom event tracking
 */

// Type definitions for New Relic events
export interface CustomEventData {
  [key: string]: string | number | boolean;
}

/**
 * Track a custom event with New Relic
 */
export function trackEvent(eventType: string, eventData: CustomEventData = {}) {
  try {
    if (typeof window !== 'undefined') {
      // Client-side tracking
      if (window.newrelic) {
        window.newrelic.recordEvent(eventType, eventData);
      }
    } else {
      // Server-side tracking
      if (process.env.NEW_RELIC_LICENSE_KEY) {
        const newrelic = require('newrelic');
        newrelic.recordEvent(eventType, eventData);
      }
    }
  } catch (error) {
    console.warn('New Relic event tracking failed:', error);
  }
}

/**
 * Track tool usage events
 */
export function trackToolUsage(toolName: string, additionalData: CustomEventData = {}) {
  trackEvent('ToolUsage', {
    toolName,
    timestamp: Date.now(),
    ...additionalData
  });
}

/**
 * Track conversion events
 */
export function trackConversion(conversionType: string, inputLength: number, outputLength: number) {
  trackEvent('TextConversion', {
    conversionType,
    inputLength,
    outputLength,
    timestamp: Date.now()
  });
}

/**
 * Track file upload events
 */
export function trackFileUpload(fileType: string, fileSize: number, toolName: string) {
  trackEvent('FileUpload', {
    fileType,
    fileSize,
    toolName,
    timestamp: Date.now()
  });
}

/**
 * Track error events
 */
export function trackError(errorType: string, errorMessage: string, toolName?: string) {
  trackEvent('UserError', {
    errorType,
    errorMessage,
    toolName: toolName || 'unknown',
    timestamp: Date.now()
  });
}

/**
 * Track user engagement events
 */
export function trackEngagement(action: string, details: CustomEventData = {}) {
  trackEvent('UserEngagement', {
    action,
    timestamp: Date.now(),
    ...details
  });
}

/**
 * Add New Relic to window type for TypeScript
 */
declare global {
  interface Window {
    newrelic?: {
      recordEvent: (eventType: string, eventData: CustomEventData) => void;
      setCustomAttribute: (key: string, value: string | number | boolean) => void;
    };
  }
}