import { Redis } from '@upstash/redis';
import { Ratelimit } from '@upstash/ratelimit';

// Create a new ratelimiter, that allows:
// - 10 requests per 10 seconds for API endpoints
// - Sliding window algorithm for better fairness
export const rateLimiter = new Ratelimit({
  redis: Redis.fromEnv(),
  // 10 requests per 10 seconds
  limiter: Ratelimit.slidingWindow(10, "10 s"),
  analytics: true,
  prefix: "app:ratelimit",
});

export async function checkRateLimit(identifier: string) {
  try {
    const { success, limit, reset, remaining } = await rateLimiter.limit(identifier);
    
    return {
      success,
      remainingTime: reset,
      limit,
      remaining,
    };
  } catch (error) {
    console.error('Rate limit check failed:', error);
    // Default to allowing the request if rate limiting fails
    return {
      success: true,
      remainingTime: 0,
      limit: 10,
      remaining: 10,
    };
  }
}