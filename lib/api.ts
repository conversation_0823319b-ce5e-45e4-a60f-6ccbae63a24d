// Interfaces
export interface SentimentResult {
  score: number;
  explanation: string;
  emotions: {
    love: number;
    joy: number;
    sadness: number;
    anger: number;
    romantic: number;
    fear: number;
  };
}

export interface ParaphraseResult {
  paraphrasedText: string;
}

// Fun queue messages for different scenarios
const BUSY_MESSAGES = [
  "🤖 Whoa there! Our AI is doing mental gymnastics for someone else. Give it a sec to catch its breath!",
  "🧠 Brain.exe is currently occupied! Another user is making our AI think deep thoughts.",
  "⏳ In line at the neural network! Your request will be processed faster than you can say 'artificial intelligence'!",
  "🎯 Target acquired, but we're in a queue! Hang tight while we process other requests.",
  "🔄 Our AI is multitasking! Your request is in line, and we'll get to it shortly.",
];

let isProcessing = false;
const processingQueue: (() => void)[] = [];

async function checkQueue(): Promise<void> {
  if (processingQueue.length === 0 || isProcessing) return;

  isProcessing = true;
  const nextCallback = processingQueue.shift();
  if (nextCallback) {
    try {
      await nextCallback();
    } catch (error) {
      console.error("Error processing queue item:", error);
    }
  }
  isProcessing = false;
  checkQueue();
}

export async function processAIRequest<T>(
  operation: () => Promise<T>
): Promise<T> {
  if (isProcessing) {
    return new Promise((resolve, reject) => {
      processingQueue.push(async () => {
        try {
          const result = await operation();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  isProcessing = true;
  try {
    return await operation();
  } finally {
    isProcessing = false;
    checkQueue();
  }
}

import { getProvider } from "./provider-factory";

export async function paraphraseText(
  text: string,
  tone: string
): Promise<ParaphraseResult> {
  return processAIRequest(async () => {
    try {
      const providerName = process.env.PARAPHRASER_PROVIDER || "deepseek";
      const modelName = process.env.PARAPHRASER_MODEL || "deepseek-chat";
      const apiKey = process.env.DEEPSEEK_API_KEY;
      if (!apiKey) {
        throw new Error("API key for paraphraser is not configured.");
      }
      const provider = getProvider(providerName, apiKey);

      const messages = [
        {
          role: "system",
          content:
            "You are a professional paraphrasing assistant. Provide only the paraphrased text without any explanations or additional comments.",
        },
        {
          role: "user",
          content: `Paraphrase this text in a ${tone} tone: "${text}"`,
        },
      ];

      const response = await fetch(provider.apiUrl, {
        method: "POST",
        headers: provider.headers,
        body: JSON.stringify(provider.formatBody(modelName, messages)),
      });

      if (!response.ok) {
        throw new Error("Failed to paraphrase text");
      }

      const data = await response.json();
      return {
        paraphrasedText: data.choices[0].message.content.trim(),
      };
    } catch (error) {
      console.error("Paraphrase error:", error);
      throw error;
    }
  });
}
