import { Recipe, RecipeGeneratorRequest, NutritionalInfo } from '@/types/recipe';

// Custom error classes for better error handling
class OpenRouterError extends Error {
  constructor(message: string, public details?: any) {
    super(message);
    this.name = 'OpenRouterError';
  }
}

class RecipeGenerationError extends Error {
  constructor(message: string, public details?: any) {
    super(message);
    this.name = 'RecipeGenerationError';
  }
}

export async function processRecipeRequest(data: RecipeGeneratorRequest): Promise<Recipe | null> {
  try {
    // Initialize recipe with loading state
    const initialRecipe: Recipe = {
      id: Date.now().toString(),
      title: "Preparing your recipe...",
      description: "Generating your delicious recipe...",
      ingredients: [],
      instructions: {
        steps: [],
        tips: []
      },
      nutritionalInfo: { calories: 0, protein: 0, carbs: 0, fat: 0 },
      cookingTime: data.filters.maxCookingTime || 30,
      servings: data.servings || 2,
      cuisine: data.filters.cuisine || 'Mixed',
      difficulty: 'medium',
      tags: [],
      status: {
        recipe: 'loading',
        image: 'idle'
      }
    };

    try {
      // Step 1: Generate Recipe Content
      const prompt = generatePrompt(data);
      const recipeContent = await generateRecipeContent(data, prompt);

      if (!recipeContent) {
        throw new RecipeGenerationError('Failed to generate recipe content');
      }

      // Step 2: Extract Recipe Information
      const title = extractTitle(recipeContent);
      const description = extractDescription(recipeContent);

      if (!title || !description) {
        throw new RecipeGenerationError('Failed to extract recipe information');
      }

      // Create recipe without image first
      const recipeWithoutImage: Recipe = {
        ...initialRecipe,
        title,
        description,
        ingredients: extractIngredients(recipeContent),
        instructions: extractInstructions(recipeContent),
        nutritionalInfo: extractNutritionalInfo(recipeContent),
        status: {
          recipe: 'completed',
          image: data.generateImage ? 'loading' : 'idle'
        }
      };

      // Step 3: Generate Image only if recipe was successful
      if (data.generateImage) {
        try {
          // Update status to generating
          const recipeGeneratingImage: Recipe = {
            ...recipeWithoutImage,
            status: {
              recipe: 'completed',
              image: 'loading'
            }
          };

          const imageUrl = await generateImage(title, description);
          if (imageUrl) {
            const signedImageUrl = await getSignedBackblazeUrl(imageUrl);
            return {
              ...recipeGeneratingImage,
              image: signedImageUrl,
              status: {
                recipe: 'completed',
                image: 'success'
              }
            };
          } else {
            return {
              ...recipeGeneratingImage,
              status: {
                recipe: 'completed',
                image: 'failed'
              },
              error: {
                image: handleImageError(new Error('image-generation-failed'))
              }
            };
          }
        } catch (imageError) {
          console.error('Image generation failed:', imageError);
          return {
            ...recipeWithoutImage,
            status: {
              recipe: 'completed',
              image: 'failed'
            },
            error: {
              image: handleImageError(imageError)
            }
          };
        }
      }

      return recipeWithoutImage;

    } catch (error) {
      console.error('Recipe generation failed:', error);
      return {
        ...initialRecipe,
        title: "Recipe Generation Failed",
        description: handleRecipeError(error),
        status: {
          recipe: 'failed',
          image: 'idle'
        },
        error: {
          recipe: error instanceof Error ? error.message : "Recipe generation failed"
        }
      };
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    return null;
  }
}

export const handleRecipeError = (error: any): string => {
  // Log the error for debugging but don't show to user
  console.error('Recipe generation error:', error);

  const funnyMessages = [
    "Oops! Our chef AI had a little kitchen mishap. Let's try cooking up that recipe again! 🧑‍🍳",
    "Looks like our recipe ingredients got a bit jumbled. Mind if we give it another stir? 🥘",
    "Our digital kitchen timer needs a quick reset. Shall we try that recipe again? ⏲️",
    "The AI sous-chef is having a coffee break. Let's catch them after their espresso! ☕",
    "Recipe machine temporarily out of order - probably too many cooks in the kitchen! 👩‍🍳👨‍🍳",
    "Our recipe pot is taking a moment to simmer. Care to try again? 🍲"
  ];

  return funnyMessages[Math.floor(Math.random() * funnyMessages.length)];
};

export const handleImageError = (error: any): string => {
  // Log the error for debugging but don't show to user
  console.error('Image generation error:', error);

  const funnyMessages = [
    "Our food photographer is adjusting the lighting. Let's try that photo again! 📸",
    "The garnish wasn't quite right. One more shot! 🌿",
    "Our AI food stylist needs a moment to perfect the plating. 🍽️",
    "Oops! The camera lens got a bit steamy from all this cooking. 📷",
    "The dish was so good, someone ate it before we could take a photo! 😋",
    "Our food photography studio is having a quick coffee break. ☕"
  ];

  return funnyMessages[Math.floor(Math.random() * funnyMessages.length)];
};

async function getSignedBackblazeUrl(imageUrl: string): Promise<string> {
  try {
    // If no image URL is provided, return empty string
    if (!imageUrl) {
      return '';
    }

    // Only get signed URL if it's a Backblaze URL
    if (!imageUrl.includes('backblazeb2.com')) {
      return imageUrl;
    }

    // Call your backend endpoint to generate a signed URL
    const response = await fetch('/api/get-signed-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to get signed URL');
    }

    const { signedUrl } = await response.json();
    return signedUrl;
  } catch (error) {
    console.error('Error getting signed URL:', error);
    return imageUrl; // Fallback to original URL
  }
}

function generatePrompt(data: RecipeGeneratorRequest): string {
  return `Generate a detailed recipe using these ingredients: ${data.ingredients.join(', ')}.
Please create a ${data.filters.cuisine || 'any'} cuisine recipe and format it with the following sections:

Title: [A descriptive name reflecting the ${data.filters.cuisine || 'chosen'} cuisine style]
Description: A brief, appetizing description highlighting the ${data.filters.cuisine || 'cuisine'} elements

Ingredients: List each ingredient with precise quantity and dont add any symbols

Instructions:
- Include somehelpful tips in parentheses for key important steps only. Tips should be in italics format
- Keep steps clear and concise
- don't include the numbers as bullet points but follow the flow.
- use emojis wherever appropriate
- Ensure each step reflects ${data.filters.cuisine || 'appropriate'} cooking techniques
- Do not include nutritional information in the steps
- Don't use any symbols in the output

Tips: Add 2-3 key tips specific to ${data.filters.cuisine || 'this'} cuisine

Nutritional Information (per serving):
- Calories (kcal)
- Protein (g)
- Carbs (g)
- Fat (g)

Please ensure each section is clearly labeled and separated, with nutritional information only in its dedicated section.`;
}

async function generateRecipeContent(data: RecipeGeneratorRequest, prompt: string): Promise<string> {
  try {
    if (!process.env.OPENROUTER_PICKUPLINEGENERATOR_KEY) {
      throw new OpenRouterError('OpenRouter API key is missing');
    }

    if (!prompt || prompt.trim() === '') {
      throw new OpenRouterError('Recipe prompt cannot be empty');
    }

    console.log('Sending request to OpenRouter API...');

    const messages = [
      {
        role: 'system',
        content: 'You are a professional chef who creates detailed, easy-to-follow recipes. Always include complete instructions and measurements. Format your response with clear section headers in plain text.'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENROUTER_PICKUPLINEGENERATOR_KEY}`,
        'HTTP-Referer': process.env.NODE_ENV === 'production' ? 'https://freetextconverter.com' : 'http://localhost:3000',
        'X-Title': 'Free Text Converter'
      },
      body: JSON.stringify({
        model: "openai/gpt-4o-mini",
        messages,
        temperature: 0.7,
        max_tokens: 1500,
        stream: false
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      console.error('OpenRouter API Error:', {
        status: response.status,
        statusText: response.statusText,
        errorData
      });
      throw new OpenRouterError(
        `API request failed with status ${response.status}: ${response.statusText}`,
        errorData
      );
    }

    const result = await response.json();
    console.log('OpenRouter API Response:', result);

    // API response validation
    if (!result?.choices?.[0]?.message?.content) {
      throw new OpenRouterError('Invalid API response format', result);
    }

    const recipeContent = result.choices[0].message.content;
    console.log('Successfully generated recipe content');
    return recipeContent;

  } catch (error) {
    console.error('Error generating recipe content:', error);
    throw error instanceof OpenRouterError
      ? new RecipeGenerationError(`Recipe generation failed: ${error.message}`, error.details)
      : new RecipeGenerationError('An unexpected error occurred while generating your recipe', error);
  }
}

async function generateImage(title: string, description: string): Promise<string | null> {
  try {
    if (!process.env.FLUX_API_KEY) {
      throw new Error('Flux API key is missing');
    }

    console.log('Submitting image generation task to Flux...');

    const headers = {
      'Content-Type': 'application/json',
      'x-key': process.env.FLUX_API_KEY,
      'Accept': 'application/json'
    };

    // Submit task to Flux API using the enhanced Ultra model
    const submitResponse = await fetch('https://api.bfl.ml/v1/flux-pro-1.1-ultra', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        prompt: `Professional food photography of ${title}. ${description}. Appetizing presentation, high-end restaurant style, beautiful lighting.`,
        prompt_upsampling: true,
        safety_tolerance: 2,
        output_format: "jpeg",
        aspect_ratio: "4:3", // Better for food photos
        raw: false // More processed for appealing food shots
      })
    });

    if (!submitResponse.ok) {
      const errorText = await submitResponse.text();
      console.error('Failed to submit Flux image generation task:', {
        status: submitResponse.status,
        statusText: submitResponse.statusText,
        error: errorText
      });
      return null;
    }

    const submitData = await submitResponse.json();
    console.log('Flux image generation task submitted:', submitData);

    if (!submitData.id) {
      console.error('No task ID received from Flux:', submitData);
      return null;
    }

    // Poll for result with a maximum of 5 attempts
    console.log('Polling for Flux image generation result...');
    for (let i = 0; i < 5; i++) {
      console.log(`Polling attempt ${i + 1}/5`);
      const resultResponse = await fetch(`https://api.bfl.ml/v1/get_result?id=${submitData.id}`, {
        headers
      });

      if (!resultResponse.ok) {
        console.error('Failed to poll Flux result:', resultResponse.statusText);
        continue;
      }

      const result = await resultResponse.json();
      console.log('Flux poll result:', result);

      if (result.status === 'Ready' && result.result?.sample) {
        console.log('Flux image generation succeeded');
        return result.result.sample;
      }

      // Wait 3 seconds before next poll
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    console.error('Flux image generation timed out or failed');
    return null;
  } catch (error) {
    console.error('Error generating image with Flux:', error);
    return null;
  }
}

// Helper functions to parse the AI response
function extractTitle(content: string | null): string {
  if (!content) return 'Recipe Not Found';

  const titleMatch = content.match(/Title:\s*\[?(.*?)\]?(?=\n|Description:|$)/i);
  if (titleMatch?.[1]) {
    return titleMatch[1]
      .replace(/^\[|\]$/g, '') // Remove square brackets if present
      .replace(/\*\*/g, '')    // Remove asterisks
      .trim();
  }

  // Fallback: try to find the first non-instruction line
  const lines = content.split('\n');
  for (const line of lines) {
    const cleanLine = line.trim()
      .replace(/\*\*/g, '')    // Remove asterisks
      .replace(/^\*|\*$/g, '') // Remove single asterisks
      .trim();

    if (cleanLine &&
        !cleanLine.includes('Instructions:') &&
        !cleanLine.startsWith('-') &&
        !cleanLine.includes('Description:') &&
        !cleanLine.includes('Ingredients:')) {
      return cleanLine;
    }
  }

  return 'Recipe Not Found';
}

function extractDescription(content: string | null): string {
  if (!content) return '';

  // First try to match the entire section
  const descSection = content.match(/Description:\s*([\s\S]*?)(?=\n\s*(?:Ingredients:|Instructions:)|$)/i);
  if (descSection?.[1]) {
    // Clean up the description text
    return descSection[1]
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('-'))
      .join(' ')
      .replace(/^\[|\]$/g, '')    // Remove square brackets
      .replace(/\*\*/g, '')       // Remove double asterisks
      .replace(/^\*|\*$/g, '')    // Remove single asterisks
      .replace(/Instructions:.*$/, '') // Remove any instructions that got included
      .replace(/\s+/g, ' ')
      .trim();
  }

  return '';
}

function extractIngredients(content: string | null): string[] {
  if (!content) return [];

  const ingredientsMatch = content.match(/Ingredients:([\s\S]*?)(?=Instructions:|$)/i);
  if (!ingredientsMatch?.[1]) return [];

  return ingredientsMatch[1]
    .split('\n')
    .map(line => line.trim())
    .filter(line => line && !line.toLowerCase().includes('ingredients'));
}

function extractInstructions(content: string | null): { steps: string[], tips: string[] } {
  if (!content) return { steps: [], tips: [] };

  const instructionsMatch = content.match(/Instructions:([\s\S]*?)(?=Nutritional Information:|Tips:|$)/i);
  if (!instructionsMatch?.[1]) return { steps: [], tips: [] };

  const lines = instructionsMatch[1]
    .split('\n')
    .map(line => line.trim())
    .filter(line => line && !line.toLowerCase().includes('instructions'));

  const steps: string[] = [];
  const tips: string[] = [];

  // Helper function to check if text contains nutritional information
  const isNutritionalInfo = (text: string): boolean => {
    const nutritionalKeywords = [
      'nutrition', 'calories', 'protein', 'carbs', 'fat',
      'kcal', 'serving', 'dietary', 'nutritional'
    ];
    return nutritionalKeywords.some(keyword =>
      text.toLowerCase().includes(keyword)
    );
  };

  lines.forEach(line => {
    // Extract tips in parentheses
    const tipMatch = line.match(/\((.*?)\)/);
    if (tipMatch) {
      const tip = tipMatch[1].trim();
      // Only add tip if it's not nutritional information
      if (!isNutritionalInfo(tip)) {
        tips.push(tip);
      }
      // Remove the tip from the step
      line = line.replace(/\((.*?)\)/, '').trim();
    }
    if (line) {
      steps.push(line);
    }
  });

  // Also extract standalone tips section if it exists
  const tipsMatch = content.match(/Tips:([\s\S]*?)(?=Nutritional Information:|$)/i);
  if (tipsMatch?.[1]) {
    const additionalTips = tipsMatch[1]
      .split('\n')
      .map(line => line.trim())
      .filter(line =>
        line &&
        !line.toLowerCase().includes('tips:') &&
        !isNutritionalInfo(line)
      );
    tips.push(...additionalTips);
  }

  return { steps, tips };
}

function extractNutritionalInfo(content: string | null): NutritionalInfo {
  if (!content) {
    return {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    };
  }

  const nutritionalMatch = content.match(/Nutritional Information[:\s]*([\s\S]*?)(?=\n\n|$)/i);
  if (!nutritionalMatch?.[1]) {
    return {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    };
  }

  const nutritionalText = nutritionalMatch[1];

  // Extract numeric values using more robust patterns
  const getNumericValue = (pattern: RegExp): number => {
    const match = nutritionalText.match(pattern);
    if (match && match[1]) {
      const value = parseFloat(match[1]);
      return isNaN(value) ? 0 : Math.round(value);
    }
    return 0;
  };

  return {
    calories: getNumericValue(/calories?:?\s*(\d+)/i),
    protein: getNumericValue(/protein:?\s*(\d+\.?\d*)/i),
    carbs: getNumericValue(/carbs?(?:\(?\s*carbohydrates?\)?)?:?\s*(\d+\.?\d*)/i),
    fat: getNumericValue(/fat:?\s*(\d+\.?\d*)/i)
  };
}
