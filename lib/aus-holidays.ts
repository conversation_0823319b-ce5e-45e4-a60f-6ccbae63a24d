import { addDays } from 'date-fns';

export interface Holiday {
  date: Date;
  name: string;
  type: 'public' | 'observance' | 'bank' | 'school';
  state?: string[];
}

export function getAustralianHolidays(year: number): Holiday[] {
  const holidays: Holiday[] = [
    // National Public Holidays
    {
      date: new Date(year, 0, 1), // January 1
      name: "New Year's Day",
      type: 'public'
    },
    {
      date: new Date(year, 0, 26), // January 26
      name: "Australia Day",
      type: 'public'
    },
    {
      date: getGoodFriday(year),
      name: "Good Friday",
      type: 'public'
    },
    {
      date: getEasterMonday(year),
      name: "Easter Monday",
      type: 'public'
    },
    {
      date: new Date(year, 3, 25), // April 25
      name: "Anzac Day",
      type: 'public'
    },
    {
      date: new Date(year, 11, 25), // December 25
      name: "Christmas Day",
      type: 'public'
    },
    {
      date: new Date(year, 11, 26), // December 26
      name: "Boxing Day",
      type: 'public'
    },
    // Labour Day - varies by state
    {
      date: new Date(year, 2, getFirstMonday(year, 2)), // First Monday in March
      name: "Labour Day",
      type: 'public',
      state: ['WA']
    },
    {
      date: new Date(year, 9, getFirstMonday(year, 9)), // First Monday in October
      name: "Labour Day",
      type: 'public',
      state: ['ACT', 'NSW', 'SA']
    },
    {
      date: new Date(year, 4, getFirstMonday(year, 4)), // First Monday in May
      name: "Labour Day",
      type: 'public',
      state: ['QLD']
    },
    // Queen's Birthday
    {
      date: new Date(year, 5, getSecondMonday(year, 5)), // Second Monday in June
      name: "Queen's Birthday",
      type: 'public',
      state: ['ACT', 'NSW', 'NT', 'SA', 'TAS', 'VIC']
    }
  ];

  return holidays;
}

// Helper functions to calculate dates
function getGoodFriday(year: number): Date {
  const easterSunday = getEasterSunday(year);
  return addDays(easterSunday, -2);
}

function getEasterMonday(year: number): Date {
  const easterSunday = getEasterSunday(year);
  return addDays(easterSunday, 1);
}

function getEasterSunday(year: number): Date {
  const f = Math.floor;
  const G = year % 19;
  const C = f(year / 100);
  const H = (C - f(C / 4) - f((8 * C + 13) / 25) + 19 * G + 15) % 30;
  const I = H - f(H / 28) * (1 - f(29 / (H + 1)) * f((21 - G) / 11));
  const J = (year + f(year / 4) + I + 2 - C + f(C / 4)) % 7;
  const L = I - J;
  const month = 3 + f((L + 40) / 44);
  const day = L + 28 - 31 * f(month / 4);

  return new Date(year, month - 1, day);
}

function getFirstMonday(year: number, month: number): number {
  const date = new Date(year, month, 1);
  const day = date.getDay();
  return 1 + ((8 - day) % 7);
}

function getSecondMonday(year: number, month: number): number {
  return getFirstMonday(year, month) + 7;
}