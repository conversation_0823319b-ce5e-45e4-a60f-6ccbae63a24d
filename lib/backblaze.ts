import { Buffer } from 'buffer';

interface BackblazeAuthResponse {
  authorizationToken: string;
  apiUrl: string;
  downloadUrl: string;
}

interface BackblazeUploadUrlResponse {
  uploadUrl: string;
  authorizationToken: string;
}

export async function getBackblazeAuth(): Promise<BackblazeAuthResponse> {
  const keyId = process.env.BACKBLAZE_KEY_ID;
  const applicationKey = process.env.BACKBLAZE_APP_KEY;
  
  const authString = Buffer.from(`${keyId}:${applicationKey}`).toString('base64');
  
  const response = await fetch('https://api.backblazeb2.com/b2api/v2/b2_authorize_account', {
    headers: {
      Authorization: `Basic ${authString}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to authenticate with Backblaze B2');
  }

  return response.json();
}

export async function getUploadUrl(authToken: string, apiUrl: string): Promise<BackblazeUploadUrlResponse> {
  const bucketId = process.env.BACKBLAZE_BUCKET;
  
  const response = await fetch(`${apiUrl}/b2api/v2/b2_get_upload_url`, {
    method: 'POST',
    headers: {
      Authorization: authToken,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ bucketId }),
  });

  if (!response.ok) {
    throw new Error('Failed to get upload URL');
  }

  return response.json();
}

export const uploadImage = async (base64Image: string): Promise<string> => {
  try {
    const response = await fetch('/api/recipe', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ base64Image }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to upload image');
    }

    const { imageUrl } = await response.json();
    return imageUrl;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};