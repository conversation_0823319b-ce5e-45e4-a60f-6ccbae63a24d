// Material Design Color Palette
// Based on Google's Material Design Color System

export interface MaterialColor {
  name: string
  hex: string
  rgb: { r: number; g: number; b: number }
  category: string
}

export const materialColors: MaterialColor[] = [
  // Red
  { name: 'Red 50', hex: '#ffebee', rgb: { r: 255, g: 235, b: 238 }, category: 'Red' },
  { name: 'Red 100', hex: '#ffcdd2', rgb: { r: 255, g: 205, b: 210 }, category: 'Red' },
  { name: 'Red 200', hex: '#ef9a9a', rgb: { r: 239, g: 154, b: 154 }, category: 'Red' },
  { name: 'Red 300', hex: '#e57373', rgb: { r: 229, g: 115, b: 115 }, category: 'Red' },
  { name: 'Red 400', hex: '#ef5350', rgb: { r: 239, g: 83, b: 80 }, category: 'Red' },
  { name: 'Red 500', hex: '#f44336', rgb: { r: 244, g: 67, b: 54 }, category: 'Red' },
  { name: 'Red 600', hex: '#e53935', rgb: { r: 229, g: 57, b: 53 }, category: 'Red' },
  { name: 'Red 700', hex: '#d32f2f', rgb: { r: 211, g: 47, b: 47 }, category: 'Red' },
  { name: 'Red 800', hex: '#c62828', rgb: { r: 198, g: 40, b: 40 }, category: 'Red' },
  { name: 'Red 900', hex: '#b71c1c', rgb: { r: 183, g: 28, b: 28 }, category: 'Red' },

  // Pink
  { name: 'Pink 50', hex: '#fce4ec', rgb: { r: 252, g: 228, b: 236 }, category: 'Pink' },
  { name: 'Pink 100', hex: '#f8bbd9', rgb: { r: 248, g: 187, b: 208 }, category: 'Pink' },
  { name: 'Pink 200', hex: '#f48fb1', rgb: { r: 244, g: 143, b: 177 }, category: 'Pink' },
  { name: 'Pink 300', hex: '#f06292', rgb: { r: 240, g: 98, b: 146 }, category: 'Pink' },
  { name: 'Pink 400', hex: '#ec407a', rgb: { r: 236, g: 64, b: 122 }, category: 'Pink' },
  { name: 'Pink 500', hex: '#e91e63', rgb: { r: 233, g: 30, b: 99 }, category: 'Pink' },
  { name: 'Pink 600', hex: '#d81b60', rgb: { r: 216, g: 27, b: 96 }, category: 'Pink' },
  { name: 'Pink 700', hex: '#c2185b', rgb: { r: 194, g: 24, b: 91 }, category: 'Pink' },
  { name: 'Pink 800', hex: '#ad1457', rgb: { r: 173, g: 20, b: 87 }, category: 'Pink' },
  { name: 'Pink 900', hex: '#880e4f', rgb: { r: 136, g: 14, b: 79 }, category: 'Pink' },

  // Purple
  { name: 'Purple 50', hex: '#f3e5f5', rgb: { r: 243, g: 229, b: 245 }, category: 'Purple' },
  { name: 'Purple 100', hex: '#e1bee7', rgb: { r: 225, g: 190, b: 231 }, category: 'Purple' },
  { name: 'Purple 200', hex: '#ce93d8', rgb: { r: 206, g: 147, b: 216 }, category: 'Purple' },
  { name: 'Purple 300', hex: '#ba68c8', rgb: { r: 186, g: 104, b: 200 }, category: 'Purple' },
  { name: 'Purple 400', hex: '#ab47bc', rgb: { r: 171, g: 71, b: 188 }, category: 'Purple' },
  { name: 'Purple 500', hex: '#9c27b0', rgb: { r: 156, g: 39, b: 176 }, category: 'Purple' },
  { name: 'Purple 600', hex: '#8e24aa', rgb: { r: 142, g: 36, b: 170 }, category: 'Purple' },
  { name: 'Purple 700', hex: '#7b1fa2', rgb: { r: 123, g: 31, b: 162 }, category: 'Purple' },
  { name: 'Purple 800', hex: '#6a1b9a', rgb: { r: 106, g: 27, b: 154 }, category: 'Purple' },
  { name: 'Purple 900', hex: '#4a148c', rgb: { r: 74, g: 20, b: 140 }, category: 'Purple' },

  // Deep Purple
  { name: 'Deep Purple 50', hex: '#ede7f6', rgb: { r: 237, g: 231, b: 246 }, category: 'Deep Purple' },
  { name: 'Deep Purple 100', hex: '#d1c4e9', rgb: { r: 209, g: 196, b: 233 }, category: 'Deep Purple' },
  { name: 'Deep Purple 200', hex: '#b39ddb', rgb: { r: 179, g: 157, b: 219 }, category: 'Deep Purple' },
  { name: 'Deep Purple 300', hex: '#9575cd', rgb: { r: 149, g: 117, b: 205 }, category: 'Deep Purple' },
  { name: 'Deep Purple 400', hex: '#7e57c2', rgb: { r: 126, g: 87, b: 194 }, category: 'Deep Purple' },
  { name: 'Deep Purple 500', hex: '#673ab7', rgb: { r: 103, g: 58, b: 183 }, category: 'Deep Purple' },
  { name: 'Deep Purple 600', hex: '#5e35b1', rgb: { r: 94, g: 53, b: 177 }, category: 'Deep Purple' },
  { name: 'Deep Purple 700', hex: '#512da8', rgb: { r: 81, g: 45, b: 168 }, category: 'Deep Purple' },
  { name: 'Deep Purple 800', hex: '#4527a0', rgb: { r: 69, g: 39, b: 160 }, category: 'Deep Purple' },
  { name: 'Deep Purple 900', hex: '#311b92', rgb: { r: 49, g: 27, b: 146 }, category: 'Deep Purple' },

  // Indigo
  { name: 'Indigo 50', hex: '#e8eaf6', rgb: { r: 232, g: 234, b: 246 }, category: 'Indigo' },
  { name: 'Indigo 100', hex: '#c5cae9', rgb: { r: 197, g: 202, b: 233 }, category: 'Indigo' },
  { name: 'Indigo 200', hex: '#9fa8da', rgb: { r: 159, g: 168, b: 218 }, category: 'Indigo' },
  { name: 'Indigo 300', hex: '#7986cb', rgb: { r: 121, g: 134, b: 203 }, category: 'Indigo' },
  { name: 'Indigo 400', hex: '#5c6bc0', rgb: { r: 92, g: 107, b: 192 }, category: 'Indigo' },
  { name: 'Indigo 500', hex: '#3f51b5', rgb: { r: 63, g: 81, b: 181 }, category: 'Indigo' },
  { name: 'Indigo 600', hex: '#3949ab', rgb: { r: 57, g: 73, b: 171 }, category: 'Indigo' },
  { name: 'Indigo 700', hex: '#303f9f', rgb: { r: 48, g: 63, b: 159 }, category: 'Indigo' },
  { name: 'Indigo 800', hex: '#283593', rgb: { r: 40, g: 53, b: 147 }, category: 'Indigo' },
  { name: 'Indigo 900', hex: '#1a237e', rgb: { r: 26, g: 35, b: 126 }, category: 'Indigo' },

  // Blue
  { name: 'Blue 50', hex: '#e3f2fd', rgb: { r: 227, g: 242, b: 253 }, category: 'Blue' },
  { name: 'Blue 100', hex: '#bbdefb', rgb: { r: 187, g: 222, b: 251 }, category: 'Blue' },
  { name: 'Blue 200', hex: '#90caf9', rgb: { r: 144, g: 202, b: 249 }, category: 'Blue' },
  { name: 'Blue 300', hex: '#64b5f6', rgb: { r: 100, g: 181, b: 246 }, category: 'Blue' },
  { name: 'Blue 400', hex: '#42a5f5', rgb: { r: 66, g: 165, b: 245 }, category: 'Blue' },
  { name: 'Blue 500', hex: '#2196f3', rgb: { r: 33, g: 150, b: 243 }, category: 'Blue' },
  { name: 'Blue 600', hex: '#1e88e5', rgb: { r: 30, g: 136, b: 229 }, category: 'Blue' },
  { name: 'Blue 700', hex: '#1976d2', rgb: { r: 25, g: 118, b: 210 }, category: 'Blue' },
  { name: 'Blue 800', hex: '#1565c0', rgb: { r: 21, g: 101, b: 192 }, category: 'Blue' },
  { name: 'Blue 900', hex: '#0d47a1', rgb: { r: 13, g: 71, b: 161 }, category: 'Blue' },

  // Light Blue
  { name: 'Light Blue 50', hex: '#e1f5fe', rgb: { r: 225, g: 245, b: 254 }, category: 'Light Blue' },
  { name: 'Light Blue 100', hex: '#b3e5fc', rgb: { r: 179, g: 229, b: 252 }, category: 'Light Blue' },
  { name: 'Light Blue 200', hex: '#81d4fa', rgb: { r: 129, g: 212, b: 250 }, category: 'Light Blue' },
  { name: 'Light Blue 300', hex: '#4fc3f7', rgb: { r: 79, g: 195, b: 247 }, category: 'Light Blue' },
  { name: 'Light Blue 400', hex: '#29b6f6', rgb: { r: 41, g: 182, b: 246 }, category: 'Light Blue' },
  { name: 'Light Blue 500', hex: '#03a9f4', rgb: { r: 3, g: 169, b: 244 }, category: 'Light Blue' },
  { name: 'Light Blue 600', hex: '#039be5', rgb: { r: 3, g: 155, b: 229 }, category: 'Light Blue' },
  { name: 'Light Blue 700', hex: '#0288d1', rgb: { r: 2, g: 136, b: 209 }, category: 'Light Blue' },
  { name: 'Light Blue 800', hex: '#0277bd', rgb: { r: 2, g: 119, b: 189 }, category: 'Light Blue' },
  { name: 'Light Blue 900', hex: '#01579b', rgb: { r: 1, g: 87, b: 155 }, category: 'Light Blue' },

  // Cyan
  { name: 'Cyan 50', hex: '#e0f2f1', rgb: { r: 224, g: 242, b: 241 }, category: 'Cyan' },
  { name: 'Cyan 100', hex: '#b2dfdb', rgb: { r: 178, g: 223, b: 219 }, category: 'Cyan' },
  { name: 'Cyan 200', hex: '#80cbc4', rgb: { r: 128, g: 203, b: 196 }, category: 'Cyan' },
  { name: 'Cyan 300', hex: '#4db6ac', rgb: { r: 77, g: 182, b: 172 }, category: 'Cyan' },
  { name: 'Cyan 400', hex: '#26a69a', rgb: { r: 38, g: 166, b: 154 }, category: 'Cyan' },
  { name: 'Cyan 500', hex: '#009688', rgb: { r: 0, g: 150, b: 136 }, category: 'Cyan' },
  { name: 'Cyan 600', hex: '#00897b', rgb: { r: 0, g: 137, b: 123 }, category: 'Cyan' },
  { name: 'Cyan 700', hex: '#00796b', rgb: { r: 0, g: 121, b: 107 }, category: 'Cyan' },
  { name: 'Cyan 800', hex: '#00695c', rgb: { r: 0, g: 105, b: 92 }, category: 'Cyan' },
  { name: 'Cyan 900', hex: '#004d40', rgb: { r: 0, g: 77, b: 64 }, category: 'Cyan' },

  // Teal
  { name: 'Teal 50', hex: '#e0f2f1', rgb: { r: 224, g: 242, b: 241 }, category: 'Teal' },
  { name: 'Teal 100', hex: '#b2dfdb', rgb: { r: 178, g: 223, b: 219 }, category: 'Teal' },
  { name: 'Teal 200', hex: '#80cbc4', rgb: { r: 128, g: 203, b: 196 }, category: 'Teal' },
  { name: 'Teal 300', hex: '#4db6ac', rgb: { r: 77, g: 182, b: 172 }, category: 'Teal' },
  { name: 'Teal 400', hex: '#26a69a', rgb: { r: 38, g: 166, b: 154 }, category: 'Teal' },
  { name: 'Teal 500', hex: '#009688', rgb: { r: 0, g: 150, b: 136 }, category: 'Teal' },
  { name: 'Teal 600', hex: '#00897b', rgb: { r: 0, g: 137, b: 123 }, category: 'Teal' },
  { name: 'Teal 700', hex: '#00796b', rgb: { r: 0, g: 121, b: 107 }, category: 'Teal' },
  { name: 'Teal 800', hex: '#00695c', rgb: { r: 0, g: 105, b: 92 }, category: 'Teal' },
  { name: 'Teal 900', hex: '#004d40', rgb: { r: 0, g: 77, b: 64 }, category: 'Teal' },

  // Green
  { name: 'Green 50', hex: '#e8f5e8', rgb: { r: 232, g: 245, b: 232 }, category: 'Green' },
  { name: 'Green 100', hex: '#c8e6c9', rgb: { r: 200, g: 230, b: 201 }, category: 'Green' },
  { name: 'Green 200', hex: '#a5d6a7', rgb: { r: 165, g: 214, b: 167 }, category: 'Green' },
  { name: 'Green 300', hex: '#81c784', rgb: { r: 129, g: 199, b: 132 }, category: 'Green' },
  { name: 'Green 400', hex: '#66bb6a', rgb: { r: 102, g: 187, b: 106 }, category: 'Green' },
  { name: 'Green 500', hex: '#4caf50', rgb: { r: 76, g: 175, b: 80 }, category: 'Green' },
  { name: 'Green 600', hex: '#43a047', rgb: { r: 67, g: 160, b: 71 }, category: 'Green' },
  { name: 'Green 700', hex: '#388e3c', rgb: { r: 56, g: 142, b: 60 }, category: 'Green' },
  { name: 'Green 800', hex: '#2e7d32', rgb: { r: 46, g: 125, b: 50 }, category: 'Green' },
  { name: 'Green 900', hex: '#1b5e20', rgb: { r: 27, g: 94, b: 32 }, category: 'Green' },

  // Light Green
  { name: 'Light Green 50', hex: '#f1f8e9', rgb: { r: 241, g: 248, b: 233 }, category: 'Light Green' },
  { name: 'Light Green 100', hex: '#dcedc8', rgb: { r: 220, g: 237, b: 200 }, category: 'Light Green' },
  { name: 'Light Green 200', hex: '#c5e1a5', rgb: { r: 197, g: 225, b: 165 }, category: 'Light Green' },
  { name: 'Light Green 300', hex: '#aed581', rgb: { r: 174, g: 213, b: 129 }, category: 'Light Green' },
  { name: 'Light Green 400', hex: '#9ccc65', rgb: { r: 156, g: 204, b: 101 }, category: 'Light Green' },
  { name: 'Light Green 500', hex: '#8bc34a', rgb: { r: 139, g: 195, b: 74 }, category: 'Light Green' },
  { name: 'Light Green 600', hex: '#7cb342', rgb: { r: 124, g: 179, b: 66 }, category: 'Light Green' },
  { name: 'Light Green 700', hex: '#689f38', rgb: { r: 104, g: 159, b: 56 }, category: 'Light Green' },
  { name: 'Light Green 800', hex: '#558b2f', rgb: { r: 85, g: 139, b: 47 }, category: 'Light Green' },
  { name: 'Light Green 900', hex: '#33691e', rgb: { r: 51, g: 105, b: 30 }, category: 'Light Green' },

  // Lime
  { name: 'Lime 50', hex: '#f9fbe7', rgb: { r: 249, g: 251, b: 231 }, category: 'Lime' },
  { name: 'Lime 100', hex: '#f0f4c3', rgb: { r: 240, g: 244, b: 195 }, category: 'Lime' },
  { name: 'Lime 200', hex: '#e6ee9c', rgb: { r: 230, g: 238, b: 156 }, category: 'Lime' },
  { name: 'Lime 300', hex: '#dce775', rgb: { r: 220, g: 231, b: 117 }, category: 'Lime' },
  { name: 'Lime 400', hex: '#d4e157', rgb: { r: 212, g: 225, b: 87 }, category: 'Lime' },
  { name: 'Lime 500', hex: '#cddc39', rgb: { r: 205, g: 220, b: 57 }, category: 'Lime' },
  { name: 'Lime 600', hex: '#c0ca33', rgb: { r: 192, g: 202, b: 51 }, category: 'Lime' },
  { name: 'Lime 700', hex: '#afb42b', rgb: { r: 175, g: 180, b: 43 }, category: 'Lime' },
  { name: 'Lime 800', hex: '#9e9d24', rgb: { r: 158, g: 157, b: 36 }, category: 'Lime' },
  { name: 'Lime 900', hex: '#827717', rgb: { r: 130, g: 119, b: 23 }, category: 'Lime' },

  // Yellow
  { name: 'Yellow 50', hex: '#fffde7', rgb: { r: 255, g: 253, b: 231 }, category: 'Yellow' },
  { name: 'Yellow 100', hex: '#fff9c4', rgb: { r: 255, g: 249, b: 196 }, category: 'Yellow' },
  { name: 'Yellow 200', hex: '#fff59d', rgb: { r: 255, g: 245, b: 157 }, category: 'Yellow' },
  { name: 'Yellow 300', hex: '#fff176', rgb: { r: 255, g: 241, b: 118 }, category: 'Yellow' },
  { name: 'Yellow 400', hex: '#ffee58', rgb: { r: 255, g: 238, b: 88 }, category: 'Yellow' },
  { name: 'Yellow 500', hex: '#ffeb3b', rgb: { r: 255, g: 235, b: 59 }, category: 'Yellow' },
  { name: 'Yellow 600', hex: '#fdd835', rgb: { r: 253, g: 216, b: 53 }, category: 'Yellow' },
  { name: 'Yellow 700', hex: '#fbc02d', rgb: { r: 251, g: 192, b: 45 }, category: 'Yellow' },
  { name: 'Yellow 800', hex: '#f9a825', rgb: { r: 249, g: 168, b: 37 }, category: 'Yellow' },
  { name: 'Yellow 900', hex: '#f57f17', rgb: { r: 245, g: 127, b: 23 }, category: 'Yellow' },

  // Amber
  { name: 'Amber 50', hex: '#fff8e1', rgb: { r: 255, g: 248, b: 225 }, category: 'Amber' },
  { name: 'Amber 100', hex: '#ffecb3', rgb: { r: 255, g: 236, b: 179 }, category: 'Amber' },
  { name: 'Amber 200', hex: '#ffe082', rgb: { r: 255, g: 224, b: 130 }, category: 'Amber' },
  { name: 'Amber 300', hex: '#ffd54f', rgb: { r: 255, g: 213, b: 79 }, category: 'Amber' },
  { name: 'Amber 400', hex: '#ffca28', rgb: { r: 255, g: 202, b: 40 }, category: 'Amber' },
  { name: 'Amber 500', hex: '#ffc107', rgb: { r: 255, g: 193, b: 7 }, category: 'Amber' },
  { name: 'Amber 600', hex: '#ffb300', rgb: { r: 255, g: 179, b: 0 }, category: 'Amber' },
  { name: 'Amber 700', hex: '#ffa000', rgb: { r: 255, g: 160, b: 0 }, category: 'Amber' },
  { name: 'Amber 800', hex: '#ff8f00', rgb: { r: 255, g: 143, b: 0 }, category: 'Amber' },
  { name: 'Amber 900', hex: '#ff6f00', rgb: { r: 255, g: 111, b: 0 }, category: 'Amber' },

  // Orange
  { name: 'Orange 50', hex: '#fff3e0', rgb: { r: 255, g: 243, b: 224 }, category: 'Orange' },
  { name: 'Orange 100', hex: '#ffe0b2', rgb: { r: 255, g: 224, b: 178 }, category: 'Orange' },
  { name: 'Orange 200', hex: '#ffcc80', rgb: { r: 255, g: 204, b: 128 }, category: 'Orange' },
  { name: 'Orange 300', hex: '#ffb74d', rgb: { r: 255, g: 183, b: 77 }, category: 'Orange' },
  { name: 'Orange 400', hex: '#ffa726', rgb: { r: 255, g: 167, b: 38 }, category: 'Orange' },
  { name: 'Orange 500', hex: '#ff9800', rgb: { r: 255, g: 152, b: 0 }, category: 'Orange' },
  { name: 'Orange 600', hex: '#fb8c00', rgb: { r: 251, g: 140, b: 0 }, category: 'Orange' },
  { name: 'Orange 700', hex: '#f57c00', rgb: { r: 245, g: 124, b: 0 }, category: 'Orange' },
  { name: 'Orange 800', hex: '#ef6c00', rgb: { r: 239, g: 108, b: 0 }, category: 'Orange' },
  { name: 'Orange 900', hex: '#e65100', rgb: { r: 230, g: 81, b: 0 }, category: 'Orange' },

  // Deep Orange
  { name: 'Deep Orange 50', hex: '#fbe9e7', rgb: { r: 251, g: 233, b: 231 }, category: 'Deep Orange' },
  { name: 'Deep Orange 100', hex: '#ffccbc', rgb: { r: 255, g: 204, b: 188 }, category: 'Deep Orange' },
  { name: 'Deep Orange 200', hex: '#ffab91', rgb: { r: 255, g: 171, b: 145 }, category: 'Deep Orange' },
  { name: 'Deep Orange 300', hex: '#ff8a65', rgb: { r: 255, g: 138, b: 101 }, category: 'Deep Orange' },
  { name: 'Deep Orange 400', hex: '#ff7043', rgb: { r: 255, g: 112, b: 67 }, category: 'Deep Orange' },
  { name: 'Deep Orange 500', hex: '#ff5722', rgb: { r: 255, g: 87, b: 34 }, category: 'Deep Orange' },
  { name: 'Deep Orange 600', hex: '#f4511e', rgb: { r: 244, g: 81, b: 30 }, category: 'Deep Orange' },
  { name: 'Deep Orange 700', hex: '#e64a19', rgb: { r: 230, g: 74, b: 25 }, category: 'Deep Orange' },
  { name: 'Deep Orange 800', hex: '#d84315', rgb: { r: 216, g: 67, b: 21 }, category: 'Deep Orange' },
  { name: 'Deep Orange 900', hex: '#bf360c', rgb: { r: 191, g: 54, b: 12 }, category: 'Deep Orange' },

  // Brown
  { name: 'Brown 50', hex: '#efebe9', rgb: { r: 239, g: 235, b: 233 }, category: 'Brown' },
  { name: 'Brown 100', hex: '#d7ccc8', rgb: { r: 215, g: 204, b: 200 }, category: 'Brown' },
  { name: 'Brown 200', hex: '#bcaaa4', rgb: { r: 188, g: 170, b: 164 }, category: 'Brown' },
  { name: 'Brown 300', hex: '#a1887f', rgb: { r: 161, g: 136, b: 127 }, category: 'Brown' },
  { name: 'Brown 400', hex: '#8d6e63', rgb: { r: 141, g: 110, b: 99 }, category: 'Brown' },
  { name: 'Brown 500', hex: '#795548', rgb: { r: 121, g: 85, b: 72 }, category: 'Brown' },
  { name: 'Brown 600', hex: '#6d4c41', rgb: { r: 109, g: 76, b: 65 }, category: 'Brown' },
  { name: 'Brown 700', hex: '#5d4037', rgb: { r: 93, g: 64, b: 55 }, category: 'Brown' },
  { name: 'Brown 800', hex: '#4e342e', rgb: { r: 78, g: 52, b: 46 }, category: 'Brown' },
  { name: 'Brown 900', hex: '#3e2723', rgb: { r: 62, g: 39, b: 35 }, category: 'Brown' },

  // Grey
  { name: 'Grey 50', hex: '#fafafa', rgb: { r: 250, g: 250, b: 250 }, category: 'Grey' },
  { name: 'Grey 100', hex: '#f5f5f5', rgb: { r: 245, g: 245, b: 245 }, category: 'Grey' },
  { name: 'Grey 200', hex: '#eeeeee', rgb: { r: 238, g: 238, b: 238 }, category: 'Grey' },
  { name: 'Grey 300', hex: '#e0e0e0', rgb: { r: 224, g: 224, b: 224 }, category: 'Grey' },
  { name: 'Grey 400', hex: '#bdbdbd', rgb: { r: 189, g: 189, b: 189 }, category: 'Grey' },
  { name: 'Grey 500', hex: '#9e9e9e', rgb: { r: 158, g: 158, b: 158 }, category: 'Grey' },
  { name: 'Grey 600', hex: '#757575', rgb: { r: 117, g: 117, b: 117 }, category: 'Grey' },
  { name: 'Grey 700', hex: '#616161', rgb: { r: 97, g: 97, b: 97 }, category: 'Grey' },
  { name: 'Grey 800', hex: '#424242', rgb: { r: 66, g: 66, b: 66 }, category: 'Grey' },
  { name: 'Grey 900', hex: '#212121', rgb: { r: 33, g: 33, b: 33 }, category: 'Grey' },

  // Blue Grey
  { name: 'Blue Grey 50', hex: '#eceff1', rgb: { r: 236, g: 239, b: 241 }, category: 'Blue Grey' },
  { name: 'Blue Grey 100', hex: '#cfd8dc', rgb: { r: 207, g: 216, b: 220 }, category: 'Blue Grey' },
  { name: 'Blue Grey 200', hex: '#b0bec5', rgb: { r: 176, g: 190, b: 197 }, category: 'Blue Grey' },
  { name: 'Blue Grey 300', hex: '#90a4ae', rgb: { r: 144, g: 164, b: 174 }, category: 'Blue Grey' },
  { name: 'Blue Grey 400', hex: '#78909c', rgb: { r: 120, g: 144, b: 156 }, category: 'Blue Grey' },
  { name: 'Blue Grey 500', hex: '#607d8b', rgb: { r: 96, g: 125, b: 139 }, category: 'Blue Grey' },
  { name: 'Blue Grey 600', hex: '#546e7a', rgb: { r: 84, g: 110, b: 122 }, category: 'Blue Grey' },
  { name: 'Blue Grey 700', hex: '#455a64', rgb: { r: 69, g: 90, b: 100 }, category: 'Blue Grey' },
  { name: 'Blue Grey 800', hex: '#37474f', rgb: { r: 55, g: 71, b: 79 }, category: 'Blue Grey' },
  { name: 'Blue Grey 900', hex: '#263238', rgb: { r: 38, g: 50, b: 56 }, category: 'Blue Grey' },
]

export const materialColorCategories = [
  'Red', 'Pink', 'Purple', 'Deep Purple', 'Indigo', 'Blue', 'Light Blue', 'Cyan', 'Teal',
  'Green', 'Light Green', 'Lime', 'Yellow', 'Amber', 'Orange', 'Deep Orange', 'Brown', 'Grey', 'Blue Grey'
]

export const getMaterialColorsByCategory = (category: string): MaterialColor[] => {
  return materialColors.filter(color => color.category === category)
}

export const findMaterialColor = (hex: string): MaterialColor | undefined => {
  return materialColors.find(color => color.hex.toLowerCase() === hex.toLowerCase())
}
