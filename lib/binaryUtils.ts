// Binary to decimal conversion
export function binaryToDecimal(binary: string): number {
  // Remove spaces and validate binary string
  const cleanBinary = binary.replace(/\s/g, '');
  if (!/^[01]+$/.test(cleanBinary)) {
    throw new Error('Invalid binary number');
  }
  return parseInt(cleanBinary, 2);
}

// Decimal to binary conversion
export function decimalToBinary(decimal: number, bits: number = 8): string {
  if (!Number.isInteger(decimal)) {
    throw new Error('Please enter a valid integer');
  }
  // Handle negative numbers using two's complement
  if (decimal < 0) {
    const maxValue = Math.pow(2, bits);
    decimal = maxValue + decimal;
  }
  return decimal.toString(2).padStart(bits, '0');
}

// Binary operations
export function binaryAND(a: string, b: string): string {
  const len = Math.max(a.length, b.length);
  const num1 = a.padStart(len, "0");
  const num2 = b.padStart(len, "0");
  let result = "";

  for (let i = 0; i < len; i++) {
    result += (Number(num1[i]) & Number(num2[i])).toString();
  }
  return result;
}

export function binaryOR(a: string, b: string): string {
  const len = Math.max(a.length, b.length);
  const num1 = a.padStart(len, "0");
  const num2 = b.padStart(len, "0");
  let result = "";

  for (let i = 0; i < len; i++) {
    result += (Number(num1[i]) | Number(num2[i])).toString();
  }
  return result;
}

export function binaryXOR(a: string, b: string): string {
  const len = Math.max(a.length, b.length);
  const num1 = a.padStart(len, "0");
  const num2 = b.padStart(len, "0");
  let result = "";

  for (let i = 0; i < len; i++) {
    result += (Number(num1[i]) ^ Number(num2[i])).toString();
  }
  return result;
}

export function binaryNOT(a: string): string {
  return a
    .split("")
    .map((bit) => (bit === "1" ? "0" : "1"))
    .join("");
}

// Binary addition with step-by-step explanation
export function binaryAdd(a: string, b: string): string {
  const len = Math.max(a.length, b.length);
  const num1 = a.padStart(len, "0");
  const num2 = b.padStart(len, "0");
  let carry = 0;
  let result = "";

  for (let i = len - 1; i >= 0; i--) {
    const sum = Number(num1[i]) + Number(num2[i]) + carry;
    result = (sum % 2).toString() + result;
    carry = Math.floor(sum / 2);
  }

  if (carry) result = "1" + result;
  return result;
}

// Binary subtraction
export function binarySubtract(a: string, b: string): string {
  const decimalA = parseInt(a, 2);
  const decimalB = parseInt(b, 2);
  const result = decimalA - decimalB;

  if (result < 0) {
    throw new Error(
      "Result is negative. Use signed binary operations for negative results."
    );
  }

  return result.toString(2);
}

// Binary multiplication
export function binaryMultiply(a: string, b: string): string {
  const decimalA = parseInt(a, 2);
  const decimalB = parseInt(b, 2);
  const result = decimalA * decimalB;
  return result.toString(2);
}

// Binary division
export function binaryDivide(a: string, b: string): string {
  const decimalA = parseInt(a, 2);
  const decimalB = parseInt(b, 2);

  if (decimalB === 0) {
    throw new Error("Division by zero is not allowed");
  }

  const result = Math.floor(decimalA / decimalB);
  return result.toString(2);
}

// Get step-by-step addition explanation
export function getBinaryAdditionSteps(
  a: string,
  b: string
): Array<{
  position: number;
  bit1: string;
  bit2: string;
  carry: string;
  sum: string;
  result: string;
}> {
  const len = Math.max(a.length, b.length);
  const num1 = a.padStart(len, "0");
  const num2 = b.padStart(len, "0");
  let carry = 0;
  let result = "";
  const steps = [];

  for (let i = len - 1; i >= 0; i--) {
    const bit1 = Number(num1[i]);
    const bit2 = Number(num2[i]);
    const sum = bit1 + bit2 + carry;
    const resultBit = sum % 2;
    const newCarry = Math.floor(sum / 2);

    result = resultBit.toString() + result;

    steps.push({
      position: len - 1 - i,
      bit1: bit1.toString(),
      bit2: bit2.toString(),
      carry: carry.toString(),
      sum: sum.toString(),
      result: resultBit.toString(),
    });

    carry = newCarry;
  }

  if (carry) {
    result = "1" + result;
    steps.push({
      position: len,
      bit1: "0",
      bit2: "0",
      carry: carry.toString(),
      sum: carry.toString(),
      result: carry.toString(),
    });
  }

  return steps;
}

// Validate binary string
export function isValidBinary(binary: string): boolean {
  return /^[01]+$/.test(binary.replace(/\s/g, ''));
}

// Format binary string with spaces for readability
export function formatBinary(binary: string): string {
  const clean = binary.replace(/\s/g, '');
  return clean.match(/.{1,4}/g)?.join(' ') || clean;
}

// Quick reference data
export const binaryReference = {
  powers: Array.from({ length: 8 }, (_, i) => ({
    power: i,
    value: Math.pow(2, i),
    binary: decimalToBinary(Math.pow(2, i), 8)
  })),
  commonValues: [
    { decimal: 0, binary: '0000' },
    { decimal: 1, binary: '0001' },
    { decimal: 2, binary: '0010' },
    { decimal: 4, binary: '0100' },
    { decimal: 8, binary: '1000' },
    { decimal: 15, binary: '1111' },
  ]
};
