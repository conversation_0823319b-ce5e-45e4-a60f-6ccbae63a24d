import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export const config = {
  matcher: [
    "/",
    "/qr-code/analytics",
    "/api/generate-image",
    "/markdown-converter",
    "/image-generator",
    "/maintenance",
  ],
};

export function middleware(request: NextRequest) {
  const response = NextResponse.next();

  // Add header for standalone pages
  if (request.nextUrl.pathname === "/") {
    response.headers.set("x-standalone-page", "true");
  }

  if (request.nextUrl.pathname.startsWith("/markdown-converter")) {
    response.headers.set("x-standalone-page", "true");
  }

  // Temporarily disable image-generator - redirect to maintenance page
  if (request.nextUrl.pathname.startsWith("/image-generator")) {
    const maintenanceUrl = new URL("/maintenance", request.url);
    return NextResponse.redirect(maintenanceUrl);
  }

  // Check if the path is protected
  if (
    request.nextUrl.pathname.startsWith("/qr-code/analytics") ||
    request.nextUrl.pathname.startsWith("/api/generate-image")
  ) {
    const token = request.cookies.get("firebaseToken")?.value;

    if (!token) {
      if (request.nextUrl.pathname.startsWith("/api/")) {
        // For API routes, return unauthorized status
        return new NextResponse(
          JSON.stringify({ error: "Authentication required" }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      // For pages, redirect to signin
      const signInUrl = new URL("/auth/signin", request.url);
      signInUrl.searchParams.set("redirect", request.nextUrl.pathname);
      return NextResponse.redirect(signInUrl);
    }
  }

  return response;
}
