# AI Provider API Keys
DEEPSEEK_API_KEY=""
OPENROUTER_GROK_API_KEY=""
GOOGLE_API_KEY=""
FLUX_API_KEY=""

# Backblaze Config
BACKBLAZE_IMAGE_KEY_ID="YOUR_KEY_HERE"
BACKBLAZE_IMAGE_APP_KEY="YOUR_KEY_HERE"
BACKBLAZE_BUCKET="YOUR_KEY_HERE"

# Email Config for Contact Form
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-app-password

# Base URL for QR Code tracking
NEXT_PUBLIC_BASE_URL=https://freetextconverter.com

# Firebase Config
NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id

# Upstash Redis Configuration
UPSTASH_REDIS_REST_URL=https://your-upstash-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-upstash-token

# Model Configuration (Optional - defaults are set in the code)
PICKUP_LINE_MODEL=""
HUMANIZER_MODEL=""
PARAPHRASER_MODEL=""
RECIPE_MODEL=""

# Provider Configuration (Optional - defaults are set in the code)
HUMANIZER_PROVIDER=""
PARAPHRASER_PROVIDER=""
PICKUP_LINE_PROVIDER=""
RECIPE_PROVIDER=""

SENTIMENT_PROVIDER=""
SENTIMENT_MODEL=""

IMAGE_GENERATION_MODEL=""

OPENROUTER_API_KEY=""
