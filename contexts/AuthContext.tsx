'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, onAuthStateChanged, signOut as firebaseSignOut } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import Cookies from 'js-cookie';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const refreshToken = async () => {
    if (user) {
      try {
        const token = await user.getIdToken(true);
        Cookies.set('firebaseToken', token, {
          path: '/',
          expires: 1, // 1 day
          secure: process.env.NODE_ENV === 'production',
          sameSite: process.env.NODE_ENV === 'production' ? 'lax' : 'strict'
        });
      } catch (error) {
        console.error('Error refreshing token:', error);
        // If token refresh fails, sign out the user
        await signOut();
      }
    }
  };

  const signOut = async () => {
    console.log("AuthContext: Starting sign out...");
    try {
      // First remove the cookie
      Cookies.remove('firebaseToken', { path: '/' });
      console.log("AuthContext: Cookie removed");

      // Then sign out from Firebase
      await firebaseSignOut(auth);
      console.log("AuthContext: Firebase sign out completed");

      // Force redirect using window.location for reliability
      setTimeout(() => {
        console.log("AuthContext: Redirecting to sign in page");
        window.location.href = '/auth/signin';
      }, 100);

    } catch (error) {
      console.error('AuthContext: Error signing out:', error);
      // Even if Firebase signOut fails, still redirect to sign in
      Cookies.remove('firebaseToken', { path: '/' });
      window.location.href = '/auth/signin';
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      console.log("Auth state changed. User:", firebaseUser);
      setUser(firebaseUser);

      if (firebaseUser) {
        console.log("User is signed in. Setting token...");
        try {
          // Get fresh token and set in cookies
          const token = await firebaseUser.getIdToken();
          Cookies.set('firebaseToken', token, {
            path: '/',
            expires: 1, // 1 day
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'lax' : 'strict'
          });
          console.log("Token set successfully.");
        } catch (error) {
          console.error('Error setting token:', error);
        }
      } else {
        console.log("User is signed out. Removing token...");
        // Remove token when user signs out
        Cookies.remove('firebaseToken', { path: '/' });
      }

      setLoading(false);
      console.log("Auth loading complete.");
    });

    // Set up token refresh interval (every 50 minutes)
    const tokenRefreshInterval = setInterval(() => {
      refreshToken();
    }, 50 * 60 * 1000);

    return () => {
      unsubscribe();
      clearInterval(tokenRefreshInterval);
    };
  }, []);

  const value: AuthContextType = {
    user,
    loading,
    signOut,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
