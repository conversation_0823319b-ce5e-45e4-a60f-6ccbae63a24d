'use client';

import { createContext, useContext, useState, useEffect } from 'react';

type ThemeType = 'native' | 'green' | 'dark' | 'rose' | 'amber' | 'cyan' | 'sunset' | 'ocean' | 'midnight' | 'aurora' | 'candy' | 'forest' | 'lavender' | 'crimson' | 'sapphire' | 'emerald' | 'volcano' | 'galaxy' | 'neon' | 'vintage' | 'monochrome' | 'tropical' | 'arctic' | 'desert' | 'cosmic' | 'royal';

interface ThemeStyles {
  bg: string;
  hover: string;
  text: string;
  border: string;
  category: string;
}

interface ThemeMetadata {
  name: string;
  description: string;
  category: 'solid' | 'gradient' | 'nature' | 'professional' | 'vibrant' | 'dark' | 'light';
  isGradient: boolean;
}

export const themeMetadata: Record<ThemeType, ThemeMetadata> = {
  native: { name: 'Native Blue', description: 'Original brand theme', category: 'solid', isGradient: false },
  green: { name: 'Emerald Green', description: 'Fresh and natural', category: 'solid', isGradient: false },
  dark: { name: 'Dark Mode', description: 'Classic dark theme', category: 'dark', isGradient: false },
  rose: { name: 'Rose Pink', description: 'Elegant and warm', category: 'solid', isGradient: false },
  amber: { name: 'Amber Gold', description: 'Warm and inviting', category: 'solid', isGradient: false },
  cyan: { name: 'Cyan Blue', description: 'Cool and modern', category: 'solid', isGradient: false },
  sunset: { name: 'Sunset', description: 'Warm gradient sunset', category: 'gradient', isGradient: true },
  ocean: { name: 'Ocean Breeze', description: 'Cool ocean gradient', category: 'gradient', isGradient: true },
  midnight: { name: 'Midnight Sky', description: 'Deep night gradient', category: 'dark', isGradient: true },
  aurora: { name: 'Aurora Borealis', description: 'Northern lights inspired', category: 'nature', isGradient: true },
  candy: { name: 'Candy Pop', description: 'Sweet and vibrant', category: 'vibrant', isGradient: true },
  forest: { name: 'Deep Forest', description: 'Rich forest greens', category: 'nature', isGradient: true },
  lavender: { name: 'Lavender Fields', description: 'Soft purple gradient', category: 'light', isGradient: true },
  crimson: { name: 'Crimson Fire', description: 'Bold red gradient', category: 'vibrant', isGradient: true },
  sapphire: { name: 'Sapphire Depths', description: 'Deep blue elegance', category: 'professional', isGradient: true },
  emerald: { name: 'Emerald Jewel', description: 'Precious green tones', category: 'nature', isGradient: true },
  volcano: { name: 'Volcanic Heat', description: 'Fiery orange-red', category: 'vibrant', isGradient: true },
  galaxy: { name: 'Galaxy Far Away', description: 'Deep space colors', category: 'dark', isGradient: true },
  neon: { name: 'Neon Glow', description: 'Electric bright green', category: 'vibrant', isGradient: true },
  vintage: { name: 'Vintage Warmth', description: 'Retro warm tones', category: 'professional', isGradient: true },
  monochrome: { name: 'Monochrome', description: 'Classic grayscale', category: 'professional', isGradient: true },
  tropical: { name: 'Tropical Paradise', description: 'Tropical blue-green', category: 'nature', isGradient: true },
  arctic: { name: 'Arctic Ice', description: 'Cool light blue', category: 'light', isGradient: true },
  desert: { name: 'Desert Sunset', description: 'Warm desert colors', category: 'nature', isGradient: true },
  cosmic: { name: 'Cosmic Wonder', description: 'Deep space mystery', category: 'dark', isGradient: true },
  royal: { name: 'Royal Purple', description: 'Majestic purple-blue', category: 'professional', isGradient: true }
};

export const themes: Record<ThemeType, ThemeStyles> = {
  native: {
    bg: "bg-[#125C88]",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-gray-200 dark:border-gray-700",
    category: "text-gray-300"
  },
  green: {
    bg: "bg-emerald-700",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-emerald-500 dark:border-emerald-600",
    category: "text-emerald-200"
  },
  dark: {
    bg: "bg-gray-900",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-gray-700 dark:border-gray-600",
    category: "text-gray-300"
  },
  rose: {
    bg: "bg-rose-600",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-rose-400 dark:border-rose-500",
    category: "text-rose-200"
  },
  amber: {
    bg: "bg-amber-600",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-amber-400 dark:border-amber-500",
    category: "text-amber-200"
  },
  cyan: {
    bg: "bg-cyan-600",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-cyan-400 dark:border-cyan-500",
    category: "text-cyan-200"
  },
  sunset: {
    bg: "bg-gradient-to-b from-orange-500 via-rose-500 to-pink-600",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-rose-400 dark:border-rose-500",
    category: "text-orange-200"
  },
  ocean: {
    bg: "bg-gradient-to-b from-blue-500 via-cyan-500 to-teal-500",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-cyan-400 dark:border-cyan-500",
    category: "text-blue-200"
  },
  midnight: {
    bg: "bg-gradient-to-b from-blue-900 via-indigo-900 to-purple-900",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-indigo-700 dark:border-indigo-800",
    category: "text-indigo-200"
  },
  aurora: {
    bg: "bg-gradient-to-b from-green-400 via-teal-500 to-blue-500",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-teal-400 dark:border-teal-500",
    category: "text-teal-200"
  },
  candy: {
    bg: "bg-gradient-to-b from-pink-400 via-purple-500 to-indigo-500",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-purple-400 dark:border-purple-500",
    category: "text-pink-200"
  },
  forest: {
    bg: "bg-gradient-to-b from-green-800 via-green-700 to-green-900",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-green-600 dark:border-green-700",
    category: "text-green-200"
  },
  lavender: {
    bg: "bg-gradient-to-b from-purple-300 via-purple-400 to-purple-600",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-purple-300 dark:border-purple-400",
    category: "text-purple-100"
  },
  crimson: {
    bg: "bg-gradient-to-b from-red-700 via-red-800 to-red-900",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-red-600 dark:border-red-700",
    category: "text-red-200"
  },
  sapphire: {
    bg: "bg-gradient-to-b from-blue-700 via-blue-800 to-blue-900",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-blue-600 dark:border-blue-700",
    category: "text-blue-200"
  },
  emerald: {
    bg: "bg-gradient-to-b from-emerald-600 via-emerald-700 to-emerald-800",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-emerald-500 dark:border-emerald-600",
    category: "text-emerald-200"
  },
  volcano: {
    bg: "bg-gradient-to-b from-orange-600 via-red-600 to-red-800",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-orange-500 dark:border-red-600",
    category: "text-orange-200"
  },
  galaxy: {
    bg: "bg-gradient-to-b from-purple-900 via-blue-900 to-black",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-purple-700 dark:border-purple-800",
    category: "text-purple-200"
  },
  neon: {
    bg: "bg-gradient-to-b from-lime-400 via-green-500 to-emerald-600",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-lime-400 dark:border-green-500",
    category: "text-lime-200"
  },
  vintage: {
    bg: "bg-gradient-to-b from-yellow-700 via-orange-700 to-red-800",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-yellow-600 dark:border-orange-700",
    category: "text-yellow-200"
  },
  monochrome: {
    bg: "bg-gradient-to-b from-gray-700 via-gray-800 to-gray-900",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-gray-600 dark:border-gray-700",
    category: "text-gray-300"
  },
  tropical: {
    bg: "bg-gradient-to-b from-teal-500 via-cyan-600 to-blue-600",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-teal-400 dark:border-cyan-500",
    category: "text-teal-200"
  },
  arctic: {
    bg: "bg-gradient-to-b from-blue-100 via-blue-300 to-blue-500",
    hover: "hover:bg-black/10",
    text: "text-blue-900",
    border: "border-blue-300 dark:border-blue-400",
    category: "text-blue-700"
  },
  desert: {
    bg: "bg-gradient-to-b from-yellow-500 via-orange-500 to-red-500",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-yellow-400 dark:border-orange-500",
    category: "text-yellow-200"
  },
  cosmic: {
    bg: "bg-gradient-to-b from-indigo-900 via-purple-900 to-pink-900",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-indigo-700 dark:border-purple-800",
    category: "text-indigo-200"
  },
  royal: {
    bg: "bg-gradient-to-b from-purple-700 via-indigo-800 to-blue-900",
    hover: "hover:bg-white/10",
    text: "text-white",
    border: "border-purple-600 dark:border-indigo-700",
    category: "text-purple-200"
  }
};

interface ThemeContextType {
  currentTheme: ThemeType;
  setCurrentTheme: (theme: ThemeType) => void;
  isDarkMode: boolean;
  setIsDarkMode: (isDark: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [currentTheme, setCurrentTheme] = useState<ThemeType>(() => {
    if (typeof window !== 'undefined') {
      const storedTheme = localStorage.getItem('theme') as ThemeType;
      // Validate that the stored theme exists in our themes object
      if (storedTheme && themes[storedTheme]) {
        return storedTheme;
      }
      // If invalid theme, reset to native and clear localStorage
      localStorage.removeItem('theme');
      return 'native';
    }
    return 'native';
  });

  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('isDarkMode') === 'true';
    }
    return false;
  });

  // Wrapper function to validate theme before setting
  const setValidatedTheme = (theme: ThemeType) => {
    if (themes[theme]) {
      setCurrentTheme(theme);
    } else {
      console.warn(`Invalid theme: ${theme}. Falling back to native.`);
      setCurrentTheme('native');
    }
  };

  useEffect(() => {
    localStorage.setItem('theme', currentTheme);
  }, [currentTheme]);

  useEffect(() => {
    document.documentElement.classList.toggle("dark", isDarkMode);
    localStorage.setItem('isDarkMode', isDarkMode.toString());
  }, [isDarkMode]);

  return (
    <ThemeContext.Provider value={{ currentTheme, setCurrentTheme: setValidatedTheme, isDarkMode, setIsDarkMode }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Utility functions for theme management
export function getThemesByCategory(category: ThemeMetadata['category']): ThemeType[] {
  return Object.entries(themeMetadata)
    .filter(([_, metadata]) => metadata.category === category)
    .map(([theme, _]) => theme as ThemeType);
}

export function getGradientThemes(): ThemeType[] {
  return Object.entries(themeMetadata)
    .filter(([_, metadata]) => metadata.isGradient)
    .map(([theme, _]) => theme as ThemeType);
}

export function getSolidThemes(): ThemeType[] {
  return Object.entries(themeMetadata)
    .filter(([_, metadata]) => !metadata.isGradient)
    .map(([theme, _]) => theme as ThemeType);
}

export function getRandomTheme(): ThemeType {
  const allThemes = Object.keys(themes) as ThemeType[];
  return allThemes[Math.floor(Math.random() * allThemes.length)];
}

export function getThemeDisplayName(theme: ThemeType): string {
  return themeMetadata[theme]?.name || theme;
}

export function getThemeDescription(theme: ThemeType): string {
  return themeMetadata[theme]?.description || '';
}

export function resetThemeStorage(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('theme');
    localStorage.removeItem('isDarkMode');
    window.location.reload();
  }
}

// Theme categories for organization
export const themeCategories = {
  solid: getThemesByCategory('solid'),
  gradient: getThemesByCategory('gradient'),
  nature: getThemesByCategory('nature'),
  professional: getThemesByCategory('professional'),
  vibrant: getThemesByCategory('vibrant'),
  dark: getThemesByCategory('dark'),
  light: getThemesByCategory('light')
} as const;
