'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the structure of our user preferences
export interface UserPreferences {
  favorites: string[]; // Array of tool hrefs that are favorited
  collapsedCategories: string[]; // Array of category names that are collapsed
}

// Default preferences
const defaultPreferences: UserPreferences = {
  favorites: [],
  collapsedCategories: [],
};

// Define the context type
interface UserPreferencesContextType {
  preferences: UserPreferences;
  toggleFavorite: (href: string) => void;
  toggleCategoryCollapse: (category: string) => void;
  isFavorite: (href: string) => boolean;
  isCategoryCollapsed: (category: string) => boolean;
}

// Create the context
const UserPreferencesContext = createContext<UserPreferencesContextType | undefined>(undefined);

// Storage key for local storage
const STORAGE_KEY = 'userPreferences';

// Provider component
export function UserPreferencesProvider({ children }: { children: ReactNode }) {
  // Initialize state from local storage or default values
  const [preferences, setPreferences] = useState<UserPreferences>(() => {
    if (typeof window !== 'undefined') {
      const savedPreferences = localStorage.getItem(STORAGE_KEY);
      return savedPreferences ? JSON.parse(savedPreferences) : defaultPreferences;
    }
    return defaultPreferences;
  });

  // Save preferences to local storage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(preferences));
    }
  }, [preferences]);

  // Toggle a tool as favorite/unfavorite
  const toggleFavorite = (href: string) => {
    setPreferences(prev => {
      const isFavorited = prev.favorites.includes(href);
      
      if (isFavorited) {
        // Remove from favorites
        return {
          ...prev,
          favorites: prev.favorites.filter(fav => fav !== href),
        };
      } else {
        // Add to favorites
        return {
          ...prev,
          favorites: [...prev.favorites, href],
        };
      }
    });
  };

  // Toggle a category as collapsed/expanded
  const toggleCategoryCollapse = (category: string) => {
    setPreferences(prev => {
      const isCollapsed = prev.collapsedCategories.includes(category);
      
      if (isCollapsed) {
        // Expand the category
        return {
          ...prev,
          collapsedCategories: prev.collapsedCategories.filter(cat => cat !== category),
        };
      } else {
        // Collapse the category
        return {
          ...prev,
          collapsedCategories: [...prev.collapsedCategories, category],
        };
      }
    });
  };

  // Check if a tool is favorited
  const isFavorite = (href: string) => {
    return preferences.favorites.includes(href);
  };

  // Check if a category is collapsed
  const isCategoryCollapsed = (category: string) => {
    return preferences.collapsedCategories.includes(category);
  };

  // Provide the context value
  const value = {
    preferences,
    toggleFavorite,
    toggleCategoryCollapse,
    isFavorite,
    isCategoryCollapsed,
  };

  return (
    <UserPreferencesContext.Provider value={value}>
      {children}
    </UserPreferencesContext.Provider>
  );
}

// Custom hook to use the context
export function useUserPreferences() {
  const context = useContext(UserPreferencesContext);
  if (context === undefined) {
    throw new Error('useUserPreferences must be used within a UserPreferencesProvider');
  }
  return context;
}
