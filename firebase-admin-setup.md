# Firebase Admin Setup for Server-Side Operations

## Required Environment Variables

Add these to your `.env.local` file:

```env
# Firebase Admin SDK Credentials (for server-side operations)
FIREBASE_CLIENT_EMAIL=your-firebase-admin-service-account-email
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key-here\n-----END PRIVATE KEY-----"
```

## How to Get These Credentials

1. **Go to Firebase Console**: <https://console.firebase.google.com/>
2. **Select your project**: `freetextconverter`
3. **Go to Project Settings** (gear icon) → **Service accounts** tab
4. **Click "Generate new private key"**
5. **Download the JSON file**
6. **Extract the values**:
   - `client_email` → `FIREBASE_CLIENT_EMAIL`
   - `private_key` → `FIREBASE_PRIVATE_KEY` (keep the quotes and newlines)

## Example

```env
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n"
```

## Why This is Needed

- API routes run on the server-side
- Server-side Firebase operations require Admin SDK authentication
- This fixes the "PERMISSION_DENIED" errors you're seeing
- Enables proper credit deduction and image saving
