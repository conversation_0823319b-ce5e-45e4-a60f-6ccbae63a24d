# New Relic Setup Instructions

This guide explains how to set up New Relic monitoring for your TextConverter application.

## 🚀 Quick Setup

### 1. Get New Relic License Key

1. Sign up for a free New Relic account at [newrelic.com](https://newrelic.com)
2. Go to "Account settings" → "API keys"
3. Copy your License Key

### 2. Configure Environment Variables

#### For Vercel Deployment:

1. Go to your Vercel project dashboard
2. Navigate to "Settings" → "Environment Variables"
3. Add these variables:

```bash
NEW_RELIC_LICENSE_KEY=your_license_key_here
NEW_RELIC_APP_NAME=TextConverter
```

#### For Local Development:

Update your `.env.local` file:

```bash
NEW_RELIC_LICENSE_KEY=your_license_key_here
NEW_RELIC_APP_NAME=TextConverter
```

### 3. Deploy to Vercel

Push your code to trigger a new deployment. New Relic will automatically start monitoring your application.

## 📊 What's Being Monitored

### Application Performance Monitoring (APM)
- Server-side performance metrics
- API response times
- Error tracking and stack traces
- Database query performance (if applicable)

### Real User Monitoring (RUM)
- Page load times
- User interactions
- Browser performance
- Core Web Vitals

### Custom Events Tracking
- **Text Conversions**: Type, input/output length
- **Tool Usage**: Which tools are used most frequently
- **User Engagement**: Copy, download actions
- **API Performance**: Request/response times, success/error rates
- **Sentiment Analysis**: Usage and results

### Error Monitoring
- JavaScript errors
- API failures
- Performance issues
- Custom error tracking

## 🔍 Key Metrics to Monitor

1. **User Engagement**
   - Most popular text conversion types
   - Copy/download rates
   - Tool usage patterns

2. **Performance**
   - API response times
   - Page load speeds
   - Error rates

3. **Business Metrics**
   - Daily active users
   - Conversion completion rates
   - Feature adoption

## 📈 Accessing Your Data

1. Log into [one.newrelic.com](https://one.newrelic.com)
2. Select your "TextConverter" application
3. Explore:
   - **APM**: Server performance and API metrics
   - **Browser**: Real user monitoring data
   - **Events**: Custom events and user behavior
   - **Errors**: Error tracking and debugging

## 🔧 Advanced Configuration

### Custom Dashboards

Create custom dashboards in New Relic to track:
- Text conversion trends
- Popular tools usage
- User engagement metrics
- API performance over time

### Alerts

Set up alerts for:
- High error rates (> 5%)
- Slow API responses (> 2 seconds)
- Low user engagement
- Memory/CPU usage issues

### Integration with Slack/Email

Configure notifications to alert your team when issues occur.

## 🆓 Free Tier Limits

New Relic's free tier includes:
- 100GB of data ingestion per month
- 1 full user
- 3-day data retention
- Basic alerting

This is sufficient for most small to medium applications.

## 🛠️ Troubleshooting

### New Relic Not Working?

1. Check environment variables are set correctly
2. Verify license key is valid
3. Check application logs for New Relic errors
4. Ensure instrumentation.ts is being called

### No Data in New Relic?

1. Wait 5-10 minutes for data to appear
2. Generate some traffic to your application
3. Check browser console for errors
4. Verify the application name matches in New Relic UI

## 📞 Support

For New Relic support:
- [New Relic Documentation](https://docs.newrelic.com)
- [Community Forum](https://discuss.newrelic.com)
- [Support Portal](https://support.newrelic.com) (paid plans)

---

**Note**: Remember to keep your New Relic license key secure and never commit it to version control.