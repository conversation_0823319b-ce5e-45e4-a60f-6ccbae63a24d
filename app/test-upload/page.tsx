'use client';

import { useState, useRef } from 'react';
import { uploadImage } from '@/lib/backblaze';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { toast } from 'sonner';

export default function TestUpload() {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      // Convert file to base64
      const reader = new FileReader();
      reader.onloadend = async () => {
        try {
          const base64String = reader.result as string;
          const url = await uploadImage(base64String);
          setImageUrl(url);
          toast.success('Image uploaded successfully!');
        } catch (error) {
          console.error('Upload error:', error);
          toast.error('Failed to upload image');
        } finally {
          setIsUploading(false);
        }
      };

      reader.onerror = (error) => {
        console.error('File reading error:', error);
        toast.error('Failed to read file');
        setIsUploading(false);
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('File handling error:', error);
      toast.error('Failed to handle file');
      setIsUploading(false);
    }
  };

  return (
    <div className="container mx-auto p-8 space-y-8">
      <h1 className="text-2xl font-bold">Test Image Upload</h1>
      
      <div className="space-y-4">
        <input
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          ref={fileInputRef}
          className="hidden"
        />
        
        <Button
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
        >
          {isUploading ? 'Uploading...' : 'Select Image'}
        </Button>

        {imageUrl && (
          <div className="space-y-4">
            <p className="text-sm break-all">Uploaded URL: {imageUrl}</p>
            <div className="relative w-64 h-64 border rounded-lg overflow-hidden">
              <Image
                src={imageUrl}
                alt="Uploaded image"
                fill
                className="object-cover"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}