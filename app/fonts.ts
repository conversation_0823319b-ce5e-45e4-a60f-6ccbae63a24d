import {
  <PERSON>,
  <PERSON>_Script,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>_Marker,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Open_Sans,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Source_Sans_3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Great_Vibes,
  Homemade_Apple
} from "next/font/google"

const inter = Inter({ subsets: ["latin"] })
const dancingScript = Dancing_Script({ subsets: ["latin"], weight: ["400", "700"] })
const pacifico = Pacifico({ subsets: ["latin"], weight: "400" })
const lobster = Lobster({ subsets: ["latin"], weight: "400" })
const permanentMarker = Permanent_Marker({ subsets: ["latin"], weight: "400" })
const satisfy = Satisfy({ subsets: ["latin"], weight: "400" })
const roboto = Roboto({ subsets: ["latin"], weight: ["400", "700"] })
const openSans = Open_Sans({ subsets: ["latin"], weight: ["400", "700"] })
const lato = La<PERSON>({ subsets: ["latin"], weight: ["400", "700"] })
const montserrat = Montserrat({ subsets: ["latin"], weight: ["400", "700"] })
const oswald = <PERSON>({ subsets: ["latin"], weight: ["400", "700"] })
const sourceSans = Source_Sans_3({ subsets: ["latin"], weight: ["400", "700"] })
const poppins = Poppins({ subsets: ["latin"], weight: ["400", "700"] })
const alexBrush = Alex_Brush({ subsets: ["latin"], weight: "400" })
const greatVibes = Great_Vibes({ subsets: ["latin"], weight: "400" })
const homemadeApple = Homemade_Apple({ subsets: ["latin"], weight: "400" })

export const fonts = {
  inter,
  dancingScript,
  pacifico,
  lobster,
  permanentMarker,
  satisfy,
  roboto,
  openSans,
  lato,
  montserrat,
  oswald,
  sourceSans,
  poppins,
  alexBrush,
  greatVibes,
  homemadeApple
}