'use client';

import { useEffect, useState } from 'react';
import { Suspense } from 'react';

interface RedirectProps {
  id: string;
}

function RedirectContent({ id }: RedirectProps) {
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const recordScan = async () => {
      try {
        console.log(' [QR Redirect] Starting redirect process for:', id);
        
        const response = await fetch(`/api/qr/scan?id=${encodeURIComponent(id)}`);
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to process scan');
        }
        
        if (!data.redirectUrl) {
          throw new Error('No redirect URL provided');
        }
        
        console.log(' [QR Redirect] Scan recorded, redirecting to:', data.redirectUrl);
        
        // Add a small delay to ensure the user sees the loading state
        setTimeout(() => {
          window.location.href = data.redirectUrl;
        }, 500);
        
      } catch (error) {
        console.error(' [QR Redirect] Error:', error);
        setError(error instanceof Error ? error.message : 'An unexpected error occurred');
      }
    };

    recordScan();
  }, [id]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center p-8 bg-white rounded-lg shadow-md">
          <div className="text-red-500 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h1 className="text-xl font-semibold text-red-800 mb-2">Error</h1>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center p-8 bg-white rounded-lg shadow-md">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <h1 className="text-xl font-semibold text-gray-800 mb-2">Redirecting...</h1>
        <p className="text-gray-600">Please wait while we process your scan.</p>
      </div>
    </div>
  );
}

export default async function QRCodeRedirect({ params }: { params: { id: string } }) {
  const id = await params.id;
  
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center p-8 bg-white rounded-lg shadow-md">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    }>
      <RedirectContent id={id} />
    </Suspense>
  );
}