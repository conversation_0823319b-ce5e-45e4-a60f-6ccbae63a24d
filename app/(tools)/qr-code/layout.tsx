import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'QR Code Generator - Free Online QR Code Generator Tool',
  description: 'Generate customizable QR codes for URLs, text, and more. Features include color customization, size adjustment, and download options.',
  keywords: 'qr code generator, qr code creator, custom qr code, free qr code tool',
  openGraph: {
    title: 'QR Code Generator - Free Online QR Code Generator Tool',
    description: 'Generate customizable QR codes for URLs, text, and more.',
    url: 'https://freetextconverter.com/qr-code',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'QR Code Generator - Free Online QR Code Generator Tool',
    description: 'Generate customizable QR codes for URLs, text, and more.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/qr-code'
  }
};

export default function QRCodeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}