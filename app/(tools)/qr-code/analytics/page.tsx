import { Metadata } from 'next';
import { QRCodeAnalytics } from "@/components/QRCodeAnalytics";
import { Footer } from "@/components/Footer";

export const metadata: Metadata = {
  title: 'QR Code Analytics - Track Your Generated QR Codes',
  description: 'View and manage your generated QR codes with detailed analytics.',
  keywords: 'qr code analytics, qr code tracking, qr code management'
};

// Separate client component for the page content
function AnalyticsContent() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <section className="py-12 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-5xl font-bold tracking-tight mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              QR Code Analytics
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Track, manage, and analyze your generated QR codes
            </p>
          </div>
        </section>

        <div className="max-w-4xl mx-auto">
          <QRCodeAnalytics />
        </div>
      </div>
      <Footer />
    </main>
  );
}

// Server component that renders the client component
export default function AnalyticsPage() {
  return <AnalyticsContent />;
}