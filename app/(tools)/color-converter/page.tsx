import { ColorConverter } from "@/components/ColorConverter";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';
import { Palette, Zap, Copy } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Color Converter & Palette Generator - HEX, RGB, HSL, Material Design Colors | Free Text Converter',
  description: 'Professional color converter with HEX, RGB, HSL conversion, Material Design colors, color blindness simulation, WCAG contrast checker, and gradient generator. Perfect for designers and developers.',
  keywords: [
    'color converter',
    'hex to rgb converter',
    'rgb to hsl converter',
    'material design colors',
    'color blindness simulator',
    'wcag contrast checker',
    'gradient generator',
    'color palette generator',
    'accessibility colors',
    'color harmony tool',
    'web colors',
    'css colors',
    'design tools',
    'color picker',
    'color codes'
  ],
  authors: [{ name: 'Free Text Converter' }],
  creator: 'Free Text Converter',
  publisher: 'Free Text Converter',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'Color Converter & Palette Generator - Professional Design Tool',
    description: 'Convert colors between HEX, RGB, HSL formats. Features Material Design colors, color blindness simulation, WCAG contrast checking, and gradient generation.',
    url: 'https://freetextconverter.com/color-converter',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
    images: [
      {
        url: '/og-color-converter.png',
        width: 1200,
        height: 630,
        alt: 'Color Converter Tool - HEX, RGB, HSL Conversion with Material Design Colors',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Color Converter & Palette Generator',
    description: 'Professional color conversion tool with Material Design colors, accessibility checking, and gradient generation.',
    images: ['/og-color-converter.png'],
  },
  alternates: {
    canonical: 'https://freetextconverter.com/color-converter'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function ColorConverterPage() {
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: 'Color Converter & Palette Generator',
    description: 'Professional color converter tool with HEX, RGB, HSL conversion, Material Design colors, color blindness simulation, and WCAG contrast checking.',
    url: 'https://freetextconverter.com/color-converter',
    applicationCategory: 'DesignApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    featureList: [
      'HEX to RGB conversion',
      'RGB to HSL conversion',
      'Material Design color palette',
      'Color blindness simulation (Protanopia, Deuteranopia, Tritanopia)',
      'WCAG 2.1 contrast checking (AA, AAA compliance)',
      'Gradient generator with linear and radial options',
      'Color harmony generation (complementary, analogous, triadic)',
      'Accessibility compliance testing',
      'Color palette management and saving',
      'CSS color code export',
      'Keyboard shortcuts for power users',
      'Mobile-responsive design'
    ],
    creator: {
      '@type': 'Organization',
      name: 'Free Text Converter',
    },
    mainEntity: {
      '@type': 'SoftwareApplication',
      name: 'Color Converter',
      applicationCategory: 'DesignApplication',
      operatingSystem: 'Web Browser',
      description: 'Convert colors between HEX, RGB, and HSL formats with advanced features for designers and developers.',
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="min-h-screen flex flex-col">
        <div className="flex-1">
          <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-4">
            <div className="max-w-5xl mx-auto px-4 pt-3 pb-4 sm:pt-4 sm:pb-6 text-center">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Color Converter
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-3">
                Professional color converter with HEX, RGB, HSL conversion, Material Design colors, color blindness simulation,
                WCAG contrast checking, and gradient generation. Perfect for designers, developers, and accessibility professionals.
              </p>

              {/* Feature highlights - Compact Version */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mt-3">
                <div className="flex flex-col items-center p-2 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:transform hover:scale-105 transition-transform">
                  <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <Palette className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Material Design Colors</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">
                    Google's Material Design palette with all color variations
                  </p>
                </div>
                <div className="flex flex-col items-center p-2 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:transform hover:scale-105 transition-transform">
                  <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <Zap className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Accessibility Tools</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">
                    WCAG contrast checking and color blindness simulation
                  </p>
                </div>
                <div className="flex flex-col items-center p-2 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:transform hover:scale-105 transition-transform">
                  <div className="w-8 h-8 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <Copy className="w-4 h-4 text-pink-600 dark:text-pink-400" />
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Advanced Features</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">
                    Keyboard shortcuts, history, and professional tools
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Main content */}
          <div className="max-w-5xl mx-auto py-8">
            <ColorConverter />
          </div>

          {/* Support section */}
          <div className="max-w-5xl mx-auto py-8">
            <SupportSection />
          </div>

          {/* FAQ Section */}
          <section className="max-w-5xl mx-auto py-8">
            <h2 className="text-xl font-semibold mb-6 text-gray-800 dark:text-gray-200">Frequently Asked Questions</h2>
            <div className="space-y-6 border-2 border-gray-100 dark:border-gray-800 rounded-xl p-6 bg-white/30 dark:bg-gray-900/30 backdrop-blur-sm">
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">What are the different color formats?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  HEX uses hexadecimal values (#RRGGBB), RGB uses decimal values for Red, Green, and Blue (rgb(r,g,b)), and HSL represents colors using Hue, Saturation, and Lightness (hsl(h,s%,l%)).
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">When should I use each format?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  HEX is commonly used in CSS and design tools, RGB is useful for manipulating individual color channels, and HSL is intuitive for adjusting color properties like saturation and brightness.
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">Are the conversions accurate?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  Yes, our converter uses precise mathematical formulas to ensure accurate color conversions between all formats. The visual preview helps verify the results.
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">Can I use this tool for web development?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  Absolutely! Our color converter is perfect for web developers and designers. It provides all the common color formats used in CSS and design tools, making it easy to copy and paste into your code.
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">What are Material Design colors?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  Material Design colors are Google's standardized color palette used across their products and recommended for modern UI design. Our tool includes all Material Design colors with proper naming and variations (50-900).
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">How does the color blindness simulator work?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  Our simulator shows how colors appear to people with different types of color vision deficiency (Protanopia, Deuteranopia, Tritanopia). This helps ensure your designs are accessible to all users.
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">What is WCAG contrast checking?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  WCAG (Web Content Accessibility Guidelines) contrast checking ensures your color combinations meet accessibility standards. Our tool tests AA and AAA compliance levels for both normal and large text.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
      <Footer />
    </div>
    </>
  );
}
