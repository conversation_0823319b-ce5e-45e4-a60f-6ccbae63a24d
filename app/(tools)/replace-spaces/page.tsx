import { Card, CardContent } from "@/components/ui/card";
import { ReplaceSpaces } from "@/components/ReplaceSpaces";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { ReplaceSpacesGuide } from "@/components/ReplaceSpacesGuide";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Replace Spaces - Online Space Replacement Tool',
  description: 'Free online tool to replace spaces in text with any character. Perfect for creating URLs, filenames, and code variables.',
  keywords: 'replace spaces, space replacement tool, remove spaces, text formatter',
  openGraph: {
    title: 'Replace Spaces - Online Space Replacement Tool',
    description: 'Free online tool to replace spaces in text with any character.',
    url: 'https://freetextconverter.com/replace-spaces',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Replace Spaces - Online Space Replacement Tool',
    description: 'Free online tool to replace spaces in text with any character.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/replace-spaces'
  }
};

export default function ReplaceSpacesPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section */}
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Replace Spaces
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Replace spaces in your text with any character or string
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Custom Characters</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Replace spaces with any character of your choice</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Instant Copy</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Copy converted text with a single click</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Bulk Processing</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Process multiple lines of text at once</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Content */}
          <div className="max-w-5xl mx-auto px-4">
            <div className="grid gap-8">
              {/* Tool Section */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <ReplaceSpaces />
              </div>

              {/* How to Guide Section */}
              <section className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg p-6">
                <ReplaceSpacesGuide />
              </section>

              {/* Support Section */}
              <SupportSection />
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}