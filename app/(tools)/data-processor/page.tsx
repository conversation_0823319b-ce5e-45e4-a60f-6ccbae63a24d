import { CSVConverter } from "@/components/CSVConverter";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';
import { <PERSON>rk<PERSON>, Table } from "lucide-react";
import { Card } from "@/components/ui/card";
import { FileDown } from "@/components/icons";
import { DataProcessor } from "@/components/DataProcessor";

export const metadata: Metadata = {
  title: 'Data Processor - Free Online Data Converter & Editor',
  description: 'Free online data tools for converting between CSV, JSON, and Excel formats. Clean, format, and analyze your data with our easy-to-use converter.',
  keywords: 'csv converter, excel converter, json to csv, csv to json, csv editor, table formatter',
  openGraph: {
    title: 'Data Processor - Free Online Data Converter & Editor',
    description: 'Free online data tools for converting between CSV, JSON, and Excel formats. Clean, format, and analyze your data.',
    url: 'https://freetextconverter.com/data-processor',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Data Processor - Free Online Data Converter & Editor',
    description: 'Free online data tools for converting between CSV, JSON, and Excel formats. Clean, format, and analyze your data.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/data-processor'
  }
};

export default function CSVToolsPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Data Processor
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Convert, clean, and analyze your data files with ease
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-6 h-6 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Format Conversion</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Convert between CSV, JSON, and Excel formats</p>
                </div>
                
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-6 h-6 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Table Editor</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Edit, sort, and filter your data</p>
                </div>
                
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-6 h-6 text-pink-600 dark:text-pink-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Data Cleaning</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Clean and validate your data</p>
                </div>
              </div>
            </div>
          </section>

          <div className="max-w-6xl mx-auto px-4">
            <div className="grid gap-6 md:grid-cols-3">
            </div>

            <DataProcessor />
          </div>
        </div>
      </div>

      <SupportSection />
      <Footer />
    </div>
  );
}
