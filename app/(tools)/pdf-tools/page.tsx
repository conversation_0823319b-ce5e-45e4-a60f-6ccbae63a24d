import { Metadata } from 'next';
import PDFToolsPageClient from '@/components/pdf-tools/PDFToolsPageClient';

export const metadata: Metadata = {
  title: 'PDF Tools - Free Online PDF Converter and Editor',
  description: 'Free online PDF tools for merging, splitting, compressing, and converting PDF files. Easy to use with no installation required.',
  keywords: 'pdf tools, pdf converter, pdf editor, merge pdf, split pdf, compress pdf, pdf to word, pdf to jpg',
  openGraph: {
    title: 'PDF Tools - Free Online PDF Converter and Editor',
    description: 'Free online PDF tools for merging, splitting, compressing, and converting PDF files.',
    url: 'https://freetextconverter.com/pdf-tools',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PDF Tools - Free Online PDF Converter and Editor',
    description: 'Free online PDF tools for merging, splitting, compressing, and converting PDF files.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools'
  }
};

export default function PDFToolsPage() {
  return <PDFToolsPageClient />;
}
