import { Metadata } from 'next';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { FileDown, Minimize } from 'lucide-react';
import { PDFCompressToolClient } from "@/components/pdf-tools/client/PDFCompressToolClient";

export const metadata: Metadata = {
  title: 'Compress PDF - Reduce PDF File Size Online',
  description: 'Reduce the size of your PDF files while maintaining quality with our free online PDF compressor tool. No installation or registration required.',
  keywords: 'compress pdf, reduce pdf size, pdf compressor, optimize pdf, shrink pdf',
  openGraph: {
    title: 'Compress PDF - Reduce PDF File Size Online',
    description: 'Reduce the size of your PDF files while maintaining quality with our free online PDF compressor tool.',
    url: 'https://freetextconverter.com/pdf-tools/compress',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Compress PDF - Reduce PDF File Size Online',
    description: 'Reduce the size of your PDF files while maintaining quality with our free online PDF compressor tool.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/compress'
  }
};

export default function CompressPDFPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-gray-900 dark:via-green-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-6 overflow-hidden">
            <div className="max-w-5xl mx-auto px-6 py-6 text-center relative">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
                Compress PDF Files
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Reduce the size of your PDF files while maintaining quality. Free, secure, and no installation required.
              </p>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative mb-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600"></div>

              {/* Content */}
              <div className="p-6">
                <PDFCompressToolClient />
              </div>
            </div>
          </div>

          {/* How to Use Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">How to Compress PDF Files</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Upload Your PDF File</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the upload button or drag and drop your PDF file into the upload area.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Choose Compression Level</h3>
                    <p className="text-gray-600 dark:text-gray-400">Select your preferred compression level: low, medium, or high. Higher compression means smaller file size but may affect quality.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Compress PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the "Compress PDF" button to reduce the size of your PDF document.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 flex items-center justify-center font-bold">4</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Download Your Compressed PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Once the compression process is complete, download your optimized PDF file.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">How does PDF compression work?</h3>
                <p className="text-gray-600 dark:text-gray-400">PDF compression works by optimizing images, removing redundant information, and applying various compression algorithms to reduce the file size while trying to maintain visual quality.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Will compression affect the quality of my PDF?</h3>
                <p className="text-gray-600 dark:text-gray-400">The impact on quality depends on the compression level you choose. Low compression will have minimal impact on quality, while high compression may reduce image quality but result in smaller file sizes.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my data secure?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files are never uploaded to our servers, ensuring complete privacy and security.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What's the maximum file size?</h3>
                <p className="text-gray-600 dark:text-gray-400">Each PDF file can be up to 100MB in size. This limit ensures optimal performance while still accommodating most PDF documents.</p>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}
