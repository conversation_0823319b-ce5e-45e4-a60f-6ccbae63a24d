import type { Metada<PERSON> } from 'next';
import { FileWarning } from 'lucide-react';
import PDFRepairToolClient from '@/components/pdf-tools/client/PDFRepairToolClient';
import Script from 'next/script';

export const metadata: Metadata = {
  title: 'Repair PDF - Fix Corrupted & Damaged PDF Files | Text Converter',
  description: 'Repair corrupted PDF files online for free. Fix broken cross-references, recover images, restore damaged PDF structure, and rescue unreadable documents with our browser-based repair tool.',
  keywords: ['repair PDF', 'fix corrupted PDF', 'recover PDF', 'damaged PDF repair', 'PDF recovery tool', 'PDF file fixer', 'broken PDF', 'unreadable PDF', 'PDF structure repair', 'recover PDF content', 'free PDF repair'],
  openGraph: {
    title: 'Repair PDF - Fix Corrupted & Damaged PDF Files',
    description: 'Repair and recover damaged PDF files with our free online tool. Fix cross-references, recover content, and make unreadable PDFs accessible again.',
    url: 'https://freetextconverter.com/pdf-tools/repair',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
    images: [
      {
        url: 'https://freetextconverter.com/og-images/pdf-repair.png',
        width: 1200,
        height: 630,
        alt: 'PDF Repair Tool',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Repair PDF - Fix Corrupted & Damaged PDF Files',
    description: 'Repair corrupted PDF files online for free. Fix PDF structure and recover content from damaged documents.',
    images: ['https://freetextconverter.com/og-images/pdf-repair.png'],
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/repair'
  }
};

export default function PDFRepairPage() {
  return (
    <>
      <Script id="schema-pdf-repair" type="application/ld+json" dangerouslySetInnerHTML={{ 
        __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "SoftwareApplication",
          "name": "PDF Repair Tool",
          "applicationCategory": "WebApplication",
          "operatingSystem": "Any",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "description": "Repair corrupted PDF files and recover content from damaged documents. Fix broken cross-references, recover images, and restore PDF structure.",
          "featureList": [
            "Standard and advanced repair options",
            "Cross-reference table fixing",
            "Image recovery",
            "Font reconstruction",
            "Browser-based processing for privacy"
          ],
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.6",
            "ratingCount": "124"
          }
        })
      }} />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-amber-50 via-white to-amber-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-12 mb-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900 dark:text-white">
                Repair PDF - Fix Corrupted PDF Files
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                Restore damaged or corrupted PDF files with our repair tool. Recover content, fix broken elements, and get your documents working again in seconds.
              </p>
            </div>
            <div className="md:w-1/3 flex justify-center">
              <div className="w-48 h-48 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center">
                <FileWarning className="h-24 w-24 text-amber-500 dark:text-amber-400" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Tool Section */}
      <section className="container mx-auto px-4 py-8 mb-12">
        <div className="max-w-4xl mx-auto">
          <PDFRepairToolClient />
        </div>
      </section>

      {/* How to Use Section */}
      <section className="container mx-auto px-4 py-8 mb-12">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">
            How to Repair Your PDF Files
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center mb-4">
                <span className="text-amber-600 dark:text-amber-400 font-bold text-lg">1</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                Upload Your PDF
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Select the corrupted PDF file from your device that you&apos;d like to repair.
              </p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center mb-4">
                <span className="text-amber-600 dark:text-amber-400 font-bold text-lg">2</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                Begin Repair Process
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Click the repair button and let our tool analyze and fix your PDF. Our algorithm will identify and repair various issues.
              </p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center mb-4">
                <span className="text-amber-600 dark:text-amber-400 font-bold text-lg">3</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                Download Repaired PDF
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Once the repair is complete, download your fixed PDF. Preview it first to ensure it meets your expectations.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What types of PDF damage can this tool fix?</h3>
            <p className="text-gray-600 dark:text-gray-400">Our repair tool can fix a variety of PDF issues including corrupted file structures, broken cross-references, damaged object streams, truncated files, and problems with the PDF header. It works best with structurally damaged files rather than files with missing content.</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Can all corrupted PDFs be repaired?</h3>
            <p className="text-gray-600 dark:text-gray-400">While our tool can fix many common PDF issues, severely damaged files with substantial content loss may not be fully recoverable. The repair success depends on the nature and extent of the corruption. We recommend trying the repair feature even for heavily damaged files, as partial recovery is often possible.</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my data secure during the repair process?</h3>
            <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files are never uploaded to our servers, ensuring complete privacy and security for sensitive documents.</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What should I do if the repaired file still has issues?</h3>
            <p className="text-gray-600 dark:text-gray-400">If the initial repair doesn&apos;t fully resolve your PDF issues, you can try our advanced repair option, which applies more intensive recovery techniques. For severely damaged files, you might need to try multiple repair approaches or consider professional recovery services.</p>
          </div>
        </div>
      </div>

      {/* Support Section */}
      <div className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">Need Help With Severely Damaged PDFs?</h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl">
              If you&apos;re having trouble repairing a particularly damaged PDF file, our support team might be able to provide additional assistance.
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <a href="/contact" className="inline-block px-6 py-3 bg-amber-600 hover:bg-amber-700 text-white rounded-lg font-medium transition-colors duration-200">
              Contact Support
            </a>
          </div>
        </div>
      </div>
    </>
  );
} 