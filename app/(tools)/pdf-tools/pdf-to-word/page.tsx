import { Metadata } from 'next';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { FileType } from 'lucide-react';
import { PDFToWordToolClient } from "@/components/pdf-tools/client/PDFToWordToolClient";

export const metadata: Metadata = {
  title: 'PDF to Word - Convert PDF to DOCX Online',
  description: 'Convert PDF files to editable Word documents with our free online PDF to Word converter. No installation or registration required.',
  keywords: 'pdf to word, pdf to docx, convert pdf to word, pdf converter, extract text from pdf',
  openGraph: {
    title: 'PDF to Word - Convert PDF to DOCX Online',
    description: 'Convert PDF files to editable Word documents with our free online PDF to Word converter.',
    url: 'https://freetextconverter.com/pdf-tools/pdf-to-word',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PDF to Word - Convert PDF to DOCX Online',
    description: 'Convert PDF files to editable Word documents with our free online PDF to Word converter.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/pdf-to-word'
  }
};

export default function PDFToWordPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-blue-50 via-cyan-50 to-teal-50 dark:from-gray-900 dark:via-blue-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-6 overflow-hidden">
            <div className="max-w-5xl mx-auto px-6 py-6 text-center relative">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                Convert PDF to Word
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Transform your PDF files into editable Word documents. Free, secure, and no installation required.
              </p>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative mb-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-blue-600 via-cyan-600 to-teal-600"></div>

              {/* Content */}
              <div className="p-6">
                <PDFToWordToolClient />
              </div>
            </div>
          </div>

          {/* How to Use Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">How to Convert PDF to Word</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Upload Your PDF File</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the upload button or drag and drop your PDF file into the upload area.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Convert to Word</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the "Convert to Word" button to transform your PDF into an editable Word document.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Wait for Processing</h3>
                    <p className="text-gray-600 dark:text-gray-400">Our tool will extract text and formatting from your PDF. This may take a few moments depending on the file size.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">4</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Download Your Word Document</h3>
                    <p className="text-gray-600 dark:text-gray-400">Once the conversion is complete, download your editable Word document.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">How accurate is the PDF to Word conversion?</h3>
                <p className="text-gray-600 dark:text-gray-400">Our converter extracts text and basic formatting from your PDF files. Simple documents with primarily text content will convert with high accuracy. Complex layouts, tables, and images may not be perfectly preserved in the converted document.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Can I convert scanned PDFs to Word?</h3>
                <p className="text-gray-600 dark:text-gray-400">For best results, your PDF should contain actual text rather than scanned images of text. If you have a scanned document, you may want to use our OCR (Optical Character Recognition) tool first to make the text recognizable.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my data secure?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files are never uploaded to our servers, ensuring complete privacy and security.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What's the maximum file size?</h3>
                <p className="text-gray-600 dark:text-gray-400">Each PDF file can be up to 100MB in size. This limit ensures optimal performance while still accommodating most PDF documents.</p>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}
