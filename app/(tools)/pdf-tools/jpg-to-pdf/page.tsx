import { Metadata } from 'next';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { FileUp } from 'lucide-react';
import { JPGToPDFToolClient } from "@/components/pdf-tools/client/JPGToPDFToolClient";

export const metadata: Metadata = {
  title: 'JPG to PDF - Convert Images to PDF Online',
  description: 'Convert JPG, PNG, and other image formats to PDF with our free online image to PDF converter. No installation or registration required.',
  keywords: 'jpg to pdf, image to pdf, convert jpg to pdf, png to pdf, picture to pdf',
  openGraph: {
    title: 'JPG to PDF - Convert Images to PDF Online',
    description: 'Convert JPG, PNG, and other image formats to PDF with our free online image to PDF converter.',
    url: 'https://freetextconverter.com/pdf-tools/jpg-to-pdf',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'JPG to PDF - Convert Images to PDF Online',
    description: 'Convert JPG, PNG, and other image formats to PDF with our free online image to PDF converter.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/jpg-to-pdf'
  }
};

export default function JPGToPDFPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-red-50 via-rose-50 to-pink-50 dark:from-gray-900 dark:via-red-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-6 overflow-hidden">
            <div className="max-w-5xl mx-auto px-6 py-6 text-center relative">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                Convert JPG to PDF
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Transform your JPG, PNG, and other image files into PDF documents. Free, secure, and no installation required.
              </p>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative mb-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-red-600 via-rose-600 to-pink-600"></div>

              {/* Content */}
              <div className="p-6">
                <JPGToPDFToolClient />
              </div>
            </div>
          </div>

          {/* How to Use Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">How to Convert JPG to PDF</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Upload Your Image Files</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the upload button or drag and drop your JPG, PNG, or other image files into the upload area. You can select multiple files at once.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Arrange Your Images</h3>
                    <p className="text-gray-600 dark:text-gray-400">Drag and drop the images to reorder them. The images will be added to the PDF in the order shown, from top to bottom.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Convert to PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the "Convert to PDF" button to transform your images into a PDF document.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 flex items-center justify-center font-bold">4</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Download Your PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Once the conversion process is complete, download your new PDF file.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What image formats are supported?</h3>
                <p className="text-gray-600 dark:text-gray-400">Our tool supports all common image formats including JPG, JPEG, PNG, BMP, GIF, and TIFF. You can even mix different image formats in a single PDF.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Can I customize the PDF page size?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, you can choose from standard page sizes like A4, Letter, or Legal, or you can set a custom page size for your PDF document.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my data secure?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files are never uploaded to our servers, ensuring complete privacy and security.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What's the maximum number of images I can convert?</h3>
                <p className="text-gray-600 dark:text-gray-400">You can convert up to 50 images at once with our free tool. For larger conversions, you may need to process them in batches.</p>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}
