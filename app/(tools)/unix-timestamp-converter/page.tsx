import { UnixTimestampConverter } from "@/components/UnixTimestampConverter";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';
import { Clock, Zap, Calendar } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Unix Timestamp Converter - Convert Unix Timestamps to Human Readable Dates',
  description: 'Convert Unix timestamps to human-readable dates and vice versa. Features real-time conversion, timezone support, and relative time display.',
  keywords: 'unix timestamp converter, epoch converter, timestamp to date, date to timestamp, unix time',
  openGraph: {
    title: 'Unix Timestamp Converter - Convert Unix Timestamps to Human Readable Dates',
    description: 'Convert Unix timestamps to human-readable dates and vice versa with our free online converter.',
    url: 'https://freetextconverter.com/unix-timestamp-converter',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Unix Timestamp Converter - Convert Unix Timestamps to Human Readable Dates',
    description: 'Convert Unix timestamps to human-readable dates and vice versa with our free online converter.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/unix-timestamp-converter'
  }
};

export default function UnixTimestampConverterPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          <section className="bg-gradient-to-br from-green-50 via-teal-50 to-emerald-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 rounded-lg">
            <div className="max-w-4xl mx-auto px-4 pt-8 pb-12 sm:pt-10 sm:pb-16 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
                Unix Timestamp Converter
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
                Convert Unix timestamps to human-readable dates and vice versa. Features timezone support, relative time, and instant conversion.
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:transform hover:scale-105 transition-transform">
                  <div className="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-3">
                    <Clock className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Real-Time Conversion</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    Instant conversion between Unix timestamps and human dates
                  </p>
                </div>
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:transform hover:scale-105 transition-transform">
                  <div className="w-12 h-12 rounded-full bg-teal-100 dark:bg-teal-900 flex items-center justify-center mb-3">
                    <Zap className="w-6 h-6 text-teal-600 dark:text-teal-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Time Information</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    Local time, UTC time, timezone, and relative time display
                  </p>
                </div>
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:transform hover:scale-105 transition-transform">
                  <div className="w-12 h-12 rounded-full bg-emerald-100 dark:bg-emerald-900 flex items-center justify-center mb-3">
                    <Calendar className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Current Time</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    One-click current timestamp and date generation
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Main content */}
          <div className="max-w-4xl mx-auto py-8">
            <UnixTimestampConverter />
          </div>

          {/* Support section */}
          <div className="max-w-4xl mx-auto py-8">
            <SupportSection />
          </div>

          {/* FAQ Section */}
          <section className="max-w-4xl mx-auto py-8">
            <h2 className="text-xl font-semibold mb-6 text-gray-800 dark:text-gray-200">Frequently Asked Questions</h2>
            <div className="space-y-6 border-2 border-gray-100 dark:border-gray-800 rounded-xl p-6 bg-white/30 dark:bg-gray-900/30 backdrop-blur-sm">
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="font-medium mb-2 text-gray-900 dark:text-gray-100">What is a Unix timestamp?</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  A Unix timestamp (also known as Unix time or epoch time) represents the number of seconds that have elapsed since January 1, 1970, 00:00:00 UTC. It's widely used in computer systems and programming.
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="font-medium mb-2 text-gray-900 dark:text-gray-100">Why do some timestamps have 10 digits while others have 13?</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Unix timestamps can be represented in seconds (10 digits) or milliseconds (13 digits). Our converter automatically detects and handles both formats. The standard Unix timestamp uses seconds.
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="font-medium mb-2 text-gray-900 dark:text-gray-100">How accurate is the conversion?</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Our converter provides precise conversions and accounts for timezone differences. The displayed times are accurate to the second and include timezone information for clarity.
                </p>
              </div>
            </div>
          </section>

          {/* Additional Resources */}
          <section className="max-w-4xl mx-auto py-8">
            <h2 className="text-xl font-semibold mb-6 text-gray-800 dark:text-gray-200">Additional Resources</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border-2 border-gray-100 dark:border-gray-800 rounded-xl p-6 bg-white/30 dark:bg-gray-900/30 backdrop-blur-sm">
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6">
                <h3 className="font-medium mb-4 text-gray-900 dark:text-gray-100">Common Use Cases</h3>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li>• Database timestamp conversion</li>
                  <li>• API response parsing</li>
                  <li>• Log file analysis</li>
                  <li>• Event scheduling</li>
                  <li>• Cross-timezone coordination</li>
                </ul>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6">
                <h3 className="font-medium mb-4 text-gray-900 dark:text-gray-100">Tips & Tricks</h3>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li>• Use the timezone selector for accurate local times</li>
                  <li>• Switch between seconds and milliseconds as needed</li>
                  <li>• Copy timestamps directly to clipboard</li>
                  <li>• Use common timestamps for quick reference</li>
                  <li>• Check relative time for human-readable durations</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Footer */}
        </div>
      </div>
      <Footer />
    </div>
  )
}
