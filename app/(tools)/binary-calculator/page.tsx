import { BinaryCalculator } from "@/components/BinaryCalculator";
import { BinaryArithmeticCalculator } from "@/components/BinaryArithmeticCalculator";
import { BinaryOperations } from "@/components/BinaryOperations";
import { BinaryTutorialBeginner } from "@/components/BinaryTutorialBeginner";
import { BinaryTutorialIntermediate } from "@/components/BinaryTutorialIntermediate";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Binary Calculator - Free Online Binary, Hex, Decimal & Octal Converter with Arithmetic Operations',
  description: 'Free online binary calculator with arithmetic operations (+, -, ×, ÷), bitwise operations (AND, OR, XOR, NOT), number system conversion (binary, decimal, hexadecimal, octal), and interactive tutorials. Learn binary math with step-by-step explanations, memory functions, and calculation history.',
  keywords: 'binary calculator, binary converter, binary arithmetic, bitwise operations, decimal to binary, hex converter, hexadecimal calculator, octal converter, binary math, binary tutorial, binary addition, binary subtraction, binary multiplication, binary division, AND OR XOR NOT operations, number system converter, base converter, programming calculator, computer science calculator, digital logic calculator, binary to hex, hex to binary, binary to decimal, decimal to binary',
  openGraph: {
    title: 'Binary Calculator - Free Online Binary, Hex, Decimal & Octal Converter with Arithmetic Operations',
    description: 'Free online binary calculator with arithmetic operations, bitwise operations, number system conversion, and interactive tutorials. Perfect for programmers, students, and engineers.',
    url: 'https://freetextconverter.com/binary-calculator',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Binary Calculator - Free Online Binary, Hex, Decimal & Octal Converter',
    description: 'Free online binary calculator with arithmetic operations, bitwise operations, number system conversion, and interactive tutorials.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/binary-calculator'
  }
};

export default function BinaryCalculatorPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Binary Calculator & Learning Platform
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Complete binary calculator with arithmetic operations (+, -, ×, ÷), bitwise operations (AND, OR, XOR, NOT), number system conversion (Binary ↔ Decimal ↔ Hexadecimal ↔ Octal), memory functions, and interactive tutorials
              </p>

              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Binary Arithmetic</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Add, subtract, multiply, divide</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Bitwise Operations</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">AND, OR, XOR, NOT operations</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Hex & Multi-Base</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Binary, Decimal, Hex, Octal</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Interactive Tutorials</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Learn binary with step-by-step guides</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative">
            {/* Decorative background elements */}
            <div className="absolute -top-20 -right-20 w-64 h-64 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-full blur-3xl -z-10"></div>
            <div className="absolute -bottom-20 -left-20 w-64 h-64 bg-gradient-to-tr from-pink-400/5 to-yellow-400/5 rounded-full blur-3xl -z-10"></div>

            {/* Card with pattern background */}
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Decorative pattern */}
              <div className="absolute inset-0 opacity-5 dark:opacity-10 pointer-events-none">
                <div className="absolute inset-0 bg-grid-gray-900/[0.2] [mask-image:linear-gradient(to_bottom,white,transparent)]"
                     style={{ backgroundSize: '32px 32px' }}></div>
              </div>

              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"></div>

              {/* Content */}
              <div className="p-6">
                <Tabs defaultValue="converter" className="w-full">
                  <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="converter">Converter</TabsTrigger>
                    <TabsTrigger value="calculator">Calculator</TabsTrigger>
                    <TabsTrigger value="bitwise">Bitwise Ops</TabsTrigger>
                    <TabsTrigger value="beginner">Learn Basic</TabsTrigger>
                    <TabsTrigger value="intermediate">Learn Advanced</TabsTrigger>
                  </TabsList>

                  <TabsContent value="converter" className="mt-6">
                    <BinaryCalculator />
                  </TabsContent>

                  <TabsContent value="calculator" className="mt-6">
                    <BinaryArithmeticCalculator />
                  </TabsContent>

                  <TabsContent value="bitwise" className="mt-6">
                    <BinaryOperations />
                  </TabsContent>

                  <TabsContent value="beginner" className="mt-6">
                    <BinaryTutorialBeginner />
                  </TabsContent>

                  <TabsContent value="intermediate" className="mt-6">
                    <BinaryTutorialIntermediate />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </div>

          {/* Guide Section */}
          <div className="relative mt-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-green-500 via-teal-500 to-cyan-500"></div>

              <div className="p-6 space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Binary, Hexadecimal & Number System Calculator Guide
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    Learn how to use our comprehensive binary calculator for arithmetic operations, bitwise operations, and number system conversions between Binary, Decimal, Hexadecimal (Hex), and Octal formats
                  </p>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                        <svg className="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <h3 className="font-semibold text-blue-700 dark:text-blue-400">Supported Number Systems</h3>
                    </div>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Binary (Base-2) - Uses 0,1</li>
                      <li>Decimal (Base-10) - Uses 0-9</li>
                      <li>Hexadecimal (Base-16) - Uses 0-9,A-F</li>
                      <li>Octal (Base-8) - Uses 0-7</li>
                      <li>Pentary (Base-5) - Uses 0-4</li>
                    </ul>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                        <svg className="h-5 w-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <h3 className="font-semibold text-purple-700 dark:text-purple-400">Features</h3>
                    </div>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Real-time conversion & calculation</li>
                      <li>Binary arithmetic (+, -, ×, ÷)</li>
                      <li>Bitwise operations (AND, OR, XOR, NOT)</li>
                      <li>Memory functions (M+, M-, MR, MC)</li>
                      <li>Calculation history & export</li>
                      <li>Step-by-step explanations</li>
                    </ul>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center">
                        <svg className="h-5 w-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h3 className="font-semibold text-pink-700 dark:text-pink-400">Common Uses</h3>
                    </div>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Programming & Software Development</li>
                      <li>Computer Science & Engineering Education</li>
                      <li>Digital Electronics & Hardware Design</li>
                      <li>Cybersecurity & Cryptography</li>
                      <li>Data Analysis & Processing</li>
                      <li>Game Development & Graphics Programming</li>
                    </ul>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                        <svg className="h-5 w-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      </div>
                      <h3 className="font-semibold text-yellow-700 dark:text-yellow-400">How to Use</h3>
                    </div>
                    <ol className="list-decimal list-inside space-y-1 text-sm">
                      <li>Choose your tool: Converter, Calculator, or Bitwise Operations</li>
                      <li>Enter numbers in Binary, Decimal, Hex, or Octal format</li>
                      <li>Perform arithmetic or bitwise operations</li>
                      <li>View step-by-step calculations and explanations</li>
                      <li>Use memory functions and export calculation history</li>
                      <li>Learn with interactive tutorials and exercises</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}
