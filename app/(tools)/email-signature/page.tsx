import { EmailSignature } from '@/components/EmailSignature';
import { Footer } from '@/components/Footer';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Email Signature Generator - TextConverter',
  description: 'Create professional email signatures with our easy-to-use generator. Customize your signature with your personal details, social media links, and profile image.',
};

export default function EmailSignaturePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section with unique gradient */}
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-4 pb-8 sm:pt-6 sm:pb-12 lg:pt-8 lg:pb-14 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 text-[#1C6997] dark:text-[#1C6997]">
                Email Signature Generator
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Create professional email signatures with real-time preview and multiple export options.
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Real-time Preview</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">See changes instantly as you type</p>
                </div>
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Multiple Exports</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">Download as image or HTML</p>
                </div>
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Social Integration</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">Add your social media profiles</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Content */}
          <div className="p-4">
            <EmailSignature />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
