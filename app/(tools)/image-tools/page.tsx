import ImageTools from "@/components/ImageTools";
import { Maximize2, Settings, Zap, Upload, RotateCw, Download } from "lucide-react";
import { Footer } from "@/components/Footer";

export default function ImageToolsPage() {
  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-b from-purple-50 to-white dark:from-gray-950 dark:to-gray-900">
      {/* Hero Section - More Compact */}
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center space-y-3">
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
            Image Tools
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300">
            Transform your images instantly with our powerful browser-based tools. 
            Resize, compress, convert formats (PNG, JPEG, WebP), crop, rotate, and batch process - all in one place.
          </p>
        </div>
      </div>

      {/* Features Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-purple-100 dark:border-purple-900">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-lg p-2 mb-3">
              <Maximize2 className="w-full h-full text-white" />
            </div>
            <h3 className="text-base font-semibold text-purple-900 dark:text-purple-100 mb-1">
              Image Processing
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Resize, crop, rotate and transform your images with precision
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-purple-100 dark:border-purple-900">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-lg p-2 mb-3">
              <Settings className="w-full h-full text-white" />
            </div>
            <h3 className="text-base font-semibold text-purple-900 dark:text-purple-100 mb-1">
              Format Conversion
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Convert between PNG, JPEG, and WebP with quality control
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-purple-100 dark:border-purple-900">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-lg p-2 mb-3">
              <Zap className="w-full h-full text-white" />
            </div>
            <h3 className="text-base font-semibold text-purple-900 dark:text-purple-100 mb-1">
              Batch Processing
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Process multiple images at once with consistent settings
            </p>
          </div>
        </div>

        {/* Main Tool */}
        <div className="max-w-5xl mx-auto mb-12">
          <ImageTools />
        </div>

        {/* Guide Section */}
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 text-center">
            How to Use
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex flex-col items-center text-center space-y-3">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                <Upload className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">1. Upload Images</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Click the upload area or drag and drop your images. Supports PNG, JPEG, and WebP formats up to 10MB.
                You can upload multiple images for batch processing.
              </p>
            </div>
            <div className="flex flex-col items-center text-center space-y-3">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                <RotateCw className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">2. Edit & Transform</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                • Resize dimensions while maintaining aspect ratio
                • Rotate in 90-degree increments
                • Flip horizontally or vertically
                • Crop to specific dimensions
                • Convert between different formats
              </p>
            </div>
            <div className="flex flex-col items-center text-center space-y-3">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                <Download className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">3. Download</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Once satisfied with the changes, download your transformed images. For multiple images,
                they'll be processed in batch with your selected settings.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto">
        <Footer />
      </div>
    </div>
  );
}
