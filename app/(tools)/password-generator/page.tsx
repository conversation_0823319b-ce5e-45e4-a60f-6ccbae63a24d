import { PasswordGenerator } from "@/components/PasswordGenerator";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Password Generator - Free Online Secure Password Generator',
  description: 'Generate strong, secure passwords and passphrases with our free online password generator. Customize length, character types, and more.',
  keywords: 'password generator, secure password, passphrase generator, random password, strong password',
  openGraph: {
    title: 'Password Generator - Free Online Secure Password Generator',
    description: 'Generate strong, secure passwords and passphrases with our free online password generator.',
    url: 'https://freetextconverter.com/password-generator',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Password Generator - Free Online Secure Password Generator',
    description: 'Generate strong, secure passwords and passphrases with our free online password generator.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/password-generator'
  }
};

export default function PasswordGeneratorPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Password Generator
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Generate secure and customizable passwords
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Strong Security</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Cryptographically secure</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Customizable</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Adjust length and complexity</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Quick Copy</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Copy password instantly</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <PasswordGenerator />
          </div>

          {/* Guide Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-[#1C6997] dark:text-[#1C6997] mb-2">
                  Password Generation Guide
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                  Learn how to use our password generator and create secure passwords
                </p>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="h-5 w-5 text-[#1C6997]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <h3 className="font-semibold">Password Mode</h3>
                  </div>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Choose length (8-50 characters)</li>
                    <li>Select character types</li>
                    <li>Avoid ambiguous characters</li>
                    <li>Prevent character repetition</li>
                  </ul>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="h-5 w-5 text-[#1C6997]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                    </svg>
                    <h3 className="font-semibold">Passphrase Mode</h3>
                  </div>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Select word count (2-8)</li>
                    <li>Choose word separator</li>
                    <li>Add number suffix</li>
                    <li>Easy to remember</li>
                  </ul>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="h-5 w-5 text-[#1C6997]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <h3 className="font-semibold">Security Features</h3>
                  </div>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Cryptographic security</li>
                    <li>Strength analysis</li>
                    <li>No server storage</li>
                    <li>Client-side generation</li>
                  </ul>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="h-5 w-5 text-[#1C6997]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="font-semibold">Best Practices</h3>
                  </div>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Use unique passwords</li>
                    <li>Minimum 12 characters</li>
                    <li>Mix character types</li>
                    <li>Regular updates</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}