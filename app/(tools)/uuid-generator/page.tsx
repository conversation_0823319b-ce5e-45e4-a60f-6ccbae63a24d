import { UUIDGenerator } from "@/components/UUIDGenerator";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'UUID Generator - Free Online UUID v4 Generator',
  description: 'Generate secure, random UUIDs (Universally Unique Identifiers) with our free online UUID generator. Perfect for developers and system administrators.',
  keywords: 'uuid generator, uuid v4, unique identifier, random uuid, guid generator',
  openGraph: {
    title: 'UUID Generator - Free Online UUID v4 Generator',
    description: 'Generate secure, random UUIDs (Universally Unique Identifiers) with our free online UUID generator.',
    url: 'https://freetextconverter.com/uuid-generator',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'UUID Generator - Free Online UUID v4 Generator',
    description: 'Generate secure, random UUIDs (Universally Unique Identifiers) with our free online UUID generator.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/uuid-generator'
  }
};

export default function UUIDGeneratorPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                UUID Generator
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Generate secure, random UUIDs (Version 4)
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Guaranteed Unique</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Globally unique identifiers</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Batch Generation</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Generate multiple UUIDs at once</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Quick Copy</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Copy UUIDs instantly</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main content */}
          <div className="max-w-4xl mx-auto px-4 mb-8">
            <UUIDGenerator />
          </div>

          {/* Support section */}
          <div className="max-w-4xl mx-auto px-4 mb-8">
            <SupportSection />
          </div>

          {/* FAQ Section */}
          <section className="max-w-4xl mx-auto py-8">
            <h2 className="text-xl font-semibold mb-6 text-gray-800 dark:text-gray-200">Frequently Asked Questions</h2>
            <div className="space-y-6 border-2 border-gray-100 dark:border-gray-800 rounded-xl p-6 bg-white/30 dark:bg-gray-900/30 backdrop-blur-sm">
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">What is a UUID?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  A UUID (Universally Unique Identifier) is a 128-bit identifier that's guaranteed to be unique across space and time. It's commonly used in databases, distributed systems, and software development.
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">Are these UUIDs truly unique?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  Yes! Our generator uses version 4 UUIDs which are created using random or pseudo-random numbers. The probability of generating a duplicate UUID is extremely low (about 1 in 2¹²⁸).
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">What are UUIDs used for?</h3>
                <p className="text-sm italic text-gray-600 dark:text-gray-400">
                  UUIDs are commonly used as unique identifiers in databases, distributed systems, and software applications. They're perfect for generating unique IDs without a central coordination system.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
      <Footer />
    </div>
  );
}
