import { CodeFormatter } from "@/components/CodeFormatter";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';
import { Suspense } from 'react';

export const metadata: Metadata = {
  title: 'Code Formatter - Online Code Beautifier Tool',
  description: 'Free online code formatter and beautifier supporting multiple programming languages. Format your code with proper indentation and style.',
  keywords: 'code formatter, code beautifier, code indentation, code style, format code',
};

export default function CodeFormatterPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section with unique gradient - Reduced Size */}
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-4">
            <div className="max-w-4xl mx-auto px-4 pt-3 pb-4 sm:pt-4 sm:pb-6 text-center">
              <h1 className="text-3xl font-bold tracking-tight mb-2 text-[#1C6997] dark:text-[#1C6997]">
                Code Formatter
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-3">
                Format and beautify your code with proper indentation and consistent style.
              </p>

              {/* Feature highlights - Compact Version */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mt-3">
                <div className="flex flex-col items-center p-2 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Multiple Languages</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Supports various programming languages</p>
                </div>
                <div className="flex flex-col items-center p-2 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Smart Indentation</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Automatically fixes code indentation</p>
                </div>
                <div className="flex flex-col items-center p-2 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-8 h-8 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-4 h-4 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Style Options</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Customize formatting preferences</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Content */}
          <div className="max-w-5xl mx-auto px-4">
            <div className="grid gap-8">
              {/* Tool Section */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-purple-100 dark:border-purple-900">
                <Suspense fallback={<div>Loading...</div>}>
                  <CodeFormatter />
                </Suspense>
              </div>

              {/* Support Section */}
              <SupportSection />
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}