import { PickupLineGenerator } from "@/components/PickupLineGenerator";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: "AI Pickup Line Generator - Free Text Converter",
  description: "Generate creative and personalized pickup lines using AI. Choose from different styles and customize based on interests.",
  keywords: 'pickup line generator, AI pickup lines, dating lines, conversation starters, creative pickup lines',
  openGraph: {
    title: 'Pickup Line Generator - Free Online AI-Powered Pickup Lines',
    description: 'Generate creative and contextual pickup lines based on interests and preferences.',
    url: 'https://freetextconverter.com/pickup-line-generator',
    siteName: 'Free Text Converter',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Pickup Line Generator - Free Online AI-Powered Pickup Lines',
    description: 'Generate creative and contextual pickup lines based on interests and preferences.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pickup-line-generator'
  }
};

export default function Page() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          <section className="py-8 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Pickup Line Generator
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Generate creative and fun pickup lines
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">AI Powered</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Smart and contextual lines</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Customizable</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Choose style and tone</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Quick Copy</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Copy with one click</p>
                </div>
              </div>
            </div>
          </section>

          <PickupLineGenerator />

          {/* How to use guide */}
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Pickup Line Generation Guide
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                Learn how to create personalized and engaging pickup lines
              </p>
            </div>

            <div className="grid gap-4 md:grid-cols-3">
              <div className="bg-gradient-to-r from-pink-50 to-rose-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <svg className="h-5 w-5 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <h3 className="font-semibold">Enter Context</h3>
                </div>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Describe interests and hobbies</li>
                  <li>Add relevant details</li>
                  <li>Keep it concise</li>
                  <li>Be specific</li>
                </ul>
              </div>

              <div className="bg-gradient-to-r from-pink-50 to-rose-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <svg className="h-5 w-5 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                  </svg>
                  <h3 className="font-semibold">Choose Style</h3>
                </div>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Select tone (funny, clever, etc.)</li>
                  <li>Pick gender preferences</li>
                  <li>Adjust for context</li>
                  <li>Match your personality</li>
                </ul>
              </div>

              <div className="bg-gradient-to-r from-pink-50 to-rose-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <svg className="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <h3 className="font-semibold">Generate & Copy</h3>
                </div>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Click generate button</li>
                  <li>Review the result</li>
                  <li>Copy to clipboard</li>
                  <li>Try different variations</li>
                </ul>
              </div>
            </div>
          </div>

          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}