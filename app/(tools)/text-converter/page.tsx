import { Converter } from "@/components/Converter";
import { Guide } from "@/components/Guide";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { BuyMeCoffeeButton } from "@/components/BuyMeCoffeeButton";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Free Text Converter - Online Text Formatting & Conversion Tools',
  description: 'Free online text conversion tools for case transformation, space replacement, code formatting, binary calculations, and date calculations.',
  keywords: 'text converter, text formatter, case converter, space replacer, code formatter, binary calculator, date calculator, free text tools',
  openGraph: {
    title: 'Free Text Converter - Online Text Formatting & Conversion Tools',
    description: 'Free online text conversion tools for case transformation, space replacement, code formatting, binary calculations, and date calculations.',
    url: 'https://www.freetextconverter.com/text-converter',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Text Converter - Online Text Formatting & Conversion Tools',
    description: 'Free online text conversion tools for case transformation, space replacement, code formatting, binary calculations, and date calculations.',
  },
  alternates: {
    canonical: 'https://www.freetextconverter.com/text-converter'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'Cxva3uuPrmXH-6T2MHevNg2DiLOc_rK0dWf4Hecv-z8'
  }
};

export const preferredRegion = 'auto'
export const revalidate = 3600

export default function TextConverterPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        {/* Hero Section with unique gradient */}
        <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
          <div className="container mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 3xl:px-0 3xl:max-w-7xl 4xl:max-w-[1800px]">
            <div className="max-w-4xl mx-auto text-center">
              <div className="flex justify-center items-center mb-4">
                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight text-[#1C6997] dark:text-[#1C6997]">
                  Free Text Converter Tools
                </h1>
                <Guide />
              </div>
              <p className="text-base sm:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Transform, analyze, and format your text with our comprehensive suite of free online tools
              </p>

              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-white/70 dark:hover:bg-gray-800/70 transition-colors">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </div>
                  <h3 className="text-base font-semibold mb-1">Case Conversion</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">Transform text case with multiple options</p>
                </div>

                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-white/70 dark:hover:bg-gray-800/70 transition-colors">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-base font-semibold mb-1">Text Formatting</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">Format and beautify your text content</p>
                </div>

                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-white/70 dark:hover:bg-gray-800/70 transition-colors">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                  </div>
                  <h3 className="text-base font-semibold mb-1">AI Tools</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">Advanced AI-powered text processing</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content with Split Layout */}
        <div className="container mx-auto px-4 py-8 3xl:px-0 3xl:max-w-7xl 4xl:max-w-[1800px]">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <Converter />
          </div>

          {/* Support & Feedback Section */}
          <div className="mt-8">
            <SupportSection />
          </div>

        </div>
      </div>
      <Footer />
    </div>
  );
}
