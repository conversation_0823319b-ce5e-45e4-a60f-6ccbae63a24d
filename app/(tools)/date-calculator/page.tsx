import { DateCalculatorComponent } from "@/components/DateCalculatorComponent";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Date Calculator - Free Online Date and Time Calculator',
  description: 'Free online date calculator for calculating date differences, adding or subtracting days, and working with various date formats.',
  keywords: 'date calculator, date difference calculator, add days calculator, date format converter',
  openGraph: {
    title: 'Date Calculator - Free Online Date and Time Calculator',
    description: 'Free online date calculator for calculating date differences, adding or subtracting days, and working with various date formats.',
    url: 'https://freetextconverter.com/date-calculator',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Date Calculator - Free Online Date and Time Calculator',
    description: 'Free online date calculator for calculating date differences, adding or subtracting days, and working with various date formats.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/date-calculator'
  }
};

export default function DateCalculatorPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section */}
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Date Calculator
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Calculate date differences, add or subtract dates, and work with time intervals
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Date Difference</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Calculate days between any two dates</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Add/Subtract Days</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Easily add or subtract days from a date</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Format Converter</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Convert between different date formats</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <DateCalculatorComponent />
          </div>

          {/* Guide Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-[#1C6997] dark:text-[#1C6997] mb-2">
                  Date Calculation Guide
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                  Learn how to use our date calculator effectively
                </p>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="h-5 w-5 text-[#1C6997]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <h3 className="font-semibold">Date Calculations</h3>
                  </div>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Find days between dates</li>
                    <li>Add days to a date</li>
                    <li>Subtract days from a date</li>
                    <li>Include/exclude weekends</li>
                  </ul>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="h-5 w-5 text-[#1C6997]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <h3 className="font-semibold">Format Support</h3>
                  </div>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>MM/DD/YYYY</li>
                    <li>DD/MM/YYYY</li>
                    <li>YYYY-MM-DD</li>
                    <li>Custom formats</li>
                  </ul>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="h-5 w-5 text-[#1C6997]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <h3 className="font-semibold">Features</h3>
                  </div>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Real-time calculations</li>
                    <li>Multiple date formats</li>
                    <li>Weekend handling</li>
                    <li>Business days calculation</li>
                  </ul>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="h-5 w-5 text-[#1C6997]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="font-semibold">How to Use</h3>
                  </div>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Select calculation type</li>
                    <li>Enter dates or days</li>
                    <li>Choose format options</li>
                    <li>Get instant results</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}
