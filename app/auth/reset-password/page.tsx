import { ResetPassword } from '@/components/ResetPassword';
import { Footer } from '@/components/Footer';
import { Metadata } from 'next';
import { Suspense } from 'react';

export const metadata: Metadata = {
  title: 'Reset Password - QR Code Generator',
  description: 'Reset your password',
};

export default function ResetPasswordPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 bg-gray-100">
        <div className="w-full max-w-md mx-auto pt-16 pb-24 px-4">
          <h1 className="text-2xl font-bold mb-6 text-center bg-gradient-to-r from-[#e81cff] to-[#40c9ff] text-transparent bg-clip-text">
            Reset Password
          </h1>
          <Suspense fallback={
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          }>
            <ResetPassword />
          </Suspense>
        </div>
      </div>
      <Footer />
    </div>
  );
}