import { SignIn } from '@/components/SignIn';
import { Footer } from '@/components/Footer';
import { AuthRedirectHandler } from '@/components/AuthRedirectHandler';
import { Metadata } from 'next';
import { Suspense } from 'react';

export const metadata: Metadata = {
  title: 'Sign In - QR Code Generator',
  description: 'Sign in to access your QR code analytics',
};

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

export default function SignInPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <AuthRedirectHandler />
      <div className="flex-1 bg-gray-100">
        <div className="w-full max-w-md mx-auto pt-16 pb-24 px-4">
          <h1 className="text-2xl font-bold mb-6 text-center bg-gradient-to-r from-[#e81cff] to-[#40c9ff] text-transparent bg-clip-text">
            Welcome Back
          </h1>
          <Suspense fallback={
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          }>
            <SignIn />
          </Suspense>
        </div>
      </div>
      <Footer />
    </div>
  );
}
