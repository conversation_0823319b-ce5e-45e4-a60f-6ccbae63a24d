'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { HomeIcon, Power } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useCallback, useEffect, useRef } from 'react';

interface Position {
  x: number;
  y: number;
}

interface EnemySnake {
  positions: Position[];
  direction: 'UP' | 'DOWN' | 'LEFT' | 'RIGHT';
}

const GRID_SIZE = 20;
const CELL_SIZE = 20;
const INITIAL_SPEED = 400; // Even slower initial speed
const MIN_SPEED = 200; // Slower minimum speed
const ENEMY_SPEED = 500; // Slower enemy speed

const getRandomPosition = (): Position => ({
  x: Math.floor(Math.random() * GRID_SIZE),
  y: Math.floor(Math.random() * GRID_SIZE)
});

const getRandomDirection = (): 'UP' | 'DOWN' | 'LEFT' | 'RIGHT' => {
  const directions = ['UP', 'DOWN', 'LEFT', 'RIGHT'];
  return directions[Math.floor(Math.random() * directions.length)] as 'UP' | 'DOWN' | 'LEFT' | 'RIGHT';
};

const createEnemySnake = (): EnemySnake => ({
  positions: [
    getRandomPosition(),
    { x: 0, y: 0 },
    { x: 0, y: 0 }
  ],
  direction: getRandomDirection()
});

const useInterval = (callback: () => void, delay: number | null) => {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay !== null) {
      const id = setInterval(() => savedCallback.current(), delay);
      return () => clearInterval(id);
    }
  }, [delay]);
};

export default function NotFound() {
  const [snake, setSnake] = useState<Position[]>([{ x: 10, y: 10 }]);
  const [food, setFood] = useState<Position>(getRandomPosition());
  const [direction, setDirection] = useState<'UP' | 'DOWN' | 'LEFT' | 'RIGHT'>('RIGHT');
  const [gameStarted, setGameStarted] = useState(false);
  const [score, setScore] = useState(0);
  const [highScore, setHighScore] = useState(0);
  const [speed, setSpeed] = useState(INITIAL_SPEED);
  const [showMouseMessage, setShowMouseMessage] = useState(false);
  const [enemySnakes, setEnemySnakes] = useState<EnemySnake[]>([]);

  // Move enemy snakes
  useInterval(() => {
    if (!gameStarted) return;

    setEnemySnakes(prevSnakes => 
      prevSnakes.map(enemy => {
        const head = enemy.positions[0];
        const newHead = { ...head };

        switch (enemy.direction) {
          case 'UP':
            newHead.y = (head.y - 1 + GRID_SIZE) % GRID_SIZE;
            break;
          case 'DOWN':
            newHead.y = (head.y + 1) % GRID_SIZE;
            break;
          case 'LEFT':
            newHead.x = (head.x - 1 + GRID_SIZE) % GRID_SIZE;
            break;
          case 'RIGHT':
            newHead.x = (head.x + 1) % GRID_SIZE;
            break;
        }

        // Randomly change direction occasionally
        if (Math.random() < 0.1) {
          enemy.direction = getRandomDirection();
        }

        const newPositions = [newHead, ...enemy.positions.slice(0, -1)];
        return { ...enemy, positions: newPositions };
      })
    );
  }, ENEMY_SPEED);

  const checkCollisionWithEnemies = (head: Position) => {
    return enemySnakes.some(enemy => 
      enemy.positions.some(pos => pos.x === head.x && pos.y === head.y)
    );
  };

  const moveSnake = useCallback(() => {
    if (!gameStarted) return;

    setSnake(prevSnake => {
      const head = prevSnake[0];
      const newHead = { ...head };

      switch (direction) {
        case 'UP':
          newHead.y = (head.y - 1 + GRID_SIZE) % GRID_SIZE;
          break;
        case 'DOWN':
          newHead.y = (head.y + 1) % GRID_SIZE;
          break;
        case 'LEFT':
          newHead.x = (head.x - 1 + GRID_SIZE) % GRID_SIZE;
          break;
        case 'RIGHT':
          newHead.x = (head.x + 1) % GRID_SIZE;
          break;
      }

      // Check collision with enemies
      if (checkCollisionWithEnemies(newHead)) {
        endGame();
        return prevSnake;
      }

      // Check if snake hits itself
      if (prevSnake.some(segment => segment.x === newHead.x && segment.y === newHead.y)) {
        endGame();
        return prevSnake;
      }

      const newSnake = [newHead, ...prevSnake];
      
      if (newHead.x === food.x && newHead.y === food.y) {
        setFood(getRandomPosition());
        setScore(prev => {
          const newScore = prev + 1;
          if (newScore % 5 === 0) {
            setSpeed(current => Math.max(current * 0.9, MIN_SPEED));
            // Add new enemy snake every 5 points
            setEnemySnakes(prev => [...prev, createEnemySnake()]);
          }
          return newScore;
        });
      } else {
        newSnake.pop();
      }

      return newSnake;
    });
  }, [direction, food, gameStarted]);

  useInterval(moveSnake, gameStarted ? speed : null);

  const handleKeyPress = useCallback((e: KeyboardEvent) => {
    if (!gameStarted) return;

    switch (e.key) {
      case 'ArrowUp':
        setDirection(prev => prev !== 'DOWN' ? 'UP' : prev);
        break;
      case 'ArrowDown':
        setDirection(prev => prev !== 'UP' ? 'DOWN' : prev);
        break;
      case 'ArrowLeft':
        setDirection(prev => prev !== 'RIGHT' ? 'LEFT' : prev);
        break;
      case 'ArrowRight':
        setDirection(prev => prev !== 'LEFT' ? 'RIGHT' : prev);
        break;
    }
  }, [gameStarted]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  const startGame = () => {
    setSnake([{ x: 10, y: 10 }]);
    setFood(getRandomPosition());
    setDirection('RIGHT');
    setScore(0);
    setSpeed(INITIAL_SPEED);
    setEnemySnakes([createEnemySnake()]); // Start with one enemy
    setGameStarted(true);
  };

  const endGame = () => {
    setGameStarted(false);
    if (score > highScore) {
      setHighScore(score);
    }
  };

  return (
    <main 
      className="min-h-screen pt-20 relative overflow-hidden bg-gray-950"
      onClick={() => {
        if (gameStarted) {
          setShowMouseMessage(true);
          setTimeout(() => setShowMouseMessage(false), 2000);
        }
      }}
    >
      {/* Mouse Click Message */}
      <AnimatePresence>
        {showMouseMessage && gameStarted && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="fixed top-1/4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-6 py-3 rounded-xl shadow-lg z-50 font-semibold"
          >
            Come on mate, use your keyboard arrows to play! 😄
          </motion.div>
        )}
      </AnimatePresence>

      <div className="container mx-auto relative z-10 flex flex-col items-center justify-center min-h-[calc(100vh-5rem)]">
        {/* Title */}
        <motion.h1
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-7xl font-black text-center mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 text-transparent bg-clip-text"
        >
          Errorrrrrrrrrrr 404!
          <div className="text-3xl mt-2 text-gray-400 font-normal">
           “Oops! We made a typo somewhere. Was it us? Was it you? We’ll never know.” Till then guide the Bug Snake through the system...
          </div>
        </motion.h1>

        {/* Game Area */}
        <div className="relative bg-gray-900/50 rounded-2xl backdrop-blur-xl border border-purple-500/20 p-8 mb-8">
          <div className="relative" style={{ width: GRID_SIZE * CELL_SIZE, height: GRID_SIZE * CELL_SIZE }}>
            {/* Player Snake */}
            <AnimatePresence>
              {(gameStarted ? snake : [{ x: 10, y: 10 }]).map((segment, index, array) => {
                // Calculate the connection direction with the next segment
                const nextSegment = array[index + 1];
                const prevSegment = array[index - 1];
                
                // Determine the rotation angle based on segment positions
                let rotation = 0;
                if (prevSegment) {
                  if (prevSegment.x < segment.x) rotation = 0; // facing right
                  else if (prevSegment.x > segment.x) rotation = 180; // facing left
                  else if (prevSegment.y < segment.y) rotation = 90; // facing down
                  else if (prevSegment.y > segment.y) rotation = 270; // facing up
                } else {
                  // Head rotation based on movement direction
                  if (direction === 'RIGHT') rotation = 0;
                  else if (direction === 'LEFT') rotation = 180;
                  else if (direction === 'DOWN') rotation = 90;
                  else if (direction === 'UP') rotation = 270;
                }

                return (
                  <motion.div
                    key={`snake-${segment.x}-${segment.y}-${index}`}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    className="absolute"
                    style={{
                      left: segment.x * CELL_SIZE,
                      top: segment.y * CELL_SIZE,
                      width: CELL_SIZE,
                      height: CELL_SIZE,
                      transform: `rotate(${rotation}deg)`,
                    }}
                  >
                    {index === 0 ? (
                      // Snake head
                      <div className="w-full h-full relative">
                        {/* Head shape */}
                        <div className="absolute inset-0 bg-green-400">
                          <svg viewBox="0 0 100 100" className="w-full h-full">
                            <path
                              d="M 50 20 
                                 C 70 20, 90 35, 90 50 
                                 C 90 65, 70 80, 50 80 
                                 C 30 80, 10 65, 10 50 
                                 C 10 35, 30 20, 50 20 
                                 L 50 50 Z"
                              fill="currentColor"
                            />
                            {/* Eyes */}
                            <circle cx="30" cy="40" r="5" fill="black"/>
                            <circle cx="70" cy="40" r="5" fill="black"/>
                            {/* Tongue */}
                            <path
                              d="M 50 60 L 60 70 L 50 65 L 40 70 L 50 60"
                              fill="red"
                            />
                          </svg>
                        </div>
                      </div>
                    ) : (
                      // Snake body
                      <div className="w-full h-full relative overflow-hidden">
                        <div 
                          className={`absolute inset-0 bg-green-600`}
                          style={{
                            clipPath: nextSegment
                              ? 'polygon(25% 0%, 75% 0%, 75% 100%, 25% 100%)'
                              : 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)'
                          }}
                        />
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </AnimatePresence>

            {/* Enemy Snakes */}
            <AnimatePresence>
              {gameStarted && enemySnakes.map((enemy, enemyIndex) => 
                enemy.positions.map((segment, index, array) => {
                  const prevSegment = array[index - 1];
                  let rotation = 0;
                  if (prevSegment) {
                    if (prevSegment.x < segment.x) rotation = 0;
                    else if (prevSegment.x > segment.x) rotation = 180;
                    else if (prevSegment.y < segment.y) rotation = 90;
                    else if (prevSegment.y > segment.y) rotation = 270;
                  } else {
                    if (enemy.direction === 'RIGHT') rotation = 0;
                    else if (enemy.direction === 'LEFT') rotation = 180;
                    else if (enemy.direction === 'DOWN') rotation = 90;
                    else if (enemy.direction === 'UP') rotation = 270;
                  }

                  return (
                    <motion.div
                      key={`enemy-${enemyIndex}-${segment.x}-${segment.y}-${index}`}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      exit={{ scale: 0 }}
                      className="absolute"
                      style={{
                        left: segment.x * CELL_SIZE,
                        top: segment.y * CELL_SIZE,
                        width: CELL_SIZE,
                        height: CELL_SIZE,
                        transform: `rotate(${rotation}deg)`,
                      }}
                    >
                      {index === 0 ? (
                        // Enemy head
                        <div className="w-full h-full relative">
                          <div className="absolute inset-0 bg-red-400">
                            <svg viewBox="0 0 100 100" className="w-full h-full">
                              <path
                                d="M 50 20 
                                   C 70 20, 90 35, 90 50 
                                   C 90 65, 70 80, 50 80 
                                   C 30 80, 10 65, 10 50 
                                   C 10 35, 30 20, 50 20 
                                   L 50 50 Z"
                                fill="currentColor"
                              />
                              {/* Eyes */}
                              <circle cx="30" cy="40" r="5" fill="black"/>
                              <circle cx="70" cy="40" r="5" fill="black"/>
                              {/* Angry eyebrows */}
                              <path d="M 25 35 L 35 30" stroke="black" strokeWidth="2"/>
                              <path d="M 65 30 L 75 35" stroke="black" strokeWidth="2"/>
                            </svg>
                          </div>
                        </div>
                      ) : (
                        // Enemy body
                        <div className="w-full h-full relative overflow-hidden">
                          <div 
                            className="absolute inset-0 bg-red-600"
                            style={{
                              clipPath: index === array.length - 1
                                ? 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)'
                                : 'polygon(25% 0%, 75% 0%, 75% 100%, 25% 100%)'
                            }}
                          />
                        </div>
                      )}
                    </motion.div>
                  );
                })
              )}
            </AnimatePresence>

            {/* Food */}
            {gameStarted && (
              <motion.div
                className="absolute"
                style={{
                  left: food.x * CELL_SIZE,
                  top: food.y * CELL_SIZE,
                  width: CELL_SIZE,
                  height: CELL_SIZE,
                }}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                }}
              >
                <div className="w-full h-full bg-yellow-400 rounded-full transform scale-50" />
              </motion.div>
            )}
          </div>

          {/* Game UI */}
          {gameStarted && (
            <div className="absolute top-4 left-4 right-4 flex justify-between items-center">
              <div className="bg-gray-900/80 rounded-lg px-4 py-2 text-purple-400 font-mono">
                Score: {score}
              </div>
              <div className="bg-gray-900/80 rounded-lg px-4 py-2 text-purple-400 font-mono">
                High Score: {highScore}
              </div>
            </div>
          )}

          {/* Game Start/End Overlay */}
          {!gameStarted && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900/90 backdrop-blur-sm">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="text-center space-y-4"
              >
                <h2 className="text-2xl font-bold text-purple-400 mb-4">
                  {score === 0 ? "Bug Snake!" : "Game Over!"}
                </h2>
                {score === 0 ? (
                  <div className="text-gray-300 mb-4 space-y-2">
                    <p className="text-lg font-semibold mb-3">How to Play:</p>
                    <ul className="space-y-2">
                      <li>🎮 Use arrow keys to control the snake</li>
                      <li>✨ Collect glowing sparkles to grow longer</li>
                      <li>🚫 Avoid hitting yourself</li>
                      <li>🏃‍♂️ Snake moves faster every 5 points</li>
                      <li>🔄 Wrap around the edges to survive</li>
                    </ul>
                  </div>
                ) : (
                  <p className="text-gray-300 mb-4">
                    Score: {score} {score > highScore ? "New High Score!" : ""}
                  </p>
                )}
                <Button
                  onClick={startGame}
                  className="bg-purple-500 hover:bg-purple-600 text-white px-8 py-4 rounded-xl transition-all duration-300 hover:scale-105"
                >
                  {score === 0 ? "Start Game" : "Play Again"}
                </Button>
              </motion.div>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button 
            onClick={() => window.history.back()}
            className="group bg-purple-500 hover:bg-purple-600 text-white px-8 py-6 rounded-xl transition-all duration-300 hover:scale-105"
          >
            <Power className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" />
            <span className="text-lg">Go Back</span>
          </Button>
          
          <Link href="/">
            <Button 
              variant="outline"
              className="group border-2 border-purple-500/30 hover:border-purple-500/50 text-purple-400 px-8 py-6 rounded-xl transition-all duration-300 hover:scale-105"
            >
              <HomeIcon className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" />
              <span className="text-lg">Return Home</span>
            </Button>
          </Link>
        </div>
      </div>
    </main>
  );
}
