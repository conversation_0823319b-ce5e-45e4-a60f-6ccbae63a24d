@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.bg-grid-slate-200 {
  background-image: linear-gradient(to right, rgb(226 232 240 / 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgb(226 232 240 / 0.1) 1px, transparent 1px);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Grid background pattern */
.bg-grid-gray-900\/\[0\.2\] {
  background-image: linear-gradient(to right, #0000001a 1px, transparent 1px),
                    linear-gradient(to bottom, #0000001a 1px, transparent 1px);
}

/* Sign In Form Styles */
.form-container {
  width: 400px;
  background: rgba(255, 255, 255, 0.9);
  border: 4px solid;
  border-image: linear-gradient(45deg, #e81cff, #40c9ff) 1;
  animation: gradient-border 4s ease infinite;
  padding: 32px 24px;
  font-size: 14px;
  color: #333;
  display: flex;
  flex-direction: column;
  gap: 20px;
  box-sizing: border-box;
  border-radius: 16px;
  margin: 0 auto;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  backdrop-filter: blur(8px);
}

@keyframes gradient-border {
  0% {
    border-image: linear-gradient(45deg, #e81cff, #40c9ff) 1;
  }
  25% {
    border-image: linear-gradient(135deg, #40c9ff, #e81cff) 1;
  }
  50% {
    border-image: linear-gradient(225deg, #e81cff, #40c9ff) 1;
  }
  75% {
    border-image: linear-gradient(315deg, #40c9ff, #e81cff) 1;
  }
  100% {
    border-image: linear-gradient(45deg, #e81cff, #40c9ff) 1;
  }
}

.form-container button:active {
  scale: 0.95;
}

.form-container .form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-container .form-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.form-container .form-group label {
  display: block;
  margin-bottom: 5px;
  color: #4b5563;
  font-weight: 600;
  font-size: 12px;
}

.form-container .form-group input {
  width: 100%;
  padding: 12px 16px;
  border-radius: 8px;
  color: #333;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.form-container .form-group input::placeholder {
  color: #9ca3af;
  opacity: 0.8;
}

.form-container .form-group input:focus {
  outline: none;
  border-color: #e81cff;
  background-color: #ffffff;
  box-shadow: 0 0 0 3px rgba(232, 28, 255, 0.1);
}

.form-container .form-submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: inherit;
  color: #4b5563;
  font-weight: 600;
  width: 100%;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  padding: 12px 16px;
  font-size: inherit;
  gap: 8px;
  margin-top: 8px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.form-container .form-submit-btn:hover {
  background: linear-gradient(145deg, #e81cff, #40c9ff);
  border-color: transparent;
  color: white;
}

.form-container .form-submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-container .form-divider {
  position: relative;
  text-align: center;
  margin: 20px 0;
}

.form-container .form-divider::before,
.form-container .form-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 45%;
  height: 1px;
  background-color: #e5e7eb;
}

.form-container .form-divider::before {
  left: 0;
}

.form-container .form-divider::after {
  right: 0;
}

.form-container .form-divider span {
  background-color: #ffffff;
  padding: 0 10px;
  color: #6b7280;
  font-size: 12px;
}

.form-container .google-btn {
  background: #f3f4f6;
}

.form-container .google-btn:hover {
  background: linear-gradient(145deg, #e81cff, #40c9ff);
  color: white;
}

.form-container .google-icon {
  width: 20px;
  height: 20px;
}

/* Animation for form elements and PDF tools */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-pulse {
  animation: pulse 4s ease-in-out infinite;
}

.delay-1000 {
  animation-delay: 1s;
}

.form-container .form-group {
  animation: fadeIn 0.5s ease forwards;
}

.form-container .form-group:nth-child(2) {
  animation-delay: 0.1s;
}

.form-container .form-submit-btn {
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.2s;
}

.form-container .form-divider {
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.3s;
}

.form-container .google-btn {
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.4s;
}
