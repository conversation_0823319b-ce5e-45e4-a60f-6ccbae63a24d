import type { Metadata } from 'next';

export const siteInfo = {
  name: 'Free Text Converter',
  description: 'An easy-to-use online tool for text conversion and replacing spaces with custom characters.',
  url: 'https://freetextconverter.com',
  author: '<PERSON><PERSON>'
};

export const metadata: Metadata = {
  metadataBase: new URL(siteInfo.url),
  title: {
    default: 'Free Text Converter - Online Text Formatting Tools',
    template: '%s | Free Text Converter'
  },
  description: 'An easy-to-use online tool for text conversion, analysis and formatting.',
  keywords: ['text converter', 'text tools', 'formatting', 'online tools', 'free tools'],
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/android-chrome-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/android-chrome-512x512.png', sizes: '512x512', type: 'image/png' }
    ],
    apple: [
      { url: '/apple-touch-icon.png' }
    ],
    shortcut: [{ url: '/favicon.ico' }]
  },
  manifest: '/site.webmanifest',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    alternateLocale: ['en_GB'],
    url: 'https://www.freetextconverter.com',
    siteName: 'Free Text Converter',
    title: 'Free Text Converter - Online Text Tools Suite',
    description: 'A comprehensive suite of free online tools for text conversion, formatting, content generation, and text analysis. Transform your text with ease.',
    images: [
      {
        url: '/Pixel-and-Code-1280x630.png',
        width: 1280,
        height: 630,
        alt: 'Free Text Converter - Online Text Tools Suite'
      }
    ],
    countryName: 'Australia',
    emails: ['<EMAIL>'],
    phoneNumbers: []
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Text Converter - Online Text Tools Suite',
    description: 'A comprehensive suite of free online tools for text conversion, formatting, content generation, and text analysis.',
    creator: '@freetextconverter'
  },
  verification: {
    google: 'Cxva3uuPrmXH-6T2MHevNg2DiLOc_rK0dWf4Hecv-z8'
  },
  other: {
    'google-adsense-account': 'ca-pub-****************'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  }
};
