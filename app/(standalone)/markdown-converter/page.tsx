import { Footer } from '@/components/Footer';
import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import MarkdownConverterClient from './markdown-converter-client';

export const metadata: Metadata = {
  title: 'Markdown Converter - Convert Markdown to HTML and Plain Text',
  description: 'Free online markdown converter. Convert markdown to HTML, plain text, and vice versa. Perfect for documentation and content creation.',
  keywords: 'markdown converter, markdown to html, html to markdown, plain text converter, markdown editor',
};

export default function MarkdownConverterPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header with Return Button */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <a
              href="/"
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span className="font-medium">Return to App</span>
            </a>
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              Markdown Converter
            </h1>
            <div className="w-24"></div> {/* Spacer for centering */}
          </div>
        </div>
      </header>

      <div className="flex-1">
        {/* Full width split layout */}
        <MarkdownConverterClient />
      </div>

      <Footer />
    </div>
  );
}
