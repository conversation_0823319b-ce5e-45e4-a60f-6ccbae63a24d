'use client';

import { UserPreferencesProvider } from '@/contexts/UserPreferencesContext';
import Script from 'next/script';
import { Toaster } from "sonner";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";

export default function MarkdownConverterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <Script id="schema-script" type="application/ld+json">
        {`
          {
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "Markdown Converter",
            "description": "Convert between Markdown, HTML, and plain text formats.",
            "applicationCategory": "Text Editor",
            "url": "https://freetextconverter.com/markdown-converter",
            "author": {
              "@type": "Person",
              "name": "Text Converter"
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            }
          }
        `}
      </Script>
      <UserPreferencesProvider>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          {children}
        </div>
      </UserPreferencesProvider>

      <Toaster />
      <Analytics />
      <SpeedInsights />
    </>
  );
}
