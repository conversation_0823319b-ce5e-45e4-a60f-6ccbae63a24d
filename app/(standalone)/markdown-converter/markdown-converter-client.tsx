'use client';

import dynamic from 'next/dynamic';

// Dynamically import the MarkdownConverter component with no SSR
const MarkdownConverter = dynamic(
  () => import('@/components/MarkdownConverter').then(mod => mod.MarkdownConverter),
  { 
    ssr: false,
    loading: () => <p>Loading Markdown Converter...</p> // Optional loading component
  }
);

export default function MarkdownConverterClient() {
  return <MarkdownConverter />;
}