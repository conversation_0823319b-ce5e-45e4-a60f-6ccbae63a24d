'use client';

import { UserPreferencesProvider } from '@/contexts/UserPreferencesContext';
import { Toaster } from "sonner";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";

export default function StandaloneLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <UserPreferencesProvider>
        {children}
      </UserPreferencesProvider>
      
      <Toaster />
      <Analytics />
      <SpeedInsights />
    </>
  );
}
