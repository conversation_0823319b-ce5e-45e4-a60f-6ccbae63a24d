import { NextRequest, NextResponse } from "next/server";
import { processAIRequest } from "@/lib/api";
import { getProvider } from "@/lib/provider-factory";

async function generatePickupLines(
  context: string,
  style: string,
  fromGender: string,
  toGender: string
) {
  const providerName = process.env.PICKUP_LINE_PROVIDER || "openrouter";
  const modelName = process.env.PICKUP_LINE_MODEL || "x-ai/grok-3-mini-beta";
  const apiKey = process.env.OPENROUTER_GROK_API_KEY;
  if (!apiKey) {
    throw new Error("API key for pickup lines is not configured.");
  }
  const provider = getProvider(providerName, apiKey);

  let genderContext = "";
  if (fromGender !== "no-preference" && toGender !== "no-preference") {
    genderContext = `from a ${fromGender} person to a ${toGender} person`;
  } else if (fromGender !== "no-preference") {
    genderContext = `from a ${fromGender} person`;
  } else if (toGender !== "no-preference") {
    genderContext = `for a ${toGender} person`;
  }

  const systemPrompt = `Generate three unique, flirty, and engaging pickup lines based on the given context and preferences. Present each pickup line as a separate bullet point. Each pickup line should align with the specified style and gender preferences, using relevant metaphors, slang, and emojis. Ensure the lines are appropriate, memorable, and designed to make the recipient blush. Each line should be distinct and stand out on its own.`;
  const userPrompt = `Context: ${context}\nStyle: ${style}\nGender Preferences: ${
    genderContext ? genderContext : "No preference"
  }`;

  const messages = [
    {
      role: "system",
      content: systemPrompt,
    },
    {
      role: "user",
      content: userPrompt,
    },
  ];

  const response = await fetch(provider.apiUrl, {
    method: "POST",
    headers: provider.headers,
    body: JSON.stringify({
      ...provider.formatBody(modelName, messages),
      temperature: 1.0,
      max_tokens: 1024,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error("OpenRouter API error:", {
      status: response.status,
      statusText: response.statusText,
      error: errorText,
    });
    throw new Error(`API request failed: ${response.statusText}`);
  }

  const data = await response.json();
  console.log("OpenRouter API full response:", JSON.stringify(data, null, 2));

  if (data.error) {
    if (
      data.error.code === 429 ||
      (data.error.metadata?.raw &&
        data.error.metadata.raw.includes("rate_limit_exceeded"))
    ) {
      throw new Error(
        "🚦 Whoa there! Our AI is catching its breath. Try again in a few moments! 🌟"
      );
    }
    throw new Error(
      data.error.message || "Something went wrong with the AI service"
    );
  }

  if (!data.choices?.[0]?.message?.content) {
    console.error(
      "Invalid API response structure:",
      JSON.stringify(data, null, 2)
    );
    throw new Error("Our AI is feeling a bit confused. Please try again! 🤔");
  }

  // Function to strip markdown formatting
  function stripMarkdown(text: string) {
    return text
      .replace(/!\[.*?\]\(.*?\)/g, "") // Remove images
      .replace(/<.*?>/g, "") // Remove HTML tags
      .replace(/https?:\/\/[^\s]+/g, "") // Remove URLs
      .replace(/\\n/g, "\n") // Replace escaped newlines
      .replace(/[\*_\[\]]/g, ""); // Remove markdown formatting characters
  }

  // Extract pickup lines
  const lines = data.choices[0].message.content.split("\n");
  const pickupLines = lines
    .filter((line: string) => line.startsWith("- ") || line.startsWith("* "))
    .slice(0, 3)
    .map((line: string) => stripMarkdown(line.substring(2).trim()));

  if (pickupLines.length < 3) {
    console.warn("Less than three pickup lines generated.");
    // Handle accordingly, e.g., request again or use defaults
  }

  return {
    pickupLines: pickupLines,
  };
}

export async function POST(request: NextRequest) {
  try {
    const { context, style, fromGender, toGender } = await request.json();

    if (!context || !style || !fromGender || !toGender) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    const result = await processAIRequest(() =>
      generatePickupLines(context, style, fromGender, toGender)
    );
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in pickup line generation:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to generate pickup lines",
      },
      { status: 500 }
    );
  }
}
