import { NextResponse } from 'next/server';
import type { B2 } from 'backblaze-b2';

let b2: any;

async function getB2Client() {
  if (!b2) {
    // Using require for CommonJS module
    const B2 = (await import('backblaze-b2')).default || require('backblaze-b2');
    b2 = new (B2 as any)({
      applicationKeyId: process.env.BACKBLAZE_KEY_ID!,
      applicationKey: process.env.BACKBLAZE_APP_KEY!
    });
  }
  return b2;
}

export async function POST(request: Request) {
  try {
    const b2Client = await getB2Client();
    await b2Client.authorize();

    const { imageUrl } = await request.json();
    
    // Extract file name from URL
    const fileName = imageUrl.split('/').pop();
    
    // Get download authorization
    const auth = await b2Client.getDownloadAuthorization({
      bucketId: process.env.BACKBLAZE_BUCKET!,
      fileNamePrefix: `recipes/${fileName}`,
      validDurationInSeconds: 3600 // URL valid for 1 hour
    });

    // Generate the signed URL
    const signedUrl = `${auth.data.downloadUrl}/file/${process.env.BACKBLAZE_BUCKET}/recipes/${fileName}?${auth.data.authorizationToken}`;

    return NextResponse.json({ signedUrl });
  } catch (error) {
    console.error('Error generating signed URL:', error);
    return NextResponse.json(
      { error: 'Failed to generate signed URL' },
      { status: 500 }
    );
  }
}