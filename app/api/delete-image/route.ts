import { NextResponse } from 'next/server';

async function deleteFromBackblaze(fileUrl: string) {
  try {
    const keyId = process.env.BACKBLAZE_IMAGE_KEY_ID;
    const applicationKey = process.env.BACKBLAZE_IMAGE_APP_KEY;
    const bucketName = process.env.BACKBLAZE_BUCKET;
    const prefix = process.env.BACKBLAZE_IMAGE_PREFIX || 'imagegenerator/';

    console.log('Attempting to delete:', { fileUrl });

    if (!keyId || !applicationKey || !bucketName) {
      throw new Error('Missing Backblaze configuration');
    }

    // Extract filename from URL and handle prefix
    let fileName: string;
    
    try {
      // Remove authorization token if present
      const urlObj = new URL(fileUrl);
      urlObj.searchParams.delete('Authorization');
      const cleanUrl = urlObj.toString();
      
      // Extract the filename
      if (cleanUrl.includes('/file/' + bucketName + '/')) {
        // It's a Backblaze URL, extract everything after the bucket name
        const parts = cleanUrl.split('/file/' + bucketName + '/');
        fileName = decodeURIComponent(parts[1]);
      } else {
        // It's not a Backblaze URL, use the last part and add prefix
        const lastPart = cleanUrl.split('/').pop() || '';
        fileName = `${prefix}${lastPart}`;
      }
    } catch (error) {
      console.error('Error parsing URL:', error);
      // Fallback to simple URL parsing
      const parts = fileUrl.split('?')[0].split('/');
      fileName = `${prefix}${parts[parts.length - 1]}`;
    }

    console.log('Extracted file name:', fileName);

    // Authenticate with Backblaze
    const credentials = Buffer.from(`${keyId}:${applicationKey}`).toString('base64');
    console.log('Authenticating with Backblaze...');
    
    const authResponse = await fetch('https://api.backblazeb2.com/b2api/v2/b2_authorize_account', {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${credentials}`,
      },
    });

    if (!authResponse.ok) {
      const authError = await authResponse.text();
      console.error('Backblaze authentication failed:', authError);
      throw new Error('Failed to authenticate with Backblaze');
    }

    const authData = await authResponse.json();
    console.log('Successfully authenticated with Backblaze');

    // Get file info to get fileId
    console.log('Listing files with prefix:', fileName);
    const listFileResponse = await fetch(`${authData.apiUrl}/b2api/v2/b2_list_file_names`, {
      method: 'POST',
      headers: {
        'Authorization': authData.authorizationToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bucketId: authData.allowed.bucketId,
        prefix: fileName,
        maxFileCount: 1,
      }),
    });

    const listData = await listFileResponse.json();
    console.log('List files response:', listData);
    
    if (!listFileResponse.ok) {
      console.error('Failed to list files:', listData);
      throw new Error(`Failed to find file in Backblaze: ${listData.message || listFileResponse.statusText}`);
    }

    const file = listData.files[0];
    if (!file) {
      console.warn('File not found in Backblaze:', fileName);
      // Don't throw error if file is not found, it might have been already deleted
      return;
    }

    console.log('Found file:', file);

    // Delete the file
    console.log('Deleting file...');
    const deleteResponse = await fetch(`${authData.apiUrl}/b2api/v2/b2_delete_file_version`, {
      method: 'POST',
      headers: {
        'Authorization': authData.authorizationToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileId: file.fileId,
        fileName: file.fileName,
      }),
    });

    const deleteData = await deleteResponse.json().catch(() => null);
    if (!deleteResponse.ok) {
      console.error('Failed to delete file:', deleteData);
      throw new Error(`Failed to delete file from Backblaze: ${deleteData?.message || deleteResponse.statusText}`);
    }

    console.log('Successfully deleted file from Backblaze:', fileName);
  } catch (error) {
    console.error('Error in deleteFromBackblaze:', error);
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    const { imageUrl } = await request.json();

    if (!imageUrl) {
      return NextResponse.json({ error: 'Image URL is required' }, { status: 400 });
    }

    await deleteFromBackblaze(imageUrl);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in delete-image API:', error);
    
    return NextResponse.json(
      { error: 'Failed to delete image', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}