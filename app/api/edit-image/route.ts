import { NextResponse } from 'next/server';
import sharp from 'sharp';
import { <PERSON><PERSON><PERSON> } from 'buffer';

const FLUX_API_URL = 'https://api.bfl.ml/v1';
const MAX_IMAGE_SIZE = 1024;

// Helper function to process uploaded image
async function processImage(buffer: Buffer): Promise<Buffer> {
  return sharp(buffer)
    .resize(MAX_IMAGE_SIZE, MAX_IMAGE_SIZE, {
      fit: 'contain',
      background: { r: 0, g: 0, b: 0, alpha: 0 }
    })
    .toFormat('png')
    .toBuffer();
}

// Helper function to upload to Backblaze
async function uploadToBackblaze(imageBuffer: Buffer, filename: string) {
  try {
    const keyId = process.env.BACKBLAZE_IMAGE_KEY_ID;
    const applicationKey = process.env.BACKBLAZE_IMAGE_APP_KEY;
    const bucketName = process.env.BACKBLAZE_BUCKET;
    const prefix = process.env.BACKBLAZE_IMAGE_PREFIX || "imagegenerator/";

    if (!keyId || !applicationKey || !bucketName) {
      throw new Error("Missing Backblaze configuration for image upload.");
    }

    const credentials = Buffer.from(`${keyId}:${applicationKey}`).toString("base64");

    const authResponse = await fetch("https://api.backblazeb2.com/b2api/v2/b2_authorize_account", {
      method: "GET",
      headers: { Authorization: `Basic ${credentials}` },
    });
    if (!authResponse.ok) throw new Error("Failed to authenticate with Backblaze.");
    const auth = await authResponse.json();

    const uploadUrlResponse = await fetch(`${auth.apiUrl}/b2api/v2/b2_get_upload_url`, {
      method: "POST",
      headers: { Authorization: auth.authorizationToken, "Content-Type": "application/json" },
      body: JSON.stringify({ bucketId: auth.allowed.bucketId }),
    });
    if (!uploadUrlResponse.ok) throw new Error("Failed to get Backblaze upload URL.");
    const { uploadUrl, authorizationToken } = await uploadUrlResponse.json();

    const fullFilename = `${prefix}${filename}`;
    const uploadResponse = await fetch(uploadUrl, {
      method: "POST",
      headers: {
        Authorization: authorizationToken,
        "X-Bz-File-Name": encodeURIComponent(fullFilename),
        "Content-Type": "image/png",
        "Content-Length": imageBuffer.length.toString(),
        "X-Bz-Content-Sha1": "do_not_verify",
      },
      body: imageBuffer,
    });
    if (!uploadResponse.ok) throw new Error("Failed to upload image to Backblaze.");

    return `${auth.downloadUrl}/file/${bucketName}/${encodeURIComponent(fullFilename)}`;
  } catch (error) {
    console.error("Backblaze upload error:", error);
    throw new Error("Failed to save image to storage.");
  }
}

export async function POST(request: Request) {
  try {
    // Validate environment variables
    if (!process.env.FLUX_API_KEY) {
      return NextResponse.json(
        { error: "Flux API key is missing" },
        { status: 500 }
      );
    }

    const formData = await request.formData();
    const inputImage = formData.get('inputImage') as File;
    const prompt = formData.get('prompt') as string;
    const editType = formData.get('editType') as string;
    const strength = parseFloat(formData.get('strength') as string);
    const aspectRatio = formData.get('aspectRatio') as string;
    const safetyTolerance = parseInt(formData.get('safetyTolerance') as string) || 2;

    // Validate input parameters
    if (!inputImage || !prompt) {
      return NextResponse.json(
        { error: "Input image and prompt are required" },
        { status: 400 }
      );
    }

    // Process uploaded image
    const imageBuffer = Buffer.from(await inputImage.arrayBuffer());
    const processedImageBuffer = await processImage(imageBuffer);
    const base64Image = processedImageBuffer.toString('base64');

    const headers = {
      "Content-Type": "application/json",
      "x-key": process.env.FLUX_API_KEY || "",
      Accept: "application/json",
    };

    // Determine API endpoint based on edit type
    let apiEndpoint: string;
    let requestBody: any;

    switch (editType) {
      case 'kontext-pro':
        apiEndpoint = 'flux-kontext-pro';
        requestBody = {
          prompt,
          input_image: base64Image,
          aspect_ratio: aspectRatio,
          output_format: "jpeg",
          safety_tolerance: safetyTolerance,
          prompt_upsampling: true
        };
        break;

      case 'kontext-max':
        apiEndpoint = 'flux-kontext-max';
        requestBody = {
          prompt,
          input_image: base64Image,
          aspect_ratio: aspectRatio,
          output_format: "jpeg",
          safety_tolerance: safetyTolerance,
          prompt_upsampling: true
        };
        break;

      case 'fill':
        apiEndpoint = 'flux-fill-pro';
        requestBody = {
          prompt,
          image: base64Image,
          strength: strength,
          output_format: "jpeg",
          safety_tolerance: safetyTolerance
        };
        break;

      case 'expand':
        apiEndpoint = 'flux-expand';
        requestBody = {
          image: base64Image,
          aspect_ratio: aspectRatio,
          prompt: prompt,
          output_format: "jpeg",
          safety_tolerance: safetyTolerance
        };
        break;

      default:
        return NextResponse.json(
          { error: "Invalid edit type" },
          { status: 400 }
        );
    }

    // Submit edit task to Flux API
    console.log(`Sending request to Flux API: ${apiEndpoint}...`);
    const submitResponse = await fetch(`${FLUX_API_URL}/${apiEndpoint}`, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!submitResponse.ok) {
      const errorText = await submitResponse.text();
      console.error("Flux API error:", {
        status: submitResponse.status,
        statusText: submitResponse.statusText,
        error: errorText,
      });
      throw new Error(
        `Flux API error: ${submitResponse.status} ${submitResponse.statusText}`
      );
    }

    const submitData = await submitResponse.json();
    if (!submitData.id) {
      throw new Error("No task ID received from Flux API");
    }

    // Poll for results
    console.log("Polling for edit results...");
    let editedImageUrl = null;
    const maxAttempts = 30;
    const pollInterval = 3000;

    for (let i = 0; i < maxAttempts; i++) {
      console.log(`Poll attempt ${i + 1} of ${maxAttempts}...`);

      try {
        const resultResponse = await fetch(
          `${FLUX_API_URL}/get_result?id=${submitData.id}`,
          { headers }
        );

        if (!resultResponse.ok) {
          console.warn(
            `Poll attempt ${i + 1} failed with status:`,
            resultResponse.status
          );
          await new Promise((resolve) => setTimeout(resolve, pollInterval));
          continue;
        }

        const result = await resultResponse.json();
        console.log(`Poll attempt ${i + 1} result:`, result);

        if (result.status === "Ready" && result.result?.sample) {
          editedImageUrl = result.result.sample;
          console.log("Image editing completed successfully");
          break;
        } else if (result.status === "Error") {
          throw new Error(
            `Image editing failed: ${result.error || "Unknown error"}`
          );
        } else {
          console.log(
            `Poll attempt ${i + 1}: Status is ${result.status}, continuing...`
          );
        }
      } catch (error) {
        console.error(`Poll attempt ${i + 1} exception:`, error);
      }

      // Wait before next attempt
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    }

    if (!editedImageUrl) {
      throw new Error("Failed to edit image after multiple attempts");
    }

    // Download the edited image from the temporary URL
    const imageResponse = await fetch(editedImageUrl);
    if (!imageResponse.ok) {
      throw new Error("Failed to download edited image from temporary URL.");
    }
    const editedImageBuffer = Buffer.from(await imageResponse.arrayBuffer());

    // Upload the edited image to Backblaze to get a persistent URL
    const uniqueFilename = `edit_${Date.now()}_${Math.random().toString(36).substring(2, 9)}.png`;
    const persistentImageUrl = await uploadToBackblaze(editedImageBuffer, uniqueFilename);

    // Get image dimensions using image-js
    const { Image } = require('image-js');
    const image = await Image.load(editedImageBuffer);

    return NextResponse.json({
      imageUrl: persistentImageUrl, // Return the persistent Backblaze URL
      width: image.width,
      height: image.height,
      success: true,
    });

  } catch (error) {
    console.error("Error in edit-image:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Failed to edit image";

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
