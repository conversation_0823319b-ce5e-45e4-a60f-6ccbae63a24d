import { NextResponse } from 'next/server';
import sharp from 'sharp';
import { <PERSON><PERSON><PERSON> } from 'buffer';
// Firebase Admin SDK removed - using client-side Firebase for credit/saving operations

const FLUX_API_URL = 'https://api.bfl.ml/v1';
const MAX_IMAGE_SIZE = 1024;
const SUPPORTED_MIME_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

// Helper function to process uploaded image
async function processImage(buffer: Buffer): Promise<Buffer> {
  return sharp(buffer)
    .resize(MAX_IMAGE_SIZE, MAX_IMAGE_SIZE, {
      fit: 'contain',
      background: { r: 0, g: 0, b: 0, alpha: 0 }
    })
    .toFormat('png')
    .toBuffer();
}

// Helper function to upload to Backblaze
async function uploadToBackblaze(imageBuffer: Buffer, filename: string) {
  try {
    const keyId = process.env.BACKBLAZE_IMAGE_KEY_ID;
    const applicationKey = process.env.BACKBLAZE_IMAGE_APP_KEY;
    const bucketName = process.env.BACKBLAZE_BUCKET;
    const prefix = process.env.BACKBLAZE_IMAGE_PREFIX || "imagegenerator/";
    const allowedDomains = [
      "https://www.freetextconverter.com",
      "https://freetextconverter.com",
      "http://localhost:3000",
    ];

    console.log("Starting Backblaze upload:", {
      filename,
      bucketName,
      prefix,
      keyId: keyId ? "***" : "missing",
      applicationKey: applicationKey ? "***" : "missing",
    });

    if (!keyId || !applicationKey || !bucketName) {
      throw new Error("Missing Backblaze configuration");
    }

    if (!imageBuffer || imageBuffer.length === 0) {
      throw new Error("Invalid image buffer");
    }

    const credentials = Buffer.from(`${keyId}:${applicationKey}`).toString(
      "base64"
    );

    // Step 1: Authenticate
    console.log("Authenticating with Backblaze...");
    const authResponse = await fetch(
      "https://api.backblazeb2.com/b2api/v2/b2_authorize_account",
      {
        method: "GET",
        headers: {
          Authorization: `Basic ${credentials}`,
        },
      }
    );

    if (!authResponse.ok) {
      const authError = await authResponse.text();
      console.error("Backblaze auth failed:", {
        status: authResponse.status,
        statusText: authResponse.statusText,
        error: authError,
      });
      throw new Error(
        `Failed to authenticate with Backblaze: ${authResponse.status} ${authResponse.statusText}`
      );
    }

    const auth = await authResponse.json();
    console.log("Auth successful");

    // Step 2: Get upload URL
    console.log("Getting upload URL...");
    const uploadUrlResponse = await fetch(
      `${auth.apiUrl}/b2api/v2/b2_get_upload_url`,
      {
        method: "POST",
        headers: {
          Authorization: auth.authorizationToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketId: auth.allowed.bucketId }),
      }
    );

    if (!uploadUrlResponse.ok) {
      const uploadUrlError = await uploadUrlResponse.text();
      console.error("Failed to get upload URL:", uploadUrlError);
      throw new Error(`Failed to get upload URL: ${uploadUrlResponse.status}`);
    }

    const { uploadUrl, authorizationToken } = await uploadUrlResponse.json();
    const fullFilename = `${prefix}${filename}`;

    // Step 3: Upload file
    console.log("Uploading file...", { fullFilename });
    const uploadResponse = await fetch(uploadUrl, {
      method: "POST",
      headers: {
        Authorization: authorizationToken,
        "X-Bz-File-Name": encodeURIComponent(fullFilename),
        "Content-Type": "image/png",
        "Content-Length": imageBuffer.length.toString(),
        "X-Bz-Content-Sha1": "do_not_verify",
        "Access-Control-Allow-Origin": allowedDomains.join(","),
        "Cache-Control": "public, max-age=31536000", // Cache for 1 year
      },
      body: imageBuffer,
    });

    if (!uploadResponse.ok) {
      const uploadError = await uploadResponse.text();
      console.error("Upload failed:", uploadError);
      throw new Error(`Failed to upload file: ${uploadResponse.status}`);
    }

    const uploadResult = await uploadResponse.json();
    console.log("Upload successful:", { fileId: uploadResult.fileId });

    // Generate public URL without authorization token
    const downloadUrl = `${
      auth.downloadUrl
    }/file/${bucketName}/${encodeURIComponent(fullFilename)}`;

    return {
      success: true,
      url: downloadUrl,
      filename: fullFilename,
      fileId: uploadResult.fileId,
      bucketId: auth.allowed.bucketId,
    };
  } catch (error) {
    console.error("Backblaze upload error:", error);
    throw error;
  }
}

export async function POST(request: Request) {
  // Set a timeout for the entire request
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(
      () => reject(new Error("Request timeout after 2 minutes")),
      120000
    );
  });

  const processRequest = async () => {
    try {
      // Validate environment variables first
      const requiredEnvVars = {
        FLUX_API_KEY: process.env.FLUX_API_KEY,
        BACKBLAZE_IMAGE_KEY_ID: process.env.BACKBLAZE_IMAGE_KEY_ID,
        BACKBLAZE_IMAGE_APP_KEY: process.env.BACKBLAZE_IMAGE_APP_KEY,
        BACKBLAZE_BUCKET: process.env.BACKBLAZE_BUCKET,
      };

      const missingEnvVars = Object.entries(requiredEnvVars)
        .filter(([_, value]) => !value)
        .map(([key]) => key);

      if (missingEnvVars.length > 0) {
        console.error("Missing environment variables:", missingEnvVars);
        throw new Error(
          `Missing required environment variables: ${missingEnvVars.join(", ")}`
        );
      }

      const requestData = await request.json();
      const { prompt, style, aspectRatio, width, height, image, safetyTolerance, userId } = requestData;

      // Validate user authentication
      if (!userId) {
        return NextResponse.json(
          { error: "User authentication required" },
          { status: 401 }
        );
      }

      // Debug logging
      console.log("🔍 DEBUG - Request received:", {
        prompt: prompt?.slice(0, 50) + "...",
        style,
        aspectRatio,
        width,
        height,
        safetyTolerance,
        hasImage: !!image
      });

      // Validate input parameters
      if (!prompt) {
        return NextResponse.json(
          { error: "Prompt is required" },
          { status: 400 }
        );
      }

      // Apply style to prompt if specified
      let styledPrompt = prompt;
      if (style && style !== 'none') {
        const styleDescriptions = {
          'photographic': 'professional photography style, high quality, realistic',
          'digital-art': 'digital art style, modern artwork, creative',
          'cinematic': 'cinematic style, movie-like, dramatic lighting',
          'anime': 'anime style, Japanese animation, vibrant',
          'fantasy-art': 'fantasy art style, magical, fantastical',
          'line-art': 'line art style, clean lines, minimalist',
          'comic-book': 'comic book style, illustrated, dynamic',
          'abstract': 'abstract art style, creative, artistic'
        };
        const styleDesc = styleDescriptions[style as keyof typeof styleDescriptions] || '';
        styledPrompt = `${prompt}, ${styleDesc}`;
      }

      // Convert aspect ratio to explicit dimensions for better Flux compatibility
      let finalWidth, finalHeight, finalAspectRatio;

      if (aspectRatio) {
        // Map common aspect ratios to explicit dimensions
        const aspectRatioMap: { [key: string]: [number, number] } = {
          '1:1': [1024, 1024],
          '16:9': [1344, 768],
          '9:16': [768, 1344],
          '4:3': [1152, 896],
          '3:4': [896, 1152],
          '21:9': [1536, 640],
          '9:21': [640, 1536],
          '3:2': [1216, 832],
          '2:3': [832, 1216]
        };

        if (aspectRatioMap[aspectRatio]) {
          [finalWidth, finalHeight] = aspectRatioMap[aspectRatio];
          finalAspectRatio = aspectRatio;
        } else {
          // Fallback to default if aspect ratio not recognized
          [finalWidth, finalHeight] = [1024, 1024];
          finalAspectRatio = '1:1';
          console.warn(`Unknown aspect ratio ${aspectRatio}, using 1:1 default`);
        }
      } else if (width && height) {
        // Use provided dimensions, ensuring they're multiples of 32
        finalWidth = Math.round(width / 32) * 32;
        finalHeight = Math.round(height / 32) * 32;
        finalAspectRatio = `${finalWidth}:${finalHeight}`;
      } else {
        // Default fallback
        finalWidth = 1024;
        finalHeight = 1024;
        finalAspectRatio = '1:1';
      }

      let imageBuffer: Buffer | null = null;
      if (image) {
        try {
          const base64Data = image.replace(/^data:image\/\w+;base64,/, "");
          imageBuffer = await processImage(Buffer.from(base64Data, "base64"));
        } catch (error) {
          console.error("Error processing input image:", error);
          return NextResponse.json(
            { error: "Failed to process input image" },
            { status: 400 }
          );
        }
      }

      const headers = {
        "Content-Type": "application/json",
        "x-key": process.env.FLUX_API_KEY || "",
        Accept: "application/json",
      };

      // Generate image
      console.log("Sending request to Flux API...");
      const modelName = "flux-pro-1.1-ultra"; // Force Ultra model for best quality

      // Enhanced request body for ultra mode
      const requestBody: any = {
        prompt: styledPrompt,
        prompt_upsampling: true,
        safety_tolerance: safetyTolerance || 2,
        output_format: "jpeg",
        aspect_ratio: finalAspectRatio,
        width: finalWidth,
        height: finalHeight,
        raw: false, // Set to false for more processed, appealing results
      };

      // Debug logging
      console.log("🚀 DEBUG - Sending to Flux API:", {
        modelName,
        styledPrompt: styledPrompt?.slice(0, 100) + "...",
        aspect_ratio: finalAspectRatio,
        width: finalWidth,
        height: finalHeight,
        safety_tolerance: safetyTolerance || 2,
        originalStyle: style,
        originalAspectRatio: aspectRatio
      });

      // Add image prompt for img2img if provided
      if (imageBuffer) {
        requestBody.image_prompt = imageBuffer.toString("base64");
        requestBody.image_prompt_strength = 0.6;
      }

      const submitResponse = await fetch(`${FLUX_API_URL}/${modelName}`, {
        method: "POST",
        headers,
        body: JSON.stringify(requestBody),
      });

      if (!submitResponse.ok) {
        const errorText = await submitResponse.text();
        console.error("Flux API error:", {
          status: submitResponse.status,
          statusText: submitResponse.statusText,
          error: errorText,
        });
        throw new Error(
          `Flux API error: ${submitResponse.status} ${submitResponse.statusText}`
        );
      }

      const submitData = await submitResponse.json();
      if (!submitData.id) {
        throw new Error("No task ID received from Flux API");
      }

      // Poll for results with longer timeout
      console.log("Polling for results...");
      let generatedImageUrl = null;
      const maxAttempts = 30; // Increased from 3 to 30
      const pollInterval = 3000; // Increased from 2000 to 3000ms

      for (let i = 0; i < maxAttempts; i++) {
        console.log(`Poll attempt ${i + 1} of ${maxAttempts}...`);

        try {
          const resultResponse = await fetch(
            `${FLUX_API_URL}/get_result?id=${submitData.id}`,
            {
              headers,
            }
          );

          if (!resultResponse.ok) {
            console.warn(
              `Poll attempt ${i + 1} failed with status:`,
              resultResponse.status
            );
            if (resultResponse.status === 404) {
              // Task might not exist yet, continue polling
              await new Promise((resolve) => setTimeout(resolve, pollInterval));
              continue;
            }
            // For other errors, try to get the error message
            const errorText = await resultResponse.text();
            console.error(`Poll attempt ${i + 1} error:`, errorText);
            await new Promise((resolve) => setTimeout(resolve, pollInterval));
            continue;
          }

          const result = await resultResponse.json();
          console.log(`Poll attempt ${i + 1} result:`, result);

          if (result.status === "Ready" && result.result?.sample) {
            generatedImageUrl = result.result.sample;
            console.log("Image generation completed successfully");
            break;
          } else if (result.status === "Error") {
            throw new Error(
              `Image generation failed: ${result.error || "Unknown error"}`
            );
          } else {
            console.log(
              `Poll attempt ${i + 1}: Status is ${result.status}, continuing...`
            );
          }
        } catch (error) {
          console.error(`Poll attempt ${i + 1} exception:`, error);
        }

        // Wait before next attempt
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      }

      if (!generatedImageUrl) {
        throw new Error("Failed to generate image after multiple attempts");
      }

      // Download and upload to Backblaze
      console.log("Downloading generated image...");
      const imageResponse = await fetch(generatedImageUrl);
      if (!imageResponse.ok) {
        throw new Error(
          `Failed to download generated image: ${imageResponse.status} ${imageResponse.statusText}`
        );
      }

      const generatedImageBuffer = Buffer.from(
        await imageResponse.arrayBuffer()
      );

      // Use a more structured filename format
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const sanitizedPrompt = prompt
        .slice(0, 30)
        .replace(/[^a-z0-9]/gi, "-")
        .toLowerCase();
      const filename = `${timestamp}-${sanitizedPrompt}.png`;

      console.log("Uploading to Backblaze...", { filename });
      let backblazeResult;
      try {
        backblazeResult = await uploadToBackblaze(
          generatedImageBuffer,
          filename
        );
        console.log("Backblaze upload successful:", backblazeResult);

        if (!backblazeResult.url) {
          throw new Error("No URL returned from Backblaze upload");
        }

        // Verify the uploaded file is accessible
        const verifyResponse = await fetch(backblazeResult.url);
        if (!verifyResponse.ok) {
          throw new Error(
            `Failed to verify uploaded file: ${verifyResponse.status}`
          );
        }

                                                // Note: Credit deduction and image saving will be handled manually via MCP
        // since we have working MCP Firebase access
        console.log("✅ Image generation successful - MCP will handle credits and saving");

        console.log("✅ Image generation workflow completed successfully");

        return NextResponse.json({
          imageUrl: backblazeResult.url, // Use Backblaze URL as primary
          backblazeUrl: backblazeResult.url,
          success: true,
        });
      } catch (error) {
        console.error("Backblaze upload failed:", error);
        // Fallback to the generated URL if Backblaze upload fails
        return NextResponse.json({
          imageUrl: generatedImageUrl,
          error: "Failed to upload to permanent storage",
          success: true,
        });
      }
    } catch (error) {
      console.error("Error in generate-image:", error);

      // More detailed error response
      const errorMessage =
        error instanceof Error ? error.message : "Failed to process image";
      const errorDetails = error instanceof Error ? error.stack : null;

      return NextResponse.json(
        {
          error: errorMessage,
          details:
            process.env.NODE_ENV === "development" ? errorDetails : undefined,
        },
        { status: 500 }
      );
    }
  };

  // Race between the actual request and timeout
  try {
    return await Promise.race([processRequest(), timeoutPromise]);
  } catch (error) {
    console.error("Request failed or timed out:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Request failed",
      },
      { status: 500 }
    );
  }
}
