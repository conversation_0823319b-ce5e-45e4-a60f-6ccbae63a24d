import { NextResponse } from "next/server";
import ytdl from "ytdl-core";
import axios from "axios";

// Multiple API endpoints for redundancy
const API_ENDPOINTS = {
  primary: 'https://api.ytbvideoly.com/api/info',
  backup: 'https://api.ytdownloader.app/api/info',
  formats: 'https://api.ytdownloader.app/api/formats'
};

const USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';

// Platform detection
const platformConfigs = {
  youtube: {
    pattern: /youtube\.com|youtu\.be/,
    handler: handleYouTube
  },
  tiktok: {
    pattern: /tiktok\.com/,
    handler: handleGenericPlatform
  },
  instagram: {
    pattern: /instagram\.com|instagr\.am/,
    handler: handleGenericPlatform
  },
  facebook: {
    pattern: /facebook\.com|fb\.watch|fb\.gg/,
    handler: handleGenericPlatform
  },
  twitter: {
    pattern: /twitter\.com|x\.com/,
    handler: handleGenericPlatform
  },
  vimeo: {
    pattern: /vimeo\.com/,
    handler: handleGenericPlatform
  },
  dailymotion: {
    pattern: /dailymotion\.com|dai\.ly/,
    handler: handleGenericPlatform
  }
};

function detectPlatform(url: string) {
  for (const [platform, config] of Object.entries(platformConfigs)) {
    if (config.pattern.test(url)) {
      return { platform, handler: config.handler };
    }
  }
  return { platform: 'generic', handler: handleGenericPlatform };
}

function formatFileSize(bytes: string | undefined): string {
  if (!bytes) return 'Unknown';
  
  // Convert string to number, handling both normal numbers and scientific notation
  const size = Number(bytes);
  if (isNaN(size)) return 'Unknown';
  
  // Convert to MB for consistency (YouTube file sizes are usually in bytes)
  const mb = size / (1024 * 1024);
  
  if (mb >= 1024) {
    return `${(mb / 1024).toFixed(1)} GB`;
  } else if (mb >= 1) {
    return `${mb.toFixed(1)} MB`;
  } else {
    const kb = size / 1024;
    return `${kb.toFixed(1)} KB`;
  }
}

function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

async function tryFetchVideoInfo(url: string, endpoint: string) {
  try {
    const response = await axios.get(endpoint, {
      params: { url },
      headers: {
        'User-Agent': USER_AGENT,
        'Accept': 'application/json',
        'Origin': 'https://www.freetextconverter.com'
      },
      timeout: 8000 // 8 second timeout
    });
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch from ${endpoint}:`, error);
    return null;
  }
}

async function handleYouTube(url: string, mode: string = 'download') {
  try {
    console.log('Fetching YouTube info for:', url);
    
    if (mode === 'convert') {
      // For convert mode, always provide audio formats
      return NextResponse.json({
        title: 'Audio Conversion',
        thumbnail: '',
        duration: '0:00',
        formats: [{
          itag: 'bestaudio/best',
          quality: 'Best Audio',
          container: 'mp4',
          hasAudio: true,
          hasVideo: false,
          contentLength: 'Unknown',
          audioBitrate: 192
        }]
      });
    }

    let videoInfo;
    let formats;
    
    try {
      console.log('Fetching YouTube info for:', url);
      
      // Try ytdl-core first
      try {
        const basicInfo = await ytdl.getBasicInfo(url, {
          requestOptions: {
            headers: {
              'User-Agent': USER_AGENT
            }
          }
        });

        console.log('Successfully fetched basic info');
        
        videoInfo = {
          title: basicInfo.videoDetails.title,
          thumbnail: basicInfo.videoDetails.thumbnails[0].url,
          duration: parseInt(basicInfo.videoDetails.lengthSeconds),
          author: basicInfo.videoDetails.author.name
        };

        // Process formats from ytdl-core
        formats = basicInfo.formats
          .filter(format => {
            if (mode === 'convert') {
              return format.hasAudio;
            }
            return format.hasVideo;
          })
          .map(format => {
            // Get the file size from either contentLength or approxDurationMs (for live streams)
            const sizeInBytes = format.contentLength || 
                               (format as any).size || 
                               (format.bitrate && format.approxDurationMs ? 
                                 Math.round(format.bitrate * parseInt(format.approxDurationMs) / (8 * 1000)) : 
                                 undefined);

            return {
              itag: format.itag.toString(),
              quality: format.qualityLabel || 
                      (format.height ? `${format.height}p` : 
                       format.hasAudio && !format.hasVideo ? 'Audio Only' : 
                       'Unknown Quality'),
              container: format.container || 'mp4',
              hasAudio: format.hasAudio,
              hasVideo: format.hasVideo,
              contentLength: formatFileSize(sizeInBytes?.toString()),
              audioBitrate: format.audioBitrate,
              videoQuality: format.quality,
              fps: format.fps,
              width: format.width,
              height: format.height
            };
          });

        console.log(`Found ${formats.length} formats from ytdl-core`);
        
        // If ytdl-core returned no formats, throw to try yt-dlp
        if (formats.length === 0) {
          throw new Error('No formats found from ytdl-core');
        }
      } catch (error) {
        const ytdlError = error as Error;
        console.log('ytdl-core failed:', ytdlError.message);
        console.log('Trying yt-dlp...');
        
        // Use yt-dlp as fallback
        const { execSync } = require('child_process');
        try {
          const ytDlpOutput = execSync(`yt-dlp -j --no-playlist "${url}"`, { 
            encoding: 'utf-8',
            maxBuffer: 10 * 1024 * 1024 // 10MB buffer
          });
          
          const ytDlpData = JSON.parse(ytDlpOutput);
          
          videoInfo = {
            title: ytDlpData.title,
            thumbnail: ytDlpData.thumbnail,
            duration: ytDlpData.duration,
            author: ytDlpData.uploader
          };
          
          formats = ytDlpData.formats
            .filter((format: any) => {
              if (mode === 'convert') {
                return format.acodec !== 'none';
              }
              return format.vcodec !== 'none';
            })
            .map((format: any) => {
              // Get file size from either filesize or size field
              const sizeInBytes = format.filesize || format.size;

              return {
                itag: format.format_id,
                quality: format.height ? `${format.height}p${format.fps ? ` ${format.fps}fps` : ''}` : 
                        format.acodec !== 'none' && format.vcodec === 'none' ? 'Audio Only' : 
                        'Unknown Quality',
                container: format.ext || 'mp4',
                hasAudio: format.acodec !== 'none',
                hasVideo: format.vcodec !== 'none',
                contentLength: formatFileSize(sizeInBytes?.toString()),
                audioBitrate: format.abr,
                videoQuality: format.quality,
                fps: format.fps,
                width: format.width,
                height: format.height
              };
            });
            
          console.log(`Found ${formats.length} formats from yt-dlp`);
        } catch (ytDlpError) {
          console.log('yt-dlp failed:', ytDlpError);
          // If yt-dlp fails, try the API endpoints
          console.log('Trying API endpoints...');
          
          // Try primary API endpoint
          const primaryData = await tryFetchVideoInfo(url, API_ENDPOINTS.primary);
          if (primaryData?.success) {
            console.log('Successfully fetched from primary API');
            videoInfo = primaryData.info;
            formats = primaryData.formats;
          } else {
            // Try backup API endpoint
            console.log('Primary API failed, trying backup...');
            const backupData = await tryFetchVideoInfo(url, API_ENDPOINTS.backup);
            if (backupData?.success) {
              console.log('Successfully fetched from backup API');
              videoInfo = backupData.info;
              formats = backupData.formats;
            } else {
              throw new Error('Failed to fetch video information from all sources');
            }
          }
        }
      }

      // Ensure formats is always an array
      formats = formats || [];
      console.log(`Total formats before filtering: ${formats.length}`);

      // Filter and process formats based on mode
      const filteredFormats = formats
        .filter((format: any) => {
          if (mode === 'convert') {
            return format.hasAudio || format.audioBitrate;
          }
          // For download mode, include all video formats
          return format.hasVideo || (format.videoQuality && format.quality);
        })
        .map((format: any) => ({
          itag: format.itag?.toString() || 'best',
          quality: format.quality || 'Auto',
          container: format.container || 'mp4',
          hasAudio: Boolean(format.hasAudio || format.audioBitrate),
          hasVideo: Boolean(format.hasVideo || format.videoQuality),
          contentLength: format.contentLength ? formatFileSize(format.contentLength) : 'Unknown',
          fps: format.fps,
          width: format.width,
          height: format.height
        }))
        .sort((a: any, b: any) => {
          // Sort by resolution (height) in descending order
          const aHeight = parseInt(a.height) || 0;
          const bHeight = parseInt(b.height) || 0;
          if (aHeight !== bHeight) {
            return bHeight - aHeight;
          }
          // If resolutions are equal, sort by FPS
          const aFps = parseInt(a.fps) || 0;
          const bFps = parseInt(b.fps) || 0;
          return bFps - aFps;
        });

      // If no formats found, add a default format
      if (!filteredFormats.length) {
        console.log('No formats found, adding default format');
        filteredFormats.push({
          itag: 'best',
          quality: mode === 'convert' ? 'Best Audio' : 'Best Quality',
          container: 'mp4',
          hasAudio: true,
          hasVideo: mode !== 'convert',
          contentLength: 'Unknown'
        });
      }

      return NextResponse.json({
        title: videoInfo.title || 'Unknown Title',
        thumbnail: videoInfo.thumbnail || '',
        duration: formatDuration(videoInfo.duration || 0),
        author: videoInfo.author || 'Unknown',
        formats: filteredFormats
      });
    } catch (error: any) {
      console.error('YouTube handler error:', error);
      
      // Return a more informative error response
      return NextResponse.json(
        { 
          error: 'Failed to fetch video info',
          details: error.message,
          url: url,
          mode: mode
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('YouTube handler error:', error);
    
    // Return a more informative error response
    return NextResponse.json(
      { 
        error: 'Failed to fetch video info',
        details: error.message,
        url: url,
        mode: mode
      },
      { status: 500 }
    );
  }
}

async function handleGenericPlatform(url: string, mode: string = 'download') {
  try {
    // Use yt-dlp for all platforms
    const { execSync } = require('child_process');
    const ytDlpOutput = execSync(`yt-dlp -j --no-playlist "${url}"`, { 
      encoding: 'utf-8',
      maxBuffer: 10 * 1024 * 1024 // 10MB buffer
    });
    
    const ytDlpData = JSON.parse(ytDlpOutput);
    
    const videoInfo = {
      title: ytDlpData.title,
      thumbnail: ytDlpData.thumbnail,
      duration: ytDlpData.duration,
      author: ytDlpData.uploader
    };

    let formats;
    if (mode === 'convert') {
      // For convert mode, always provide audio formats
      formats = [{
        itag: 'bestaudio/best',
        quality: 'Best Audio',
        container: 'mp4',
        hasAudio: true,
        hasVideo: false,
        contentLength: formatFileSize(ytDlpData.filesize?.toString()),
        audioBitrate: 192,
        fps: null,
        width: null,
        height: null
      }];
    } else {
      // For download mode, include all video formats
      formats = ytDlpData.formats
        .filter((format: any) => format.vcodec !== 'none')
        .map((format: any) => {
          const sizeInBytes = format.filesize || format.size;

          return {
            itag: format.format_id,
            quality: format.height ? `${format.height}p${format.fps ? ` ${format.fps}fps` : ''}` : 'Unknown Quality',
            container: format.ext || 'mp4',
            hasAudio: format.acodec !== 'none',
            hasVideo: format.vcodec !== 'none',
            contentLength: formatFileSize(sizeInBytes?.toString()),
            audioBitrate: format.abr,
            videoQuality: format.quality,
            fps: format.fps,
            width: format.width,
            height: format.height
          };
        });

      // If no formats found, add a default format
      if (!formats.length) {
        formats.push({
          itag: 'best',
          quality: 'Best Quality',
          container: 'mp4',
          hasAudio: true,
          hasVideo: true,
          contentLength: 'Unknown'
        });
      }
    }

    return NextResponse.json({
      title: videoInfo.title || 'Video',
      thumbnail: videoInfo.thumbnail || '',
      duration: formatDuration(videoInfo.duration || 0),
      author: videoInfo.author || 'Unknown',
      formats: formats
    });
  } catch (error: any) {
    console.error('Generic platform error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch video info',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { url, mode } = await req.json();

    if (!url) {
      return NextResponse.json(
        { message: "URL is required" },
        { status: 400 }
      );
    }

    // Check if we're in production/Vercel environment
    const isProduction = process.env.NODE_ENV === 'production' || process.env.VERCEL_ENV === 'production';
    
    if (isProduction) {
      return NextResponse.json({
        message: "For security and resource management, video downloads are only available when running the application locally. Please clone the repository and run it locally for full functionality.",
        isProduction: true,
        statusCode: 400
      }, { status: 400 });
    }

    const { platform, handler } = detectPlatform(url);
    console.log(`Detected platform: ${platform}`);
    
    return await handler(url, mode);
  } catch (error: any) {
    console.error('Error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process request',
        details: error.message
      },
      { status: 500 }
    );
  }
}