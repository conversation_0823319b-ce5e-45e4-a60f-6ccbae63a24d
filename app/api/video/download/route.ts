import { NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";
import { createReadStream, statSync, mkdirSync, existsSync } from "fs";
import { unlink } from "fs/promises";
import path from "path";
import os from "os";

const execAsync = promisify(exec);

// Get yt-dlp path dynamically
function getYtDlpPath() {
  try {
    return execAsync('which yt-dlp').then(({stdout}) => stdout.trim());
  } catch (error) {
    console.error('yt-dlp not found in PATH');
    return process.platform === 'darwin' ? '/opt/homebrew/bin/yt-dlp' : '/usr/local/bin/yt-dlp';
  }
}

// Platform configuration types
interface PlatformConfig {
  urlPattern: RegExp;
  format: (itag: string) => string;
  usesMergeFormat: boolean;
}

const platformConfigs: Record<string, PlatformConfig> = {
  youtube: {
    urlPattern: /youtube\.com|youtu\.be/,
    format: (itag: string) => itag,
    usesMergeFormat: true
  },
  tiktok: {
    urlPattern: /tiktok\.com/,
    format: () => 'best',
    usesMergeFormat: false
  },
  instagram: {
    urlPattern: /instagram\.com/,
    format: () => 'best',
    usesMergeFormat: false
  },
  facebook: {
    urlPattern: /facebook\.com|fb\.watch/,
    format: () => 'best',
    usesMergeFormat: false
  },
  twitter: {
    urlPattern: /twitter\.com|x\.com/,
    format: () => 'best',
    usesMergeFormat: false
  }
};

function detectPlatform(url: string): { platform: string; config: PlatformConfig } {
  for (const [platform, config] of Object.entries(platformConfigs)) {
    if (config.urlPattern.test(url)) {
      return { platform, config };
    }
  }
  return { 
    platform: 'generic', 
    config: { 
      urlPattern: /./, 
      format: () => 'best',
      usesMergeFormat: false 
    } 
  };
}

export async function POST(req: Request) {
  try {
    const { url, itag, title, type, convertFormat, convertQuality } = await req.json();
    
    if (!url) {
      return NextResponse.json(
        { message: "URL is required" },
        { status: 400 }
      );
    }

    // Check if we're in production environment
    const isProduction = process.env.NODE_ENV === 'production' || process.env.VERCEL_ENV === 'production';
    
    if (isProduction) {
      return NextResponse.json({
        message: "For security and resource management, video downloads are only available when running the application locally. Please clone the repository and run it locally for full functionality.",
        statusCode: 400,
        isProduction: true
      }, { status: 400 });
    }

    // Create temp directory if it doesn't exist
    const tempDir = path.join(os.tmpdir(), 'video-downloads');
    if (!existsSync(tempDir)) {
      mkdirSync(tempDir, { recursive: true });
    }

    // Generate safe filenames
    const safeTitle = title?.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'video';
    const outputPath = path.join(tempDir, `${safeTitle}_${Date.now()}.${type === 'convert' ? convertFormat : 'mp4'}`);

    const { platform, config } = detectPlatform(url);
    console.log(`Detected platform: ${platform}`);

    // Get yt-dlp path
    const ytDlpPath = await getYtDlpPath();

    let command;
    if (type === 'convert') {
      // Command for audio conversion
      command = [
        ytDlpPath,
        '-f bestaudio/best',  // Select best audio format
        '--extract-audio',    // Extract audio
        `--audio-format ${convertFormat}`,  // Set output format (mp3, m4a, etc)
        `--audio-quality ${convertQuality}k`,  // Set quality (bitrate)
        '--no-playlist',
        `--output ${outputPath}`,
        '--no-warnings',
        '--no-call-home',
        `"${url}"`
      ].filter(Boolean).join(' ');
    } else {
      // Command for video download
      command = [
        ytDlpPath,
        `-f ${config.usesMergeFormat ? `${config.format(itag)}+bestaudio` : config.format(itag)}`,
        '--no-playlist',
        '--merge-output-format mp4',
        `--output ${outputPath}`,
        '--no-warnings',
        '--no-call-home',
        `"${url}"`
      ].filter(Boolean).join(' ');
    }

    console.log('Executing command:', command);
    const { stdout, stderr } = await execAsync(command);
    console.log('stdout:', stdout);
    console.log('stderr:', stderr);

    // Get file stats
    const stat = statSync(outputPath);
    const fileSize = stat.size;
    
    // Create readable stream
    const videoStream = createReadStream(outputPath);
    
    // Clean up: Delete the file after streaming
    videoStream.on('end', () => {
      unlink(outputPath).catch(console.error);
    });
    
    // Set appropriate content type based on the format
    const contentType = type === 'convert' 
      ? convertFormat === 'mp3' ? 'audio/mpeg' : `audio/${convertFormat}`
      : 'video/mp4';
    
    // Return streaming response
    return new NextResponse(videoStream as any, {
      headers: {
        'Content-Type': contentType,
        'Content-Length': fileSize.toString(),
        'Content-Disposition': `attachment; filename="${safeTitle}.${type === 'convert' ? convertFormat : 'mp4'}"`,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error: any) {
    console.error('Download error:', error);
    return NextResponse.json({ 
      error: error.message,
      details: error.stack
    }, { status: 500 });
  }
}

// Handle CORS preflight
export async function OPTIONS(req: Request) {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': 'https://www.freetextconverter.com',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}