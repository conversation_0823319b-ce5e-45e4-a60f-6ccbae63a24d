import { NextResponse } from 'next/server';
import sharp from 'sharp';

const ALLOWED_DOMAINS = ['bfldeliverysc.blob.core.windows.net']; // Add any other allowed domains

async function processEmojiImage(inputBuffer: Buffer): Promise<Buffer> {
  try {
    // Create a white background image
    const backgroundBuffer = await sharp({
      create: {
        width: 64,
        height: 64,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      }
    }).png().toBuffer();

    // Process the main image
    const processedImage = await sharp(inputBuffer)
      // Convert to PNG and ensure alpha channel
      .toFormat('png')
      .ensureAlpha()
      // Resize maintaining aspect ratio
      .resize(64, 64, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      })
      // Increase contrast to help separate foreground from background
      .modulate({
        brightness: 1.1,
        saturation: 1.2
      })
      // Make white pixels transparent
      .threshold(240)
      .toBuffer();

    // Composite the processed image over the transparent background
    return await sharp(backgroundBuffer)
      .composite([
        {
          input: processedImage,
          blend: 'over'
        }
      ])
      .png({
        quality: 100,
        compressionLevel: 9,
        palette: true
      })
      .toBuffer();
  } catch (error) {
    console.error('Error processing emoji image:', error);
    throw error;
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');

    if (!imageUrl) {
      return new NextResponse('Missing URL parameter', { status: 400 });
    }

    // Validate URL domain
    const urlObj = new URL(imageUrl);
    if (!ALLOWED_DOMAINS.includes(urlObj.hostname)) {
      return new NextResponse('Invalid image domain', { status: 403 });
    }

    const response = await fetch(imageUrl, {
      headers: {
        'Accept': 'image/*'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType?.startsWith('image/')) {
      return new NextResponse('Invalid content type', { status: 400 });
    }

    // Process image to look more like an emoji
    const arrayBuffer = await response.arrayBuffer();
    const emojiBuffer = await processEmojiImage(Buffer.from(arrayBuffer));

    return new NextResponse(emojiBuffer, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'public, max-age=31536000',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    console.error('Error proxying image:', error);
    return new NextResponse(
      error instanceof Error ? error.message : 'Failed to fetch image', 
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: Request) {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}