import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  increment, 
  serverTimestamp, 
  setDoc, 
  updateDoc, 
  query, 
  where,
  runTransaction
} from 'firebase/firestore';

interface QRCodeData {
  text: string;
  userId: string;
  content: string;
  type: string;
  [key: string]: any;
}

export async function GET(request: Request): Promise<NextResponse> {
  const startTime = Date.now();
  console.log(' [QR Scan] Starting scan process...');
  
  try {
    const { searchParams } = new URL(request.url);
    const qrCodeId = searchParams.get('id');
    const userAgent = request.headers.get('user-agent') || '';
    
    console.log(' [QR Scan] Device Info:', {
      userAgent,
      isMobile: /Mobile|Android|iPhone/i.test(userAgent),
      timestamp: new Date().toISOString()
    });

    if (!qrCodeId) {
      console.error(' [QR Scan] No QR code ID provided');
      return NextResponse.json(
        { error: 'QR code ID is required' },
        { status: 400 }
      );
    }

    // Ensure db is properly initialized
    if (!db) {
      throw new Error('Firebase database not initialized');
    }
    
    // Clean up QR code ID by removing any URL encoding and quotes
    const cleanQrCodeId = decodeURIComponent(qrCodeId).replace(/"/g, '');
    console.log(' [QR Scan] Looking up QR code:', cleanQrCodeId);

    // Get the QR code document
    const qrCodeRef = doc(collection(db, 'qr_codes'), cleanQrCodeId);
    const qrCodeSnap = await getDoc(qrCodeRef);

    if (!qrCodeSnap.exists()) {
      console.error(' [QR Scan] QR code not found:', cleanQrCodeId);
      return NextResponse.json(
        { error: 'QR code not found' },
        { status: 404 }
      );
    }

    const qrCode = qrCodeSnap.data() as QRCodeData;
    const userId = qrCode.userId;
    const qrCodePath = `qr_codes/${cleanQrCodeId}`;

    if (!qrCode.content) {
      console.error(' [QR Scan] QR code has no content');
      return NextResponse.json(
        { error: 'Invalid QR code content' },
        { status: 400 }
      );
    }

    try {
      // Record the scan with detailed info
      console.log(' [QR Scan] Recording scan details...');
      
      const scanRef = doc(collection(db, `${qrCodePath}/scans`));
      const scanData = {
        timestamp: serverTimestamp(),
        deviceType: /Mobile|Android|iPhone/i.test(userAgent) ? 'mobile' : 'desktop',
        userAgent: userAgent,
        scanId: scanRef.id,
        success: true
      };
      
      await setDoc(scanRef, scanData);
      console.log(' [QR Scan] Recorded scan:', {
        scanId: scanRef.id,
        path: `${qrCodePath}/scans/${scanRef.id}`,
        deviceType: scanData.deviceType
      });

    } catch (error) {
      console.error(' [QR Scan] Error recording scan:', error);
      // Continue execution - don't fail the scan if analytics fails
    }

    const endTime = Date.now();
    console.log(' [QR Scan] Scan process completed in', endTime - startTime, 'ms');

    return NextResponse.json({
      content: qrCode.content,
      type: qrCode.type || 'text',
      redirectUrl: qrCode.content // Use the content field as the redirect URL
    });

  } catch (error) {
    console.error(' [QR Scan] Error processing scan:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to find which user owns a QR code
async function findQRCodeOwner(qrCodeId: string): Promise<[string | null, QRCodeData | null]> {
  console.log(' [QR Scan] Finding QR code owner for:', qrCodeId);
  
  try {
    // First try to get the QR code directly from the qrCodes collection
    const qrCodeRef = doc(db, 'qr_codes', qrCodeId);
    const qrCodeSnap = await getDoc(qrCodeRef);
    
    if (qrCodeSnap.exists()) {
      const qrCodeData = qrCodeSnap.data() as QRCodeData;
      const userId = qrCodeData.userId;
      console.log(' [QR Scan] Found QR code:', {
        userId,
        qrCodeData
      });
      return [userId, qrCodeData];
    }
    
    // If not found in qrCodes collection, try the users collection
    const usersRef = collection(db, 'users');
    const usersSnap = await getDocs(usersRef);
    console.log(' [QR Scan] Found users:', usersSnap.size);
    
    for (const userDoc of usersSnap.docs) {
      console.log(' [QR Scan] Checking user:', userDoc.id);
      const userQrCodeRef = doc(collection(db, `users/${userDoc.id}/qrcodes`), qrCodeId);
      const userQrCodeSnap = await getDoc(userQrCodeRef);
      
      if (userQrCodeSnap.exists()) {
        console.log(' [QR Scan] Found QR code in user:', {
          userId: userDoc.id,
          qrCodeData: userQrCodeSnap.data()
        });
        return [userDoc.id, userQrCodeSnap.data() as QRCodeData];
      }
    }
  } catch (error) {
    console.error(' [QR Scan] Error finding QR code owner:', error);
  }
  
  console.log(' [QR Scan] QR code not found in any collection');
  return [null, null];
}

export async function POST(request: Request): Promise<NextResponse> {
  try {
    const { qrCodeId, userId, deviceType = 'unknown', location = null } = await request.json();

    if (!qrCodeId || !userId) {
      return NextResponse.json(
        { error: 'QR code ID and user ID are required' },
        { status: 400 }
      );
    }

    // Add scan to the nested scans collection
    const scanRef = doc(collection(db, `users/${userId}/qrcodes/${qrCodeId}/scans`));
    await setDoc(scanRef, {
      timestamp: serverTimestamp(),
      deviceType,
      location,
      count: 1
    });

    // Update the QR code's scan count
    const qrCodeRef = doc(db, `users/${userId}/qrcodes/${qrCodeId}`);
    await updateDoc(qrCodeRef, {
      scanCount: increment(1),
      lastScanned: serverTimestamp()
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(' [QR Scan] Error processing scan:', error);
    return NextResponse.json(
      { error: 'Failed to process scan' },
      { status: 500 }
    );
  }
}