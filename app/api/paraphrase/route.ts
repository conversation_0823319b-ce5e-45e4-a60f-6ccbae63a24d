import { NextRequest, NextResponse } from 'next/server';
import { paraphraseText } from '@/lib/api';
import { checkRateLimit } from '@/lib/rate-limit';

export async function POST(request: NextRequest) {
  try {
    const ip = request.headers.get('x-forwarded-for') || 'unknown';
    const rateLimitResult = await checkRateLimit(ip);
    
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          remainingTime: rateLimitResult.remainingTime 
        },
        { status: 429 }
      );
    }

    const { text, tone } = await request.json();

    if (!text || !tone) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const result = await paraphraseText(text, tone);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Paraphrase error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}