import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc, increment, collection, addDoc, serverTimestamp } from 'firebase/firestore';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    // Split the ID to get userId and qrId
    const [userId, qrId] = id.split('_');
    
    if (!userId || !qrId) {
      return NextResponse.json(
        { error: 'Invalid QR code ID format' },
        { status: 400 }
      );
    }

    // Get QR code from the nested collection
    const qrDoc = doc(db, 'users', userId, 'qrcodes', qrId);
    const qrSnapshot = await getDoc(qrDoc);

    if (!qrSnapshot.exists()) {
      return NextResponse.json(
        { error: 'QR code not found' }, 
        { status: 404 }
      );
    }

    // Get the QR code data
    const data = qrSnapshot.data();

    // Create a scan record in the scans subcollection
    const scansRef = collection(qrDoc, 'scans');
    await addDoc(scansRef, {
      timestamp: serverTimestamp(),
      deviceType: request.headers.get('user-agent')?.includes('Mobile') ? 'mobile' : 'desktop',
      count: 1
    });

    // Increment scan count and update last scanned timestamp
    await updateDoc(qrDoc, {
      scanCount: increment(1),
      lastScanned: serverTimestamp()
    });

    // Return the original URL/content for redirection
    return NextResponse.json({
      redirectUrl: data.text || data.content,
      success: true
    });
  } catch (error) {
    console.error('Error processing QR code scan:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}