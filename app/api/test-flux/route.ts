import { NextResponse } from 'next/server';

const FLUX_API_URL = 'https://api.bfl.ml/v1';

export async function GET() {
  try {
    const fluxApiKey = process.env.FLUX_API_KEY;
    
    if (!fluxApiKey) {
      return NextResponse.json({ 
        error: 'FLUX_API_KEY not configured',
        hasKey: false
      }, { status: 500 });
    }

    // Test a simple request to Flux API
    const headers = {
      'Content-Type': 'application/json',
      'x-key': fluxApiKey,
      'Accept': 'application/json'
    };

    const testResponse = await fetch(`${FLUX_API_URL}/flux-pro-1.1`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        prompt: "test",
        width: 512,
        height: 512,
        steps: 1,
        samples: 1
      })
    });

    const responseText = await testResponse.text();
    
    return NextResponse.json({ 
      message: 'Flux API test completed',
      hasKey: true,
      status: testResponse.status,
      statusText: testResponse.statusText,
      response: responseText.substring(0, 500) // Limit response size
    });

  } catch (error) {
    console.error('Flux API test error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error',
      hasKey: !!process.env.FLUX_API_KEY
    }, { status: 500 });
  }
}
