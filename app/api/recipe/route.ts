import { NextResponse } from "next/server";
import {
  RecipeGeneratorRequest,
  Recipe,
  ImageStatus,
  RecipeStatus,
} from "@/types/recipe";
import { processRecipeRequest } from "@/lib/recipe-utils";
import { Buffer } from "buffer";
import { checkRateLimit } from "@/lib/rate-limit";
import { headers } from "next/headers";
import { getProvider } from "@/lib/provider-factory";

const FLUX_API_URL = "https://api.bfl.ml/v1";

const languages: { [key: string]: string } = {
  en: "English",
  es: "Spanish",
  fr: "French",
  de: "German",
  it: "Italian",
  pt: "Portuguese",
  ne: "Nepali",
  zh: "Chinese",
  ja: "Japanese",
  ko: "Korean",
  ru: "Russian",
};

async function generateRecipePrompt(
  request: RecipeGeneratorRequest
): Promise<string> {
  const { ingredients, filters, servings } = request;
  const languageCode = filters.outputLanguage || "en";
  const languageName = languages[languageCode] || "English";

  let prompt = `Generate a detailed recipe in ${languageName} that uses these ingredients: ${ingredients.join(
    ", "
  )}.\n`;

  if (filters.cuisine) {
    prompt += `The recipe should be ${filters.cuisine} cuisine.\n`;
  }

  if (filters.maxCookingTime) {
    prompt += `The cooking time should be under ${filters.maxCookingTime} minutes.\n`;
  }

  if (filters.dietary && filters.dietary.length > 0) {
    prompt += `The recipe should be suitable for ${filters.dietary.join(
      " and "
    )} diets.\n`;
  }

  if (servings) {
    prompt += `The recipe should serve ${servings} people.\n`;
  }

  prompt += `\nProvide the recipe in this format:
- Title
- Description
- Cooking Time (in minutes)
- Difficulty Level (easy/medium/hard)
- Ingredients with measurements
- Step by step instructions but don't include the bullet points or numbers just follow the flow
- Cooking tips
- Nutritional information (calories, protein, carbs, fat)`;

  return prompt;
}

async function generateFoodImage(
  recipeName: string,
  description: string
): Promise<string | null> {
  try {
    console.log("Submitting image generation task...");

    const headers = {
      "Content-Type": "application/json",
      "x-key": process.env.FLUX_API_KEY || "",
      Accept: "application/json",
    };

    const submitResponse = await fetch(`${FLUX_API_URL}/flux-pro-1.1-ultra`, {
      method: "POST",
      headers,
      body: JSON.stringify({
        prompt: `Professional food photography of ${recipeName}. ${description}. Appetizing presentation, high-end restaurant style, beautiful lighting.`,
        prompt_upsampling: true,
        safety_tolerance: 2,
        output_format: "jpeg",
        aspect_ratio: "1:1", // Square format for recipe cards
        raw: false
      }),
    });

    if (!submitResponse.ok) {
      const errorText = await submitResponse.text();
      console.error("Failed to submit image generation task:", {
        status: submitResponse.status,
        statusText: submitResponse.statusText,
        error: errorText,
      });
      return null;
    }

    const submitData = await submitResponse.json();
    console.log("Image generation task submitted:", submitData);

    if (!submitData.id) {
      console.error("No task ID received:", submitData);
      return null;
    }

    // Poll for result with a maximum of 3 attempts
    console.log("Polling for image generation result...");
    for (let i = 0; i < 3; i++) {
      console.log(`Polling attempt ${i + 1}/3`);
      const resultResponse = await fetch(
        `${FLUX_API_URL}/get_result?id=${submitData.id}`,
        {
          headers,
        }
      );

      if (!resultResponse.ok) {
        console.error("Poll request failed:", {
          status: resultResponse.status,
          statusText: resultResponse.statusText,
        });
        continue;
      }

      const result = await resultResponse.json();
      console.log("Poll result:", result);

      if (result.status === "Ready" && result.result?.sample) {
        console.log("Image generation succeeded:", result);
        // Ensure we're getting a valid URL
        const imageUrl = result.result.sample;
        // Test if the URL is accessible
        try {
          const testResponse = await fetch(imageUrl, { method: "HEAD" });
          if (testResponse.ok) {
            return imageUrl;
          }
          console.error("Image URL not accessible:", imageUrl);
        } catch (error) {
          console.error("Error testing image URL:", error);
        }
      }

      // Wait 2 seconds before next poll
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    console.error("Image generation timed out or failed");
    return null;
  } catch (error) {
    console.error("Error generating food image:", error);
    return null;
  }
}

function parseRecipeText(
  text: string,
  requestData: RecipeGeneratorRequest
): Recipe {
  const sections = text
    .split("###")
    .map((s) => s.trim())
    .filter(Boolean);
  const recipe: Recipe = {
    id: Math.random().toString(36).substring(7),
    title: "",
    description: "",
    cookingTime: 30,
    servings: requestData.servings || 2,
    difficulty: "medium",
    cuisine: requestData.filters?.cuisine || "International",
    recipeLanguage: requestData.filters?.outputLanguage || "en",
    ingredients: [],
    instructions: {
      steps: [],
      tips: [],
    },
    nutritionalInfo: {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
    },
    tags: [],
    status: {
      recipe: "completed" as RecipeStatus,
      image: "loading" as ImageStatus,
    },
  };

  const ingredientTranslations: Record<string, string> = {
    "चिकन मिन्स": "Minced Chicken",
    प्याज: "Onion",
    "गहुँको च": "Wheat Flour",
    "अदुवा-लसुनको पेस्ट": "Ginger-Garlic Paste",
    "जिरा पाउडर": "Cumin Powder",
    "धनिया पाउडर": "Coriander Powder",
    "मिर्चको पाउडर": "Chili Powder",
    नुन: "Salt",
    तेल: "Oil",
    पानी: "Water",
  };

  for (const section of sections) {
    const [header, ...contentArr] = section
      .split("\n")
      .map((s) => s.trim())
      .filter(Boolean);
    const content = contentArr.join("\n");

    switch (header.toLowerCase()) {
      case "title":
        recipe.title = content;
        break;
      case "description":
        recipe.description = content;
        break;
      case "cooking time":
        const timeMatch = content.match(/\d+/);
        recipe.cookingTime = timeMatch ? parseInt(timeMatch[0]) : 30;
        break;
      case "difficulty level":
        recipe.difficulty = content.toLowerCase() as "easy" | "medium" | "hard";
        break;
      case "ingredients":
        recipe.ingredients = content
          .split("\n")
          .map((line) => {
            const cleanLine = line.replace(/^[•-]\s*/, "").trim();
            if (!cleanLine) return null;

            // Find the Nepali ingredient name
            const nepaliName = Object.keys(ingredientTranslations).find((key) =>
              cleanLine.includes(key)
            );
            if (nepaliName) {
              const englishName = ingredientTranslations[nepaliName];
              // Add English translation in brackets if not already present
              if (!cleanLine.includes("(")) {
                return `${cleanLine} (${englishName})`;
              }
            }
            return cleanLine;
          })
          .filter(Boolean) as string[];
        break;
      case "step by step instructions":
      case "instructions":
        recipe.instructions = {
          steps: content
            .split("\n")
            .filter((line) => line.trim() && !line.startsWith("###"))
            .map((line) => {
              // First remove any existing bullet points or dashes
              let cleanLine = line.replace(/^[-•]\s*/, "");
              // Then handle the numbering, keeping the original number
              cleanLine = cleanLine.replace(/^\d+\.\s*/, (match) => match);
              return cleanLine;
            })
            .filter(Boolean),
          tips: [],
        };
        break;
      case "cooking tips":
        if (recipe.instructions) {
          recipe.instructions.tips = content
            .split("\n")
            .map((line) => line.replace(/^[•💡-]\s*/, ""))
            .filter(Boolean);
        }
        break;
      case "nutritional information":
      case "nutritional information (per serving)":
        const nutritionLines = content.split("\n");
        for (const line of nutritionLines) {
          const cleanLine = line.replace(/^[•-]\s*/, "").toLowerCase();

          // Extract numbers using regex
          const numberMatch = cleanLine.match(/\d+/);
          if (!numberMatch) continue;

          const value = parseInt(numberMatch[0]);

          if (cleanLine.includes("calories")) {
            recipe.nutritionalInfo.calories = value;
          } else if (cleanLine.includes("protein")) {
            recipe.nutritionalInfo.protein = value;
          } else if (cleanLine.includes("carb")) {
            recipe.nutritionalInfo.carbs = value;
          } else if (cleanLine.includes("fat")) {
            recipe.nutritionalInfo.fat = value;
          }
        }
        break;
    }
  }

  return recipe;
}

export async function POST(request: Request) {
  try {
    const headersList = await headers();
    const ip = headersList.get("x-forwarded-for") || "unknown";
    await checkRateLimit(ip);

    const data: RecipeGeneratorRequest = await request.json();
    const { ingredients, filters, servings, generateImage } = data;

    if (!ingredients || ingredients.length === 0) {
      return NextResponse.json(
        { error: "No ingredients provided" },
        { status: 400 }
      );
    }

    const languageCode = filters.outputLanguage || "en";
    const languageName = languages[languageCode] || "English";
    const prompt =
      `Generate a detailed recipe in ${languageName} that uses these ingredients: ${ingredients.join(
        ", "
      )}.\n\n` +
      (filters.cuisine
        ? `The recipe should be ${filters.cuisine} cuisine.\n`
        : "") +
      (filters.maxCookingTime
        ? `The cooking time should be under ${filters.maxCookingTime} minutes.\n`
        : "") +
      (filters.dietary?.length
        ? `The recipe should be suitable for ${filters.dietary.join(
            " and "
          )} diets.\n`
        : "") +
      (servings ? `The recipe should serve ${servings} people.\n` : "") +
      `\nProvide the recipe in this format:
- Title
- Description
- Cooking Time (in minutes)
- Difficulty Level (easy/medium/hard)
- Ingredients with measurements
- Step by step instructions
- Cooking tips
- Nutritional information (calories, protein, carbs, fat)`;

    const messages = [
      {
        role: "system",
        content:
          "You are a professional chef who creates detailed, accurate recipes.",
      },
      {
        role: "user",
        content: prompt,
      },
    ];

    const providerName = process.env.RECIPE_PROVIDER || "google";
    const modelName = process.env.RECIPE_MODEL || "gemini-1.5-pro-latest";
    const apiKey = process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error("API key for recipes is not configured.");
    }
    const provider = getProvider(providerName, apiKey);

    const response = await fetch(provider.apiUrl, {
      method: "POST",
      headers: provider.headers,
      body: JSON.stringify(provider.formatBody(modelName, messages)),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Failed to generate recipe:", {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });
      return NextResponse.json(
        { error: "Failed to generate recipe" },
        { status: 500 }
      );
    }

    const responseData = await response.json();
    console.log("Recipe generation response:", responseData);

    if (!responseData.candidates || !responseData.candidates[0].content) {
      console.error("Invalid recipe generation response:", responseData);
      return NextResponse.json(
        { error: "Failed to generate recipe" },
        { status: 500 }
      );
    }

    const recipeText = responseData.candidates[0].content.parts[0].text;
    console.log("Generated recipe text:", recipeText);

    const recipe = parseRecipeText(recipeText, data);

    if (generateImage) {
      const imageUrl = await generateFoodImage(
        recipe.title || "Recipe",
        recipe.description
      );
      if (imageUrl) {
        recipe.imageUrl = imageUrl;
        recipe.image = imageUrl;
        recipe.status.image = "success" as ImageStatus;
      } else {
        recipe.status.image = "failed" as ImageStatus;
        recipe.imageError = "Failed to generate image";
      }
    }

    return NextResponse.json(recipe);
  } catch (error) {
    console.error("Error processing recipe request:", error);
    return NextResponse.json(
      { error: "Failed to generate recipe" },
      { status: 500 }
    );
  }
}

// Handle GET requests for recipe status
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Recipe ID is required" },
        { status: 400 }
      );
    }

    // In a real application, you would fetch the recipe status from a database
    // For now, we'll just return a mock response
    return NextResponse.json({
      status: {
        recipe: "completed",
        image: "success" as ImageStatus,
      },
      image: null, // The image URL would be fetched from your storage
    });
  } catch (error) {
    console.error("Error checking recipe status:", error);
    return NextResponse.json(
      { error: "Failed to check recipe status" },
      { status: 500 }
    );
  }
}

// Handle PUT requests for image upload
export async function PUT(request: Request) {
  try {
    // Check rate limit
    const ip = (await headers()).get("x-forwarded-for") ?? "127.0.0.1";
    const rateLimitResult = await checkRateLimit(`upload_${ip}`);

    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          error: "Too many requests",
          details: {
            remainingTime: rateLimitResult.remainingTime,
            limit: rateLimitResult.limit,
            remaining: rateLimitResult.remaining,
          },
        },
        {
          status: 429,
          headers: {
            "X-RateLimit-Limit": rateLimitResult.limit.toString(),
            "X-RateLimit-Remaining": rateLimitResult.remaining.toString(),
            "X-RateLimit-Reset": rateLimitResult.remainingTime.toString(),
          },
        }
      );
    }

    const { base64Image } = await request.json();

    // Remove data URL prefix
    const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, "");
    const imageBuffer = Buffer.from(base64Data, "base64");

    // Get Backblaze credentials
    const keyId = process.env.BACKBLAZE_IMAGE_KEY_ID;
    const applicationKey = process.env.BACKBLAZE_IMAGE_APP_KEY;
    const bucketName = process.env.BACKBLAZE_BUCKET;
    const prefix = process.env.BACKBLAZE_PREFIX || "recipes/";

    // Log environment variables (without exposing sensitive data)
    console.log("Backblaze configuration:", {
      hasKeyId: !!keyId,
      hasAppKey: !!applicationKey,
      hasBucketName: !!bucketName,
      prefix,
      keyIdLength: keyId?.length,
      appKeyLength: applicationKey?.length,
      bucketName,
    });

    if (!keyId || !applicationKey || !bucketName) {
      const error = {
        error: "Missing Backblaze configuration",
        details: {
          hasKeyId: !!keyId,
          hasAppKey: !!applicationKey,
          hasBucketName: !!bucketName,
        },
      };
      console.error("Configuration error:", error);
      return NextResponse.json(error, { status: 500 });
    }

    // Authenticate with Backblaze
    const credentials = Buffer.from(`${keyId}:${applicationKey}`).toString(
      "base64"
    );
    console.log(
      "Attempting Backblaze authentication with credentials length:",
      credentials.length
    );

    const authResponse = await fetch(
      "https://api.backblazeb2.com/b2api/v2/b2_authorize_account",
      {
        method: "GET",
        headers: {
          Authorization: `Basic ${credentials}`,
        },
      }
    );

    let authResponseText = "";
    try {
      authResponseText = await authResponse.text();
      console.log("Raw auth response:", authResponseText);
    } catch (e) {
      console.error("Failed to read auth response text:", e);
    }

    if (!authResponse.ok) {
      const error = {
        error: "Failed to authenticate with Backblaze",
        details: {
          status: authResponse.status,
          statusText: authResponse.statusText,
          error: authResponseText || "No error text available",
          headers: Object.fromEntries(authResponse.headers.entries()),
        },
      };
      console.error("Authentication error:", error);
      return NextResponse.json(error, { status: 500 });
    }

    if (!authResponseText) {
      const error = {
        error: "Empty authentication response",
        details: {
          status: authResponse.status,
          statusText: authResponse.statusText,
        },
      };
      console.error("Authentication error:", error);
      return NextResponse.json(error, { status: 500 });
    }

    let auth;
    try {
      auth = JSON.parse(authResponseText);
    } catch (e) {
      const error = {
        error: "Failed to parse authentication response",
        details: {
          error: e instanceof Error ? e.message : "Unknown error",
          responseText: authResponseText,
        },
      };
      console.error("Parse error:", error);
      return NextResponse.json(error, { status: 500 });
    }

    console.log("Backblaze authentication successful, response:", {
      apiUrl: auth.apiUrl,
      authorizationToken: auth.authorizationToken ? "present" : "missing",
      downloadUrl: auth.downloadUrl,
      allowed: auth.allowed,
    });

    // Get the actual bucket ID from the auth response
    const bucketId = auth.allowed?.bucketId;
    if (!bucketId) {
      const error = {
        error: "Bucket ID not found in auth response",
        details: {
          bucketName,
          authResponse: auth,
        },
      };
      console.error("Bucket ID error:", error);
      return NextResponse.json(error, { status: 500 });
    }

    // Get upload URL
    console.log("Requesting upload URL for bucket:", { bucketName, bucketId });
    const uploadUrlResponse = await fetch(
      `${auth.apiUrl}/b2api/v2/b2_get_upload_url`,
      {
        method: "POST",
        headers: {
          Authorization: auth.authorizationToken || "",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketId }),
      }
    );

    let uploadUrlResponseText = "";
    try {
      uploadUrlResponseText = await uploadUrlResponse.text();
      console.log("Raw upload URL response:", uploadUrlResponseText);
    } catch (e) {
      console.error("Failed to read upload URL response text:", e);
    }

    if (!uploadUrlResponse.ok) {
      const error = {
        error: "Failed to get upload URL",
        details: {
          status: uploadUrlResponse.status,
          statusText: uploadUrlResponse.statusText,
          error: uploadUrlResponseText || "No error text available",
          bucketId,
          headers: Object.fromEntries(uploadUrlResponse.headers.entries()),
        },
      };
      console.error("Upload URL error:", error);
      return NextResponse.json(error, { status: 500 });
    }

    const uploadUrlData = JSON.parse(uploadUrlResponseText);
    const { uploadUrl, authorizationToken } = uploadUrlData;

    if (!uploadUrl || !authorizationToken) {
      const error = {
        error: "Invalid upload URL response",
        details: {
          hasUploadUrl: !!uploadUrl,
          hasAuthToken: !!authorizationToken,
          response: uploadUrlData,
        },
      };
      console.error("Upload URL validation error:", error);
      return NextResponse.json(error, { status: 500 });
    }

    console.log("Upload URL received:", uploadUrl);

    // Generate a unique filename
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(7);
    const filename = `${prefix}${timestamp}-${randomString}.png`;

    // Upload the file
    console.log("Uploading file:", filename);

    const uploadResponse = await fetch(uploadUrl, {
      method: "POST",
      headers: {
        Authorization: authorizationToken,
        "X-Bz-File-Name": filename,
        "Content-Type": "image/png",
        "Content-Length": imageBuffer.length.toString(),
        "X-Bz-Content-Sha1": "do_not_verify",
      },
      body: imageBuffer,
    });

    if (!uploadResponse.ok) {
      const error = {
        error: "Failed to upload file",
        details: {
          status: uploadResponse.status,
          statusText: uploadResponse.statusText,
          filename,
        },
      };
      console.error("Upload error:", error);
      return NextResponse.json(error, { status: 500 });
    }

    const uploadResult = await uploadResponse.json();
    console.log("Upload successful:", uploadResult);

    // Construct the public URL using the bucket name instead of bucket ID
    const publicUrl = `${auth.downloadUrl}/file/${bucketName}/${filename}`;
    console.log("File public URL:", publicUrl);

    return NextResponse.json({
      success: true,
      url: publicUrl,
      filename,
    });
  } catch (error) {
    console.error("Upload error:", error);
    return NextResponse.json(
      {
        error: "Failed to upload image",
        details:
          error instanceof Error
            ? {
                message: error.message,
                stack: error.stack,
              }
            : error,
      },
      { status: 500 }
    );
  }
}
