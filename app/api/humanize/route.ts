import { NextRequest, NextResponse } from "next/server";
import { processAIRequest } from "@/lib/api";
import { getProvider } from "@/lib/provider-factory";

// Basic quality check function
function hasBasicQualityIssues(text: string): string[] {
  const issues: string[] = [];

  // Check for common spelling errors that appeared in testing
  const commonErrors = [
    { error: /\bsomone\b/gi, correct: "someone" },
    { error: /\bshoud\b/gi, correct: "should" },
    { error: /wouldn't hurt neither\b/gi, correct: "wouldn't hurt either" },
    { error: /\bya know\b/gi, correct: "" },
    { error: /\bhey\b/gi, correct: "" },
    { error: /\bbasically\b/gi, correct: "" }
  ];

  commonErrors.forEach(({ error, correct }) => {
    if (error.test(text)) {
      issues.push(`Contains error that should be "${correct}"`);
    }
  });

  return issues;
}

export async function POST(request: NextRequest) {
  try {
    const { text, level } = await request.json();

    if (!text || !level) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    const result = await processAIRequest(async () => {
      const providerName = process.env.HUMANIZER_PROVIDER || "deepseek";
      const modelName = process.env.HUMANIZER_MODEL || "deepseek-chat";
      const apiKey = process.env.DEEPSEEK_API_KEY;
      if (!apiKey) {
        throw new Error("API key for humanizer is not configured.");
      }
      const provider = getProvider(providerName, apiKey);

      let levelPrompt = "";
      switch (level) {
        case "basic":
          levelPrompt = `Rewrite this text using clear, simple English with PERFECT grammar and spelling. Keep the same length (±10%). Use straightforward vocabulary and sentence structure. Ensure ZERO spelling errors, ZERO grammar mistakes, and ZERO typos. Write in clean, professional standard English without slang, contractions, or casual expressions like "ya know" or "hey".`;
          break;
        case "medium":
          levelPrompt = `Rewrite this text in polished standard English with FLAWLESS grammar and spelling. Keep similar length (±10%). Use varied sentence structure and professional vocabulary. Ensure ZERO spelling errors, ZERO grammar mistakes, and ZERO typos. Make it engaging and well-written while maintaining professional quality. Preserve the original meaning exactly.`;
          break;
        case "advanced":
          levelPrompt = `Rewrite this text using sophisticated, eloquent English with IMPECCABLE grammar and spelling. Maintain similar length (±10%). Use refined vocabulary, elegant sentence structures, and seamless flow. Ensure ZERO spelling errors, ZERO grammar mistakes, and ZERO typos. Create exceptionally polished, high-quality content that demonstrates masterful writing skills. Avoid any casual language or informal expressions.`;
          break;
      }

      const messages = [
        {
          role: "system",
          content:
            "You are an expert text humanization assistant. Your task is to rewrite text naturally while strictly maintaining similar length (±10% of input). CRITICAL REQUIREMENTS: 1) ZERO spelling errors 2) ZERO grammar mistakes 3) ZERO typos 4) NO casual expressions like 'ya know', 'hey', 'basically' 5) NO contradictory messages 6) Maintain professional quality. Provide ONLY the rewritten text without explanations, quotes, or comments.",
        },
        {
          role: "user",
          content: `${levelPrompt}\n\nOriginal text: "${text}"\n\nRewrite this text following the requirements above. Output only the rewritten text.`,
        },
      ];

      const response = await fetch(provider.apiUrl, {
        method: "POST",
        headers: provider.headers,
        body: JSON.stringify(provider.formatBody(modelName, messages)),
      });

      if (!response.ok) {
        throw new Error("Failed to humanize text");
      }

      const data = await response.json();
      const humanizedText = data.choices[0].message.content.trim();

      // Basic quality check
      const qualityIssues = hasBasicQualityIssues(humanizedText);
      if (qualityIssues.length > 0) {
        console.warn("Quality issues detected:", qualityIssues);
        // For now, we'll still return the text but log the issues
        // In the future, we could retry with a different prompt
      }

      return {
        humanizedText,
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Humanize error:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to process request",
      },
      { status: 500 }
    );
  }
}
