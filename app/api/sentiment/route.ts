import { NextRequest, NextResponse } from "next/server";
import { getProvider } from "@/lib/provider-factory";
import { trackEvent, trackError } from "@/lib/newrelic-utils";

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const { text } = await request.json();
    
    // Track API request
    trackEvent('ApiRequest', {
      endpoint: '/api/sentiment',
      method: 'POST',
      textLength: text?.length || 0,
      timestamp: startTime
    });

    if (!text) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    const providerName = process.env.SENTIMENT_PROVIDER || "openrouter";
    const modelName = process.env.SENTIMENT_MODEL || "google/gemini-flash-1.5";
    const apiKey = process.env.OPENROUTER_API_KEY; // Assuming a generic openrouter key for sentiment
    if (!apiKey) {
      throw new Error("API key for sentiment analysis is not configured.");
    }
    const provider = getProvider(providerName, apiKey);

    const messages = [
      {
        role: "system",
        content:
          'You are a sentiment analysis expert. Analyze the following text and return a JSON object with this exact structure: { "score": number, "explanation": string, "emotions": { "love": number, "joy": number, "sadness": number, "anger": number, "romantic": number, "fear": number } }. The score should be between -1 (very negative) and 1 (very positive). The explanation should be a brief, one-sentence summary. The emotions should be a score from 0 to 1 for each category.',
      },
      {
        role: "user",
        content: text,
      },
    ];

    const response = await fetch(provider.apiUrl, {
      method: "POST",
      headers: provider.headers,
      body: JSON.stringify(provider.formatBody(modelName, messages)),
    });

    if (!response.ok) {
      throw new Error("Failed to analyze sentiment");
    }

    const data = await response.json();
    const sentimentResult = JSON.parse(data.choices[0].message.content);
    
    // Track successful API response
    const duration = Date.now() - startTime;
    trackEvent('ApiResponse', {
      endpoint: '/api/sentiment',
      status: 'success',
      duration,
      sentimentScore: sentimentResult.score,
      timestamp: Date.now()
    });

    return NextResponse.json(sentimentResult);
  } catch (error) {
    console.error("Sentiment analysis error:", error);
    
    // Track API error
    const duration = Date.now() - startTime;
    trackError('ApiError', error instanceof Error ? error.message : 'Unknown error', '/api/sentiment');
    trackEvent('ApiResponse', {
      endpoint: '/api/sentiment',
      status: 'error',
      duration,
      timestamp: Date.now()
    });
    
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to process request",
      },
      { status: 500 }
    );
  }
}
