import './globals.css'
import { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { ThemeProvider } from '@/contexts/ThemeContext';
import Script from 'next/script';


// New Relic browser timing helper
function getNewRelicBrowserTimingHeader() {
  if (process.env.NODE_ENV === 'production' && process.env.NEW_RELIC_LICENSE_KEY) {
    try {
      const newrelic = require('newrelic');
      return newrelic.getBrowserTimingHeader();
    } catch (error) {
      console.warn('New Relic browser timing header failed:', error);
      return '';
    }
  }
  return '';
}

// Dynamically import components
const ClientLayout = dynamic(
  () => import('@/components/ClientLayout').then(mod => mod.ClientLayout),
  {
    ssr: true,
    loading: () => <div className="animate-pulse">Loading...</div>
  }
);

// Metadata configuration
export const metadata = {
  metadataBase: new URL('https://www.freetextconverter.com'),
  title: 'Text Converter - Free Online Text Tools',
  description: 'Free online text conversion tools. Convert, modify, and transform your text easily.',
};

// Static page configuration
export const staticParams = {
  dynamic: 'force-static',
  revalidate: 3600 // Revalidate every hour
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const newRelicHeader = getNewRelicBrowserTimingHeader();

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <link rel="manifest" href="/site.webmanifest" />
        {/* New Relic Browser Monitoring */}
        {newRelicHeader && (
          <Script
            id="new-relic-browser"
            strategy="beforeInteractive"
            dangerouslySetInnerHTML={{ __html: newRelicHeader }}
          />
        )}
        {/* Google AdSense */}
        <Script
          async
          src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4658876844822388"
          crossOrigin="anonymous"
          strategy="afterInteractive"
        />
        <Script id="microsoft-clarity" strategy="afterInteractive">
          {`
            (function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "p4iebhcxpb");
          `}
        </Script>
{/* Google Analytics */}
        <Script
          strategy="afterInteractive"
          src="https://www.googletagmanager.com/gtag/js?id=G-G4SZ4DNQNG"
        />
        <Script
          id="google-analytics"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-G4SZ4DNQNG');
            `,
          }}
        />
      </head>
      <body className="min-h-screen bg-background antialiased">
        <ThemeProvider>
          <Suspense fallback={<div className="animate-pulse">Loading app...</div>}>
            {children}
          </Suspense>
        </ThemeProvider>
      </body>
    </html>
  );
}
