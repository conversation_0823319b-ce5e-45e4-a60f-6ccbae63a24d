'use client';

import { Toaster } from "sonner"
import Script from 'next/script'
import { Analytics } from "@vercel/analytics/react"
import { UserPreferencesProvider } from '@/contexts/UserPreferencesContext'
import { AuthProvider } from '@/contexts/AuthContext'
import { SpeedInsights } from "@vercel/speed-insights/next"

export default function HomepageLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <Script id="schema-script" type="application/ld+json">
        {`
          {
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "Text Converter",
            "description": "A collection of text conversion and manipulation tools.",
            "applicationCategory": "Text Editor",
            "url": "https://freetextconverter.com",
            "author": {
              "@type": "Person",
              "name": "Text Converter"
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            }
          }
        `}
      </Script>
      <UserPreferencesProvider>
        <AuthProvider>
          <main className="w-full">
            {children}
          </main>
          <Toaster />
          <Analytics />
          <SpeedInsights />
        </AuthProvider>
      </UserPreferencesProvider>
    </>
  );
}
