import LandingPage from "@/components/LandingPage";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Free Text & Developer Tools - Online Utilities & AI-Powered Tools',
  description: 'Comprehensive collection of free online tools including text converters, AI-powered tools, developer utilities, PDF tools, and more. No registration required.',
  keywords: 'free online tools, text converter, AI tools, developer tools, PDF tools, binary calculator, password generator, paraphrase tool, text humanizer, code formatter',
  openGraph: {
    title: 'Free Text & Developer Tools - Online Utilities & AI-Powered Tools',
    description: 'Comprehensive collection of free online tools including text converters, AI-powered tools, developer utilities, PDF tools, and more.',
    url: 'https://www.freetextconverter.com',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Text & Developer Tools - Online Utilities & AI-Powered Tools',
    description: 'Comprehensive collection of free online tools including text converters, AI-powered tools, developer utilities, PDF tools, and more.',
  },
  alternates: {
    canonical: 'https://www.freetextconverter.com'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'Cxva3uuPrmXH-6T2MHevNg2DiLOc_rK0dWf4Hecv-z8'
  }
};

export const preferredRegion = 'auto'
export const revalidate = 3600

export default function Home() {
  return (
    <div className="w-full">
      <LandingPage />
    </div>
  );
}
