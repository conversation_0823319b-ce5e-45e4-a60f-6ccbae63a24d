'use client';

import React from 'react';
import { Footer } from "@/components/Footer";
import { Construction, Clock, Wrench, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function MaintenancePage() {
  return (
    <div className="min-h-screen bg-background overflow-hidden">
      <div className="relative">
        {/* Hero Background with Gradient */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-orange-200 via-amber-200 to-yellow-200 dark:from-orange-950 dark:via-amber-950 dark:to-yellow-950 h-[500px] opacity-60" />

        {/* Animated Background Pattern */}
        <div className="absolute inset-0 bg-grid-white/10 bg-[size:30px_30px] h-[500px] [mask-image:radial-gradient(ellipse_at_center,white,transparent_75%)]" />

        {/* Floating Icons */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute left-[10%] top-[20%] text-orange-500/20 dark:text-orange-300/20 animate-float">
            <Construction className="w-8 h-8" />
          </div>
          <div className="absolute right-[25%] top-[15%] text-amber-500/20 dark:text-amber-300/20 animate-float delay-1000">
            <Wrench className="w-6 h-6" />
          </div>
          <div className="absolute right-[15%] top-[35%] text-yellow-500/20 dark:text-yellow-300/20 animate-float delay-2000">
            <Clock className="w-7 h-7" />
          </div>
          <div className="absolute left-[20%] top-[40%] text-orange-500/20 dark:text-orange-300/20 animate-float delay-3000">
            <Construction className="w-5 h-5" />
          </div>
        </div>

        {/* Content */}
        <div className="w-[90%] max-w-[1000px] mx-auto px-4">
          <div className="pt-20 pb-20 text-center relative z-10">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/80 dark:bg-white/10 backdrop-blur-sm border border-orange-100 dark:border-orange-800 mb-6">
              <Construction className="w-4 h-4 text-orange-500 mr-2" />
              <span className="text-sm font-medium text-orange-600 dark:text-orange-400">
                Under Maintenance
              </span>
            </div>

            <div className="mb-8">
              <div className="relative inline-block">
                <div className="absolute inset-0 bg-orange-500/20 rounded-full blur-xl"></div>
                <Wrench className="w-20 h-20 text-orange-500 mx-auto mb-6 animate-bounce relative z-10" />
              </div>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 animate-fade-in">
              <span className="bg-gradient-to-r from-orange-600 via-amber-600 to-yellow-600 dark:from-orange-400 dark:via-amber-400 dark:to-yellow-400 bg-clip-text text-transparent">
                Image Generator
              </span>
              <br />
              <span className="text-muted-foreground text-2xl sm:text-3xl font-medium">
                Temporarily Unavailable
              </span>
            </h1>

                                    <p className="text-lg sm:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto animate-fade-in-up">
              We&apos;re working hard to improve your image generation experience.
              This feature is temporarily disabled while we make some exciting updates!
            </p>

            <div className="flex items-center justify-center gap-2 mb-10 text-muted-foreground animate-fade-in-up delay-200">
              <Clock className="w-5 h-5 animate-pulse" />
              <span className="font-medium">We&apos;ll be back soon!</span>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-fade-in-up delay-300">
              <Link
                href="/"
                className="inline-flex items-center px-8 py-3 rounded-xl bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Link>

              <Link
                href="/all-tools"
                className="inline-flex items-center px-8 py-3 rounded-xl border border-orange-200 dark:border-orange-800 hover:bg-orange-50 dark:hover:bg-orange-950/50 text-foreground font-semibold transition-all duration-200 transform hover:scale-105 backdrop-blur-sm"
              >
                Explore Other Tools
              </Link>
            </div>

                                     <div className="mt-8 p-8 rounded-2xl bg-white/50 dark:bg-white/5 backdrop-blur-sm border border-orange-100/30 dark:border-orange-800/30 max-w-lg mx-auto animate-fade-in-up delay-500">
              <h3 className="font-bold text-lg mb-4 text-orange-600 dark:text-orange-400 text-center">
                What&apos;s Coming?
              </h3>
              <ul className="text-sm text-muted-foreground space-y-3">
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  Enhanced AI models
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                  Faster generation times
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  New editing features
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  Improved user experience
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-20">
        <Footer />
      </div>

      <style jsx global>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(5deg); }
        }
        @keyframes fade-in {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        @keyframes fade-in-up {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
        .animate-fade-in {
          animation: fade-in 1s ease-out forwards;
        }
        .animate-fade-in-up {
          animation: fade-in-up 1s ease-out forwards;
        }
        .delay-200 {
          animation-delay: 200ms;
        }
        .delay-300 {
          animation-delay: 300ms;
        }
        .delay-500 {
          animation-delay: 500ms;
        }
        .delay-1000 {
          animation-delay: 1000ms;
        }
        .delay-2000 {
          animation-delay: 2000ms;
        }
        .delay-3000 {
          animation-delay: 3000ms;
        }
      `}</style>
    </div>
  );
}
