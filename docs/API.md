# API Documentation

## Video Processing APIs

### Video Info Endpoint
```typescript
// GET /api/video/info
// Purpose: Fetch video metadata

// Request
{
  url: string;  // Video URL from supported platforms
  mode?: 'info' | 'download';  // Optional mode parameter
}

// Response
{
  title: string;
  duration: string;
  formats: VideoFormat[];
  thumbnail: string;
}
```

### Video Download Endpoint
```typescript
// POST /api/video/download
// Purpose: Download video in specified format

// Request
{
  url: string;
  format: string;
  quality: string;
}

// Response
- File stream for direct download
- Error object if failed
```

## Text Processing APIs

### Text Humanizer
```typescript
// POST /api/humanizer
// Uses OpenRouter API

// Request
{
  text: string;
  style?: 'casual' | 'formal';
}

// Response
{
  humanized: string;
  original: string;
}
```

### Paraphrase Tool
```typescript
// POST /api/paraphrase
// Uses OpenRouter API

// Request
{
  text: string;
  strength: 'low' | 'medium' | 'high';
}

// Response
{
  paraphrased: string;
  original: string;
}
```

## Storage APIs

### Backblaze Integration
```typescript
// GET /api/get-signed-url
// Generate signed URLs for Backblaze

// Request
{
  filename: string;
  duration?: number;  // URL validity duration
}

// Response
{
  url: string;
  expiresAt: number;
}
```

## Authentication APIs

### Firebase Auth
```typescript
// POST /api/auth/token
// Verify and refresh Firebase tokens

// Request
{
  token: string;
}

// Response
{
  valid: boolean;
  newToken?: string;
}
```

## Error Handling

### Common Error Responses
```typescript
// Standard error response format
{
  error: {
    code: string;
    message: string;
    details?: any;
  }
}

// Common error codes
- INVALID_URL
- PLATFORM_NOT_SUPPORTED
- RATE_LIMIT_EXCEEDED
- AUTH_REQUIRED
```

## Rate Limiting

### Limits by Endpoint
```typescript
// Video endpoints
- 10 requests per minute per IP
- 100 requests per day per user

// Text processing
- 60 requests per minute
- 1000 requests per day

// Storage operations
- 100 uploads per hour
- 1000 downloads per hour
```

## Security Measures

### API Protection
1. CORS configuration
2. Rate limiting
3. Input validation
4. Token verification

### File Upload Security
1. Size limits
2. Type verification
3. Virus scanning
4. Metadata stripping

## Testing APIs

### Endpoint Testing
```bash
# Video info
curl -X GET "https://freetextconverter.com/api/video/info?url=VIDEO_URL"

# Text humanizer
curl -X POST "https://freetextconverter.com/api/humanizer" \
  -H "Content-Type: application/json" \
  -d '{"text":"Your text here"}'
```

## Monitoring & Logging

### Key Metrics
1. Response times
2. Error rates
3. Usage patterns
4. Resource consumption

### Log Levels
```typescript
// Log categories
- ERROR: Critical failures
- WARN: Potential issues
- INFO: Standard operations
- DEBUG: Detailed info
```

## API Updates Process

### Version Control
1. Document changes
2. Update tests
3. Deploy gradually
4. Monitor impacts

### Migration Guide
1. Announce changes
2. Provide timeline
3. Update documentation
4. Support transition
