# Components Documentation

## UI Components

### ClientLayout (`components/ClientLayout.tsx`)
- **Purpose**: Main layout wrapper for client-side components
- **Features**:
  - Navigation menu
  - Theme switching
  - Mobile responsiveness
- **Usage**:
```tsx
<ClientLayout>
  <YourComponent />
</ClientLayout>
```

### LikeButton (`components/LikeButton.tsx`)
- **Purpose**: Handle user likes/reactions
- **Dependencies**: Firebase Firestore
- **State Management**:
  - Tracks like count
  - Updates in real-time
  - Handles user authentication

### SignIn (`components/SignIn.tsx`)
- **Purpose**: User authentication
- **Methods**:
  - Email/Password login
  - Google OAuth
  - Token management
- **Important**: Sets Firebase token in cookies

## Tool-Specific Components

### Video Downloader
- **Location**: `app/video-downloader/`
- **Key Components**:
  - URL input
  - Format selector
  - Progress indicator
- **States**:
  - Loading
  - Error handling
  - Download progress

### Text Tools
- **Common Features**:
  - Input/Output areas
  - Copy buttons
  - Character count
  - History tracking

### QR Code Generator
- **Features**:
  - Custom styling
  - Size options
  - Download formats
- **Analytics**: Tracks usage patterns

## PDF Tools

### PDF Protect Tool
- **Features**:
  - User and owner password options
  - Permission settings
  - Client-side encryption
- **Implementation**:
  - Client component: `components/pdf-tools/client/PDFProtectToolClient.tsx`
  - Main component: `components/pdf-tools/PDFProtectTool.tsx`
  - Page component: `app/pdf-tools/protect/page.tsx`

### PDF Unlock Tool
- **Features**:
  - Password decryption
  - Client-side processing
  - Secure data handling
- **Implementation**:
  - Client component: `components/pdf-tools/client/PDFUnlockToolClient.tsx`
  - Main component: `components/pdf-tools/PDFUnlockTool.tsx`
  - Page component: `app/pdf-tools/unlock/page.tsx`

### PDF Preview Component
- **Features**:
  - Renders PDF previews
  - Page navigation
  - Rotation support
- **Usage**:
```tsx
<PDFPreview file={pdfFile} pageNumber={1} />
```

## Shared Components

### Error Handling
```typescript
// Error boundary implementation
// Located in components/ErrorBoundary.tsx
class ErrorBoundary extends React.Component {
  // Catches JavaScript errors
  // Displays fallback UI
  // Reports to error tracking
}
```

### Loading States
```typescript
// Loading indicator patterns
// Used across components
const LoadingSpinner = () => (
  <div className="animate-pulse">
    Loading...
  </div>
);
```

## Component Best Practices

### State Management
1. Use React hooks for simple state
2. Firebase for persistent data
3. Context for theme/auth state

### Performance
1. Lazy load heavy components
2. Implement proper memoization
3. Use Next.js Image component

### Error Handling
1. Implement error boundaries
2. Add proper loading states
3. Show user-friendly errors

## Testing Components

### Unit Tests
- Test individual component logic
- Verify state changes
- Check error handling

### Integration Tests
- Test component interactions
- Verify data flow
- Check API integrations

## Styling Guidelines

### CSS Modules
- Use Tailwind CSS
- Follow BEM naming
- Keep styles modular

### Theme System
- Dark/Light modes
- Custom color schemes
- Responsive design

## Component Updates Checklist

### Before Updates
1. Check dependencies
2. Review current usage
3. Plan breaking changes

### After Updates
1. Test all scenarios
2. Update documentation
3. Check performance impact
