# Text Converter Architecture Documentation

## Core Components

### 1. Layout System (`app/layout.tsx`)
- **Purpose**: Root layout component that wraps all pages
- **Key Features**:
  - Google AdSense integration
  - Microsoft Clarity analytics
  - Theme provider setup
  - Dynamic client layout loading
- **Important Notes**:
  - Uses Next.js 13 App Router
  - Handles metadata configuration
  - Manages hydration warnings

### 2. Video Processing (`app/api/video/`)
- **Files**:
  - `download/route.ts`: Handles video downloads
  - `info/route.ts`: Fetches video metadata
- **Supported Platforms**:
  - YouTube
  - TikTok
  - Instagram
  - Facebook
  - Twitter
- **Implementation Details**:
  - Uses platform-specific handlers
  - Implements rate limiting
  - Handles different video qualities
  - Manages temporary file storage

### 3. Authentication & Database
- **Firebase Implementation**:
  - Located in: `lib/firebase.ts`
  - Handles user authentication
  - Manages Firestore database connections
  - Tracks user preferences and history

### 4. File Storage (Backblaze)
- **Implementation**: `lib/backblaze.ts`
- **Usage**:
  - Recipe image storage
  - Temporary video storage
  - User uploads
- **Key Functions**:
  - `getBackblazeAuth()`: Authentication
  - `getUploadUrl()`: Get upload URLs
  - `getSignedBackblazeUrl()`: Generate signed URLs

## API Routes

### 1. Video Processing
```typescript
POST /api/video/info
- Purpose: Get video metadata
- Input: URL (YouTube, TikTok, etc.)
- Output: Title, duration, formats

POST /api/video/download
- Purpose: Download video
- Input: URL, format, quality
- Output: Video file or stream
```

### 2. Text Processing
```typescript
POST /api/humanizer
- Purpose: Make text more human-like
- Uses: OpenRouter API

POST /api/paraphrase
- Purpose: Rewrite text
- Uses: OpenRouter API
```

### 3. QR Code
```typescript
POST /api/qr/scan
- Purpose: Generate/scan QR codes
- Stores: Analytics in Firebase
```

## Environment Setup

### Required Environment Variables
```env
# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY - Firebase API key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN - Auth domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID - Project ID

# Backblaze
NEXT_PUBLIC_BACKBLAZE_KEY_ID - Storage key ID
NEXT_PUBLIC_BACKBLAZE_APP_KEY - App key
NEXT_PUBLIC_BACKBLAZE_BUCKET - Bucket name

# API Keys
NEXT_PUBLIC_OPENROUTER_API_KEY - For text processing
FLUX_API_KEY - For recipe generation
```

## Common Issues & Solutions

### 1. Video Download Issues
- Check if platform is supported in `detectPlatform()`
- Verify URL format matches platform patterns
- Ensure temp directory exists and is writable

### 2. Storage Problems
- Verify Backblaze credentials
- Check file size limits
- Ensure proper CORS configuration

### 3. API Rate Limits
- OpenRouter: 60 requests/minute
- Video platforms: Varies by service
- Implement retry logic for failures

## Deployment Notes

### Vercel Configuration
- Node.js version: 18.x
- Build command: `next build`
- Output directory: `.next`
- Environment variables must be set in Vercel dashboard

### Performance Optimization
- Images served through Backblaze CDN
- Next.js static optimization enabled
- API routes deployed as edge functions

## Maintenance Tasks

### Regular Checks
1. Monitor Firebase quotas
2. Check Backblaze storage usage
3. Verify API keys validity
4. Review error logs in Sentry

### Updates
1. Keep dependencies updated
2. Check for platform API changes
3. Update security certificates
4. Review and update rate limits
