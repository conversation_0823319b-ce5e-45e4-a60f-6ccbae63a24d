# Utilities and Helpers Documentation

## Rate Limiting (`lib/rate-limit.ts`)

```typescript
// Rate limiting configuration for different endpoints
const RATE_LIMITS = {
  'api/video': {
    requests: 10,  // per minute
    dailyLimit: 100  // per day
  },
  'api/humanize': {
    requests: 60,
    dailyLimit: 1000
  }
};
```

## API Utilities (`lib/api.ts`)

### Error Handling
- Custom error types for different scenarios
- Standardized error responses
- Logging and monitoring integration

### Request Processing
- Input validation
- Rate limiting checks
- Authentication verification

## Firebase Integration (`lib/firebase.ts`)

### Authentication
- Email/Password login
- Google OAuth
- Session management
- Token refresh

### Firestore
- Document structure
- Collection organization
- Query optimization
- Batch operations

## Storage Management (`lib/storage.ts`)

### Backblaze B2
- File upload/download
- URL signing
- Access control
- Bucket management

### Local Storage
- Temporary file handling
- Cache management
- Cleanup routines

## Security (`lib/security.ts`)

### Input Validation
- XSS prevention
- SQL injection protection
- File type verification
- Size limits

### Authentication
- Token validation
- Permission checks
- Role management

## Monitoring (`lib/monitoring.ts`)

### Sentry Integration
- Error tracking
- Performance monitoring
- User context
- Release tracking

### Analytics
- Usage tracking
- Error rates
- API performance
- User behavior

## Environment Variables

```env
# API Keys
OPENROUTER_API_KEY=your_key
FLUX_API_KEY=your_key

# Firebase
FIREBASE_API_KEY=your_key
FIREBASE_AUTH_DOMAIN=your_domain
FIREBASE_PROJECT_ID=your_id

# Storage
BACKBLAZE_KEY_ID=your_key_id
BACKBLAZE_APP_KEY=your_app_key
BACKBLAZE_BUCKET=your_bucket

# Email
EMAIL_USER=your_email
EMAIL_PASS=your_password

# Monitoring
SENTRY_DSN=your_dsn
```

## Common Functions

### Text Processing
```typescript
// Text sanitization
sanitizeInput(text: string): string

// Format validation
validateFormat(text: string, format: string): boolean

// Error handling
handleAPIError(error: Error): APIResponse
```

### File Operations
```typescript
// File upload
uploadFile(file: Buffer, metadata: FileMetadata): Promise<string>

// URL signing
generateSignedUrl(path: string, expiresIn: number): string

// File cleanup
cleanupTempFiles(): Promise<void>
```

### Authentication
```typescript
// Token validation
validateToken(token: string): Promise<boolean>

// Permission check
checkPermission(user: User, action: string): boolean

// Session management
refreshSession(token: string): Promise<string>
```

## Testing Utilities

### Unit Tests
- Test data generators
- Mock API responses
- Error scenarios
- Edge cases

### Integration Tests
- API endpoint testing
- Authentication flows
- File operations
- Rate limiting

## Deployment

### Vercel Configuration
```json
{
  "env": {
    "NODE_ENV": "production"
  },
  "build": {
    "env": {
      "NEXT_PUBLIC_API_URL": "@api_url"
    }
  }
}
```

### Environment Setup
1. Development
2. Staging
3. Production

## Maintenance

### Regular Tasks
- Log rotation
- Cache clearing
- Session cleanup
- Storage optimization

### Monitoring
- Error rates
- API performance
- Storage usage
- User activity

## PDF Utilities (`lib/pdf-utils.ts`)

### Document Manipulation
```typescript
// Merge multiple PDFs
mergePDFs(files: File[], onProgress?: (progress: number) => void): Promise<Blob>

// Split PDF into multiple documents
splitPDF(file: File, pageIndices: number[], onProgress?: (progress: number) => void): Promise<Blob[]>

// Password protect a PDF
protectPDF(
  file: File,
  password: string,
  ownerPassword?: string,
  permissions?: {
    printing?: 'lowResolution' | 'highResolution' | boolean;
    modifying?: boolean;
    copying?: boolean;
    annotating?: boolean;
    fillingForms?: boolean;
    contentAccessibility?: boolean;
    documentAssembly?: boolean;
  },
  onProgress?: (progress: number) => void
): Promise<Blob>

// Remove password from a PDF (requires the correct password)
unlockPDF(file: File, password: string, onProgress?: (progress: number) => void): Promise<Blob>

// Rotate PDF pages
rotatePDF(file: File, rotation: 90 | 180 | 270, pageIndices?: number[], onProgress?: (progress: number) => void): Promise<Blob>
```

### Format Conversion
```typescript
// Convert PDF to JPEG images
pdfToJpg(file: File, pageIndices?: number[], scale?: number, quality?: number, onProgress?: (progress: number) => void): Promise<Blob[]>

// Convert JPG images to PDF
jpgToPdf(files: File[], onProgress?: (progress: number) => void): Promise<Blob>

// Convert PDF to Word
pdfToWord(file: File, onProgress?: (progress: number) => void): Promise<Blob>

// Convert Word to PDF
wordToPdf(file: File, onProgress?: (progress: number) => void): Promise<Blob>

// Convert PDF to Excel
pdfToExcel(file: File, options?: ConversionOptions, onProgress?: (progress: number) => void): Promise<Blob>
```

### Helper Functions
```typescript
// Format file size for display
formatFileSize(bytes: number): string

// Download a blob with a specific filename
downloadBlob(blob: Blob, filename: string): void

// Get the page count of a PDF
getPDFPageCount(file: File): Promise<number>
```

### Dependencies
- pdf-lib-plus-encrypt: PDF manipulation and encryption
- pdfjs-dist: PDF rendering and text extraction
- jspdf: PDF generation
