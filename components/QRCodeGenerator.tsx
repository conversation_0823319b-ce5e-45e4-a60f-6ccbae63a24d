'use client';

import { useState, useRef, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { QRCodeSVG } from 'qrcode.react';
import { db, auth } from '@/lib/firebase';
import { collection, addDoc, serverTimestamp, updateDoc, getFirestore, doc, setDoc, onSnapshot } from 'firebase/firestore';

import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SketchPicker } from 'react-color';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useRouter } from 'next/navigation';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import JsPDF from 'jspdf';
import { BarChart2, Calendar, Monitor, QrCode, Smartphone } from 'lucide-react';
import { fonts } from "@/app/fonts";
import html2canvas from 'html2canvas';
import { Info } from 'lucide-react';
import { ResponsiveContainer, XAxis, YAxis, Tooltip, BarChart, Bar } from 'recharts';

type QRCodeType = 'text' | 'url' | 'wifi' | 'email' | 'phone' | 'sms' | 'calendar' | 'location' | 'payment' | 'appdownload' | 'contact' | 'crypto' | 'socialmedia' | 'event' | 'coupon' | 'businesscard';

interface WifiData {
  ssid: string;
  password: string;
  encryption: 'WPA' | 'WEP' | 'nopass';
}

interface EmailData {
  email: string;
  subject?: string;
  body?: string;
}

interface PhoneData {
  number: string;
}

interface SMSData {
  number: string;
  message: string;
}

interface CalendarData {
  title: string;
  startDate: string;
  endDate: string;
  location?: string;
  description?: string;
}

interface LocationData {
  latitude: string;
  longitude: string;
  name?: string;
}

interface PaymentData {
  paymentType: 'paypal' | 'venmo' | 'upi';
  recipient: string;
  amount?: string;
  currency?: string;
  note?: string;
}

interface AppDownloadData {
  platform: 'ios' | 'android';
  appId: string;
}

interface ContactData {
  name: string;
  phone?: string;
  email?: string;
  organization?: string;
  title?: string;
  address?: string;
  website?: string;
}

interface CryptoData {
  address: string;
  amount?: string;
  currency: string;
}

interface SocialMediaData {
  platform: 'facebook' | 'twitter' | 'instagram' | 'linkedin' | 'youtube';
  url: string;
}

interface EventData {
  title: string;
  startDate: string;
  endDate: string;
  location?: string;
  description?: string;
}

interface CouponData {
  code: string;
  discount: string;
  expirationDate: string;
}

interface BusinessCardData {
  name: string;
  phone?: string;
  email?: string;
  organization?: string;
  title?: string;
  address?: string;
  website?: string;
}

import { User as FirebaseUser } from 'firebase/auth';

interface User {
  uid: string;
  email: string | null;
}

interface QRCodeData {
  type: string;
  content: string;
  url: string;
  title: string;
  name: string;
  description: string;
  createdAt?: any;
  trackingUrl?: string;
  imageUrl?: string;
  style?: {
    fgColor: string;
    bgColor: string;
    frameColor: string;
    includeFrame: boolean;
    size: number;
    labelFont: string;
  };
  backgroundColor?: string;
  foregroundColor?: string;
  frameColor?: string;
  isPrivate: boolean;
  size?: string;
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  labelFont?: string;
}

const qrTypes = [
  { value: 'text', icon: '📝', label: 'Text' },
  { value: 'url', icon: '🔗', label: 'URL' },
  { value: 'wifi', icon: '📶', label: 'Wi-Fi' },
  { value: 'email', icon: '✉️', label: 'Email' },
  { value: 'phone', icon: '📞', label: 'Phone' },
  { value: 'sms', icon: '💬', label: 'SMS' },
  { value: 'calendar', icon: '📅', label: 'Calendar' },
  { value: 'location', icon: '📍', label: 'Location' },
  { value: 'payment', icon: '💳', label: 'Payment' },
  { value: 'appdownload', icon: '📱', label: 'App Download' },
  { value: 'contact', icon: '👤', label: 'Contact' },
  { value: 'crypto', icon: '₿', label: 'Crypto' },
  { value: 'socialmedia', icon: '🌐', label: 'Social Media' },
  { value: 'event', icon: '🎉', label: 'Event' },
  { value: 'coupon', icon: '🎁', label: 'Coupon' },
  { value: 'businesscard', icon: '📄', label: 'Business Card' }
] as const;

// Font categories for better organization
const fontOptions = [
  // Normal Fonts
  { value: 'roboto', label: 'Roboto', font: fonts.roboto, category: 'normal' },
  { value: 'openSans', label: 'Open Sans', font: fonts.openSans, category: 'normal' },
  { value: 'lato', label: 'Lato', font: fonts.lato, category: 'normal' },
  { value: 'montserrat', label: 'Montserrat', font: fonts.montserrat, category: 'normal' },
  { value: 'oswald', label: 'Oswald', font: fonts.oswald, category: 'normal' },
  { value: 'sourceSans', label: 'Source Sans', font: fonts.sourceSans, category: 'normal' },
  { value: 'poppins', label: 'Poppins', font: fonts.poppins, category: 'normal' },

  // Decorative Fonts
  { value: 'dancingScript', label: 'Dancing Script', font: fonts.dancingScript, category: 'decorative' },
  { value: 'pacifico', label: 'Pacifico', font: fonts.pacifico, category: 'decorative' },
  { value: 'lobster', label: 'Lobster', font: fonts.lobster, category: 'decorative' }
];

const predefinedSizes = [
  { value: 128, label: 'Small (128px)' },
  { value: 256, label: 'Medium (256px)' },
  { value: 384, label: 'Large (384px)' },
  { value: 512, label: 'Extra Large (512px)' },
  { value: 'custom', label: 'Custom Size' }
];

const colorPresets = [
  { label: 'Classic', fg: '#000000', bg: '#FFFFFF', frame: '#000000' },
  { label: 'Ocean', fg: '#0077be', bg: '#ffffff', frame: '#005c91' },
  { label: 'Forest', fg: '#2d5a27', bg: '#ffffff', frame: '#1e3d1a' },
  { label: 'Sunset', fg: '#ff7e5f', bg: '#ffffff', frame: '#eb5e41' },
  { label: 'Royal', fg: '#4b0082', bg: '#ffffff', frame: '#2f0052' },
  { label: 'Modern', fg: '#2c3e50', bg: '#ffffff', frame: '#1a252f' }
];

const errorLevelOptions = [
  {
    value: 'L',
    label: 'Low (7%)',
    description: 'Minimal error correction. Creates a simpler QR pattern with fewer backup modules.',
    modules: 'Basic Pattern'
  },
  {
    value: 'M',
    label: 'Medium (15%)',
    description: 'Standard error correction. Adds more backup modules for better reliability.',
    modules: 'Standard Pattern'
  },
  {
    value: 'Q',
    label: 'Quartile (25%)',
    description: 'Enhanced error correction. Creates a denser pattern with more backup data.',
    modules: 'Dense Pattern'
  },
  {
    value: 'H',
    label: 'High (30%)',
    description: 'Maximum error correction. Generates the most complex pattern for highest reliability.',
    modules: 'Complex Pattern'
  },
];

const qrTips = [
  { title: 'Error Correction', content: 'Higher error correction levels make your QR code more resistant to damage but increase its size.' },
  { title: 'Size Selection', content: 'Choose larger sizes for better scanning at a distance or when including more data.' },
  { title: 'Color Contrast', content: 'Maintain high contrast between QR code and background colors for optimal scanning.' },
  { title: 'Frame Usage', content: 'Adding a frame can help scanners identify the QR code more easily in busy backgrounds.' },
];

export function QRCodeGenerator() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const [qrType, setQrType] = useState<QRCodeType>('text');
  const [size, setSize] = useState<number>(256);
  const [errorLevel, setErrorLevel] = useState<string>('M');
  const [fgColor, setFgColor] = useState('#000000');
  const [bgColor, setBgColor] = useState('#FFFFFF');
  const [frameColor, setFrameColor] = useState('#000000');
  const [includeFrame, setIncludeFrame] = useState(false);
  const [textData, setTextData] = useState('');
  const [urlData, setUrlData] = useState('');
  const [wifiData, setWifiData] = useState<WifiData>({
    ssid: '',
    password: '',
    encryption: 'WPA'
  });
  const [emailData, setEmailData] = useState<EmailData>({ email: '', subject: '', body: '' });
  const [phoneData, setPhoneData] = useState<PhoneData>({ number: '' });
  const [smsData, setSMSData] = useState<SMSData>({ number: '', message: '' });
  const [calendarData, setCalendarData] = useState<CalendarData>({
    title: 'My Event',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Next day
    location: '',
    description: ''
  });
  const [locationData, setLocationData] = useState<LocationData>({
    latitude: '',
    longitude: '',
    name: ''
  });
  const [paymentData, setPaymentData] = useState<PaymentData>({
    paymentType: 'paypal',
    recipient: '',
    amount: '',
    currency: 'USD',
    note: ''
  });
  const [appDownloadData, setAppDownloadData] = useState<AppDownloadData>({
    platform: 'ios',
    appId: ''
  });
  const [contactData, setContactData] = useState<ContactData>({
    name: '',
    phone: '',
    email: '',
    organization: '',
    title: '',
    address: '',
    website: ''
  });
  const [cryptoData, setCryptoData] = useState<CryptoData>({
    address: '',
    amount: '',
    currency: ''
  });
  const [socialMediaData, setSocialMediaData] = useState<SocialMediaData>({
    platform: 'facebook',
    url: ''
  });
  const [eventData, setEventData] = useState<EventData>({
    title: 'My Event',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Next day
    location: '',
    description: ''
  });
  const [couponData, setCouponData] = useState<CouponData>({
    code: '',
    discount: '',
    expirationDate: ''
  });
  const [businessCardData, setBusinessCardData] = useState<BusinessCardData>({
    name: '',
    phone: '',
    email: '',
    organization: '',
    title: '',
    address: '',
    website: ''
  });
  const [showConsentDialog, setShowConsentDialog] = useState(false);
  const [qrContent, setQrContent] = useState<string>('');
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const [showFgColorPicker, setShowFgColorPicker] = useState(false);
  const [showBgColorPicker, setShowBgColorPicker] = useState(false);
  const [showFrameColorPicker, setShowFrameColorPicker] = useState(false);
  const [qrName, setQrName] = useState('');
  const [selectedFont, setSelectedFont] = useState(fontOptions[0].value);
  const [showCustomSize, setShowCustomSize] = useState(false);
  const [customSize, setCustomSize] = useState(256);

  const [analyticsData, setAnalyticsData] = useState<{
    scanCount: number;
    lastScanned: any;
    qrCodeId: string | null;
  }>({
    scanCount: 0,
    lastScanned: null,
    qrCodeId: null
  });

  const [detailedAnalytics, setDetailedAnalytics] = useState({
    deviceTypes: { mobile: 0, desktop: 0 },
    scanTimes: [] as { hour: number; count: number }[],
    lastScans: [] as any[]
  });

  const textRef = useRef<HTMLDivElement>(null);
  const [textWidth, setTextWidth] = useState(0);
  const [textHeight, setTextHeight] = useState(0);

  useEffect(() => {
    if (textRef.current && qrName) {
      setTextWidth(textRef.current.offsetWidth);
      setTextHeight(textRef.current.offsetHeight);
    }
  }, [qrName, selectedFont]);

  useEffect(() => {
    if (!auth.currentUser) return;

    // Get the QR code ID from the URL
    const qrCodeId = window.location.pathname.split('/').pop();
    if (!qrCodeId) return;

    // Set up real-time listener for the user's qr_codes subcollection
    const userQrCodeRef = doc(db, 'users', auth.currentUser.uid, 'qr_codes', qrCodeId);
    const unsubscribeUser = onSnapshot(userQrCodeRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data();
        setAnalyticsData({
          scanCount: data.scanCount || 0,
          lastScanned: data.lastScanned,
          qrCodeId
        });
      }
    });

    // Set up listener for scans subcollection
    const scansRef = collection(db, `users/${auth.currentUser.uid}/qr_codes/${qrCodeId}/scans`);
    const unsubscribeScans = onSnapshot(scansRef, (snapshot) => {
      const deviceTypes = { mobile: 0, desktop: 0 };
      const scanTimes: { hour: number; count: number }[] = Array(24).fill(0).map((_, i) => ({ hour: i, count: 0 }));
      const lastScans: any[] = [];

      snapshot.docs.forEach(doc => {
        if (doc.id === 'init') return; // Skip init document

        const scanData = doc.data();

        // Count device types
        if (scanData.deviceType === 'mobile') deviceTypes.mobile++;
        else if (scanData.deviceType === 'desktop') deviceTypes.desktop++;

        // Track scan times
        if (scanData.timestamp) {
          const date = scanData.timestamp.toDate();
          const hour = date.getHours();
          scanTimes[hour].count++;
        }

        // Keep last 5 scans
        if (scanData.timestamp) {
          lastScans.push({
            time: scanData.timestamp,
            device: scanData.deviceType,
            userAgent: scanData.userAgent
          });
        }
      });

      // Sort last scans by time
      lastScans.sort((a, b) => b.time.seconds - a.time.seconds);

      setDetailedAnalytics({
        deviceTypes,
        scanTimes,
        lastScans: lastScans.slice(0, 5)
      });
    });

    return () => {
      unsubscribeUser();
      unsubscribeScans();
    };
  }, []);

  const getFrameDimensions = () => {
    const baseWidth = size + 64; // QR size + base padding
    const maxWidth = Math.max(baseWidth, Math.min(textWidth + 64, baseWidth * 1.5)); // Limit to 1.5x QR width
    const height = size + (qrName ? textHeight + 80 : 64); // QR size + text height + padding

    return { width: maxWidth, height };
  };

  const getQRContent = () => {
    switch (qrType) {
      case 'text':
        return textData;
      case 'url':
        return urlData.startsWith('http') ? urlData : `https://${urlData}`;
      case 'wifi':
        return `WIFI:T:${wifiData.encryption};S:${wifiData.ssid};P:${wifiData.password};;`;
      case 'email':
        return `mailto:${emailData.email}?subject=${encodeURIComponent(emailData.subject || '')}&body=${encodeURIComponent(emailData.body || '')}`;
      case 'phone':
        return `tel:${phoneData.number}`;
      case 'sms':
        return `sms:${smsData.number}:${encodeURIComponent(smsData.message)}`;
      case 'calendar':
        try {
          const formatDate = (dateStr: string) => {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
              throw new Error('Invalid date');
            }
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}${month}${day}T${hours}${minutes}${seconds}Z`;
          };

          return `BEGIN:VCALENDAR
VERSION:2.0
BEGIN:VEVENT
SUMMARY:${calendarData.title}
DTSTART;VALUE=DATE-TIME:${formatDate(calendarData.startDate)}
DTEND;VALUE=DATE-TIME:${formatDate(calendarData.endDate)}${calendarData.location ? `\nLOCATION:${calendarData.location}` : ''}${calendarData.description ? `\nDESCRIPTION:${calendarData.description}` : ''}
END:VEVENT
END:VCALENDAR`;
        } catch (error) {
          return '';
        }
      case 'location':
        return `geo:${locationData.latitude},${locationData.longitude}${locationData.name ? ` (${locationData.name})` : ''}`;
      case 'payment':
        switch (paymentData.paymentType) {
          case 'paypal':
            return `https://paypal.me/${paymentData.recipient}${paymentData.amount ? `/${paymentData.amount}` : ''}`;
          case 'venmo':
            return `venmo://paycharge?txn=pay&recipients=${paymentData.recipient}&amount=${paymentData.amount || ''}&note=${encodeURIComponent(paymentData.note || '')}`;
          case 'upi':
            return `upi://pay?pa=${paymentData.recipient}&am=${paymentData.amount || ''}&cu=${paymentData.currency || 'INR'}&tn=${encodeURIComponent(paymentData.note || '')}`;
          default:
            return '';
        }
      case 'appdownload':
        return appDownloadData.platform === 'ios'
          ? `https://apps.apple.com/app/id${appDownloadData.appId}`
          : `https://play.google.com/store/apps/details?id=${appDownloadData.appId}`;
      case 'contact':
        return `BEGIN:VCARD\nVERSION:3.0\nFN:${contactData.name}${contactData.organization ? `\nORG:${contactData.organization}` : ''}${contactData.title ? `\nTITLE:${contactData.title}` : ''}${contactData.phone ? `\nTEL:${contactData.phone}` : ''}${contactData.email ? `\nEMAIL:${contactData.email}` : ''}${contactData.address ? `\nADR:;;${contactData.address}` : ''}${contactData.website ? `\nURL:${contactData.website}` : ''}\nEND:VCARD`;
      case 'crypto':
        return `${cryptoData.currency}:${cryptoData.address}${
          cryptoData.amount ? `?amount=${cryptoData.amount}` : ''
        }`;
      case 'socialmedia':
        const platformUrls = {
          facebook: 'https://facebook.com/',
          twitter: 'https://twitter.com/',
          instagram: 'https://instagram.com/',
          linkedin: 'https://linkedin.com/in/',
          youtube: 'https://youtube.com/'
        };
        return socialMediaData.url.startsWith('http')
          ? socialMediaData.url
          : `${platformUrls[socialMediaData.platform]}${socialMediaData.url}`;
      case 'event':
        try {
          const formatDate = (dateStr: string) => {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
              throw new Error('Invalid date');
            }
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}${month}${day}T${hours}${minutes}${seconds}Z`;
          };

          return `BEGIN:VCALENDAR
VERSION:2.0
BEGIN:VEVENT
SUMMARY:${eventData.title}
DTSTART;VALUE=DATE-TIME:${formatDate(eventData.startDate)}
DTEND;VALUE=DATE-TIME:${formatDate(eventData.endDate)}${eventData.location ? `\nLOCATION:${eventData.location}` : ''}${eventData.description ? `\nDESCRIPTION:${eventData.description}` : ''}
END:VEVENT
END:VCALENDAR`;
        } catch (error) {
          return '';
        }
      case 'coupon':
        return `COUPON:${couponData.code}\nDISCOUNT:${couponData.discount}\nEXPIRES:${couponData.expirationDate}`;
      case 'businesscard':
        return `BEGIN:VCARD\nVERSION:3.0\nFN:${businessCardData.name}${
          businessCardData.organization ? `\nORG:${businessCardData.organization}` : ''
        }${businessCardData.title ? `\nTITLE:${businessCardData.title}` : ''}${
          businessCardData.phone ? `\nTEL:${businessCardData.phone}` : ''
        }${businessCardData.email ? `\nEMAIL:${businessCardData.email}` : ''}${
          businessCardData.address ? `\nADR:;;${businessCardData.address}` : ''
        }${businessCardData.website ? `\nURL:${businessCardData.website}` : ''}\nEND:VCARD`;
      default:
        return '';
    }
  };

  const handleSave = () => {
    const content = getQRContent();
    if (!content) {
      toast.error('Please fill in all required fields');
      return;
    }

    setQrContent(content);
    if (!currentUser) {
      router.push('/auth/signin');
    } else {
      if (!localStorage.getItem('cookieConsent')) {
        setShowCookieConsent(true);
      } else {
        setShowConsentDialog(true);
      }
    }
  };

  const handleSaveToAnalytics = async () => {
    setLoading(true);
    console.log('🚀 [QR Generate] Starting QR code generation...');

    try {
      if (!currentUser?.uid) {
        console.error('❌ [QR Generate] No user ID found');
        throw new Error('User ID is required');
      }

      const content = getQRContent();
      console.log('📝 [QR Generate] Content:', { type: qrType, content });

      // Save to user's QR codes collection
      const qrCodesRef = collection(db, 'users', currentUser.uid, 'qr_codes');
      const docRef = await addDoc(qrCodesRef, {
        type: qrType,
        content,
        name: qrName || 'Untitled QR Code',
        createdAt: serverTimestamp(),
        userId: currentUser.uid,
        style: {
          fgColor,
          bgColor,
          frameColor,
          includeFrame,
          size,
          labelFont: selectedFont
        },
        errorLevel,
        scanCount: 0
      });

      console.log('✅ [QR Generate] Saved QR code:', {
        path: `users/${currentUser.uid}/qrcodes/${docRef.id}`,
        docId: docRef.id
      });

      // Generate tracking URL using current origin
      const baseUrl = window.location.origin;
      const trackingUrl = `${baseUrl}/qr-code/${docRef.id}`;

      console.log('🔗 [QR Generate] URL Details:', {
        baseUrl,
        trackingUrl,
        docId: docRef.id,
        fullUrl: new URL(trackingUrl).toString(),
        windowLocation: {
          origin: window.location.origin,
          href: window.location.href,
          host: window.location.host
        }
      });

      // Update the document with the tracking URL
      await updateDoc(docRef, {
        trackingUrl,
        content: content // Store the original content
      });

      // Update the QR code to use the tracking URL
      setQrContent(trackingUrl);

      console.log('✅ [QR Generate] Updated tracking URL');

      toast.success('QR code saved successfully!');
      router.push('/qr-code/analytics');
    } catch (error) {
      console.error('❌ [QR Generate] Error:', error);
      toast.error('Failed to save QR code');
    } finally {
      setLoading(false);
      setShowConsentDialog(false);
    }
  };

  const handleViewAnalytics = () => {
    if (!auth.currentUser) {
      setShowSignInDialog(true);
    } else {
      router.push('/qr-code/analytics');
    }
  };

  const handleDownload = async (format: 'png' | 'svg' | 'pdf') => {
    const qrContainer = document.querySelector('.qr-preview') as HTMLElement;
    if (!qrContainer) return;

    if (format === 'png') {
      try {
        const canvas = await html2canvas(qrContainer, {
          scale: 4, // Higher scale for better quality
          useCORS: true,
          backgroundColor: bgColor,
          logging: false,
          width: qrContainer.offsetWidth,
          height: qrContainer.offsetHeight,
        });

        // Convert to high-quality PNG
        const pngUrl = canvas.toDataURL('image/png', 1.0);
        const link = document.createElement('a');
        link.download = `${qrName || 'qr-code'}.png`;
        link.href = pngUrl;
        link.click();
      } catch (error) {
        console.error('Error generating PNG:', error);
        toast.error('Failed to generate PNG. Please try again.');
      }
    } else if (format === 'svg') {
      try {
        const svgElement = qrContainer.querySelector('svg');
        if (!svgElement) return;

        // Create a new SVG container
        const svgContainer = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        const containerWidth = qrContainer.offsetWidth;
        const containerHeight = qrContainer.offsetHeight;

        svgContainer.setAttribute('width', containerWidth.toString());
        svgContainer.setAttribute('height', containerHeight.toString());
        svgContainer.setAttribute('viewBox', `0 0 ${containerWidth} ${containerHeight}`);

        // Add background
        const background = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        background.setAttribute('width', '100%');
        background.setAttribute('height', '100%');
        background.setAttribute('fill', bgColor);
        svgContainer.appendChild(background);

        // Add frame if enabled
        if (includeFrame) {
          const frame = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
          const padding = 16;
          frame.setAttribute('x', padding.toString());
          frame.setAttribute('y', padding.toString());
          frame.setAttribute('width', (containerWidth - padding * 2).toString());
          frame.setAttribute('height', (containerHeight - padding * 2).toString());
          frame.setAttribute('stroke', frameColor);
          frame.setAttribute('stroke-width', '8');
          frame.setAttribute('fill', 'none');
          svgContainer.appendChild(frame);
        }

        // Add QR code
        const qrClone = svgElement.cloneNode(true) as SVGElement;
        const qrX = (containerWidth - size) / 2;
        const qrY = (containerHeight - size - (qrName ? 40 : 0)) / 2;
        qrClone.setAttribute('x', qrX.toString());
        qrClone.setAttribute('y', qrY.toString());
        svgContainer.appendChild(qrClone);

        // Add text if present
        if (qrName) {
          const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          text.setAttribute('x', (containerWidth / 2).toString());
          text.setAttribute('y', (qrY + size + 30).toString());
          text.setAttribute('text-anchor', 'middle');
          text.setAttribute('fill', frameColor);
          text.setAttribute('font-family', fontOptions.find(f => f.value === selectedFont)?.font.style.fontFamily || '');
          text.setAttribute('font-size', '20');
          text.textContent = qrName;
          svgContainer.appendChild(text);
        }

        const svgData = new XMLSerializer().serializeToString(svgContainer);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const svgUrl = URL.createObjectURL(svgBlob);

        const link = document.createElement('a');
        link.download = `${qrName || 'qr-code'}.svg`;
        link.href = svgUrl;
        link.click();

        URL.revokeObjectURL(svgUrl);
      } catch (error) {
        console.error('Error generating SVG:', error);
        toast.error('Failed to generate SVG. Please try again.');
      }
    }
  };

  const renderInputFields = () => {
    switch (qrType) {
      case 'wifi':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="ssid">Network Name (SSID)</Label>
              <Input
                id="ssid"
                placeholder="Enter network name"
                value={wifiData.ssid}
                onChange={(e) => setWifiData({ ...wifiData, ssid: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter network password"
                value={wifiData.password}
                onChange={(e) => setWifiData({ ...wifiData, password: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label>Encryption</Label>
              <Select value={wifiData.encryption} onValueChange={(value: 'WPA' | 'WEP' | 'nopass') => setWifiData({ ...wifiData, encryption: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select encryption" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="WPA">WPA/WPA2</SelectItem>
                  <SelectItem value="WEP">WEP</SelectItem>
                  <SelectItem value="nopass">No Password</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );
      case 'email':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter email address"
                value={emailData.email}
                onChange={(e) => setEmailData({ ...emailData, email: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="subject">Subject (Optional)</Label>
              <Input
                id="subject"
                placeholder="Enter email subject"
                value={emailData.subject}
                onChange={(e) => setEmailData({ ...emailData, subject: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="body">Body (Optional)</Label>
              <Input
                id="body"
                placeholder="Enter email body"
                value={emailData.body}
                onChange={(e) => setEmailData({ ...emailData, body: e.target.value })}
              />
            </div>
          </div>
        );
      case 'phone':
        return (
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              placeholder="Enter phone number"
              value={phoneData.number}
              onChange={(e) => setPhoneData({ number: e.target.value })}
            />
          </div>
        );
      case 'sms':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="smsNumber">Phone Number</Label>
              <Input
                id="smsNumber"
                placeholder="Enter phone number"
                value={smsData.number}
                onChange={(e) => setSMSData({ ...smsData, number: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="message">Message</Label>
              <Input
                id="message"
                placeholder="Enter message"
                value={smsData.message}
                onChange={(e) => setSMSData({ ...smsData, message: e.target.value })}
              />
            </div>
          </div>
        );
      case 'calendar':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Event Title</Label>
              <Input
                id="title"
                placeholder="Enter event title"
                value={calendarData.title}
                onChange={(e) => setCalendarData({ ...calendarData, title: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="datetime-local"
                placeholder="Enter start date"
                value={calendarData.startDate}
                onChange={(e) => setCalendarData({ ...calendarData, startDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="datetime-local"
                placeholder="Enter end date"
                value={calendarData.endDate}
                onChange={(e) => setCalendarData({ ...calendarData, endDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">Location (Optional)</Label>
              <Input
                id="location"
                placeholder="Enter location"
                value={calendarData.location}
                onChange={(e) => setCalendarData({ ...calendarData, location: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Input
                id="description"
                placeholder="Enter description"
                value={calendarData.description}
                onChange={(e) => setCalendarData({ ...calendarData, description: e.target.value })}
              />
            </div>
          </div>
        );
      case 'location':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="latitude">Latitude</Label>
              <Input
                id="latitude"
                placeholder="Enter latitude"
                value={locationData.latitude}
                onChange={(e) => setLocationData({ ...locationData, latitude: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="longitude">Longitude</Label>
              <Input
                id="longitude"
                placeholder="Enter longitude"
                value={locationData.longitude}
                onChange={(e) => setLocationData({ ...locationData, longitude: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">Name (Optional)</Label>
              <Input
                id="name"
                placeholder="Enter name"
                value={locationData.name}
                onChange={(e) => setLocationData({ ...locationData, name: e.target.value })}
              />
            </div>
          </div>
        );
      case 'payment':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Payment Type</Label>
              <Select value={paymentData.paymentType} onValueChange={(value: 'paypal' | 'venmo' | 'upi') => setPaymentData({ ...paymentData, paymentType: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select payment type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="paypal">PayPal</SelectItem>
                  <SelectItem value="venmo">Venmo</SelectItem>
                  <SelectItem value="upi">UPI</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="recipient">Recipient</Label>
              <Input
                id="recipient"
                placeholder="Enter recipient"
                value={paymentData.recipient}
                onChange={(e) => setPaymentData({ ...paymentData, recipient: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="amount">Amount (Optional)</Label>
              <Input
                id="amount"
                placeholder="Enter amount"
                value={paymentData.amount}
                onChange={(e) => setPaymentData({ ...paymentData, amount: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="currency">Currency (Optional)</Label>
              <Input
                id="currency"
                placeholder="Enter currency"
                value={paymentData.currency}
                onChange={(e) => setPaymentData({ ...paymentData, currency: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="note">Note (Optional)</Label>
              <Input
                id="note"
                placeholder="Enter note"
                value={paymentData.note}
                onChange={(e) => setPaymentData({ ...paymentData, note: e.target.value })}
              />
            </div>
          </div>
        );
      case 'appdownload':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Platform</Label>
              <Select value={appDownloadData.platform} onValueChange={(value: 'ios' | 'android') => setAppDownloadData({ ...appDownloadData, platform: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ios">iOS</SelectItem>
                  <SelectItem value="android">Android</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="appId">App ID</Label>
              <Input
                id="appId"
                placeholder="Enter app ID"
                value={appDownloadData.appId}
                onChange={(e) => setAppDownloadData({ ...appDownloadData, appId: e.target.value })}
              />
            </div>
          </div>
        );
      case 'contact':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                placeholder="Enter name"
                value={contactData.name}
                onChange={(e) => setContactData({ ...contactData, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone (Optional)</Label>
              <Input
                id="phone"
                placeholder="Enter phone"
                value={contactData.phone}
                onChange={(e) => setContactData({ ...contactData, phone: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email (Optional)</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter email"
                value={contactData.email}
                onChange={(e) => setContactData({ ...contactData, email: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="organization">Organization (Optional)</Label>
              <Input
                id="organization"
                placeholder="Enter organization"
                value={contactData.organization}
                onChange={(e) => setContactData({ ...contactData, organization: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="title">Title (Optional)</Label>
              <Input
                id="title"
                placeholder="Enter title"
                value={contactData.title}
                onChange={(e) => setContactData({ ...contactData, title: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">Address (Optional)</Label>
              <Input
                id="address"
                placeholder="Enter address"
                value={contactData.address}
                onChange={(e) => setContactData({ ...contactData, address: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="website">Website (Optional)</Label>
              <Input
                id="website"
                placeholder="Enter website"
                value={contactData.website}
                onChange={(e) => setContactData({ ...contactData, website: e.target.value })}
              />
            </div>
          </div>
        );
      case 'crypto':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Address</Label>
              <Input
                id="address"
                placeholder="Enter address"
                value={cryptoData.address}
                onChange={(e) => setCryptoData({ ...cryptoData, address: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label>Amount (Optional)</Label>
              <Input
                id="amount"
                placeholder="Enter amount"
                value={cryptoData.amount}
                onChange={(e) => setCryptoData({ ...cryptoData, amount: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label>Currency</Label>
              <Input
                id="currency"
                placeholder="Enter currency"
                value={cryptoData.currency}
                onChange={(e) => setCryptoData({ ...cryptoData, currency: e.target.value })}
              />
            </div>
          </div>
        );
      case 'socialmedia':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Platform</Label>
              <Select value={socialMediaData.platform} onValueChange={(value: 'facebook' | 'twitter' | 'instagram' | 'linkedin' | 'youtube') => setSocialMediaData({ ...socialMediaData, platform: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="facebook">Facebook</SelectItem>
                  <SelectItem value="twitter">Twitter</SelectItem>
                  <SelectItem value="instagram">Instagram</SelectItem>
                  <SelectItem value="linkedin">LinkedIn</SelectItem>
                  <SelectItem value="youtube">YouTube</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>URL</Label>
              <Input
                id="url"
                placeholder="Enter URL"
                value={socialMediaData.url}
                onChange={(e) => setSocialMediaData({ ...socialMediaData, url: e.target.value })}
              />
            </div>
          </div>
        );
      case 'event':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Event Title</Label>
              <Input
                id="title"
                placeholder="Enter event title"
                value={eventData.title}
                onChange={(e) => setEventData({ ...eventData, title: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="datetime-local"
                placeholder="Enter start date"
                value={eventData.startDate}
                onChange={(e) => setEventData({ ...eventData, startDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="datetime-local"
                placeholder="Enter end date"
                value={eventData.endDate}
                onChange={(e) => setEventData({ ...eventData, endDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">Location (Optional)</Label>
              <Input
                id="location"
                placeholder="Enter location"
                value={eventData.location}
                onChange={(e) => setEventData({ ...eventData, location: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Input
                id="description"
                placeholder="Enter description"
                value={eventData.description}
                onChange={(e) => setEventData({ ...eventData, description: e.target.value })}
              />
            </div>
          </div>
        );
      case 'coupon':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="code">Code</Label>
              <Input
                id="code"
                placeholder="Enter code"
                value={couponData.code}
                onChange={(e) => setCouponData({ ...couponData, code: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="discount">Discount</Label>
              <Input
                id="discount"
                placeholder="Enter discount"
                value={couponData.discount}
                onChange={(e) => setCouponData({ ...couponData, discount: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="expirationDate">Expiration Date</Label>
              <Input
                id="expirationDate"
                type="date"
                placeholder="Enter expiration date"
                value={couponData.expirationDate}
                onChange={(e) => setCouponData({ ...couponData, expirationDate: e.target.value })}
              />
            </div>
          </div>
        );
      case 'businesscard':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                placeholder="Enter name"
                value={businessCardData.name}
                onChange={(e) => setBusinessCardData({ ...businessCardData, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone (Optional)</Label>
              <Input
                id="phone"
                placeholder="Enter phone"
                value={businessCardData.phone}
                onChange={(e) => setBusinessCardData({ ...businessCardData, phone: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email (Optional)</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter email"
                value={businessCardData.email}
                onChange={(e) => setBusinessCardData({ ...businessCardData, email: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="organization">Organization (Optional)</Label>
              <Input
                id="organization"
                placeholder="Enter organization"
                value={businessCardData.organization}
                onChange={(e) => setBusinessCardData({ ...businessCardData, organization: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="title">Title (Optional)</Label>
              <Input
                id="title"
                placeholder="Enter title"
                value={businessCardData.title}
                onChange={(e) => setBusinessCardData({ ...businessCardData, title: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">Address (Optional)</Label>
              <Input
                id="address"
                placeholder="Enter address"
                value={businessCardData.address}
                onChange={(e) => setBusinessCardData({ ...businessCardData, address: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="website">Website (Optional)</Label>
              <Input
                id="website"
                placeholder="Enter website"
                value={businessCardData.website}
                onChange={(e) => setBusinessCardData({ ...businessCardData, website: e.target.value })}
              />
            </div>
          </div>
        );
      case 'url':
        return (
          <div className="space-y-2">
            <Label htmlFor="text">URL</Label>
            <Input
              id="text"
              placeholder="Enter URL"
              value={urlData}
              onChange={(e) => setUrlData(e.target.value)}
            />
          </div>
        );
      default:
        return (
          <div className="space-y-2">
            <Label htmlFor="text">Text</Label>
            <Input
              id="text"
              placeholder="Enter text"
              value={textData}
              onChange={(e) => setTextData(e.target.value)}
            />
          </div>
        );
    }
  };

  const renderFontSelection = () => (
    <div className="space-y-4">
      <Label htmlFor="font-select">Label Font</Label>
      <Select
        value={selectedFont}
        onValueChange={setSelectedFont}
      >
        <SelectTrigger>
          <SelectValue placeholder="Select font" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="default">
            <span className="font-normal">Default Font</span>
          </SelectItem>

          {/* Normal Fonts */}
          <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">Normal Fonts</div>
          {fontOptions.filter(f => f.category === 'normal').map(font => (
            <SelectItem key={font.value} value={font.value}>
              <span className={font.font.className}>{font.label}</span>
            </SelectItem>
          ))}

          {/* Decorative Fonts */}
          <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">Decorative Fonts</div>
          {fontOptions.filter(f => f.category === 'decorative').map(font => (
            <SelectItem key={font.value} value={font.value}>
              <span className={font.font.className}>{font.label}</span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );

  const handleAnalyticsClick = () => {
    setShowConsentDialog(true);
  };

  const handleConsentResponse = async (consent: boolean) => {
    setShowConsentDialog(false);

    if (consent) {
      // Save consent in cookies
      document.cookie = "analytics_consent=true; max-age=31536000; path=/";

      if (!currentUser) {
        // Redirect to sign in if not authenticated
        router.push('/auth/signin');
      } else {
        // Redirect to analytics if authenticated
        router.push('/qr-code/analytics');
      }
    }
  };

  const [showSaveAnalyticsDialog, setShowSaveAnalyticsDialog] = useState(false);
  const [showSignInDialog, setShowSignInDialog] = useState(false);
  const [showAnalyticsSavedDialog, setShowAnalyticsSavedDialog] = useState(false);

  const SaveAnalyticsDialog = () => (
    <Dialog open={showSaveAnalyticsDialog} onOpenChange={setShowSaveAnalyticsDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Save for Analytics?</DialogTitle>
          <DialogDescription>
            Would you like to save this QR code to your analytics dashboard? This will allow you to:
            <ul className="list-disc list-inside mt-2">
              <li>Track how many times it's scanned</li>
              <li>See when it was last used</li>
              <li>Manage all your QR codes in one place</li>
            </ul>
            <p className="mt-2">Note: You need to sign in to access analytics.</p>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => {
            setShowSaveAnalyticsDialog(false);
            handleDownload('png');
          }}>
            No, Just Download
          </Button>
          <Button onClick={() => {
            setShowSaveAnalyticsDialog(false);
            if (!isAuthenticated) {
              setShowSignInDialog(true);
            } else {
              handleSaveToAnalytics();
            }
          }}>
            Yes, Enable Analytics
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  const SignInDialog = () => (
    <Dialog open={showSignInDialog} onOpenChange={setShowSignInDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Sign In Required</DialogTitle>
          <DialogDescription>
            Please sign in to enable QR code analytics and tracking.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => {
            setShowSignInDialog(false);
            handleDownload('png');
          }}>
            Cancel
          </Button>
          <Button onClick={() => {
            // Implement your sign-in logic here
            setShowSignInDialog(false);
            router.push('/auth/signin');
          }}>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  const AnalyticsDialog = () => {
    const formatDate = (timestamp: any) => {
      if (!timestamp) return 'Never';
      try {
        const date = timestamp.toDate();
        return date.toLocaleString();
      } catch (error) {
        return 'Invalid Date';
      }
    };

    return (
      <Dialog open={showAnalyticsSavedDialog} onOpenChange={setShowAnalyticsSavedDialog}>
        <DialogContent className="sm:max-w-[625px]">
          <DialogHeader>
            <DialogTitle>QR Code Analytics</DialogTitle>
          </DialogHeader>

          <div className="grid grid-cols-3 gap-4 py-4">
            <div className="flex flex-col items-center">
              <div className="text-2xl text-blue-600 dark:text-blue-400 mb-2">
                <QrCode className="w-6 h-6" />
              </div>
              <div className="text-sm font-medium">Total Scans</div>
              <div className="text-sm text-gray-500">{analyticsData.scanCount}</div>
            </div>

            <div className="flex flex-col items-center">
              <div className="text-2xl text-purple-600 dark:text-purple-400 mb-2">
                <Smartphone className="w-6 h-6" />
              </div>
              <div className="text-sm font-medium">Mobile Scans</div>
              <div className="text-sm text-gray-500">{detailedAnalytics.deviceTypes.mobile}</div>
            </div>

            <div className="flex flex-col items-center">
              <div className="text-2xl text-green-600 dark:text-green-400 mb-2">
                <Monitor className="w-6 h-6" />
              </div>
              <div className="text-sm font-medium">Desktop Scans</div>
              <div className="text-sm text-gray-500">{detailedAnalytics.deviceTypes.desktop}</div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Scan Activity</h4>
              <div className="h-32 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={detailedAnalytics.scanTimes}>
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">Recent Scans</h4>
              <div className="space-y-2">
                {detailedAnalytics.lastScans.map((scan, index) => (
                  <div key={index} className="flex justify-between items-center text-sm">
                    <div className="flex items-center gap-2">
                      {scan.device === 'mobile' ?
                        <Smartphone className="w-4 h-4" /> :
                        <Monitor className="w-4 h-4" />
                      }
                      <span>{formatDate(scan.time)}</span>
                    </div>
                    <span className="text-gray-500 truncate max-w-[200px]">{scan.userAgent}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">QR Code Details</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-500">Name</div>
                  <div className="font-medium">{qrName || 'Untitled'}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Created</div>
                  <div className="font-medium">{formatDate(analyticsData.lastScanned)}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Type</div>
                  <div className="font-medium capitalize">{qrType}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Last Scan</div>
                  <div className="font-medium">{formatDate(analyticsData.lastScanned)}</div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAnalyticsSavedDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  const [showSaveAnalyticsConsentDialog, setShowSaveAnalyticsConsentDialog] = useState(false);

  const handleSaveForAnalytics = async () => {
    setShowSaveAnalyticsConsentDialog(true);
  };

  const saveQRCode = async (qrData: Omit<QRCodeData, 'createdAt' | 'trackingUrl' | 'imageUrl'>) => {
    try {
      setLoading(true);

      if (!auth.currentUser) {
        toast.error('You must be logged in to save QR codes');
        return;
      }

      const db = getFirestore();
      const qrCodeId = doc(collection(db, 'qr_codes')).id;

      const qrCodeData = {
        userId: auth.currentUser.uid,
        type: qrData.type || 'text',
        content: qrData.content || '',
        url: qrData.content || '', // Add url field for compatibility
        title: qrData.name || 'Untitled QR Code', // Add default title
        name: qrData.name || 'Untitled QR Code', // Add default name
        description: '',
        createdAt: serverTimestamp(),
        scanCount: 0,
        lastScanned: null,
        deviceStats: { mobile: 0, desktop: 0 },
        hourlyStats: {},
        lastDeviceType: null,
        lastUserAgent: null,
        errorLevel: 'L',
        backgroundColor: '#FFFFFF',
        foregroundColor: '#000000',
        isPrivate: false
      };

      const baseUrl = window.location.origin;
      const trackingUrl = `${baseUrl}/qr-code/${qrCodeId}`;

      console.log('🔗 [QR Generate] URL Details:', {
        baseUrl,
        trackingUrl,
        qrCodeId,
        fullUrl: new URL(trackingUrl).toString()
      });

      // Save to user's QR codes subcollection
      const userQrCodeRef = doc(db, 'users', auth.currentUser.uid, 'qr_codes', qrCodeId);
      await setDoc(userQrCodeRef, qrCodeData);
      console.log('✅ [QR Generate] Saved to user collection');

      // Generate and upload QR code image
      const qrCodeElement = document.getElementById('qr-code');
      if (qrCodeElement) {
        const canvas = await html2canvas(qrCodeElement);
        const imageBlob = await new Promise<Blob>((resolve) => {
          canvas.toBlob((blob) => resolve(blob!), 'image/png');
        });

        const formData = new FormData();
        formData.append('file', imageBlob, `qrcodes/${auth.currentUser.uid}/${qrCodeId}.png`);

        const uploadResponse = await fetch('/api/generate-image', {
          method: 'POST',
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload QR code image');
        }

        const { url: imageUrl } = await uploadResponse.json();
        console.log('✅ [QR Generate] Image uploaded:', imageUrl);

        // Update both collections with the image URL
        const imageUpdate = { imageUrl };
        await updateDoc(userQrCodeRef, imageUpdate);
        console.log('✅ [QR Generate] Updated image URLs');
      }

      toast.success('QR code saved successfully!');
      setShowSaveAnalyticsConsentDialog(false);
      setShowAnalyticsSavedDialog(true);
    } catch (error) {
      console.error('❌ [QR Generate] Error:', error);
      toast.error('Failed to save QR code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Update the Save button click handler
  const handleSaveForAnalyticsClick = () => {
    const content = getQRContent();
    const qrData = {
      type: qrType,
      content: content,
      url: content,  // Set url same as content
      title: qrName || 'Untitled QR Code', // Set title same as name
      name: qrName || 'Untitled QR Code', // Set name same as title
      description: '', // Empty description
      isPrivate: false, // Default to false
      style: {
        fgColor,
        bgColor,
        frameColor,
        includeFrame,
        size,
        labelFont: selectedFont || 'Arial'
      },
      backgroundColor: bgColor,
      foregroundColor: fgColor,
      frameColor: frameColor,
      size: `${size}px`,
      errorCorrectionLevel: 'M',
      labelFont: selectedFont || 'Arial'
    };
    if (!currentUser) {
      setShowSignInDialog(true);
    } else {
      handleSaveForAnalytics();
    }
  };

  const SaveAnalyticsConsentDialog = () => (
    <Dialog open={showSaveAnalyticsConsentDialog} onOpenChange={setShowSaveAnalyticsConsentDialog}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Save QR Code for Analytics</DialogTitle>
          <DialogDescription asChild>
            <div className="space-y-3">
              <p>
                We'll save this QR code to your analytics dashboard. This helps you track its usage and performance.
              </p>
              <div className="space-y-2 text-sm">
                <p>By saving this QR code, you agree to:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Storage of this QR code's data</li>
                  <li>Collection of usage analytics for this QR code</li>
                  <li>Processing of this data to improve our service</li>
                </ul>
                <p className="mt-3 text-blue-600 dark:text-blue-400">
                  You can view and manage all your saved QR codes in your analytics dashboard.
                </p>
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={() => setShowSaveAnalyticsConsentDialog(false)}>
            Cancel
          </Button>
          <Button onClick={handleSaveForAnalyticsClick}>
            Save QR Code
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-8 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 rounded-xl shadow-lg">
      {/* Header with Analytics Button */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
          QR Code Generator
        </h1>
        <Button
          variant="outline"
          className="flex items-center gap-2"
          onClick={handleViewAnalytics}
        >
          <BarChart2 className="w-4 h-4" />
          View Analytics
        </Button>
      </div>

      {/* Consent Dialog */}
      <Dialog open={showConsentDialog} onOpenChange={setShowConsentDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Analytics Consent</DialogTitle>
            <DialogDescription>
              We use cookies and store data to provide analytics features. This helps us improve our service and understand how you use our QR code generator.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <p className="text-sm text-muted-foreground">
              By clicking Accept, you agree to:
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Storage of your QR code generation data</li>
                <li>Use of cookies for analytics purposes</li>
                <li>Processing of this data to improve our service</li>
              </ul>
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => handleConsentResponse(false)}>
              Decline
            </Button>
            <Button onClick={() => handleConsentResponse(true)}>
              Accept & Continue
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* QR Type Selection */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm">
        <Label className="text-blue-600 dark:text-blue-400 mb-2 block text-sm">QR Code Type</Label>
        <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2">
          {qrTypes.map((type) => (
            <Button
              key={type.value}
              variant={qrType === type.value ? "default" : "outline"}
              className={`flex flex-col items-center justify-center gap-1 h-auto py-2 px-2 text-center ${
                qrType === type.value
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-blue-50 dark:hover:bg-blue-900"
              }`}
              onClick={() => setQrType(type.value as QRCodeType)}
            >
              <span className="text-base">{type.icon}</span>
              <span className="text-xs font-medium whitespace-nowrap">{type.label}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Content Sections */}
      <div className="grid gap-8">
        {/* QR Code Name */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
          <Label htmlFor="qrName" className="text-blue-600 mb-2 block">QR Code Name</Label>
          <Input
            id="qrName"
            placeholder="Enter a name for your QR code"
            value={qrName}
            onChange={(e) => setQrName(e.target.value)}
            className="border-blue-200 focus:border-blue-400 focus:ring-blue-400"
          />
        </div>

        {/* Input Fields */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
          {renderInputFields()}
        </div>

        {/* Style Options */}
        <div className="grid gap-6 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
          <h3 className="text-lg font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
            Style Options
          </h3>

          {/* Size Selection */}
          <div className="grid gap-2">
            <Label>QR Code Size</Label>
            <Select
              value={showCustomSize ? 'custom' : size.toString()}
              onValueChange={(value) => {
                if (value === 'custom') {
                  setShowCustomSize(true);
                } else {
                  setShowCustomSize(false);
                  setSize(parseInt(value));
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select size" />
              </SelectTrigger>
              <SelectContent>
                {predefinedSizes.map((sizeOption) => (
                  <SelectItem key={sizeOption.value.toString()} value={sizeOption.value.toString()}>
                    {sizeOption.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {showCustomSize && (
              <div className="mt-2">
                <Input
                  type="number"
                  min="128"
                  max="512"
                  value={customSize}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (value >= 128 && value <= 512) {
                      setCustomSize(value);
                      setSize(value);
                    }
                  }}
                  placeholder="Enter custom size (128-512px)"
                />
              </div>
            )}
          </div>

          {/* Color Presets */}
          <div className="grid gap-2">
            <Label className="text-blue-600 dark:text-blue-400">Color Theme</Label>
            <div className="flex flex-wrap gap-2">
              {colorPresets.map((preset) => (
                <Button
                  key={preset.label}
                  variant="outline"
                  className="flex items-center gap-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900 dark:hover:to-purple-900 transition-all duration-300"
                  onClick={() => {
                    setFgColor(preset.fg);
                    setBgColor(preset.bg);
                    setFrameColor(preset.frame);
                  }}
                >
                  <div className="flex items-center gap-1">
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: preset.fg }}
                    />
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: preset.frame }}
                    />
                  </div>
                  {preset.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Custom Colors */}
          <div className="grid gap-4 sm:grid-cols-3">
            {/* Foreground Color */}
            <div>
              <Label>QR Color</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal mt-1"
                  >
                    <div
                      className="w-4 h-4 rounded-full mr-2"
                      style={{ backgroundColor: fgColor }}
                    />
                    {fgColor}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <SketchPicker
                    color={fgColor}
                    onChange={(color) => setFgColor(color.hex)}
                    className="!shadow-none"
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Background Color */}
            <div>
              <Label>Background</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal mt-1"
                  >
                    <div
                      className="w-4 h-4 rounded-full mr-2"
                      style={{ backgroundColor: bgColor }}
                    />
                    {bgColor}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <SketchPicker
                    color={bgColor}
                    onChange={(color) => setBgColor(color.hex)}
                    className="!shadow-none"
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Frame Color */}
            <div>
              <div className="flex items-center gap-2">
                <Label>Frame</Label>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={includeFrame}
                    onChange={(e) => setIncludeFrame(e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm">Include frame</span>
                </div>
              </div>
              {includeFrame && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal mt-1"
                    >
                      <div
                        className="w-4 h-4 rounded-full mr-2"
                        style={{ backgroundColor: frameColor }}
                      />
                      {frameColor}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <SketchPicker
                      color={frameColor}
                      onChange={(color) => setFrameColor(color.hex)}
                      className="!shadow-none"
                    />
                  </PopoverContent>
                </Popover>
              )}
            </div>
          </div>

          {/* Font Selection */}
          <div className="grid gap-2">
            {renderFontSelection()}
          </div>
        </div>

        {/* QR Code Preview */}
        {getQRContent() && (
          <div className="qr-preview relative bg-white p-8 rounded-lg shadow-lg">
            <div className="flex flex-col items-center space-y-4">
              <QRCodeSVG
                value={getQRContent()}
                size={size}
                // Casting errorLevel to QRCodeErrorCorrectionLevel because the type is not automatically inferred
                // This is a temporary fix until the type is fixed in the library
                level={errorLevel as 'L' | 'M' | 'Q' | 'H'}
                fgColor={fgColor}
                bgColor={bgColor}
              />
              {qrName && (
                <div
                  ref={textRef}
                  className={`mt-4 text-xl text-center break-words ${
                    fontOptions.find(f => f.value === selectedFont)?.font.className || ''
                  }`}
                  style={{
                    color: frameColor,
                    maxWidth: `${size * 1.5}px`, // Maximum text width relative to QR size
                    wordWrap: 'break-word',
                    whiteSpace: 'pre-wrap'
                  }}
                >
                  {qrName}
                </div>
              )}
            </div>
            {includeFrame && (
              <div
                className="absolute inset-0 rounded-lg"
                style={{
                  border: `8px solid ${frameColor}`,
                  width: `${getFrameDimensions().width}px`,
                  height: `${getFrameDimensions().height}px`,
                  left: '50%',
                  transform: 'translateX(-50%)'
                }}
              />
            )}
          </div>
        )}

        {/* Error Level Selection */}
        <div className="mt-6 w-full max-w-md">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Label className="text-blue-600">Error Correction Level</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" className="h-6 w-6 p-0">
                    <Info className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent>
                  <p className="text-sm">Error correction allows QR codes to be readable even when partially damaged or obscured.</p>
                </PopoverContent>
              </Popover>
            </div>
            <span className="text-sm text-gray-500">Current: {errorLevelOptions.find(l => l.value === errorLevel)?.label}</span>
          </div>
          <div className="grid grid-cols-4 gap-2">
            {errorLevelOptions.map((level) => (
              <Button
                key={level.value}
                variant={errorLevel === level.value ? "default" : "outline"}
                className={`px-2 py-1.5 h-auto flex flex-col items-center ${
                  errorLevel === level.value
                    ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md scale-105 transition-transform"
                    : "hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:scale-102 transition-transform"
                }`}
                onClick={() => setErrorLevel(level.value)}
              >
                <div className="text-center">
                  <div className="font-medium text-base">{level.value}</div>
                  <div className="text-xs whitespace-nowrap opacity-80">{level.label.split(' ')[0]}</div>
                </div>
              </Button>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-2 text-center">
            {errorLevelOptions.find(l => l.value === errorLevel)?.description}
          </p>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-2">
        <Button
          onClick={handleSaveForAnalytics}
          className="w-full sm:w-1/2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-300"
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save for Analytics'}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-full sm:w-1/2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900 dark:hover:to-purple-900 transition-all duration-300">
              Download QR Code
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => handleDownload('png')}>
              Download as PNG
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownload('svg')}>
              Download as SVG
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownload('pdf')}>
              Download as PDF
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* QR Code Tips */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
        <h3 className="text-lg font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 mb-4">
          QR Code Guide
        </h3>
        <div className="grid gap-4">
          {qrTips.map((tip, index) => (
            <div key={index} className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-semibold text-blue-600">{tip.title}</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">{tip.content}</p>
            </div>
          ))}
        </div>
      </div>


      <SaveAnalyticsDialog />
      <SignInDialog />
      <SaveAnalyticsConsentDialog />
      <AnalyticsDialog />
    </div>
  );
}

function setShowCookieConsent(arg0: boolean) {
    throw new Error('Function not implemented.');
}
