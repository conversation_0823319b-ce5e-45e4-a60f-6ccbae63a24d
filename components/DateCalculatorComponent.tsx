'use client';

import { useState, useMemo, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar as CalendarIcon, PlusCircle, Briefcase, Plus, CalendarDays, ArrowRight, Eraser, Baby, ChevronLeft, ChevronRight, X, Search, ChevronDown, ChevronUp, Minus, Clock, Timer } from 'lucide-react';
import { cn } from '@/lib/utils';
import { differenceInYears, differenceInMonths, differenceInWeeks, differenceInDays } from 'date-fns';
import { YearCalendarDialog } from '@/components/YearCalendarDialog';
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";

// Dynamically import heavy components
const ResultDisplay = dynamic(() => import('./ResultDisplay'), {
  loading: () => <div className="animate-pulse bg-gray-200 h-20 rounded-lg" />
});

export function DateCalculatorComponent() {
  type TabType = 'countDays' | 'addDays' | 'workdays' | 'addWorkdays' | 'weekday' | 'age' | 'timezone' | 'countdown' | 'recurring';

  interface Holiday {
    date: Date;
    name: string;
  }

  // US Federal Holidays for the current year
  const holidays: Holiday[] = [
    { date: new Date(new Date().getFullYear(), 0, 1), name: "New Year's Day" },
    { date: new Date(new Date().getFullYear(), 0, 16), name: "Martin Luther King Jr. Day" },
    { date: new Date(new Date().getFullYear(), 1, 20), name: "Presidents Day" },
    { date: new Date(new Date().getFullYear(), 4, 29), name: "Memorial Day" },
    { date: new Date(new Date().getFullYear(), 5, 19), name: "Juneteenth" },
    { date: new Date(new Date().getFullYear(), 6, 4), name: "Independence Day" },
    { date: new Date(new Date().getFullYear(), 8, 4), name: "Labor Day" },
    { date: new Date(new Date().getFullYear(), 9, 9), name: "Columbus Day" },
    { date: new Date(new Date().getFullYear(), 10, 11), name: "Veterans Day" },
    { date: new Date(new Date().getFullYear(), 10, 23), name: "Thanksgiving Day" },
    { date: new Date(new Date().getFullYear(), 11, 25), name: "Christmas Day" },
  ];

  const [activeTab, setActiveTab] = useState<TabType>('countDays');
  const [startDate, setStartDate] = useState('');
  const [startTime, setStartTime] = useState('00:00');
  const [endDate, setEndDate] = useState('');
  const [endTime, setEndTime] = useState('00:00');
  const [daysToAdd, setDaysToAdd] = useState<number>(0);
  const [timeUnit, setTimeUnit] = useState<'days' | 'weeks' | 'months' | 'years'>('days');
  const [workdaysToAdd, setWorkdaysToAdd] = useState<string>('');
  const [result, setResult] = useState<string | null>(null);
  const [isAddingDays, setIsAddingDays] = useState(true);
  const [includeEndDate, setIncludeEndDate] = useState(true);
  const [includeTime, setIncludeTime] = useState(false);
  const [sourceTimezone, setSourceTimezone] = useState('UTC');
  const [targetTimezone, setTargetTimezone] = useState('');
  const [timezoneDate, setTimezoneDate] = useState('');
  const [timezoneTime, setTimezoneTime] = useState('');
  const [convertedTime, setConvertedTime] = useState<string | null>(null);
  const [countdownTitle, setCountdownTitle] = useState('');
  const [countdownDate, setCountdownDate] = useState('');
  const [countdownTime, setCountdownTime] = useState('');
  const [countdownInterval, setCountdownInterval] = useState<NodeJS.Timeout | null>(null);
  const [countdownRemaining, setCountdownRemaining] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    totalSeconds: number;
  } | null>(null);

  // Saved calculations
  const [savedCalculations, setSavedCalculations] = useState<Array<{
    id: string;
    title: string;
    type: TabType;
    date: string;
    result: string;
    details?: string;
  }>>([]);

  // Date range highlighting
  const [rangeStartDate, setRangeStartDate] = useState<Date | null>(null);
  const [rangeEndDate, setRangeEndDate] = useState<Date | null>(null);
  const [highlightedDates, setHighlightedDates] = useState<Date[]>([]);

  // Recurring dates
  const [recurringTitle, setRecurringTitle] = useState('');
  const [recurringStartDate, setRecurringStartDate] = useState('');
  const [recurringEndDate, setRecurringEndDate] = useState('');
  const [recurringType, setRecurringType] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('daily');
  const [recurringInterval, setRecurringInterval] = useState(1);
  const [recurringDates, setRecurringDates] = useState<Date[]>([]);
  const [detailedResult, setDetailedResult] = useState<{
    years: number;
    months: number;
    weeks: number;
    days: number;
    hours?: number;
    minutes?: number;
    seconds?: number;
    totalDays?: number;
    totalHours?: number;
    totalMinutes?: number;
    totalSeconds?: number;
    note?: string;
    skippedDates?: Array<{ date: Date; reason: string }>;
  } | null>(null);
  const [calendarMonth, setCalendarMonth] = useState(new Date());
  const [showFullCalendar, setShowFullCalendar] = useState(false);
  const [showSkippedDates, setShowSkippedDates] = useState(false);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<('federal' | 'local' | 'observance')[]>(['federal', 'local', 'observance']);
  const [expandedMonths, setExpandedMonths] = useState<string[]>(['January']);
  const [startDay, setStartDay] = useState<'monday' | 'sunday'>('monday');

  // Ref to track previous tab for date persistence
  const prevTabRef = useRef<TabType>('countDays');

  // Calendar interaction state
  const [selectedStartDate, setSelectedStartDate] = useState<Date | null>(null);
  const [selectedEndDate, setSelectedEndDate] = useState<Date | null>(null);
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);

  // Function to handle calendar date selection
  const handleCalendarDateClick = (clickedDate: Date) => {
    const dateString = clickedDate.toISOString().split('T')[0];

    // Determine which field to populate based on active tab
    const dualDateTabs = ['countDays', 'workdays'];
    const singleDateTabs = ['addDays', 'addWorkdays', 'weekday', 'age'];
    const specialTabs = ['timezone', 'countdown', 'recurring'];

    // Don't auto-populate special tabs
    if (specialTabs.includes(activeTab)) {
      return;
    }

    if (dualDateTabs.includes(activeTab)) {
      // For dual date tabs, populate start date first, then end date
      if (!startDate || (startDate && endDate)) {
        // If no start date or both dates are set, set as start date
        setStartDate(dateString);
        setEndDate('');
        setSelectedStartDate(clickedDate);
        setSelectedEndDate(null);
      } else if (startDate && !endDate) {
        // If start date is set but no end date, set as end date
        const startDateObj = new Date(startDate);
        if (clickedDate >= startDateObj) {
          setEndDate(dateString);
          setSelectedEndDate(clickedDate);
        } else {
          // If clicked date is before start date, swap them
          setStartDate(dateString);
          setEndDate(startDate);
          setSelectedStartDate(clickedDate);
          setSelectedEndDate(startDateObj);
        }
      }
    } else if (singleDateTabs.includes(activeTab)) {
      // For single date tabs, always populate start date
      setStartDate(dateString);
      setSelectedStartDate(clickedDate);
      setSelectedEndDate(null);
    }
  };

  // Function to check if a date is in the selected range
  const isDateInRange = (date: Date) => {
    if (!selectedStartDate) return false;
    if (!selectedEndDate) return false;
    return date >= selectedStartDate && date <= selectedEndDate;
  };

  // Function to check if a date is selected (start or end)
  const isDateSelected = (date: Date) => {
    if (selectedStartDate && date.getTime() === selectedStartDate.getTime()) return 'start';
    if (selectedEndDate && date.getTime() === selectedEndDate.getTime()) return 'end';
    return false;
  };

  // Function to get day count from start date for range visualization
  const getDayCountFromStart = (date: Date) => {
    if (!selectedStartDate || !selectedEndDate) return null;
    if (!isDateInRange(date) && !isDateSelected(date)) return null;

    const daysDiff = Math.floor((date.getTime() - selectedStartDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    return daysDiff > 0 ? daysDiff : null;
  };

  // Function to get total days in selected range
  const getTotalDaysInRange = () => {
    if (!selectedStartDate || !selectedEndDate) return null;
    return Math.floor((selectedEndDate.getTime() - selectedStartDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  };

  // Update selected dates when input fields change
  useEffect(() => {
    if (startDate) {
      setSelectedStartDate(new Date(startDate));
    } else {
      setSelectedStartDate(null);
    }

    if (endDate) {
      setSelectedEndDate(new Date(endDate));
    } else {
      setSelectedEndDate(null);
    }
  }, [startDate, endDate]);

  // Function to intelligently preserve dates when switching tabs
  const preserveDatesOnTabSwitch = (previousTab: TabType, newTab: TabType, currentStartDate: string, currentEndDate: string) => {
    // Tabs that don't get auto-populated (they have their own context)
    const specialTabs = ['timezone', 'countdown', 'recurring'];

    if (specialTabs.includes(newTab)) {
      return { startDate: '', endDate: '' };
    }

    // If no dates were set in previous tab, don't populate
    if (!currentStartDate) {
      return { startDate: '', endDate: '' };
    }

    // Tabs that use both startDate and endDate
    const dualDateTabs = ['countDays', 'workdays'];
    // Tabs that use only startDate
    const singleDateTabs = ['addDays', 'addWorkdays', 'weekday', 'age'];

    if (dualDateTabs.includes(newTab)) {
      // New tab uses both dates
      if (dualDateTabs.includes(previousTab)) {
        // Previous tab also used both dates - preserve both
        return { startDate: currentStartDate, endDate: currentEndDate };
      } else if (singleDateTabs.includes(previousTab)) {
        // Previous tab used only startDate - use it as startDate, leave endDate empty
        return { startDate: currentStartDate, endDate: '' };
      }
    } else if (singleDateTabs.includes(newTab)) {
      // New tab uses only startDate
      // Use the startDate from previous tab regardless of whether it was single or dual date tab
      return { startDate: currentStartDate, endDate: '' };
    }

    // Default case - clear dates
    return { startDate: '', endDate: '' };
  };

  const holidaysList: { date: string; name: string; type: 'federal' | 'local' | 'observance'; icon?: string }[] = [
    { date: "1 Jan", name: "New Year's Day", type: "federal", icon: "🎉" },
    { date: "26 Jan", name: "Australia Day", type: "federal", icon: "🇦🇺" },
    { date: "27 Jan", name: "Australia Day Observed", type: "federal" },
    { date: "3 Mar", name: "Labour Day (WA)", type: "local" },
    { date: "10 Mar", name: "Labour Day (VIC)", type: "local" },
    { date: "21 Mar", name: "Harmony Day", type: "observance", icon: "🤝" },
    { date: "18 Apr", name: "Good Friday", type: "federal", icon: "✝️" },
    { date: "19 Apr", name: "Holy Saturday", type: "observance" },
    { date: "20 Apr", name: "Easter Sunday", type: "federal", icon: "🐰" },
    { date: "21 Apr", name: "Easter Monday", type: "federal" },
    { date: "25 Apr", name: "ANZAC Day", type: "federal", icon: "🌺" },
    { date: "5 May", name: "May Day", type: "local" },
    { date: "9 Jun", name: "King's Birthday", type: "federal", icon: "👑" },
    { date: "29 Sep", name: "King's Birthday (WA)", type: "local" },
    { date: "6 Oct", name: "Labour Day", type: "local" },
    { date: "11 Nov", name: "Remembrance Day", type: "federal", icon: "🌹" },
    { date: "24 Dec", name: "Christmas Eve", type: "observance", icon: "🎄" },
    { date: "25 Dec", name: "Christmas Day", type: "federal", icon: "🎄" },
    { date: "26 Dec", name: "Boxing Day", type: "federal" },
    { date: "31 Dec", name: "New Year's Eve", type: "observance", icon: "🎉" },
  ];

  const getMonthDays = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const getHolidayForDate = (date: string, holidays: typeof holidaysList) => {
    return holidays.find(h => h.date === date);
  };

  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());

  const renderFullMonthCalendar = (monthIndex: number) => {
    const daysInMonth = getMonthDays(currentYear, monthIndex);
    const firstDay = getFirstDayOfMonth(currentYear, monthIndex);
    const days = [];
    const today = new Date();
    const isCurrentMonth = today.getMonth() === monthIndex && today.getFullYear() === currentYear;

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-16" />);
    }

    // Add the days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${day} ${months[monthIndex].substring(0, 3)}`;
      const holiday = getHolidayForDate(dateStr, holidaysList);
      const isToday = isCurrentMonth && today.getDate() === day;
      const currentDate = new Date(currentYear, monthIndex, day);
      const dateSelection = isDateSelected(currentDate);
      const inRange = isDateInRange(currentDate);
      const isHovered = hoveredDate && currentDate.getTime() === hoveredDate.getTime();

      days.push(
        <div
          key={day}
          onClick={() => handleCalendarDateClick(currentDate)}
          onMouseEnter={() => setHoveredDate(currentDate)}
          onMouseLeave={() => setHoveredDate(null)}
          className={cn(
            "h-16 p-2 border border-gray-200 dark:border-gray-700 flex flex-col relative cursor-pointer transition-all duration-200",
            // Holiday backgrounds (lower priority)
            holiday && holiday.type === 'federal' && !dateSelection && !inRange && "bg-red-50 dark:bg-red-900/20",
            holiday && holiday.type === 'local' && !dateSelection && !inRange && "bg-blue-50 dark:bg-blue-900/20",
            holiday && holiday.type === 'observance' && !dateSelection && !inRange && "bg-gray-50 dark:bg-gray-800",
            // Selection states (higher priority)
            dateSelection === 'start' && "bg-green-500 text-white ring-2 ring-green-600 dark:ring-green-400 shadow-lg",
            dateSelection === 'end' && "bg-red-500 text-white ring-2 ring-red-600 dark:ring-red-400 shadow-lg",
            inRange && !dateSelection && "bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600",
            // Today highlighting
            isToday && !dateSelection && "ring-2 ring-blue-300 dark:ring-blue-500",
            // Hover effects
            isHovered && !dateSelection && "bg-blue-50 dark:bg-blue-900/20 ring-1 ring-blue-300",
            // Default hover
            "hover:bg-gray-50 dark:hover:bg-gray-700"
          )}
          title={`Click to select ${currentDate.toLocaleDateString()}`}
        >
          <span className={cn(
            "text-sm font-medium",
            isToday && !dateSelection && "text-blue-500 dark:text-blue-400",
            dateSelection && "text-white font-bold"
          )}>
            {day}
          </span>
          {holiday && (
            <div className="mt-1">
              <div className={cn(
                "text-xs truncate",
                dateSelection && "text-white/90"
              )} title={holiday.name}>
                {holiday.icon} {holiday.name}
              </div>
            </div>
          )}
          {/* Selection indicator */}
          {dateSelection && (
            <div className="absolute top-1 right-1">
              <div className="w-2 h-2 bg-white rounded-full opacity-80" />
            </div>
          )}

          {/* Day count overlay for range visualization */}
          {getDayCountFromStart(currentDate) && (
            <div className="absolute bottom-1 left-1">
              <div className="bg-black/70 text-white text-xs px-1 rounded">
                {getDayCountFromStart(currentDate)}
              </div>
            </div>
          )}

          {/* Total days indicator on end date */}
          {dateSelection === 'end' && getTotalDaysInRange() && (
            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
              <div className="bg-red-600 text-white text-xs px-2 py-1 rounded-full shadow-lg whitespace-nowrap">
                {getTotalDaysInRange()} days
              </div>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="w-full space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className="text-2xl font-bold">{months[monthIndex]} {currentYear}</h3>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (monthIndex === 0) {
                  setCurrentYear(prev => prev - 1);
                  setSelectedMonth(11);
                } else {
                  setSelectedMonth(prev => prev - 1);
                }
              }}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (monthIndex === 11) {
                  setCurrentYear(prev => prev + 1);
                  setSelectedMonth(0);
                } else {
                  setSelectedMonth(prev => prev + 1);
                }
              }}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div>
          <div className="grid grid-cols-7 gap-px text-center font-medium mb-1 bg-gray-100 dark:bg-gray-800 p-2 rounded-t-lg">
            {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(day => (
              <div key={day} className="text-gray-600 dark:text-gray-300">{day}</div>
            ))}
          </div>
          <div className="grid grid-cols-7 gap-px bg-white dark:bg-gray-800 rounded-b-lg">
            {days}
          </div>
        </div>
      </div>
    );
  };

  const quarterHolidays = useMemo(() => {
    const filtered = holidaysList.filter(holiday =>
      selectedTypes.includes(holiday.type) &&
      (holiday.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
       holiday.date.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    return {
      q1: filtered.filter(holiday => ['Jan', 'Feb', 'Mar'].includes(holiday.date.split(' ')[1])),
      q2: filtered.filter(holiday => ['Apr', 'May', 'Jun'].includes(holiday.date.split(' ')[1])),
      q3: filtered.filter(holiday => ['Jul', 'Aug', 'Sep'].includes(holiday.date.split(' ')[1])),
      q4: filtered.filter(holiday => ['Oct', 'Nov', 'Dec'].includes(holiday.date.split(' ')[1]))
    };
  }, [holidaysList, selectedTypes, searchTerm]);

  const holidaysByMonth = useMemo(() => {
    const filtered = holidaysList.filter(holiday =>
      (selectedTypes.includes(holiday.type) &&
      (holiday.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
       holiday.date.toLowerCase().includes(searchTerm.toLowerCase())))
    );

    return filtered.reduce((acc, holiday) => {
      const month = holiday.date.split(' ')[1];
      if (!acc[month]) {
        acc[month] = [];
      }
      acc[month].push(holiday);
      return acc;
    }, {} as Record<string, { date: string; name: string; type: 'federal' | 'local' | 'observance'; icon?: string }[]>);
  }, [holidaysList, selectedTypes, searchTerm]);

  const toggleMonth = (month: string) => {
    setExpandedMonths(prev =>
      prev.includes(month)
        ? prev.filter(m => m !== month)
        : [...prev, month]
    );
  };

  // Clear results and intelligently preserve dates when changing tabs
  useEffect(() => {
    const previousTab = prevTabRef.current;
    const newTab = activeTab;

    // Store current dates before clearing
    const currentStartDate = startDate;
    const currentEndDate = endDate;

    // Clear results
    setDetailedResult(null);
    setResult(null);
    setDaysToAdd(0);
    setWorkdaysToAdd('');

    // Clear countdown interval when changing tabs
    if (countdownInterval) {
      clearInterval(countdownInterval);
      setCountdownInterval(null);
      setCountdownRemaining(null);
    }

    // Apply intelligent date preservation
    const { startDate: newStartDate, endDate: newEndDate } = preserveDatesOnTabSwitch(
      previousTab,
      newTab,
      currentStartDate,
      currentEndDate
    );

    setStartDate(newStartDate);
    setEndDate(newEndDate);

    // Update the ref for next time
    prevTabRef.current = activeTab;
  }, [activeTab, startDate, endDate, countdownInterval]);

  // Cleanup effect for countdown timer
  useEffect(() => {
    // Cleanup function to clear interval when component unmounts
    return () => {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
    };
  }, [countdownInterval]);

  // Load saved calculations from local storage
  useEffect(() => {
    const savedCalcs = localStorage.getItem('savedDateCalculations');
    if (savedCalcs) {
      try {
        setSavedCalculations(JSON.parse(savedCalcs));
      } catch (error) {
        console.error('Error loading saved calculations:', error);
      }
    }
  }, []);

  // Save calculation to local storage
  const saveCalculation = () => {
    if (!result) return;

    const newCalculation = {
      id: Date.now().toString(),
      title: activeTab === 'countdown' ? (countdownTitle || 'Countdown') :
             activeTab === 'recurring' ? (recurringTitle || 'Recurring Event') :
             `${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Calculation`,
      type: activeTab,
      date: new Date().toISOString(),
      result: result,
      details: detailedResult ? JSON.stringify(detailedResult) : undefined
    };

    const updatedCalculations = [newCalculation, ...savedCalculations];
    setSavedCalculations(updatedCalculations);

    // Save to local storage
    localStorage.setItem('savedDateCalculations', JSON.stringify(updatedCalculations));

    // Show confirmation
    alert('Calculation saved!');
  };

  const setToday = (field: 'start' | 'end') => {
    const today = new Date();
    const dateString = today.toISOString().split('T')[0];
    const timeString = today.toTimeString().slice(0, 5); // Get HH:MM format

    if (field === 'start') {
      setStartDate(dateString);
      if (includeTime) {
        setStartTime(timeString);
      }
    } else {
      setEndDate(dateString);
      if (includeTime) {
        setEndTime(timeString);
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, setter: (value: string) => void) => {
    setter(e.target.value);
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>, setter: (value: any) => void) => {
    const value = e.target.value;
    if (value === '' || value === '-') {
      setter(value);
    } else {
      const num = parseInt(value);
      if (!isNaN(num)) {
        setter(num);
      }
    }
  };

  const [additionalValue, setAdditionalValue] = useState<number>(0);
  const [additionalUnit, setAdditionalUnit] = useState<'days' | 'weeks' | 'months' | 'years'>('days');
  const [addSubtractResult, setAddSubtractResult] = useState<string>('');

  const handleAdditionalCalculation = (isAdd: boolean) => {
    if (!addSubtractResult || !additionalValue) return;

    const currentDate = new Date(addSubtractResult);
    const multiplier = isAdd ? 1 : -1;
    let result;

    switch (additionalUnit) {
      case 'days':
        result = new Date(currentDate.setDate(currentDate.getDate() + (multiplier * additionalValue)));
        break;
      case 'weeks':
        result = new Date(currentDate.setDate(currentDate.getDate() + (multiplier * additionalValue * 7)));
        break;
      case 'months':
        result = new Date(currentDate.setMonth(currentDate.getMonth() + (multiplier * additionalValue)));
        break;
      case 'years':
        result = new Date(currentDate.setFullYear(currentDate.getFullYear() + (multiplier * additionalValue)));
        break;
    }

    setAddSubtractResult(result.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }));
  };

  const calculateResult = () => {
    if (!startDate) return;

    switch (activeTab) {
      case 'countDays': {
        if (!endDate) return;

        let start, end;

        if (includeTime) {
          // Parse dates with time
          const [startHours, startMinutes] = startTime.split(':').map(Number);
          const [endHours, endMinutes] = endTime.split(':').map(Number);

          start = new Date(startDate);
          start.setHours(startHours, startMinutes, 0);

          end = new Date(endDate);
          end.setHours(endHours, endMinutes, 0);

          // Calculate difference in milliseconds
          const diffMs = Math.abs(end.getTime() - start.getTime());
          const totalDays = diffMs / (1000 * 60 * 60 * 24);
          const totalHours = diffMs / (1000 * 60 * 60);
          const totalMinutes = diffMs / (1000 * 60);
          const totalSeconds = diffMs / 1000;

          // Extract components
          const days = Math.floor(totalDays);
          const hours = Math.floor(totalHours % 24);
          const minutes = Math.floor(totalMinutes % 60);
          const seconds = Math.floor(totalSeconds % 60);

          // Format result
          let resultText = '';
          if (days > 0) resultText += `${days} day${days !== 1 ? 's' : ''} `;
          if (hours > 0 || days > 0) resultText += `${hours} hour${hours !== 1 ? 's' : ''} `;
          if (minutes > 0 || hours > 0 || days > 0) resultText += `${minutes} minute${minutes !== 1 ? 's' : ''} `;
          resultText += `${seconds} second${seconds !== 1 ? 's' : ''}`;

          setResult(resultText);
          setDetailedResult({
            years: parseFloat((totalDays / 365).toFixed(2)),
            months: parseFloat((totalDays / 30.44).toFixed(2)),
            weeks: parseFloat((totalDays / 7).toFixed(1)),
            days: totalDays,
            hours,
            minutes,
            seconds,
            totalDays,
            totalHours,
            totalMinutes,
            totalSeconds
          });
        } else {
          // Original calculation without time
          start = new Date(startDate);
          end = new Date(endDate);
          const totalDays = Math.abs(differenceInDays(end, start)) + (includeEndDate ? 1 : 0);

          setResult(`${totalDays} days`);
          setDetailedResult({
            years: parseFloat((totalDays / 365).toFixed(2)),
            months: parseFloat((totalDays / 30.44).toFixed(2)),
            weeks: parseFloat((totalDays / 7).toFixed(1)),
            days: totalDays
          });
        }
        break;
      }
      case 'addDays': {
        if (!daysToAdd) return;

        // Create date object from input
        const start = new Date(startDate);

        // Add time if included
        if (includeTime) {
          const [hours, minutes] = startTime.split(':').map(Number);
          start.setHours(hours, minutes, 0);
        }

        let result;
        const multiplier = isAddingDays ? 1 : -1;

        // Apply the calculation based on the selected time unit
        switch (timeUnit) {
          case 'days':
            result = new Date(start.setDate(start.getDate() + (multiplier * daysToAdd)));
            break;
          case 'weeks':
            result = new Date(start.setDate(start.getDate() + (multiplier * daysToAdd * 7)));
            break;
          case 'months':
            result = new Date(start.setMonth(start.getMonth() + (multiplier * daysToAdd)));
            break;
          case 'years':
            result = new Date(start.setFullYear(start.getFullYear() + (multiplier * daysToAdd)));
            break;
        }

        // Format the result with or without time
        if (includeTime) {
          setAddSubtractResult(result.toLocaleString('en-GB', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }));

          // Set end date and time for potential further calculations
          setEndDate(result.toISOString().split('T')[0]);
          setEndTime(`${result.getHours().toString().padStart(2, '0')}:${result.getMinutes().toString().padStart(2, '0')}`);
        } else {
          setAddSubtractResult(result.toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          }));

          // Set end date for potential further calculations
          setEndDate(result.toISOString().split('T')[0]);
        }
        break;
      }
      case 'workdays': {
        if (!endDate) return;
        const start = new Date(startDate);
        const end = new Date(endDate);
        let workdays = 0;
        let skippedDates: { date: Date; reason: string }[] = [];
        let currentDate = new Date(start);

        while (currentDate <= end) {
          if (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
            skippedDates.push({ date: new Date(currentDate), reason: 'Weekend' });
          } else if (isHoliday(currentDate)) {
            const holiday = holidays.find(holiday =>
              holiday.date.getDate() === currentDate.getDate() &&
              holiday.date.getMonth() === currentDate.getMonth()
            );
            skippedDates.push({
              date: new Date(currentDate),
              reason: `Holiday: ${holiday?.name || 'Public Holiday'}`
            });
          } else {
            workdays++;
          }
          currentDate.setDate(currentDate.getDate() + 1);
        }

        setResult(`${workdays} workdays`);
        setDetailedResult({
          years: parseFloat((workdays / 260).toFixed(2)),
          months: parseFloat((workdays / 21.67).toFixed(2)),
          weeks: parseFloat((workdays / 5).toFixed(1)),
          days: workdays,
          totalDays: differenceInDays(end, start) + 1,
          note: `${includeEndDate ? 'Inclusive' : 'Exclusive'} of start and end dates`,
          skippedDates
        });
        break;
      }
      case 'addWorkdays': {
        const start = new Date(startDate);
        if (!workdaysToAdd) return;
        const workdays = isAddingDays ? Number(workdaysToAdd) : -Number(workdaysToAdd);
        let currentDate = new Date(start);
        let workdaysAdded = 0;
        let resultDate = new Date(start);

        while (workdaysAdded < Math.abs(workdays)) {
          resultDate.setDate(resultDate.getDate() + (workdays > 0 ? 1 : -1));
          if (resultDate.getDay() !== 0 && resultDate.getDay() !== 6 && !isHoliday(resultDate)) {
            workdaysAdded++;
          }
        }

        setResult(resultDate.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }));
        setDetailedResult({
          years: 0,
          months: parseFloat((Math.abs(workdays) / 21.67).toFixed(2)),
          weeks: parseFloat((Math.abs(workdays) / 5).toFixed(1)),
          days: Math.abs(workdays)
        });
        setEndDate(resultDate.toISOString().split('T')[0]);
        break;
      }
      case 'weekday': {
        const date = new Date(startDate);
        const weekday = date.toLocaleDateString('en-US', { weekday: 'long' });
        const isWorkday = date.getDay() !== 0 && date.getDay() !== 6 && !isHoliday(date);

        setResult(`${weekday}${isWorkday ? ' (Workday)' : ' (Non-workday)'}`);
        setDetailedResult({
          years: 0,
          months: 0,
          weeks: 0,
          days: isWorkday ? 1 : 0
        });
        break;
      }
      case 'age': {
        const birthDate = new Date(startDate);
        const today = new Date();
        const ageInYears = differenceInYears(today, birthDate);
        const ageInMonths = differenceInMonths(today, birthDate);
        const ageInDays = differenceInDays(today, birthDate);

        setResult(`${ageInYears} years old`);
        setDetailedResult({
          years: ageInYears,
          months: ageInMonths,
          weeks: parseFloat((ageInDays / 7).toFixed(1)),
          days: ageInDays
        });
        break;
      }
    }
  };

  const isHoliday = (date: Date) => {
    return holidays.some(holiday =>
      holiday.date.getDate() === date.getDate() &&
      holiday.date.getMonth() === date.getMonth()
    );
  };

  const getNextMonths = (date: Date, count: number) => {
    return Array.from({ length: count }, (_, i) => {
      const newDate = new Date(date);
      newDate.setMonth(date.getMonth() + i);
      return newDate;
    });
  };

  const navigateMonths = (direction: 'prev' | 'next') => {
    const newDate = new Date(calendarMonth);
    newDate.setMonth(calendarMonth.getMonth() + (direction === 'next' ? 1 : -1));
    setCalendarMonth(newDate);
  };

  const handleMonthChange = (direction: 'prev' | 'next') => {
    const newDate = new Date(calendarMonth);
    newDate.setMonth(calendarMonth.getMonth() + (direction === 'next' ? 1 : -1));
    setCalendarMonth(newDate);
  };

  const renderMonth = (monthIndex: number) => {
    const daysInMonth = getMonthDays(currentYear, monthIndex);
    const firstDay = getFirstDayOfMonth(currentYear, monthIndex);
    const days = [];

    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-9" />);
    }

    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${day} ${months[monthIndex].substring(0, 3)}`;
      const holiday = getHolidayForDate(dateStr, holidaysList);
      const isWeekend = (day + firstDay - 1) % 7 > 4;

      days.push(
        <div
          key={day}
          className={cn(
            "h-9 flex items-center justify-center text-sm relative cursor-pointer transition-colors group",
            "hover:bg-gray-50 dark:hover:bg-gray-800/50",
            isWeekend && "text-gray-500 dark:text-gray-400",
            holiday && holiday.type === 'federal' && "bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 font-medium",
            holiday && holiday.type === 'local' && "bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 font-medium",
            holiday && holiday.type === 'observance' && "bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 font-medium"
          )}
        >
          <div className="relative">
            {day}
            {holiday && (
              <div className={cn(
                "absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 rounded-full",
                holiday.type === 'federal' && "bg-red-500 dark:bg-red-400",
                holiday.type === 'local' && "bg-blue-500 dark:bg-blue-400",
                holiday.type === 'observance' && "bg-gray-500 dark:bg-gray-400"
              )} />
            )}
          </div>
          {holiday && (
            <div className="absolute hidden group-hover:block bottom-full left-1/2 -translate-x-1/2 mb-1 w-44 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg text-xs z-10 border dark:border-gray-700">
              <div className="font-medium mb-0.5">{holiday.name}</div>
              <div className="text-gray-500 dark:text-gray-400 capitalize text-[10px]">{holiday.type} Holiday</div>
            </div>
          )}
        </div>
      );
    }

    return (
      <div key={monthIndex} className="p-1.5 border rounded-lg bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 w-[252px] shadow-sm">
        <h3 className="text-center font-semibold text-base mb-1.5 text-gray-800 dark:text-gray-200">{months[monthIndex]}</h3>
        <div className="grid grid-cols-7 text-center text-xs mb-0.5 font-medium">
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
            <div key={day} className="text-gray-600 dark:text-gray-400">{day}</div>
          ))}
        </div>
        <div className="grid grid-cols-7">
          {days}
        </div>
      </div>
    );
  };

  return (
    <div className="w-full max-w-5xl mx-auto p-4 space-y-4">
      {/* Calculator Section */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3 space-y-6">
          <div className="space-y-2">
            <h1 className="text-2xl font-bold">Date Calculator</h1>
            <p className="text-gray-600 dark:text-gray-400">Calculate dates, add/subtract days, and more</p>
          </div>

        <div className="p-3 rounded-xl bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-800 dark:via-gray-750 dark:to-gray-700">
          <div className="flex flex-wrap gap-2 px-1 overflow-x-auto pb-2 -mx-3 px-3">
            <Button
              variant={activeTab === 'countDays' ? 'default' : 'outline'}
              onClick={() => setActiveTab('countDays')}
              className={cn(
                "flex items-center gap-2 h-9 px-4 transition-all whitespace-nowrap min-w-fit",
                activeTab === 'countDays'
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-white/90 dark:hover:bg-gray-800/90"
              )}
            >
              <CalendarIcon className="h-4 w-4" />
              Count Days
            </Button>
            <Button
              variant={activeTab === 'addDays' ? 'default' : 'outline'}
              onClick={() => setActiveTab('addDays')}
              className={cn(
                "flex items-center gap-2 h-9 px-4 transition-all whitespace-nowrap min-w-fit",
                activeTab === 'addDays'
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-white/90 dark:hover:bg-gray-800/90"
              )}
            >
              <PlusCircle className="h-4 w-4" />
              Add/Sub
            </Button>
            <Button
              variant={activeTab === 'workdays' ? 'default' : 'outline'}
              onClick={() => setActiveTab('workdays')}
              className={cn(
                "flex items-center gap-2 h-9 px-4 transition-all whitespace-nowrap min-w-fit",
                activeTab === 'workdays'
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-white/90 dark:hover:bg-gray-800/90"
              )}
            >
              <Briefcase className="h-4 w-4" />
              Workdays
            </Button>
            <Button
              variant={activeTab === 'addWorkdays' ? 'default' : 'outline'}
              onClick={() => setActiveTab('addWorkdays')}
              className={cn(
                "flex items-center gap-2 h-9 px-4 transition-all whitespace-nowrap min-w-fit",
                activeTab === 'addWorkdays'
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-white/90 dark:hover:bg-gray-800/90"
              )}
            >
              <Plus className="h-4 w-4" />
              Add Work
            </Button>
            <Button
              variant={activeTab === 'weekday' ? 'default' : 'outline'}
              onClick={() => setActiveTab('weekday')}
              className={cn(
                "flex items-center gap-2 h-9 px-4 transition-all whitespace-nowrap min-w-fit",
                activeTab === 'weekday'
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-white/90 dark:hover:bg-gray-800/90"
              )}
            >
              <CalendarDays className="h-4 w-4" />
              Weekday
            </Button>
            <Button
              variant={activeTab === 'age' ? 'default' : 'outline'}
              onClick={() => setActiveTab('age')}
              className={cn(
                "flex items-center gap-2 h-9 px-4 transition-all whitespace-nowrap min-w-fit",
                activeTab === 'age'
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-white/90 dark:hover:bg-gray-800/90"
              )}
            >
              <Baby className="h-4 w-4" />
              Age
            </Button>
            <Button
              variant={activeTab === 'timezone' ? 'default' : 'outline'}
              onClick={() => setActiveTab('timezone')}
              className={cn(
                "flex items-center gap-2 h-9 px-4 transition-all whitespace-nowrap min-w-fit",
                activeTab === 'timezone'
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-white/90 dark:hover:bg-gray-800/90"
              )}
            >
              <Clock className="h-4 w-4" />
              Time Zone
            </Button>
            <Button
              variant={activeTab === 'countdown' ? 'default' : 'outline'}
              onClick={() => setActiveTab('countdown')}
              className={cn(
                "flex items-center gap-2 h-9 px-4 transition-all whitespace-nowrap min-w-fit",
                activeTab === 'countdown'
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-white/90 dark:hover:bg-gray-800/90"
              )}
            >
              <Timer className="h-4 w-4" />
              Countdown
            </Button>
            <Button
              variant={activeTab === 'recurring' ? 'default' : 'outline'}
              onClick={() => setActiveTab('recurring')}
              className={cn(
                "flex items-center gap-2 h-9 px-4 transition-all whitespace-nowrap min-w-fit",
                activeTab === 'recurring'
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "hover:bg-white/90 dark:hover:bg-gray-800/90"
              )}
            >
              <CalendarDays className="h-4 w-4" />
              Recurring
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {/* Date inputs */}
          <div className="space-y-4">
            {/* Count Days & Workdays Tabs */}
            {(activeTab === 'countDays' || activeTab === 'workdays') && (
              <>
                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">Start Date</label>
                  <div className="flex flex-wrap gap-2 max-w-md">
                    <div className="flex-1">
                      <Input
                        type="date"
                        value={startDate}
                        onChange={(e) => handleInputChange(e, setStartDate)}
                        className="w-full"
                      />
                    </div>
                    {includeTime && (
                      <div className="w-32">
                        <Input
                          type="time"
                          value={startTime}
                          onChange={(e) => setStartTime(e.target.value)}
                          className="w-full"
                          step="1"
                        />
                      </div>
                    )}
                    <Button variant="outline" onClick={() => setToday('start')}>Today</Button>
                  </div>
                </div>

                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">End Date</label>
                  <div className="flex flex-wrap gap-2 max-w-md">
                    <div className="flex-1">
                      <Input
                        type="date"
                        value={endDate}
                        onChange={(e) => handleInputChange(e, setEndDate)}
                        className="w-full"
                      />
                    </div>
                    {includeTime && (
                      <div className="w-32">
                        <Input
                          type="time"
                          value={endTime}
                          onChange={(e) => setEndTime(e.target.value)}
                          className="w-full"
                          step="1"
                        />
                      </div>
                    )}
                    <Button variant="outline" onClick={() => setToday('end')}>Today</Button>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeTime"
                    checked={includeTime}
                    onCheckedChange={(checked) => setIncludeTime(checked as boolean)}
                  />
                  <label
                    htmlFor="includeTime"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Include time in calculations
                  </label>
                </div>

                {activeTab === 'countDays' && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeEndDate"
                      checked={includeEndDate}
                      onCheckedChange={(checked) => setIncludeEndDate(checked as boolean)}
                    />
                    <label
                      htmlFor="includeEndDate"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Include end date
                    </label>
                  </div>
                )}
              </>
            )}

            {/* Add/Sub Days Tab */}
            {activeTab === 'addDays' && (
              <>
                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">Start Date</label>
                  <div className="flex flex-wrap gap-2 max-w-md">
                    <div className="flex-1">
                      <Input
                        type="date"
                        value={startDate}
                        onChange={(e) => handleInputChange(e, setStartDate)}
                        className="w-full"
                      />
                    </div>
                    {includeTime && (
                      <div className="w-32">
                        <Input
                          type="time"
                          value={startTime}
                          onChange={(e) => setStartTime(e.target.value)}
                          className="w-full"
                          step="1"
                        />
                      </div>
                    )}
                    <Button variant="outline" onClick={() => setToday('start')}>Today</Button>
                  </div>
                </div>

                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">Number of {timeUnit}</label>
                  <div className="flex flex-wrap gap-2">
                    <Input
                      type="number"
                      value={daysToAdd}
                      onChange={(e) => handleNumberChange(e, setDaysToAdd)}
                      className="w-32"
                    />
                    <select
                      value={timeUnit}
                      onChange={(e) => setTimeUnit(e.target.value as 'days' | 'weeks' | 'months' | 'years')}
                      className="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm"
                    >
                      <option value="days">Days</option>
                      <option value="weeks">Weeks</option>
                      <option value="months">Months</option>
                      <option value="years">Years</option>
                    </select>
                    <div className="flex gap-2 flex-wrap">
                      <Button
                        variant={isAddingDays ? 'default' : 'outline'}
                        onClick={() => setIsAddingDays(true)}
                        className="flex items-center gap-1"
                      >
                        <Plus className="h-4 w-4" />
                        Add
                      </Button>
                      <Button
                        variant={!isAddingDays ? 'default' : 'outline'}
                        onClick={() => setIsAddingDays(false)}
                        className="flex items-center gap-1"
                      >
                        <Minus className="h-4 w-4" />
                        Subtract
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeTimeAddDays"
                    checked={includeTime}
                    onCheckedChange={(checked) => setIncludeTime(checked as boolean)}
                  />
                  <label
                    htmlFor="includeTimeAddDays"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Include time in calculations
                  </label>
                </div>
              </>
            )}

            {/* Add Work Days Tab */}
            {activeTab === 'addWorkdays' && (
              <>
                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">Start Date</label>
                  <div className="flex gap-2 max-w-xs">
                    <Input
                      type="date"
                      value={startDate}
                      onChange={(e) => handleInputChange(e, setStartDate)}
                      className="flex-1"
                    />
                    <Button variant="outline" onClick={() => setToday('start')}>Today</Button>
                  </div>
                </div>

                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">Number of Work Days</label>
                  <div className="flex gap-2 max-w-md">
                    <Input
                      type="number"
                      value={workdaysToAdd}
                      onChange={(e) => handleNumberChange(e, setWorkdaysToAdd)}
                      className="w-32"
                    />
                    <Button
                      variant={isAddingDays ? 'default' : 'outline'}
                      onClick={() => setIsAddingDays(true)}
                    >
                      Add
                    </Button>
                    <Button
                      variant={!isAddingDays ? 'default' : 'outline'}
                      onClick={() => setIsAddingDays(false)}
                    >
                      Subtract
                    </Button>
                  </div>
                </div>
              </>
            )}

            {/* Weekday Tab */}
            {activeTab === 'weekday' && (
              <div className="flex flex-col space-y-2">
                <label className="text-sm font-medium">Select Date</label>
                <div className="flex gap-2 max-w-xs">
                  <Input
                    type="date"
                    value={startDate}
                    onChange={(e) => handleInputChange(e, setStartDate)}
                    className="flex-1"
                  />
                  <Button variant="outline" onClick={() => setToday('start')}>Today</Button>
                </div>
              </div>
            )}

            {/* Age Calculator Tab */}
            {activeTab === 'age' && (
              <div className="flex flex-col space-y-2">
                <label className="text-sm font-medium">Birth Date</label>
                <div className="flex gap-2 max-w-xs">
                  <Input
                    type="date"
                    value={startDate}
                    onChange={(e) => handleInputChange(e, setStartDate)}
                    className="flex-1"
                  />
                </div>
              </div>
            )}

            {/* Time Zone Converter Tab */}
            {activeTab === 'timezone' && (
              <div className="space-y-4">
                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">Date & Time</label>
                  <div className="flex flex-wrap gap-2">
                    <Input
                      type="date"
                      value={timezoneDate}
                      onChange={(e) => setTimezoneDate(e.target.value)}
                      className="w-40"
                    />
                    <Input
                      type="time"
                      value={timezoneTime}
                      onChange={(e) => setTimezoneTime(e.target.value)}
                      className="w-32"
                      step="1"
                    />
                    <Button
                      variant="outline"
                      onClick={() => {
                        const now = new Date();
                        setTimezoneDate(now.toISOString().split('T')[0]);
                        setTimezoneTime(now.toTimeString().slice(0, 5));
                      }}
                    >
                      Now
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium">Source Time Zone</label>
                    <select
                      value={sourceTimezone}
                      onChange={(e) => setSourceTimezone(e.target.value)}
                      className="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm"
                    >
                      <option value="UTC">UTC (Coordinated Universal Time)</option>
                      <option value="America/New_York">Eastern Time (ET)</option>
                      <option value="America/Chicago">Central Time (CT)</option>
                      <option value="America/Denver">Mountain Time (MT)</option>
                      <option value="America/Los_Angeles">Pacific Time (PT)</option>
                      <option value="Europe/London">London (GMT/BST)</option>
                      <option value="Europe/Paris">Paris (CET/CEST)</option>
                      <option value="Europe/Berlin">Berlin (CET/CEST)</option>
                      <option value="Asia/Tokyo">Tokyo (JST)</option>
                      <option value="Asia/Shanghai">China (CST)</option>
                      <option value="Asia/Kolkata">India (IST)</option>
                      <option value="Australia/Sydney">Sydney (AEST/AEDT)</option>
                    </select>
                  </div>

                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium">Target Time Zone</label>
                    <select
                      value={targetTimezone}
                      onChange={(e) => setTargetTimezone(e.target.value)}
                      className="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm"
                    >
                      <option value="">Select target time zone</option>
                      <option value="UTC">UTC (Coordinated Universal Time)</option>
                      <option value="America/New_York">Eastern Time (ET)</option>
                      <option value="America/Chicago">Central Time (CT)</option>
                      <option value="America/Denver">Mountain Time (MT)</option>
                      <option value="America/Los_Angeles">Pacific Time (PT)</option>
                      <option value="Europe/London">London (GMT/BST)</option>
                      <option value="Europe/Paris">Paris (CET/CEST)</option>
                      <option value="Europe/Berlin">Berlin (CET/CEST)</option>
                      <option value="Asia/Tokyo">Tokyo (JST)</option>
                      <option value="Asia/Shanghai">China (CST)</option>
                      <option value="Asia/Kolkata">India (IST)</option>
                      <option value="Australia/Sydney">Sydney (AEST/AEDT)</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Countdown Timer Tab */}
            {activeTab === 'countdown' && (
              <div className="space-y-4">
                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">Event Title</label>
                  <Input
                    type="text"
                    value={countdownTitle}
                    onChange={(e) => setCountdownTitle(e.target.value)}
                    placeholder="e.g., Birthday, Anniversary, Deadline"
                    className="w-full"
                  />
                </div>

                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">Target Date & Time</label>
                  <div className="flex flex-wrap gap-2">
                    <Input
                      type="date"
                      value={countdownDate}
                      onChange={(e) => setCountdownDate(e.target.value)}
                      className="w-40"
                    />
                    <Input
                      type="time"
                      value={countdownTime}
                      onChange={(e) => setCountdownTime(e.target.value)}
                      className="w-32"
                    />
                  </div>
                </div>

                {countdownRemaining && (
                  <div className="mt-6 p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 rounded-lg border border-blue-100 dark:border-blue-800">
                    <h3 className="text-center text-lg font-medium mb-4">{countdownTitle || 'Time Remaining'}</h3>

                    <div className="grid grid-cols-4 gap-2 text-center">
                      <div className="p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg shadow-sm">
                        <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{countdownRemaining.days}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Days</p>
                      </div>
                      <div className="p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg shadow-sm">
                        <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{countdownRemaining.hours}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Hours</p>
                      </div>
                      <div className="p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg shadow-sm">
                        <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{countdownRemaining.minutes}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Minutes</p>
                      </div>
                      <div className="p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg shadow-sm">
                        <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{countdownRemaining.seconds}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Seconds</p>
                      </div>
                    </div>

                    <div className="mt-4 text-center">
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {countdownRemaining.totalSeconds > 0
                          ? 'Countdown in progress...'
                          : 'The countdown has ended!'}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Recurring Dates Tab */}
            {activeTab === 'recurring' && (
              <div className="space-y-4">
                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium">Event Title</label>
                  <Input
                    type="text"
                    value={recurringTitle}
                    onChange={(e) => setRecurringTitle(e.target.value)}
                    placeholder="e.g., Team Meeting, Bill Payment, Birthday"
                    className="w-full"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium">Start Date</label>
                    <Input
                      type="date"
                      value={recurringStartDate}
                      onChange={(e) => setRecurringStartDate(e.target.value)}
                      className="w-full"
                    />
                  </div>

                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium">End Date</label>
                    <Input
                      type="date"
                      value={recurringEndDate}
                      onChange={(e) => setRecurringEndDate(e.target.value)}
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium">Recurrence Type</label>
                    <select
                      value={recurringType}
                      onChange={(e) => setRecurringType(e.target.value as 'daily' | 'weekly' | 'monthly' | 'yearly')}
                      className="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                      <option value="yearly">Yearly</option>
                    </select>
                  </div>

                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium">Interval</label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        min="1"
                        value={recurringInterval}
                        onChange={(e) => setRecurringInterval(parseInt(e.target.value) || 1)}
                        className="w-20"
                      />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {recurringType === 'daily' && 'day(s)'}
                        {recurringType === 'weekly' && 'week(s)'}
                        {recurringType === 'monthly' && 'month(s)'}
                        {recurringType === 'yearly' && 'year(s)'}
                      </span>
                    </div>
                  </div>
                </div>

                {recurringDates.length > 0 && (
                  <div className="mt-6 p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 rounded-lg border border-blue-100 dark:border-blue-800">
                    <h3 className="text-center text-lg font-medium mb-4">{recurringTitle || 'Recurring Dates'}</h3>

                    <div className="max-h-60 overflow-y-auto p-2">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {recurringDates.map((date, index) => (
                          <div
                            key={index}
                            className="p-2 bg-white/70 dark:bg-gray-800/70 rounded-lg shadow-sm flex items-center justify-between"
                          >
                            <span className="text-sm font-medium">
                              {date.toLocaleDateString('en-US', {
                                weekday: 'short',
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              #{index + 1}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="mt-4 text-center">
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {recurringDates.length} occurrences found
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 mt-6">
            {activeTab === 'timezone' ? (
              <Button
                onClick={() => {
                  if (!timezoneDate || !timezoneTime || !sourceTimezone || !targetTimezone) {
                    alert('Please fill in all fields');
                    return;
                  }

                  try {
                    // Create date object in source timezone
                    const sourceDate = new Date(`${timezoneDate}T${timezoneTime}:00`);

                    // Format the date for display in both timezones
                    const sourceOptions: Intl.DateTimeFormatOptions = {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      timeZone: sourceTimezone,
                      timeZoneName: 'long'
                    };

                    const targetOptions: Intl.DateTimeFormatOptions = {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      timeZone: targetTimezone,
                      timeZoneName: 'long'
                    };

                    const sourceFormatted = new Intl.DateTimeFormat('en-US', sourceOptions).format(sourceDate);
                    const targetFormatted = new Intl.DateTimeFormat('en-US', targetOptions).format(sourceDate);

                    // Set the result
                    setResult(`Converted Time: ${targetFormatted}`);
                    setDetailedResult({
                      years: 0,
                      months: 0,
                      weeks: 0,
                      days: 0,
                      note: `From: ${sourceFormatted}\nTo: ${targetFormatted}`
                    });
                  } catch (error) {
                    console.error('Error converting timezone:', error);
                    alert('Error converting timezone. Please check your inputs.');
                  }
                }}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                disabled={!timezoneDate || !timezoneTime || !sourceTimezone || !targetTimezone}
              >
                Convert Time Zone
              </Button>
            ) : activeTab === 'countdown' ? (
              <Button
                onClick={() => {
                  if (!countdownDate || !countdownTime) {
                    alert('Please enter a target date and time');
                    return;
                  }

                  // Clear any existing interval
                  if (countdownInterval) {
                    clearInterval(countdownInterval);
                  }

                  // Set up the countdown
                  const targetDate = new Date(`${countdownDate}T${countdownTime}:00`);

                  // Function to update the countdown
                  const updateCountdown = () => {
                    const now = new Date();
                    const difference = targetDate.getTime() - now.getTime();

                    // If the target date is in the past
                    if (difference <= 0) {
                      setCountdownRemaining({
                        days: 0,
                        hours: 0,
                        minutes: 0,
                        seconds: 0,
                        totalSeconds: 0
                      });

                      // Clear the interval if it exists
                      if (countdownInterval) {
                        clearInterval(countdownInterval);
                        setCountdownInterval(null);
                      }
                      return;
                    }

                    // Calculate time units
                    const totalSeconds = Math.floor(difference / 1000);
                    const days = Math.floor(totalSeconds / (60 * 60 * 24));
                    const hours = Math.floor((totalSeconds % (60 * 60 * 24)) / (60 * 60));
                    const minutes = Math.floor((totalSeconds % (60 * 60)) / 60);
                    const seconds = Math.floor(totalSeconds % 60);

                    setCountdownRemaining({
                      days,
                      hours,
                      minutes,
                      seconds,
                      totalSeconds
                    });
                  };

                  // Initial update
                  updateCountdown();

                  // Set interval to update every second
                  const interval = setInterval(updateCountdown, 1000);
                  setCountdownInterval(interval);

                  // Set result for display
                  const formattedDate = targetDate.toLocaleString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  });

                  setResult(`Countdown to: ${countdownTitle || 'Event'} (${formattedDate})`);
                }}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                disabled={!countdownDate || !countdownTime}
              >
                Start Countdown
              </Button>
            ) : activeTab === 'recurring' ? (
              <Button
                onClick={() => {
                  if (!recurringStartDate || !recurringEndDate) {
                    alert('Please enter start and end dates');
                    return;
                  }

                  const startDate = new Date(recurringStartDate);
                  const endDate = new Date(recurringEndDate);

                  if (startDate > endDate) {
                    alert('Start date must be before end date');
                    return;
                  }

                  const dates: Date[] = [];
                  let currentDate = new Date(startDate);

                  // Calculate recurring dates based on type and interval
                  while (currentDate <= endDate) {
                    dates.push(new Date(currentDate));

                    switch (recurringType) {
                      case 'daily':
                        currentDate.setDate(currentDate.getDate() + recurringInterval);
                        break;
                      case 'weekly':
                        currentDate.setDate(currentDate.getDate() + (recurringInterval * 7));
                        break;
                      case 'monthly':
                        currentDate.setMonth(currentDate.getMonth() + recurringInterval);
                        break;
                      case 'yearly':
                        currentDate.setFullYear(currentDate.getFullYear() + recurringInterval);
                        break;
                    }
                  }

                  setRecurringDates(dates);

                  // Set result for display
                  const formattedStartDate = startDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  });

                  const formattedEndDate = endDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  });

                  setResult(`${dates.length} ${recurringTitle ? recurringTitle + ' ' : ''}occurrences found between ${formattedStartDate} and ${formattedEndDate}`);

                  // Set detailed result
                  setDetailedResult({
                    years: 0,
                    months: 0,
                    weeks: 0,
                    days: 0,
                    note: `Recurring every ${recurringInterval} ${recurringType}(s) from ${formattedStartDate} to ${formattedEndDate}`
                  });

                  // Highlight dates in calendar
                  setRangeStartDate(startDate);
                  setRangeEndDate(endDate);
                  setHighlightedDates(dates);
                }}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                disabled={!recurringStartDate || !recurringEndDate}
              >
                Calculate Recurring Dates
              </Button>
            ) : (
              <Button
                onClick={calculateResult}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
              >
                Calculate
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => {
                setStartDate('');
                setEndDate('');
                setDaysToAdd(0);
                setWorkdaysToAdd('');
                setDetailedResult(null);
                setResult(null);

                if (activeTab === 'timezone') {
                  setTimezoneDate('');
                  setTimezoneTime('');
                  setSourceTimezone('UTC');
                  setTargetTimezone('');
                }

                if (activeTab === 'countdown') {
                  if (countdownInterval) {
                    clearInterval(countdownInterval);
                    setCountdownInterval(null);
                  }
                  setCountdownTitle('');
                  setCountdownDate('');
                  setCountdownTime('');
                  setCountdownRemaining(null);
                }

                if (activeTab === 'recurring') {
                  setRecurringTitle('');
                  setRecurringStartDate('');
                  setRecurringEndDate('');
                  setRecurringInterval(1);
                  setRecurringDates([]);
                  setRangeStartDate(null);
                  setRangeEndDate(null);
                  setHighlightedDates([]);
                }
              }}
              className="flex items-center gap-2"
            >
              <Eraser className="h-4 w-4" />
              Clear
            </Button>

            {result && (
              <Button
                variant="outline"
                onClick={saveCalculation}
                className="flex items-center gap-2"
              >
                <PlusCircle className="h-4 w-4" />
                Save
              </Button>
            )}
          </div>

          {/* Result Display */}
          {activeTab === 'timezone' && result ? (
            <div className="mt-6 space-y-4">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 p-6 rounded-lg border border-blue-100 dark:border-blue-800">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg shadow-sm">
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Source Time</h3>
                    <p className="text-lg font-semibold">{detailedResult?.note?.split('\n')[0].replace('From: ', '')}</p>
                  </div>

                  <div className="p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg shadow-sm">
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Converted Time</h3>
                    <p className="text-lg font-semibold text-green-600 dark:text-green-400">{detailedResult?.note?.split('\n')[1].replace('To: ', '')}</p>
                  </div>
                </div>

                <div className="mt-6 flex justify-center">
                  <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/50 rounded-full text-sm text-blue-700 dark:text-blue-300">
                    <Clock className="h-4 w-4" />
                    <span>Time zone conversion complete</span>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-sm">
                <h3 className="font-medium mb-2">About Time Zones</h3>
                <p>Time zones are regions of the globe that observe a uniform standard time for legal, commercial, and social purposes. Time zones tend to follow the boundaries of countries and their subdivisions because it is convenient for areas in close commercial or other communication to keep the same time.</p>
              </div>
            </div>
          ) : activeTab === 'addDays' && addSubtractResult ? (
            <div className="mt-6 space-y-4">
              {/* Main Result */}
              <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg text-center border dark:border-gray-700">
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">Resulting Date</div>
                <div className="text-3xl font-bold text-blue-700 dark:text-blue-300">
                  {addSubtractResult}
                </div>
              </div>

              {/* Additional Calculation Section */}
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border dark:border-gray-700">
                <div className="flex items-center gap-2 mb-3">
                  <Plus className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Add/Subtract More Time</span>
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    value={additionalValue || ''}
                    onChange={(e) => setAdditionalValue(parseInt(e.target.value) || 0)}
                    className="w-24"
                    placeholder="Value"
                  />
                  <select
                    value={additionalUnit}
                    onChange={(e) => setAdditionalUnit(e.target.value as any)}
                    className="h-10 rounded-md border border-input bg-background px-3 py-2"
                  >
                    <option value="days">Days</option>
                    <option value="weeks">Weeks</option>
                    <option value="months">Months</option>
                    <option value="years">Years</option>
                  </select>
                  <Button
                    onClick={() => handleAdditionalCalculation(true)}
                    className="bg-green-500 hover:bg-green-600 text-white transition-colors gap-1"
                  >
                    <Plus className="h-4 w-4" /> Add
                  </Button>
                  <Button
                    onClick={() => handleAdditionalCalculation(false)}
                    className="bg-red-500 hover:bg-red-600 text-white transition-colors gap-1"
                  >
                    <Minus className="h-4 w-4" /> Subtract
                  </Button>
                </div>
              </div>
            </div>
          ) : result && (
            <>
              {activeTab === 'countDays' ? (
                // Original Count Days design
                <div className="space-y-4">
                  {/* Explanation Box */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <ul className="list-disc list-inside space-y-2 text-sm text-blue-700 dark:text-blue-300">
                      <li>Top boxes: Time period converted to days, weeks, months, and years</li>
                      <li>Bottom result: Traditional calendar breakdown</li>
                    </ul>
                  </div>

                  {/* Summary Boxes */}
                  <div className="grid grid-cols-4 gap-4">
                    <div className="bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/40 dark:to-pink-900/40 p-4 rounded-lg text-center">
                      <p className="text-2xl font-bold">{detailedResult?.days}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Total Days</p>
                    </div>
                    <div className="bg-gradient-to-br from-pink-100 to-blue-100 dark:from-pink-900/40 dark:to-blue-900/40 p-4 rounded-lg text-center">
                      <p className="text-2xl font-bold">{detailedResult?.weeks}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">As Weeks</p>
                    </div>
                    <div className="bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/40 dark:to-purple-900/40 p-4 rounded-lg text-center">
                      <p className="text-2xl font-bold">{detailedResult?.months}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">As Months</p>
                    </div>
                    <div className="bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/40 dark:to-blue-900/40 p-4 rounded-lg text-center">
                      <p className="text-2xl font-bold">{detailedResult?.years}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">As Years</p>
                    </div>
                  </div>

                  {/* Calendar Breakdown */}
                  <div className="mt-6">
                    <h3 className="font-bold mb-2">Calendar Breakdown</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {`${Math.floor(detailedResult?.years || 0)} years, ${Math.floor((detailedResult?.months || 0) % 12)} months, ${Math.floor((detailedResult?.weeks || 0) % 4)} weeks, ${Math.floor((detailedResult?.days || 0) % 7)} days${includeEndDate ? ' (inclusive)' : ''}`}
                      {includeTime && detailedResult?.hours !== undefined &&
                        `, ${detailedResult.hours} hours, ${detailedResult.minutes} minutes, ${detailedResult.seconds} seconds`
                      }
                    </p>
                  </div>

                  {/* Time Breakdown (when time is included) */}
                  {includeTime && detailedResult?.totalSeconds && (
                    <div className="mt-6">
                      <h3 className="font-bold mb-2">Time Breakdown</h3>
                      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                        <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg text-center">
                          <p className="text-xl font-bold">{Math.floor(detailedResult.totalSeconds).toLocaleString()}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Total Seconds</p>
                        </div>
                        <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg text-center">
                          <p className="text-xl font-bold">{Math.floor(detailedResult.totalMinutes).toLocaleString()}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Total Minutes</p>
                        </div>
                        <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg text-center">
                          <p className="text-xl font-bold">{Math.floor(detailedResult.totalHours).toLocaleString()}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Total Hours</p>
                        </div>
                        <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg text-center">
                          <p className="text-xl font-bold">{detailedResult.totalDays.toFixed(2)}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Total Days</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                // New design for other calculators
                <div className="space-y-4">
                  {activeTab === 'addWorkdays' ? (
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                      <p className="text-xl font-semibold">{result}</p>
                      {detailedResult && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                          {`Added ${Math.abs(detailedResult.days)} workdays`}
                        </p>
                      )}
                    </div>
                  ) : activeTab === 'weekday' ? (
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                      <p className="text-xl font-semibold">{result}</p>
                      {detailedResult && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                          {detailedResult.days === 1 ? '✅ This is a workday' : '❌ This is not a workday'}
                        </p>
                      )}
                    </div>
                  ) : (
                    <>
                      {/* Grid layout for workdays and age */}
                      <div className="grid grid-cols-4 gap-4">
                        <div className="bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/40 dark:to-pink-900/40 p-4 rounded-lg text-center">
                          <p className="text-2xl font-bold">{result}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {activeTab === 'workdays' ? 'Workdays' : 'Years'}
                          </p>
                        </div>
                        <div className="bg-gradient-to-br from-pink-100 to-blue-100 dark:from-pink-900/40 dark:to-blue-900/40 p-4 rounded-lg text-center">
                          <p className="text-2xl font-bold">{detailedResult?.weeks}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">As Weeks</p>
                        </div>
                        <div className="bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/40 dark:to-purple-900/40 p-4 rounded-lg text-center">
                          <p className="text-2xl font-bold">{detailedResult?.months}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">As Months</p>
                        </div>
                        <div className="bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/40 dark:to-blue-900/40 p-4 rounded-lg text-center">
                          <p className="text-2xl font-bold">{detailedResult?.years}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">As Years</p>
                        </div>
                      </div>

                      {/* Additional info for workdays */}
                      {activeTab === 'workdays' && detailedResult?.skippedDates && detailedResult.skippedDates.length > 0 && (
                        <div className="mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowSkippedDates((prev: boolean) => !prev)}
                            className="text-xs"
                          >
                            {showSkippedDates ? 'Hide' : 'Show'} Skipped Dates ({detailedResult.skippedDates.length})
                          </Button>

                          {showSkippedDates && (
                            <div className="mt-2 text-sm max-h-32 overflow-y-auto space-y-1">
                              {detailedResult.skippedDates.map((skip, idx) => (
                                <div key={idx} className="text-gray-600 dark:text-gray-400">
                                  {skip.date.toLocaleDateString('en-US', {
                                    weekday: 'long',
                                    day: 'numeric',
                                    month: 'long',
                                    year: 'numeric'
                                  })} - {skip.reason}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

        {/* Saved Calculations Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-950 rounded-lg shadow-sm border border-gray-200 dark:border-gray-800 p-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-lg">Saved Calculations</h3>
                {savedCalculations.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      if (confirm('Clear all saved calculations?')) {
                        setSavedCalculations([]);
                        localStorage.removeItem('savedDateCalculations');
                      }
                    }}
                    className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
                  >
                    <Eraser className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {savedCalculations.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <div className="mb-2">
                    <PlusCircle className="h-8 w-8 mx-auto opacity-50" />
                  </div>
                  <p>No saved calculations yet</p>
                  <p className="text-sm mt-1">Use the Save button after calculating to save your results</p>
                </div>
              ) : (
                <div className="space-y-2 max-h-[500px] overflow-y-auto pr-1">
                  {savedCalculations.map((calc) => (
                    <div
                      key={calc.id}
                      className="p-3 bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 hover:border-blue-300 dark:hover:border-blue-700 transition-colors"
                    >
                      <div className="flex justify-between items-start">
                        <h4 className="font-medium text-sm">{calc.title}</h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const updatedCalculations = savedCalculations.filter(c => c.id !== calc.id);
                            setSavedCalculations(updatedCalculations);
                            localStorage.setItem('savedDateCalculations', JSON.stringify(updatedCalculations));
                          }}
                          className="h-6 w-6 p-0 rounded-full text-gray-500 hover:text-red-600"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{calc.result}</p>
                      <div className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                        {new Date(calc.date).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Calendar Section - Moved below the calculator */}
      <div className="mt-8 space-y-4">
        <div className="flex justify-between mb-4">
          <h2 className="text-xl font-bold">Calendar</h2>
          <Button
            onClick={() => setShowFullCalendar(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 transition-colors"
          >
            <CalendarDays className="h-4 w-4" />
            View Full Calendar
          </Button>
        </div>

        {/* Calendar Legend */}
        {(selectedStartDate || selectedEndDate) && (
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex flex-wrap gap-4 text-sm">
              {selectedStartDate && (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded shadow-sm"></div>
                  <span className="text-gray-700 dark:text-gray-300">Start Date</span>
                </div>
              )}
              {selectedEndDate && (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded shadow-sm"></div>
                  <span className="text-gray-700 dark:text-gray-300">End Date</span>
                </div>
              )}
              {selectedStartDate && selectedEndDate && (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-600 rounded"></div>
                  <span className="text-gray-700 dark:text-gray-300">Date Range</span>
                </div>
              )}
              {getTotalDaysInRange() && (
                <div className="flex items-center gap-2 ml-auto">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    Total: {getTotalDaysInRange()} days
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-4">
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4">
            {renderFullMonthCalendar(selectedMonth)}
          </div>
        </div>

        {/* Full Calendar Dialog */}
        {showFullCalendar && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-7xl w-full max-h-[90vh] overflow-y-auto">
              <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-4">
                    <h2 className="text-2xl font-bold">Calendar {currentYear}</h2>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentYear(prev => prev - 1)}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentYear(prev => prev + 1)}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowFullCalendar(false)}
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                {/* Search and Filters */}
                <div className="flex gap-4 items-center">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder="Search holidays..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex gap-2">
                    {(['federal', 'local', 'observance'] as const).map(type => (
                      <Button
                        key={type}
                        variant={selectedTypes.includes(type) ? 'default' : 'outline'}
                        onClick={() => setSelectedTypes(prev =>
                          prev.includes(type)
                            ? prev.filter(t => t !== type)
                            : [...prev, type]
                        )}
                        className="capitalize"
                      >
                        {type}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-8">
                  {/* Calendar Grid */}
                  <div className="grid grid-cols-4 gap-1 justify-items-center content-start mx-auto max-w-[1020px]">
                    {Array.from({ length: 12 }, (_, i) => renderMonth(i))}
                  </div>

                  {/* Color Legend */}
                  <div className="flex justify-center gap-6">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500" />
                      <span>Federal Holidays</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                      <span>Local Holidays</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-gray-500" />
                      <span>Observances</span>
                    </div>
                  </div>

                  {/* Holiday List - Two Columns */}
                  <div className="grid grid-cols-2 gap-6">
                    {/* First Half of the Year */}
                    <div className="space-y-4">
                      <h3 className="font-semibold text-lg">January - June</h3>
                      <div className="space-y-2">
                        {[...quarterHolidays.q1, ...quarterHolidays.q2].map(holiday => (
                          <div
                            key={holiday.date + holiday.name}
                            className={cn(
                              "p-3 rounded-lg border",
                              holiday.type === 'federal' && "bg-red-50 dark:bg-red-900/20",
                              holiday.type === 'local' && "bg-blue-50 dark:bg-blue-900/20",
                              holiday.type === 'observance' && "bg-gray-50 dark:bg-gray-800"
                            )}
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{holiday.date}</span>
                              <span className={cn(
                                "text-xs px-2 py-1 rounded-full",
                                holiday.type === 'federal' && "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300",
                                holiday.type === 'local' && "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
                                holiday.type === 'observance' && "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                              )}>
                                {holiday.type}
                              </span>
                            </div>
                            <div className="mt-1">
                              {holiday.icon} {holiday.name}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Second Half of the Year */}
                    <div className="space-y-4">
                      <h3 className="font-semibold text-lg">July - December</h3>
                      <div className="space-y-2">
                        {[...quarterHolidays.q3, ...quarterHolidays.q4].map(holiday => (
                          <div
                            key={holiday.date + holiday.name}
                            className={cn(
                              "p-3 rounded-lg border",
                              holiday.type === 'federal' && "bg-red-50 dark:bg-red-900/20",
                              holiday.type === 'local' && "bg-blue-50 dark:bg-blue-900/20",
                              holiday.type === 'observance' && "bg-gray-50 dark:bg-gray-800"
                            )}
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{holiday.date}</span>
                              <span className={cn(
                                "text-xs px-2 py-1 rounded-full",
                                holiday.type === 'federal' && "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300",
                                holiday.type === 'local' && "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
                                holiday.type === 'observance' && "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                              )}>
                                {holiday.type}
                              </span>
                            </div>
                            <div className="mt-1">
                              {holiday.icon} {holiday.name}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
