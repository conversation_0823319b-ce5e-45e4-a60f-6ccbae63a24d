"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Copy } from "lucide-react";
import { toast } from "sonner";
import { ArrowRightLeft } from "lucide-react";

type UnitType =
  | "length"
  | "weight"
  | "volume"
  | "temperature"
  | "area"
  | "speed"
  | "digitalStorage"
  | "time"
  | "pressure";

interface UnitOption {
  value: string;
  label: string;
  conversion: number; // Base conversion rate to standard unit
}

const unitOptions: Record<UnitType, UnitOption[]> = {
  length: [
    { value: "m", label: "Meters", conversion: 1 },
    { value: "km", label: "Kilometers", conversion: 1000 },
    { value: "cm", label: "Centimeters", conversion: 0.01 },
    { value: "mm", label: "Millimeters", conversion: 0.001 },
    { value: "mi", label: "Miles", conversion: 1609.34 },
    { value: "yd", label: "Yards", conversion: 0.9144 },
    { value: "ft", label: "Feet", conversion: 0.3048 },
    { value: "in", label: "Inches", conversion: 0.0254 },
    { value: "nmi", label: "Nautical Miles", conversion: 1852 },
  ],
  weight: [
    { value: "kg", label: "Kilograms", conversion: 1 },
    { value: "g", label: "Grams", conversion: 0.001 },
    { value: "mg", label: "Milligrams", conversion: 0.000001 },
    { value: "lb", label: "Pounds", conversion: 0.453592 },
    { value: "oz", label: "Ounces", conversion: 0.0283495 },
    { value: "ct", label: "Carats", conversion: 0.0002 },
    { value: "st", label: "Stone", conversion: 6.35029 },
  ],
  volume: [
    { value: "l", label: "Liters", conversion: 1 },
    { value: "ml", label: "Milliliters", conversion: 0.001 },
    { value: "m3", label: "Cubic Meters", conversion: 1000 },
    { value: "gal", label: "Gallons (US)", conversion: 3.78541 },
    { value: "qt", label: "Quarts (US)", conversion: 0.946353 },
    { value: "pt", label: "Pints (US)", conversion: 0.473176 },
    { value: "cup", label: "Cups (US)", conversion: 0.236588 },
    { value: "galImp", label: "Gallons (Imperial)", conversion: 4.54609 },
    { value: "ptImp", label: "Pints (Imperial)", conversion: 0.568261 },
    { value: "fl-oz", label: "Fluid Ounces (US)", conversion: 0.0295735 },
  ],
  temperature: [
    { value: "c", label: "Celsius", conversion: 1 },
    { value: "f", label: "Fahrenheit", conversion: 1 },
    { value: "k", label: "Kelvin", conversion: 1 },
  ],
  area: [
    { value: "m2", label: "Square Meters", conversion: 1 },
    { value: "km2", label: "Square Kilometers", conversion: 1000000 },
    { value: "cm2", label: "Square Centimeters", conversion: 0.0001 },
    { value: "mm2", label: "Square Millimeters", conversion: 0.000001 },
    { value: "ha", label: "Hectares", conversion: 10000 },
    { value: "ac", label: "Acres", conversion: 4046.86 },
    { value: "ft2", label: "Square Feet", conversion: 0.092903 },
    { value: "mi2", label: "Square Miles", conversion: 2589988.11 },
    { value: "yd2", label: "Square Yards", conversion: 0.836127 },
    { value: "in2", label: "Square Inches", conversion: 0.00064516 },
  ],
  speed: [
    { value: "mps", label: "Meters per Second", conversion: 1 },
    { value: "kph", label: "Kilometers per Hour", conversion: 0.277778 },
    { value: "mph", label: "Miles per Hour", conversion: 0.44704 },
    { value: "fps", label: "Feet per Second", conversion: 0.3048 },
    { value: "knot", label: "Knots", conversion: 0.514444 },
  ],
  digitalStorage: [
    { value: "B", label: "Bytes", conversion: 1 },
    { value: "KB", label: "Kilobytes", conversion: 1024 },
    { value: "MB", label: "Megabytes", conversion: 1024 * 1024 },
    { value: "GB", label: "Gigabytes", conversion: 1024 * 1024 * 1024 },
    { value: "TB", label: "Terabytes", conversion: 1024 * 1024 * 1024 * 1024 },
    { value: "PB", label: "Petabytes", conversion: 1024 * 1024 * 1024 * 1024 * 1024 },
  ],
  time: [
    { value: "s", label: "Seconds", conversion: 1 },
    { value: "min", label: "Minutes", conversion: 60 },
    { value: "hr", label: "Hours", conversion: 3600 },
    { value: "d", label: "Days", conversion: 86400 },
    { value: "wk", label: "Weeks", conversion: 604800 },
    { value: "mo", label: "Months (avg)", conversion: 2629746 },
    { value: "yr", label: "Years (avg)", conversion: 31556952 },
  ],
  pressure: [
    { value: "Pa", label: "Pascals", conversion: 1 },
    { value: "kPa", label: "Kilopascals", conversion: 1000 },
    { value: "MPa", label: "Megapascals", conversion: 1000000 },
    { value: "bar", label: "Bar", conversion: 100000 },
    { value: "psi", label: "PSI (lb/in²)", conversion: 6894.76 },
    { value: "atm", label: "Atmosphere (std)", conversion: 101325 },
  ],
};

function convertTemperature(
  value: number,
  fromUnit: string,
  toUnit: string
): number {
  // First convert to Celsius
  let celsius;
  switch (fromUnit) {
    case "c":
      celsius = value;
      break;
    case "f":
      celsius = (value - 32) * (5 / 9);
      break;
    case "k":
      celsius = value - 273.15;
      break;
    default:
      return value;
  }

  // Then convert from Celsius to target unit
  switch (toUnit) {
    case "c":
      return celsius;
    case "f":
      return celsius * (9 / 5) + 32;
    case "k":
      return celsius + 273.15;
    default:
      return celsius;
  }
}

function convert(
  value: number,
  fromUnit: UnitOption,
  toUnit: UnitOption,
  unitType: UnitType
): number {
  if (unitType === "temperature") {
    return convertTemperature(value, fromUnit.value, toUnit.value);
  }

  // For other unit types, convert through the standard unit
  const standardValue = value * fromUnit.conversion;
  return standardValue / toUnit.conversion;
}

export default function UnitConverter() {
  const [unitType, setUnitType] = useState<UnitType>("length");
  const [inputValue, setInputValue] = useState<string>("1");
  const [fromUnit, setFromUnit] = useState<string>(unitOptions[unitType][0].value);
  const [toUnit, setToUnit] = useState<string>(unitOptions[unitType][1].value);

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard!");
  };

  const getResult = (): string => {
    const value = parseFloat(inputValue);
    if (isNaN(value)) return "Invalid input";

    const from = unitOptions[unitType].find((u) => u.value === fromUnit);
    const to = unitOptions[unitType].find((u) => u.value === toUnit);
    if (!from || !to) return "Invalid units";

    const result = convert(value, from, to, unitType);
    return result.toLocaleString(undefined, {
      maximumFractionDigits: 6,
      minimumFractionDigits: 0,
    });
  };

  return (
    <Card className="w-full max-w-4xl mx-auto p-8 bg-gradient-to-br from-white/80 via-white/60 to-white/40 dark:from-gray-900/80 dark:via-gray-900/60 dark:to-gray-900/40 backdrop-blur-xl border border-gray-200/50 dark:border-gray-800/50 shadow-2xl hover:shadow-3xl transition-all duration-300">
      <Tabs
        defaultValue="length"
        value={unitType}
        onValueChange={(v) => {
          setUnitType(v as UnitType);
          setFromUnit(unitOptions[v as UnitType][0].value);
          setToUnit(unitOptions[v as UnitType][1].value);
        }}
        className="w-full"
      >
        <TabsList className="grid grid-cols-3 lg:grid-cols-9 w-full mb-8 bg-gradient-to-r from-emerald-50 via-teal-50 to-cyan-50 dark:from-gray-800 dark:via-gray-800/90 dark:to-gray-800/80 p-1.5 rounded-xl">
          <TabsTrigger value="length" className="data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 rounded-lg transition-all duration-300 hover:text-emerald-600 dark:hover:text-emerald-400">Length</TabsTrigger>
          <TabsTrigger value="weight" className="data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 rounded-lg transition-all duration-300 hover:text-emerald-600 dark:hover:text-emerald-400">Weight</TabsTrigger>
          <TabsTrigger value="volume" className="data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 rounded-lg transition-all duration-300 hover:text-emerald-600 dark:hover:text-emerald-400">Volume</TabsTrigger>
          <TabsTrigger value="temperature" className="data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 rounded-lg transition-all duration-300 hover:text-emerald-600 dark:hover:text-emerald-400">Temperature</TabsTrigger>
          <TabsTrigger value="area" className="data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 rounded-lg transition-all duration-300 hover:text-emerald-600 dark:hover:text-emerald-400">Area</TabsTrigger>
          <TabsTrigger value="speed" className="data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 rounded-lg transition-all duration-300 hover:text-emerald-600 dark:hover:text-emerald-400">Speed</TabsTrigger>
          <TabsTrigger value="digitalStorage" className="data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 rounded-lg transition-all duration-300 hover:text-emerald-600 dark:hover:text-emerald-400">Digital Storage</TabsTrigger>
          <TabsTrigger value="time" className="data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 rounded-lg transition-all duration-300 hover:text-emerald-600 dark:hover:text-emerald-400">Time</TabsTrigger>
          <TabsTrigger value="pressure" className="data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 rounded-lg transition-all duration-300 hover:text-emerald-600 dark:hover:text-emerald-400">Pressure</TabsTrigger>
        </TabsList>

        {Object.keys(unitOptions).map((type) => (
          <TabsContent
            key={type}
            value={type}
            className="mt-0 space-y-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-[2fr,auto,2fr] gap-8 items-center">
              <div className="space-y-4">
                <div className="relative group">
                  <Input
                    type="number"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    className="text-lg bg-white/70 dark:bg-gray-800/70 border-gray-200/50 dark:border-gray-700/50 transition-all duration-300 group-hover:border-emerald-500/50 dark:group-hover:border-emerald-500/50 rounded-xl h-14"
                    placeholder="Enter value"
                  />
                  <div className="absolute inset-0 -z-10 bg-gradient-to-br from-emerald-500/20 via-teal-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity rounded-xl blur-xl"></div>
                </div>
                <Select value={fromUnit} onValueChange={setFromUnit}>
                  <SelectTrigger className="bg-white/70 dark:bg-gray-800/70 border-gray-200/50 dark:border-gray-700/50 h-14 rounded-xl hover:border-emerald-500/50 dark:hover:border-emerald-500/50 transition-all duration-300">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl border-gray-200/50 dark:border-gray-700/50">
                    {unitOptions[type as UnitType].map((unit) => (
                      <SelectItem key={unit.value} value={unit.value} className="hover:bg-emerald-50 dark:hover:bg-emerald-900/20">
                        {unit.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-center">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/30 dark:to-teal-900/30 flex items-center justify-center">
                  <ArrowRightLeft className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                </div>
              </div>

              <div className="space-y-4">
                <div className="relative group">
                  <div className="flex items-center space-x-2 bg-white/70 dark:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-xl p-4 h-14 group-hover:border-emerald-500/50 dark:group-hover:border-emerald-500/50 transition-all duration-300">
                    <div className="flex-1 text-lg font-medium">{getResult()}</div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleCopy(getResult())}
                      className="hover:bg-emerald-100 dark:hover:bg-emerald-900/20"
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="absolute inset-0 -z-10 bg-gradient-to-br from-emerald-500/20 via-teal-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity rounded-xl blur-xl"></div>
                </div>
                <Select value={toUnit} onValueChange={setToUnit}>
                  <SelectTrigger className="bg-white/70 dark:bg-gray-800/70 border-gray-200/50 dark:border-gray-700/50 h-14 rounded-xl hover:border-emerald-500/50 dark:hover:border-emerald-500/50 transition-all duration-300">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl border-gray-200/50 dark:border-gray-700/50">
                    {unitOptions[type as UnitType].map((unit) => (
                      <SelectItem key={unit.value} value={unit.value} className="hover:bg-emerald-50 dark:hover:bg-emerald-900/20">
                        {unit.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </Card>
  );
}
