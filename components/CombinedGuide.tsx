import { Guide } from "@/components/Guide";
import { ReplaceSpacesGuide } from "@/components/ReplaceSpacesGuide";
import BinaryGuide from "@/components/BinaryGuide";
import { Card } from "@/components/ui/card";
import { FileCode, Calendar, MessageSquareQuote, Wand2 } from "lucide-react";
import Link from "next/link";

export function CombinedGuide() {
  return (
    <section className="py-12 bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Comprehensive Guide
          </h2>
          <p className="text-slate-600 dark:text-gray-300 mt-4 text-lg">
            Explore all features and tools available in our text conversion and calculation tools.
          </p>
        </div>
        <div className="space-y-16">
          <Guide />
          <ReplaceSpacesGuide />
          <BinaryGuide />
          
          {/* Date Calculator Guide */}
          <div>
            <div className="flex items-center gap-3 mb-6">
              <Calendar className="h-8 w-8 text-purple-500" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Date Calculator Guide</h2>
            </div>
            <Card className="p-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Features</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li>• Calculate date differences</li>
                    <li>• Add or subtract days from a date</li>
                    <li>• Multiple date formats support</li>
                    <li>• Automatic timezone handling</li>
                    <li>• Business days calculation</li>
                  </ul>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/date-calculator" className="text-blue-500 hover:text-blue-600">Try Date Calculator →</Link>
              </div>
            </Card>
          </div>

          {/* Code Formatter Guide */}
          <div>
            <div className="flex items-center gap-3 mb-6">
              <FileCode className="h-8 w-8 text-red-500" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Code Formatter Guide</h2>
            </div>
            <Card className="p-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Features</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li>• Multiple language support</li>
                    <li>• Automatic indentation</li>
                    <li>• Syntax highlighting</li>
                    <li>• Error detection</li>
                    <li>• Copy formatted code</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-3">Supported Languages</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li>• JavaScript/TypeScript</li>
                    <li>• HTML/CSS</li>
                    <li>• Python</li>
                    <li>• Java</li>
                    <li>• And many more...</li>
                  </ul>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/code-formatter" className="text-blue-500 hover:text-blue-600">Try Code Formatter →</Link>
              </div>
            </Card>
          </div>

          {/* Paraphrase Tool Guide */}
          <div>
            <div className="flex items-center gap-3 mb-6">
              <MessageSquareQuote className="h-8 w-8 text-indigo-500" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Paraphrase Tool Guide</h2>
            </div>
            <Card className="p-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Features</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li>• AI-powered paraphrasing</li>
                    <li>• Multiple writing styles</li>
                    <li>• Maintains original meaning</li>
                    <li>• Improves readability</li>
                    <li>• Instant results</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-3">Writing Styles</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li>• Professional</li>
                    <li>• Casual</li>
                    <li>• Academic</li>
                    <li>• Creative</li>
                  </ul>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/paraphrase" className="text-blue-500 hover:text-blue-600">Try Paraphrase Tool →</Link>
              </div>
            </Card>
          </div>

          {/* Text Humanizer Guide */}
          <div>
            <div className="flex items-center gap-3 mb-6">
              <Wand2 className="h-8 w-8 text-pink-500" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Text Humanizer Guide</h2>
            </div>
            <Card className="p-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Features</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li>• AI-powered text transformation</li>
                    <li>• Three writing levels</li>
                    <li>• Natural language processing</li>
                    <li>• Word limit: 100 words</li>
                    <li>• Rate-limited for quality</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-3">Writing Levels</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li>• Basic: Simple and casual</li>
                    <li>• Medium: Balanced complexity</li>
                    <li>• Advanced: Professional tone</li>
                  </ul>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/humanizer" className="text-blue-500 hover:text-blue-600">Try Text Humanizer →</Link>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}