export type QueryOperator = 'equals' | 'contains' | 'greater' | 'less';
export type LogicalOperator = 'AND' | 'OR';

export interface QueryCondition {
  column: string;
  operator: QueryOperator;
  value: string;
}

export interface QueryGroup {
  operator: LogicalOperator;
  conditions: Array<QueryCondition | QueryGroup>;
}

export interface QueryBuilderProps {
  columns: string[];
  onQueryChange: (query: Query | null) => void;
  activeRows?: number;
  totalRows?: number;
}

export type Query = QueryGroup;
