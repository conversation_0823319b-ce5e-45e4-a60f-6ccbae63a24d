import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { 
  Wand2, 
  Plus, 
  X, 
  PlayCircle, 
  Save, 
  Trash2, 
  PlusCircle, 
  ChevronDown, 
  HelpCircle,
  Database
} from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Query, QueryOperator, QueryCondition, QueryGroup, LogicalOperator } from './types';
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";

interface ConditionGroupProps {
  group: Query<PERSON>roup;
  level?: number;
  onUpdate: (group: QueryGroup) => void;
  onDelete?: () => void;
  columns: string[];
}

const ConditionGroup: React.FC<ConditionGroupProps> = ({ 
  group, 
  level = 0, 
  onUpdate, 
  onDelete, 
  columns 
}) => {
  const safeGroup = {
    operator: group?.operator || 'AND',
    conditions: group?.conditions || []
  };

  const addCondition = () => {
    const newCondition: QueryCondition = {
      column: columns[0] || '',
      operator: 'equals',
      value: ''
    };
    onUpdate({
      ...safeGroup,
      conditions: [...safeGroup.conditions, newCondition]
    });
  };

  const addGroup = () => {
    const newGroup: QueryGroup = {
      operator: 'AND',
      conditions: []
    };
    onUpdate({
      ...safeGroup,
      conditions: [...safeGroup.conditions, newGroup]
    });
  };

  const updateCondition = (index: number, condition: QueryCondition | QueryGroup) => {
    const newConditions = [...safeGroup.conditions];
    newConditions[index] = condition;
    onUpdate({ ...safeGroup, conditions: newConditions });
  };

  const deleteCondition = (index: number) => {
    onUpdate({
      ...safeGroup,
      conditions: safeGroup.conditions.filter((_, i) => i !== index)
    });
  };

  return (
    <div className={`rounded-lg ${level > 0 ? 'ml-6 relative' : ''}`}>
      {level > 0 && (
        <div className="absolute -left-4 top-0 bottom-0 w-0.5 bg-border" />
      )}
      
      <div className="flex items-center gap-2 mb-3">
        <Select
          value={safeGroup.operator}
          onValueChange={(value: LogicalOperator) => onUpdate({ ...safeGroup, operator: value })}
        >
          <SelectTrigger className="w-[140px] h-9 text-sm">
            <div className="flex items-center gap-2">
              <Badge variant={safeGroup.operator === 'AND' ? 'default' : 'secondary'} className="text-xs">
                {safeGroup.operator}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="AND">Match ALL conditions</SelectItem>
            <SelectItem value="OR">Match ANY condition</SelectItem>
          </SelectContent>
        </Select>

        <div className="flex-1" />

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="h-9 px-3 text-sm"
            onClick={addCondition}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Filter
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="h-9 px-3 text-sm"
            onClick={addGroup}
          >
            <PlusCircle className="h-4 w-4 mr-1" />
            Add Group
          </Button>

          {onDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDelete}
              className="h-9 px-2 hover:bg-destructive/20 hover:text-destructive"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {safeGroup.conditions.map((condition, index) => (
          <div key={index}>
            {('operator' in condition && 'conditions' in condition) ? (
              <ConditionGroup
                group={condition as QueryGroup}
                level={level + 1}
                onUpdate={(newGroup) => updateCondition(index, newGroup)}
                onDelete={() => deleteCondition(index)}
                columns={columns}
              />
            ) : (
              <div className="flex items-center gap-2 bg-card p-3 rounded-lg border">
                <Select
                  value={(condition as QueryCondition).column}
                  onValueChange={(value) => 
                    updateCondition(index, { ...(condition as QueryCondition), column: value })
                  }
                >
                  <SelectTrigger className="w-[180px] h-9 text-sm">
                    <SelectValue placeholder="Select column" />
                  </SelectTrigger>
                  <SelectContent>
                    {columns.map((column) => (
                      <SelectItem key={column} value={column} className="text-sm">
                        {column}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={(condition as QueryCondition).operator}
                  onValueChange={(value: QueryOperator) => 
                    updateCondition(index, { ...(condition as QueryCondition), operator: value })
                  }
                >
                  <SelectTrigger className="w-[140px] h-9 text-sm">
                    <SelectValue placeholder="Condition" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="equals" className="text-sm">is equal to</SelectItem>
                    <SelectItem value="contains" className="text-sm">contains</SelectItem>
                    <SelectItem value="greater" className="text-sm">is greater than</SelectItem>
                    <SelectItem value="less" className="text-sm">is less than</SelectItem>
                  </SelectContent>
                </Select>

                <Input
                  placeholder="Enter value..."
                  value={(condition as QueryCondition).value}
                  onChange={(e) => updateCondition(index, { 
                    ...(condition as QueryCondition), 
                    value: e.target.value 
                  })}
                  className="flex-1 h-9 text-sm"
                />

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => deleteCondition(index)}
                  className="h-9 px-2 hover:bg-destructive/20 hover:text-destructive"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        ))}

        {safeGroup.conditions.length === 0 && (
          <div className="text-center py-6 text-sm text-muted-foreground bg-muted/10 rounded-lg border border-dashed">
            No filters added yet. Click "Add Filter" to start building your query.
          </div>
        )}
      </div>
    </div>
  );
};

interface ConditionGroupProps {
  group: QueryGroup;
  onUpdate: (group: QueryGroup) => void;
  columns: string[];
}

interface QueryBuilderProps {
  columns: Array<{ id: string; value: string }>;
  onQueryChange: (query: QueryGroup | null) => void;
  activeRows?: number;
  totalRows?: number;
  className?: string;
}

export function QueryBuilder({ columns, onQueryChange, activeRows, totalRows, className }: QueryBuilderProps) {
  const [query, setQuery] = useState<QueryGroup>({
    operator: 'AND',
    conditions: []
  });
  const [isOpen, setIsOpen] = useState(false);
  const [previewSQL, setPreviewSQL] = useState<string>('');
  const [isEditingSQL, setIsEditingSQL] = useState(false);
  const [editableSQL, setEditableSQL] = useState<string>('');
  const [copySuccess, setCopySuccess] = useState(false);
  const [showGuide, setShowGuide] = useState(false);

  useEffect(() => {
    if (query.conditions.length === 0) {
      setPreviewSQL('');
      setEditableSQL('');
      return;
    }

    const generateSQL = (group: QueryGroup): string => {
      if (group.conditions.length === 0) return '';

      const conditions = group.conditions.map((condition) => {
        if ('operator' in condition && 'conditions' in condition) {
          const nestedSQL = generateSQL(condition as QueryGroup);
          return nestedSQL ? `(${nestedSQL})` : '';
        }

        const typedCondition = condition as QueryCondition;
        let operator = '';
        switch (typedCondition.operator) {
          case 'equals': operator = '='; break;
          case 'contains': operator = 'CONTAINS'; break;
          case 'greater': operator = '>'; break;
          case 'less': operator = '<'; break;
        }
        return `${typedCondition.column} ${operator} "${typedCondition.value}"`;
      }).filter(Boolean);

      return conditions.join(` ${group.operator} `);
    };

    const columnsList = columns.map(col => col.value).join(', ');
    const whereClause = generateSQL(query);
    const sql = `SELECT ${columnsList}\nFROM data\nWHERE ${whereClause}`;
    setPreviewSQL(sql);
    setEditableSQL(sql);
  }, [query, columns]);

  const handleExecuteQuery = () => {
    onQueryChange(query.conditions.length > 0 ? query : null);
    setIsOpen(false);
  };

  const handleClearQuery = () => {
    setQuery({ operator: 'AND', conditions: [] });
    onQueryChange(null);
  };

  const handleSQLEdit = (sql: string) => {
    setEditableSQL(sql);
    // TODO: In the future, we can add SQL parsing to update the visual query builder
  };

  const handleCopySQL = async () => {
    try {
      await navigator.clipboard.writeText(previewSQL);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy SQL:', err);
    }
  };

  const QueryGuide = () => (
    <Dialog open={showGuide} onOpenChange={setShowGuide}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Advanced Query Builder Guide</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Basic Usage</h3>
            <ul className="list-disc pl-6 space-y-2">
              <li>Click "Add Filter" to create a new condition</li>
              <li>Select a column, operator, and value for each condition</li>
              <li>Use "Add Group" to create nested conditions</li>
              <li>Toggle between AND/OR operators to combine conditions</li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Operators</h3>
            <ul className="list-disc pl-6 space-y-2">
              <li><code>equals (=)</code>: Exact match</li>
              <li><code>contains</code>: Partial text match</li>
              <li><code>greater (&gt;)</code>: Greater than</li>
              <li><code>less (&lt;)</code>: Less than</li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Advanced Queries</h3>
            <div className="space-y-2">
              <p>You can create complex nested queries using groups:</p>
              <pre className="bg-muted p-2 rounded text-sm">
                {`SELECT column1, column2
FROM data
WHERE No. = "1" 
  OR (No. = "2" AND No. = "3" OR No. = "4")`}
              </pre>
              <p className="text-sm text-muted-foreground">To create this:</p>
              <ol className="list-decimal pl-6 text-sm">
                <li>Set top-level operator to OR</li>
                <li>Add first condition (No. = "1")</li>
                <li>Click "Add Group"</li>
                <li>Add remaining conditions in the group</li>
              </ol>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">SQL Editor</h3>
            <ul className="list-disc pl-6 space-y-2">
              <li>Click "Edit Query" to modify SQL directly</li>
              <li>Use the Copy button to copy the SQL query</li>
              <li>SQL preview shows the complete query with SELECT statement</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button 
            variant="outline" 
            size="default" 
            className={cn("flex items-center gap-2 whitespace-nowrap min-w-fit", className)}
          >
            <Database className="h-4 w-4" />
            Advanced Query
            {query.conditions.length > 0 && (
              <Badge variant="secondary" className="ml-1">
                {query.conditions.length}
              </Badge>
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Database className="h-5 w-5" />
                <span>Advanced Query Builder</span>
                {activeRows !== undefined && totalRows !== undefined && (
                  <Badge variant="secondary" className="text-xs">
                    Showing {activeRows} of {totalRows} rows
                  </Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowGuide(true)}
                className="gap-2"
              >
                <HelpCircle className="h-4 w-4" />
                Guide
              </Button>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <ConditionGroup
              group={query}
              onUpdate={setQuery}
              columns={columns.map(col => col.id)}
            />

            {previewSQL && (
              <>
                <Separator className="my-4" />
                <div className="bg-muted/20 p-3 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs font-mono">
                        SQL Preview
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={handleCopySQL}
                      >
                        {copySuccess ? (
                          <span className="text-green-500">Copied!</span>
                        ) : (
                          <span>Copy SQL</span>
                        )}
                      </Button>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 text-xs"
                      onClick={() => setIsEditingSQL(!isEditingSQL)}
                    >
                      {isEditingSQL ? 'Preview' : 'Edit Query'}
                    </Button>
                  </div>
                  {isEditingSQL ? (
                    <Textarea
                      value={editableSQL}
                      onChange={(e) => handleSQLEdit(e.target.value)}
                      className="font-mono text-sm min-h-[80px]"
                      placeholder="Edit your SQL query..."
                    />
                  ) : (
                    <code className="text-sm block whitespace-pre-wrap font-mono">
                      {previewSQL}
                    </code>
                  )}
                  {isEditingSQL && (
                    <p className="text-xs text-muted-foreground mt-2">
                      Note: You can use nested AND/OR operators with parentheses. Example: WHERE No. = "1" OR (No. = "2" AND No. = "3" OR No. = "4")
                    </p>
                  )}
                </div>
              </>
            )}
          </div>

          <DialogFooter className="gap-2">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearQuery}
                className="h-9"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Clear All
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={handleExecuteQuery}
                disabled={query.conditions.length === 0}
                className="h-9"
              >
                <PlayCircle className="h-4 w-4 mr-1" />
                Apply Filters
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <QueryGuide />
    </>
  );
}
