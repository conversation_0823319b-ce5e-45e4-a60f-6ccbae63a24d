'use client';

import { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PieController,
  ArcElement,
  LineElement,
  PointElement,
} from 'chart.js';
import { Bar, Pie, Line } from 'react-chartjs-2';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, BarChart } from "lucide-react";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PieController,
  ArcElement,
  LineElement,
  PointElement
);

interface DataVisualizationProps {
  data: any[];
  columns: string[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const DataVisualization = ({ data, columns, open, onOpenChange }: DataVisualizationProps) => {
  const [selectedChart, setSelectedChart] = useState<'bar' | 'pie' | 'line'>('bar');
  const [selectedColumn, setSelectedColumn] = useState<string>(columns[0] || '');

  const processDataForVisualization = () => {
    if (!data || !selectedColumn) return null;

    // Count frequency of values in selected column
    const frequency: { [key: string]: number } = {};
    data.forEach(row => {
      const value = String(row[selectedColumn]);
      frequency[value] = (frequency[value] || 0) + 1;
    });

    const labels = Object.keys(frequency);
    const values = Object.values(frequency);

    const chartData = {
      labels,
      datasets: [
        {
          label: selectedColumn,
          data: values,
          backgroundColor: [
            'rgba(255, 99, 132, 0.5)',
            'rgba(54, 162, 235, 0.5)',
            'rgba(255, 206, 86, 0.5)',
            'rgba(75, 192, 192, 0.5)',
            'rgba(153, 102, 255, 0.5)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };

    return chartData;
  };

  const chartData = processDataForVisualization();

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: `${selectedColumn} Distribution`,
      },
    },
  };

  if (!data || !columns.length) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[90vw] max-h-[90vh] h-[800px]">
        <DialogHeader>
          <DialogTitle>Data Visualization Dashboard</DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-full overflow-y-auto p-4">
          <Card className="p-6">
            <div className="flex justify-between items-center mb-6">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Select Column <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  {columns.map((column) => (
                    <DropdownMenuItem
                      key={column}
                      onClick={() => setSelectedColumn(column)}
                    >
                      {column}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <div className="flex gap-2">
                <Button
                  variant={selectedChart === 'bar' ? "default" : "outline"}
                  onClick={() => setSelectedChart('bar')}
                >
                  Bar
                </Button>
                <Button
                  variant={selectedChart === 'pie' ? "default" : "outline"}
                  onClick={() => setSelectedChart('pie')}
                >
                  Pie
                </Button>
                <Button
                  variant={selectedChart === 'line' ? "default" : "outline"}
                  onClick={() => setSelectedChart('line')}
                >
                  Line
                </Button>
              </div>
            </div>

            <div className="h-[400px]">
              {chartData && (
                <div className="relative w-full h-full">
                  {selectedChart === 'bar' && <Bar options={options} data={chartData} />}
                  {selectedChart === 'pie' && <Pie options={options} data={chartData} />}
                  {selectedChart === 'line' && <Line options={options} data={chartData} />}
                </div>
              )}
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Data Summary</h3>
            <div className="space-y-4">
              <div>
                <p className="font-medium">Total Records: {data.length}</p>
                <p className="font-medium">Total Columns: {columns.length}</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Column Names:</h4>
                <div className="grid grid-cols-2 gap-2">
                  {columns.map((column) => (
                    <div key={column} className="bg-muted p-2 rounded">
                      {column}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export { DataVisualization };
