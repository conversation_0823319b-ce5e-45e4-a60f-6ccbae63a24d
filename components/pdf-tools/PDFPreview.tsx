'use client';

import { useEffect, useRef, useState } from 'react';
import * as pdfjs from 'pdfjs-dist';
import { PDFDocumentProxy } from 'pdfjs-dist';
import { Loader2, AlertCircle, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';

// Initialize PDF.js worker
if (typeof window !== 'undefined' && !pdfjs.GlobalWorkerOptions.workerSrc) {
  try {
    // Use unpkg as the primary source - more reliable than CDN
    const workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

    // Set the worker source
    pdfjs.GlobalWorkerOptions.workerSrc = workerSrc;
    console.log('PDF.js worker initialized with version:', pdfjs.version);
  } catch (error) {
    console.error('Failed to initialize PDF.js worker:', error);
  }
}

interface PDFPreviewProps {
  file?: File | null;
  fileUrl?: string | null;
  pageNumber?: number;
  rotation?: number;
  width?: number;
  height?: number;
  className?: string;
}

export function PDFPreview({
  file,
  fileUrl,
  pageNumber = 1,
  rotation = 0,
  width = 300,
  height = 400,
  className = ''
}: PDFPreviewProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pdfDocument, setPdfDocument] = useState<PDFDocumentProxy | null>(null);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(pageNumber);

  // Load the PDF document when the file or fileUrl changes
  useEffect(() => {
    // Clear previous document if no source is provided
    if (!file && !fileUrl) {
      setPdfDocument(null);
      setTotalPages(0);
      return;
    }

    const loadPDF = async () => {
      try {
        setLoading(true);
        setError(null);

        let pdfData: ArrayBuffer | Uint8Array;

        if (file) {
          // Validate file size before attempting to load
          if (file.size === 0) {
            throw new Error('PDF file is empty');
          }

          // Check if file size is too large (over 100MB)
          if (file.size > 100 * 1024 * 1024) {
            throw new Error('PDF file is too large (max 100MB)');
          }

          pdfData = await file.arrayBuffer();
        } else if (fileUrl) {
          try {
            // Fetch the PDF from the URL
            console.log('Fetching PDF from URL:', fileUrl);

            // Special handling for blob URLs
            if (fileUrl.startsWith('blob:')) {
              console.log('Handling blob URL');
              const blob = await fetch(fileUrl).then(r => r.blob());

              if (!blob) {
                throw new Error('Failed to fetch blob data');
              }

              // Verify it's a PDF
              if (!blob.type.includes('pdf') && blob.type !== 'application/octet-stream') {
                console.warn('Unexpected blob type:', blob.type);
              }

              pdfData = await blob.arrayBuffer();
            } else {
              // Regular URL fetch
              const response = await fetch(fileUrl, {
                cache: 'no-store',
                headers: {
                  'Accept': 'application/pdf,application/octet-stream,*/*'
                }
              });

              if (!response.ok) {
                throw new Error(`Failed to fetch PDF: ${response.statusText}`);
              }

              // Check content type to ensure it's a PDF
              const contentType = response.headers.get('content-type');
              if (contentType && !contentType.includes('application/pdf') && !contentType.includes('octet-stream')) {
                console.warn('Unexpected content type:', contentType);
              }

              pdfData = await response.arrayBuffer();
            }

            // Verify we have data
            if (!pdfData || pdfData.byteLength === 0) {
              throw new Error('Received empty PDF data');
            }

            console.log('PDF data loaded successfully, size:', pdfData.byteLength);
          } catch (fetchError) {
            console.error('Error fetching PDF from URL:', fetchError);
            throw new Error(`Failed to fetch PDF: ${fetchError.message}`);
          }
        } else {
          throw new Error('No PDF source provided');
        }

        // Add a timeout to prevent hanging on corrupt files
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('PDF loading timed out')), 15000);
        });

        const pdf = await Promise.race([
          pdfjs.getDocument({ data: pdfData }).promise,
          timeoutPromise
        ]) as PDFDocumentProxy;

        setPdfDocument(pdf);
        setTotalPages(pdf.numPages);
        setCurrentPage(Math.min(pageNumber, pdf.numPages)); // Set to requested page or max available
      } catch (err) {
        console.error('Error loading PDF:', err);

        // Provide more detailed error messages based on the error type
        let errorMessage = 'Failed to load PDF preview';

        if (err instanceof Error) {
          console.log('Error type:', err.name, 'Message:', err.message);

          if (err.message.includes('timed out')) {
            errorMessage = 'PDF loading timed out. The file may be corrupted.';
          } else if (err.message.includes('password')) {
            errorMessage = 'This PDF is password-protected. Please unlock it first.';
          } else if (err.message.includes('fetch')) {
            errorMessage = `Failed to load PDF: ${err.message}`;
          } else if (err.message.includes('empty')) {
            errorMessage = 'The PDF file is empty or invalid.';
          } else if (err.message.includes('Invalid PDF')) {
            errorMessage = 'The file is not a valid PDF document.';
          } else if (err.message.includes('worker')) {
            errorMessage = 'Failed to load PDF.js worker. Please check your internet connection.';
          } else if (err.message.includes('network')) {
            errorMessage = 'Network error while loading PDF. Please check your internet connection.';
          } else {
            errorMessage = `Failed to load PDF: ${err.message}`;
          }
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    loadPDF();

    // Cleanup
    return () => {
      if (pdfDocument) {
        pdfDocument.destroy().catch(console.error);
      }
    };
  }, [file, fileUrl, pageNumber]);

  // Render the PDF page when the document, page number, or rotation changes
  useEffect(() => {
    if (!pdfDocument || !canvasRef.current) return;

    const renderPage = async () => {
      try {
        setLoading(true);

        // Ensure page number is valid
        const pageNum = Math.max(1, Math.min(currentPage, totalPages));

        // Get the page
        const page = await pdfDocument.getPage(pageNum);

        // Calculate scale to fit within the container
        const viewport = page.getViewport({ scale: 1, rotation });
        const scale = Math.min(
          width / viewport.width,
          height / viewport.height
        );

        // Apply the scale to the viewport
        const scaledViewport = page.getViewport({ scale, rotation });

        // Set canvas dimensions
        const canvas = canvasRef.current;
        const context = canvas.getContext('2d');

        if (!context) {
          throw new Error('Could not get canvas context');
        }

        canvas.width = scaledViewport.width;
        canvas.height = scaledViewport.height;

        // Render the page
        await page.render({
          canvasContext: context,
          viewport: scaledViewport,
        }).promise;
      } catch (err) {
        console.error('Error rendering PDF page:', err);
        setError('Failed to render PDF preview');
      } finally {
        setLoading(false);
      }
    };

    renderPage();
  }, [pdfDocument, currentPage, totalPages, rotation, width, height]);

  if (!file && !fileUrl) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}
        style={{ width, height }}
      >
        <p className="text-gray-500 dark:text-gray-400 text-sm">No PDF selected</p>
      </div>
    );
  }

  // If there's an error that persists, show a fallback display
  if (error && !loading) {
    return (
      <div
        className={`flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg p-4 ${className}`}
        style={{ width, height }}
      >
        <FileText className="h-12 w-12 text-gray-400 dark:text-gray-600 mb-2" />
        <p className="text-gray-500 dark:text-gray-400 text-sm text-center">
          {file ? file.name : fileUrl ? 'PDF Document' : 'Unknown PDF'}
        </p>
        <p className="text-red-500 dark:text-red-400 text-xs text-center mt-2">
          {error}
        </p>
        <Button
          variant="outline"
          size="sm"
          className="mt-3 text-xs"
          onClick={() => setError(null)}
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div
      className={`relative flex flex-col items-center justify-center bg-white dark:bg-gray-800 rounded-lg overflow-hidden ${className}`}
      style={{ width, height }}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 z-10">
          <Loader2 className="h-8 w-8 text-orange-500 animate-spin" />
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-red-50 dark:bg-red-900/20 z-10 p-4">
          <AlertCircle className="h-8 w-8 text-red-500 dark:text-red-400 mb-2" />
          <p className="text-red-500 dark:text-red-400 text-sm text-center px-4">{error}</p>

          {error.includes('worker') && (
            <div className="mt-2 text-xs text-gray-600 dark:text-gray-400 max-w-md">
              <p className="text-center">There was a problem loading the PDF viewer. This could be due to:</p>
              <ul className="list-disc pl-5 mt-1 text-left">
                <li>Network connectivity issues</li>
                <li>Content blocking by your browser or network</li>
                <li>Temporary CDN outage</li>
              </ul>
            </div>
          )}

          <Button
            variant="outline"
            size="sm"
            className="mt-3 text-xs"
            onClick={() => {
              setError(null);
              // Force reload the worker if there was a worker issue
              if (error.includes('worker')) {
                if (typeof window !== 'undefined') {
                  pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;
                }
              }
            }}
          >
            Retry
          </Button>
        </div>
      )}

      <canvas
        ref={canvasRef}
        className="max-w-full max-h-full object-contain"
      />

      {totalPages > 1 && (
        <div className="absolute bottom-2 left-0 right-0 text-center text-xs text-gray-500 dark:text-gray-400">
          Page {currentPage} of {totalPages}
        </div>
      )}
    </div>
  );
}
