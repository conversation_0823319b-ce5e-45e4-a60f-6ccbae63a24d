'use client';

import { useState, useRef } from 'react';
import { FileText, Upload, Trash2, AlertCircle, Download, FileImage, Archive } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { pdfToJpg, formatFileSize, downloadBlob, getPDFPageCount } from '@/lib/pdf-utils';
import JSZip from 'jszip';

interface PDFFile {
  id: string;
  name: string;
  size: number;
  file: File;
  pageCount?: number;
}

export function PDFToJPGTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [convertedImages, setConvertedImages] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [quality, setQuality] = useState<number[]>([80]);
  const [extractAllPages, setExtractAllPages] = useState<boolean>(true);
  const [selectedPages, setSelectedPages] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFile(selectedFiles[0]);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFile = async (selectedFile: File) => {
    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    try {
      // Get actual page count using our utility function
      const pageCount = await getPDFPageCount(selectedFile);

      const newFile: PDFFile = {
        id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: selectedFile.name,
        size: selectedFile.size,
        file: selectedFile,
        pageCount: pageCount
      };

      setFile(newFile);
      setConvertedImages([]);
      setError(null);
    } catch (err) {
      console.error('Error loading PDF:', err);
      toast.error("Error loading PDF", {
        description: "The selected file could not be processed. Please try another PDF file."
      });
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    // Only take the first file for conversion
    addFile(droppedFiles[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = () => {
    setFile(null);
    setConvertedImages([]);
    setError(null);
  };

  const validatePageRange = (range: string): boolean => {
    // Allow empty string
    if (!range.trim()) return true;
    
    // Check if it's a valid range format (e.g., "1-5,7,9-12")
    const rangePattern = /^(\d+(-\d+)?)(,\d+(-\d+)?)*$/;
    if (!rangePattern.test(range)) return false;
    
    // Check if the ranges are valid (start <= end)
    const ranges = range.split(',');
    for (const r of ranges) {
      if (r.includes('-')) {
        const [start, end] = r.split('-').map(Number);
        if (start > end) return false;
        if (file?.pageCount && (start > file.pageCount || end > file.pageCount)) return false;
      } else {
        const page = Number(r);
        if (file?.pageCount && page > file.pageCount) return false;
      }
    }
    
    return true;
  };

  const handleConvertPDF = async () => {
    if (!file) {
      setError("Please add a PDF file to convert.");
      return;
    }

    if (!extractAllPages && !selectedPages.trim()) {
      setError("Please specify page numbers or select 'Extract all pages'.");
      return;
    }

    if (!extractAllPages && !validatePageRange(selectedPages)) {
      setError("Invalid page range format. Use comma-separated page numbers or ranges (e.g., '1-3,5,7-9').");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);
    setConvertedImages([]);

    try {
      // Determine which pages to extract
      let pagesToExtract: number[] = [];
      const pageCount = file.pageCount || 0;
      
      if (extractAllPages) {
        // Extract all pages individually
        pagesToExtract = Array.from({ length: pageCount }, (_, i) => i);
      } else {
        // Extract specific pages based on the range
        const ranges = selectedPages.split(',');
        
        for (const range of ranges) {
          if (range.includes('-')) {
            const [start, end] = range.split('-').map(num => parseInt(num.trim()) - 1); // Convert to 0-based index
            for (let i = start; i <= end; i++) {
              if (i >= 0 && i < pageCount && !pagesToExtract.includes(i)) {
                pagesToExtract.push(i);
              }
            }
          } else {
            const pageIndex = parseInt(range.trim()) - 1; // Convert to 0-based index
            if (pageIndex >= 0 && pageIndex < pageCount && !pagesToExtract.includes(pageIndex)) {
              pagesToExtract.push(pageIndex);
            }
          }
        }
      }
      
      // Use the utility function to convert PDF to JPG
      const imageBlobs = await pdfToJpg(
        file.file,
        pagesToExtract,
        2.0, // Scale factor
        quality[0] / 100, // Quality (0-1)
        (progress) => setProgress(progress)
      );
      
      // Create URLs for each blob
      const urls = imageBlobs.map(blob => URL.createObjectURL(blob));
      
      setConvertedImages(urls);
      toast.success("Success!", {
        description: `Your PDF has been converted to ${urls.length} JPG images.`
      });
    } catch (err) {
      console.error('Error converting PDF to JPG:', err);
      setError("An error occurred while converting the PDF. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadImage = (url: string, index: number) => {
    if (!file) return;
    
    const fileName = `${file.name.replace('.pdf', '')}_page_${index + 1}.jpg`;
    
    // Create a blob from the URL and download it
    fetch(url)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName));
  };

  const downloadAllImages = async () => {
    if (!file || convertedImages.length === 0) return;
    
    // If there's only one image, download it directly
    if (convertedImages.length === 1) {
      downloadImage(convertedImages[0], 0);
      return;
    }
    
    // For multiple images, create a ZIP file
    try {
      toast.loading("Creating ZIP file...");
      
      const zip = new JSZip();
      const baseName = file.name.replace('.pdf', '');
      
      // Add each image to the ZIP file
      for (let i = 0; i < convertedImages.length; i++) {
        const url = convertedImages[i];
        const response = await fetch(url);
        const blob = await response.blob();
        zip.file(`${baseName}_page_${i + 1}.jpg`, blob);
      }
      
      // Generate the ZIP file
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      
      // Download the ZIP file
      downloadBlob(zipBlob, `${baseName}_images.zip`);
      
      toast.dismiss();
      toast.success("ZIP file created successfully!");
    } catch (err) {
      console.error('Error creating ZIP file:', err);
      toast.dismiss();
      toast.error("Failed to create ZIP file. Downloading images individually...");
      
      // Fall back to downloading images individually
      convertedImages.forEach((url, index) => {
        setTimeout(() => {
          downloadImage(url, index);
        }, index * 500); // Stagger downloads to avoid browser issues
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-pink-500 bg-pink-50 dark:bg-pink-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf"
            className="hidden"
          />

          <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop a PDF File Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={handleBrowseClick}
            variant="outline"
            className="border-pink-300 dark:border-pink-700 hover:bg-pink-50 dark:hover:bg-pink-900/20"
          >
            <Upload className="mr-2 h-4 w-4" />
            Browse Files
          </Button>
        </div>
      )}

      {/* Selected File */}
      {file && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Selected PDF</h3>
            <Button
              onClick={removeFile}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-red-500"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-pink-100 dark:bg-pink-900/30 flex items-center justify-center">
                <FileText className="h-5 w-5 text-pink-600 dark:text-pink-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(file.size)} • {file.pageCount} pages
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Conversion Options */}
      {file && !convertedImages.length && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Conversion Options</h3>
          </div>
          <div className="p-4 space-y-6">
            {/* Page Selection */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Page Selection</h4>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="extractAll" 
                  checked={extractAllPages}
                  onCheckedChange={(checked) => setExtractAllPages(checked as boolean)}
                />
                <Label htmlFor="extractAll">Convert all pages</Label>
              </div>
              
              {!extractAllPages && (
                <div className="space-y-2">
                  <Label htmlFor="pageRange">Page Range</Label>
                  <div className="flex items-center gap-2">
                    <input
                      id="pageRange"
                      placeholder="e.g., 1-3,5,7-9"
                      value={selectedPages}
                      onChange={(e) => setSelectedPages(e.target.value)}
                      disabled={extractAllPages}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    />
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Specify page numbers and/or ranges separated by commas.
                  </p>
                </div>
              )}
            </div>
            
            {/* Quality Setting */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Image Quality</h4>
                <span className="text-sm text-gray-500 dark:text-gray-400">{quality[0]}%</span>
              </div>
              
              <Slider
                value={quality}
                onValueChange={setQuality}
                min={10}
                max={100}
                step={5}
                className="w-full"
              />
              
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>Lower Quality (Smaller Files)</span>
                <span>Higher Quality (Larger Files)</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Processing...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Conversion Results */}
      {convertedImages.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Converted Images</h3>
            <Button
              onClick={downloadAllImages}
              variant="outline"
              size="sm"
              className="text-pink-600 border-pink-300 hover:bg-pink-50 dark:text-pink-400 dark:border-pink-700 dark:hover:bg-pink-900/20"
            >
              <Archive className="h-4 w-4 mr-1" />
              Download All
            </Button>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
              {convertedImages.map((url, index) => (
                <div key={index} className="relative group">
                  <img 
                    src={url} 
                    alt={`Page ${index + 1}`} 
                    className="w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700 object-cover aspect-[3/4]"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 rounded-lg flex items-center justify-center">
                    <Button
                      onClick={() => downloadImage(url, index)}
                      variant="ghost"
                      size="sm"
                      className="text-transparent group-hover:text-white opacity-0 group-hover:opacity-100 transition-all duration-300"
                    >
                      <Download className="h-5 w-5" />
                    </Button>
                  </div>
                  <div className="mt-1 text-xs text-center text-gray-500 dark:text-gray-400">
                    Page {index + 1}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center pt-4">
        {convertedImages.length === 0 && (
          <Button
            onClick={handleConvertPDF}
            disabled={!file || isProcessing}
            className="bg-pink-500 hover:bg-pink-600 text-white"
            size="lg"
          >
            <FileImage className="mr-2 h-5 w-5" />
            Convert to JPG
          </Button>
        )}
      </div>
    </div>
  );
}
