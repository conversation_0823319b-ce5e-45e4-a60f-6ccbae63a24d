'use client';

import { useState, useRef } from 'react';
import { Unlock, Upload, X, AlertCircle, Download, FileWarning, EyeOff, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { formatFileSize, downloadBlob, unlockPDF } from '@/lib/pdf-utils';

interface PDFFile {
  id: string;
  name: string;
  size: number;
  file: File;
}

export function PDFUnlockTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [unlockedPdfUrl, setUnlockedPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    setFile({
      id: `file-${Date.now()}`,
      name: selectedFile.name,
      size: selectedFile.size,
      file: selectedFile
    });
    
    setError(null);
    setUnlockedPdfUrl(null);
    
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      
      if (droppedFile.type !== 'application/pdf') {
        toast.error("Invalid file type", {
          description: "Only PDF files are allowed."
        });
        return;
      }

      setFile({
        id: `file-${Date.now()}`,
        name: droppedFile.name,
        size: droppedFile.size,
        file: droppedFile
      });
      
      setError(null);
      setUnlockedPdfUrl(null);
    }
  };

  const removeFile = () => {
    setFile(null);
    setUnlockedPdfUrl(null);
    setError(null);
  };

  const handleUnlockPDF = async () => {
    if (!file) {
      setError("Please add a password-protected PDF file to unlock.");
      return;
    }

    if (!password) {
      setError("Please enter the PDF password.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // Use the unlockPDF utility function
      const unlockedPdfBlob = await unlockPDF(
        file.file,
        password,
        (progress) => setProgress(progress)
      );

      // Create a URL for the blob
      const url = URL.createObjectURL(unlockedPdfBlob);

      setUnlockedPdfUrl(url);
      toast.success("Success!", {
        description: "Your PDF has been unlocked."
      });
    } catch (err) {
      console.error('Error unlocking PDF:', err);
      
      // Provide a more specific error message
      if (String(err).includes('password')) {
        setError("Incorrect password. Please try again.");
      } else {
        setError("An error occurred while unlocking the PDF. Please try again.");
      }
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadUnlockedPDF = () => {
    if (!unlockedPdfUrl || !file) return;

    // Create a filename based on the original file name
    const baseFileName = file.name.replace('.pdf', '');
    const fileName = `${baseFileName}_unlocked.pdf`;

    // Create a blob from the URL and download it
    fetch(unlockedPdfUrl)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName));
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf"
            className="hidden"
          />

          <Unlock className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop Password-Protected PDF File Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={() => fileInputRef.current?.click()}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            <Upload className="mr-2 h-4 w-4" />
            Select PDF File
          </Button>
        </div>
      )}

      {/* File Display */}
      {file && (
        <div className="border rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-full">
                <Unlock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{file.name}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">{formatFileSize(file.size)}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={removeFile}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Password Input */}
          <div className="space-y-4 pt-2">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">PDF Password</h3>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password">Enter the password to unlock this PDF</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter PDF password"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Progress Bar */}
          {isProcessing && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>Processing...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-2">
            {!unlockedPdfUrl ? (
              <Button
                onClick={handleUnlockPDF}
                disabled={isProcessing || !file || !password}
                className="bg-blue-500 hover:bg-blue-600 text-white flex-1"
              >
                <Unlock className="mr-2 h-4 w-4" />
                {isProcessing ? "Unlocking PDF..." : "Unlock PDF with Password"}
              </Button>
            ) : (
              <Button
                onClick={downloadUnlockedPDF}
                className="bg-green-500 hover:bg-green-600 text-white flex-1"
              >
                <Download className="mr-2 h-4 w-4" />
                Download Unlocked PDF
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Information Note */}
      <div className="rounded-lg border border-blue-200 bg-blue-50 dark:border-blue-900 dark:bg-blue-900/20 p-4">
        <div className="flex items-start">
          <FileWarning className="h-5 w-5 text-blue-600 dark:text-blue-500 mt-0.5 mr-3" />
          <div>
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-500">Important Information</h4>
            <p className="mt-1 text-sm text-blue-700 dark:text-blue-400">
              This tool removes password protection from PDF files. You must know the correct password to unlock the file. 
              All processing occurs in your browser, ensuring your file and password remain private and are never uploaded to any server.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 