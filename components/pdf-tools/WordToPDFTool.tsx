'use client';

import { useState, useRef } from 'react';
import { FileText, Upload, Trash2, AlertCircle, Download, FileType } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { wordToPdf, formatFileSize, downloadBlob } from '@/lib/pdf-utils';

interface WordFile {
  id: string;
  name: string;
  size: number;
  file: File;
}

export function WordToPDFTool() {
  const [file, setFile] = useState<WordFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFile(selectedFiles[0]);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFile = (selectedFile: File) => {
    // Check if the file is a Word document
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword', // .doc
      'application/vnd.oasis.opendocument.text', // .odt
      'text/rtf', // .rtf
    ];

    if (!validTypes.includes(selectedFile.type)) {
      toast.error("Invalid file type", {
        description: "Only Word documents (DOC, DOCX, ODT, RTF) are allowed."
      });
      return;
    }

    const newFile: WordFile = {
      id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: selectedFile.name,
      size: selectedFile.size,
      file: selectedFile
    };

    setFile(newFile);
    setPdfUrl(null);
    setError(null);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    // Only take the first file for conversion
    addFile(droppedFiles[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = () => {
    setFile(null);
    setPdfUrl(null);
    setError(null);
  };

  const handleConvertToPDF = async () => {
    if (!file) {
      setError("Please add a Word document to convert.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);
    setPdfUrl(null);

    try {
      // Use the utility function to convert Word to PDF
      const pdfBlob = await wordToPdf(
        file.file,
        (progress) => setProgress(progress)
      );
      
      // Create a URL for the blob
      const url = URL.createObjectURL(pdfBlob);
      
      setPdfUrl(url);
      toast.success("Success!", {
        description: `Your Word document has been converted to a PDF.`
      });
    } catch (err) {
      console.error('Error converting Word to PDF:', err);
      setError("An error occurred while converting the document. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadPDF = () => {
    if (!pdfUrl || !file) return;
    
    const fileName = file.name.replace(/\.(docx|doc|odt|rtf)$/i, '.pdf');
    
    // Create a blob from the URL and download it
    fetch(pdfUrl)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName));
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-teal-500 bg-teal-50 dark:bg-teal-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".doc,.docx,.odt,.rtf"
            className="hidden"
          />

          <FileType className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop a Word Document Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={handleBrowseClick}
            variant="outline"
            className="border-teal-300 dark:border-teal-700 hover:bg-teal-50 dark:hover:bg-teal-900/20"
          >
            <Upload className="mr-2 h-4 w-4" />
            Browse Files
          </Button>
        </div>
      )}

      {/* Selected File */}
      {file && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Selected Document</h3>
            <Button
              onClick={removeFile}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-red-500"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-teal-100 dark:bg-teal-900/30 flex items-center justify-center">
                <FileType className="h-5 w-5 text-teal-600 dark:text-teal-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(file.size)}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Conversion Notes */}
      {file && !pdfUrl && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Conversion Notes</h3>
          </div>
          <div className="p-4">
            <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
              <p>
                <strong>Document Formatting:</strong> Our converter will preserve text content, basic formatting, and page layout from your Word document.
              </p>
              <p>
                <strong>Supported Features:</strong> Text, basic formatting, tables, and simple images will be converted to PDF format.
              </p>
              <p>
                <strong>Best Results:</strong> For the most accurate conversion, use standard fonts and avoid complex formatting features.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Processing...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center pt-4">
        {pdfUrl ? (
          <Button
            onClick={downloadPDF}
            className="bg-teal-500 hover:bg-teal-600 text-white"
            size="lg"
          >
            <Download className="mr-2 h-5 w-5" />
            Download PDF
          </Button>
        ) : (
          <Button
            onClick={handleConvertToPDF}
            disabled={!file || isProcessing}
            className="bg-teal-500 hover:bg-teal-600 text-white"
            size="lg"
          >
            <FileText className="mr-2 h-5 w-5" />
            Convert to PDF
          </Button>
        )}
      </div>
    </div>
  );
}
