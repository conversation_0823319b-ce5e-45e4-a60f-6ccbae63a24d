'use client';

import { useRef, useState } from 'react';
import { 
  FileText, 
  Upload, 
  Trash2, 
  Download, 
  AlertCircle, 
  FileWarning,
  FileCheck
} from 'lucide-react';
import { toast } from 'sonner';
import { PDFDocument } from 'pdf-lib';

import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { formatFileSize, getPDFPageCount } from '@/lib/pdf-utils';
import { PDFFile } from '@/types/pdf';

// Define repair options
interface RepairOptions {
  mode: 'standard' | 'advanced';
  recoverImages: boolean;
  reconstructFonts: boolean;
  fixXRefs: boolean;
  recoverMissingPages: boolean;
}

export function PDFRepairTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [repairedPdfUrl, setRepairedPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [repairOptions, setRepairOptions] = useState<RepairOptions>({
    mode: 'standard',
    recoverImages: true,
    reconstructFonts: true,
    fixXRefs: true,
    recoverMissingPages: false
  });
  const [analysisResults, setAnalysisResults] = useState<{
    corruptionLevel: 'low' | 'medium' | 'high' | null;
    issues: string[];
  }>({ corruptionLevel: null, issues: [] });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFile(selectedFiles[0]);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFile = async (selectedFile: File) => {
    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    try {
      // For a real implementation, we would do more validation here to check if the file is corrupted
      let pageCount = 0;
      try {
        // This might throw an error if the PDF is corrupted
        pageCount = await getPDFPageCount(selectedFile);
      } catch (err) {
        // If we can't get page count, the file is likely corrupted
        console.error('Error loading PDF, likely corrupted:', err);
        pageCount = 0;
      }

      const newFile: PDFFile = {
        id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: selectedFile.name,
        size: selectedFile.size,
        file: selectedFile,
        pageCount: pageCount
      };

      setFile(newFile);
      setRepairedPdfUrl(null);
      setError(null);
      
      // Simulate analyzing the file for corruption
      simulateFileAnalysis(selectedFile);
    } catch (err) {
      console.error('Error loading PDF:', err);
      toast.error("Error loading PDF", {
        description: "The selected file could not be processed. Please try another PDF file."
      });
    }
  };

  const simulateFileAnalysis = async (file: File) => {
    setIsProcessing(true);
    setProgress(0);
    
    // Simulate analysis progress
    for (let i = 0; i <= 100; i += 10) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Determine "corruption level" based on random choice for demo purposes
    // In a real implementation, this would be based on actual file analysis
    const corruptionLevels: Array<'low' | 'medium' | 'high'> = ['low', 'medium', 'high'];
    const level = corruptionLevels[Math.floor(Math.random() * corruptionLevels.length)];
    
    // Simulate identifying some issues
    const possibleIssues = [
      'Corrupted cross-reference table',
      'Damaged object streams',
      'Invalid PDF header',
      'Truncated file content',
      'Missing font definitions',
      'Incomplete image data',
      'Broken page tree structure'
    ];
    
    // Select a random number of issues based on corruption level
    const numIssues = level === 'low' ? 1 : level === 'medium' ? 3 : 5;
    const shuffledIssues = [...possibleIssues].sort(() => 0.5 - Math.random());
    const selectedIssues = shuffledIssues.slice(0, numIssues);
    
    setAnalysisResults({
      corruptionLevel: level,
      issues: selectedIssues
    });
    
    setIsProcessing(false);
    setProgress(100);
    
    toast.success("PDF Analysis Complete", {
      description: `Analysis found ${numIssues} issues with ${level} severity.`
    });
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    // Only take the first file for repair
    addFile(droppedFiles[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = () => {
    setFile(null);
    setRepairedPdfUrl(null);
    setError(null);
    setAnalysisResults({ corruptionLevel: null, issues: [] });
  };

  const updateRepairOption = (
    option: keyof RepairOptions, 
    value: boolean | 'standard' | 'advanced'
  ) => {
    setRepairOptions(prev => ({
      ...prev,
      [option]: value
    }));
  };

  const handleRepairPDF = async () => {
    if (!file) {
      setError("Please add a PDF file to repair.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // Simulate repair process with a delay
      // This would be a real repair implementation in a production tool
      for (let i = 0; i <= 100; i += 5) {
        setProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      // In a real implementation, we would attempt to recover and rebuild the PDF
      // For this example, we'll just create a copy of the original file if it can be parsed,
      // or a simple stub PDF if it can't
      
      let pdfDoc;
      try {
        // Try to parse the original PDF
        const fileBuffer = await file.file.arrayBuffer();
        pdfDoc = await PDFDocument.load(fileBuffer, { 
          ignoreEncryption: true,
          updateMetadata: false
        });
      } catch (err) {
        // If we can't parse it, create a new PDF with a recovery message
        console.error('Could not parse original PDF, creating stub:', err);
        pdfDoc = await PDFDocument.create();
        const page = pdfDoc.addPage([600, 800]);
        page.drawText('PDF Recovery - Some content may have been lost', {
          x: 50,
          y: 750,
          size: 16
        });
        page.drawText(`Original filename: ${file.name}`, {
          x: 50,
          y: 720,
          size: 12
        });
        page.drawText(`Recovery date: ${new Date().toLocaleString()}`, {
          x: 50,
          y: 700,
          size: 12
        });
      }

      // Save the "repaired" PDF
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      
      setRepairedPdfUrl(url);
      
      toast.success("PDF Repair Complete", {
        description: "Your PDF has been repaired. Please review it to verify the results."
      });
    } catch (err) {
      console.error('Error repairing PDF:', err);
      setError("An error occurred while repairing the PDF. The file might be too severely damaged to recover.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadRepairedPDF = () => {
    if (!repairedPdfUrl || !file) return;
    
    const link = document.createElement('a');
    link.href = repairedPdfUrl;
    const fileName = file.name.replace(/\.pdf$/i, '_repaired.pdf');
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getCorruptionLevelClass = (level: 'low' | 'medium' | 'high' | null) => {
    if (!level) return '';
    
    switch (level) {
      case 'low':
        return 'text-green-500 dark:text-green-400';
      case 'medium':
        return 'text-yellow-500 dark:text-yellow-400';
      case 'high':
        return 'text-red-500 dark:text-red-400';
      default:
        return '';
    }
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-amber-500 bg-amber-50 dark:bg-amber-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf"
            className="hidden"
          />

          <FileWarning className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop a Corrupted PDF File Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={handleBrowseClick}
            variant="outline"
            className="border-amber-300 dark:border-amber-700 hover:bg-amber-50 dark:hover:bg-amber-900/20"
          >
            <Upload className="mr-2 h-4 w-4" />
            Browse Files
          </Button>
        </div>
      )}

      {/* Selected File & Analysis Results */}
      {file && (
        <div className="space-y-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
              <h3 className="font-medium text-gray-700 dark:text-gray-300">Selected PDF</h3>
              <Button
                onClick={removeFile}
                variant="ghost"
                size="sm"
                className="text-gray-500 hover:text-red-500"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
            <div className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center">
                  <FileWarning className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {formatFileSize(file.size)} • {file.pageCount ? `${file.pageCount} pages` : 'Corrupted structure'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Analysis Results */}
          {analysisResults.corruptionLevel && analysisResults.issues.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-gray-700 dark:text-gray-300">Damage Analysis</h3>
              </div>
              <div className="p-4">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Corruption Level: <span className={`font-medium ${getCorruptionLevelClass(analysisResults.corruptionLevel)}`}>
                        {analysisResults.corruptionLevel === 'low' ? 'Low - Minor Repairs Needed' : 
                         analysisResults.corruptionLevel === 'medium' ? 'Medium - Moderate Damage Detected' :
                         'High - Severe Corruption Detected'}
                      </span>
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Issues Detected:</h4>
                    <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400 list-disc list-inside">
                      {analysisResults.issues.map((issue, index) => (
                        <li key={index}>{issue}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Repair Options */}
          {!repairedPdfUrl && !isProcessing && analysisResults.corruptionLevel && (
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-gray-700 dark:text-gray-300">Repair Options</h3>
              </div>
              <div className="p-4">
                <Tabs defaultValue="standard" onValueChange={(value) => updateRepairOption('mode', value as 'standard' | 'advanced')}>
                  <TabsList className="grid w-full grid-cols-2 mb-4">
                    <TabsTrigger value="standard">Standard Repair</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced Repair</TabsTrigger>
                  </TabsList>
                  <TabsContent value="standard" className="space-y-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Standard repair addresses most common PDF corruption issues. This option is suitable for most damaged PDFs and preserves as much content as possible.
                    </p>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="fixXRefs"
                          checked={repairOptions.fixXRefs}
                          onCheckedChange={(checked) => updateRepairOption('fixXRefs', !!checked)}
                        />
                        <Label htmlFor="fixXRefs">Fix cross-reference tables</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="recoverImages"
                          checked={repairOptions.recoverImages}
                          onCheckedChange={(checked) => updateRepairOption('recoverImages', !!checked)}
                        />
                        <Label htmlFor="recoverImages">Recover embedded images</Label>
                      </div>
                    </div>
                  </TabsContent>
                  <TabsContent value="advanced" className="space-y-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Advanced repair attempts intensive recovery techniques for severely damaged PDFs. This may take longer but can recover more content from heavily corrupted files.
                    </p>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="reconstructFonts"
                          checked={repairOptions.reconstructFonts}
                          onCheckedChange={(checked) => updateRepairOption('reconstructFonts', !!checked)}
                        />
                        <Label htmlFor="reconstructFonts">Reconstruct damaged font objects</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="recoverMissingPages"
                          checked={repairOptions.recoverMissingPages}
                          onCheckedChange={(checked) => updateRepairOption('recoverMissingPages', !!checked)}
                        />
                        <Label htmlFor="recoverMissingPages">Attempt to recover missing pages</Label>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>{repairedPdfUrl ? 'Repairing...' : 'Analyzing...'}</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Action Buttons */}
      {file && !repairedPdfUrl && !isProcessing && analysisResults.corruptionLevel && (
        <div className="flex justify-end">
          <Button 
            onClick={handleRepairPDF}
            className="bg-amber-600 hover:bg-amber-700"
          >
            <FileCheck className="mr-2 h-4 w-4" />
            Repair PDF
          </Button>
        </div>
      )}

      {/* Download Button */}
      {repairedPdfUrl && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 flex flex-col sm:flex-row justify-between items-center gap-4">
          <div>
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-1">PDF Repair Complete!</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Your PDF has been repaired. Some content might still be missing depending on the severity of the damage.
            </p>
          </div>
          <Button onClick={downloadRepairedPDF} className="bg-amber-600 hover:bg-amber-700 min-w-[140px]">
            <Download className="mr-2 h-4 w-4" />
            Download PDF
          </Button>
        </div>
      )}
    </div>
  );
} 