'use client';

import { useState, useRef } from 'react';
import { FileText, Upload, X, Trash2, AlertCircle, Download, Scissors } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { getPDFPageCount, splitPDF, formatFileSize, downloadBlob } from '@/lib/pdf-utils';

interface PDFFile {
  id: string;
  name: string;
  size: number;
  file: File;
  pageCount?: number;
}

export function PDFSplitTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [splitPdfUrls, setSplitPdfUrls] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [splitMode, setSplitMode] = useState<'range' | 'all'>('range');
  const [pageRange, setPageRange] = useState<string>('');
  const [extractAllPages, setExtractAllPages] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFile(selectedFiles[0]);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFile = async (selectedFile: File) => {
    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    try {
      // Get actual page count using our utility function
      const pageCount = await getPDFPageCount(selectedFile);

      const newFile: PDFFile = {
        id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: selectedFile.name,
        size: selectedFile.size,
        file: selectedFile,
        pageCount: pageCount
      };

      setFile(newFile);
      setSplitPdfUrls([]);
      setError(null);
    } catch (err) {
      console.error('Error loading PDF:', err);
      toast.error("Error loading PDF", {
        description: "The selected file could not be processed. Please try another PDF file."
      });
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    // Only take the first file for splitting
    addFile(droppedFiles[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = () => {
    setFile(null);
    setSplitPdfUrls([]);
    setError(null);
  };

  // Using formatFileSize from pdf-utils.ts

  const validatePageRange = (range: string): boolean => {
    // Allow empty string
    if (!range.trim()) return true;

    // Check if it's a valid range format (e.g., "1-5,7,9-12")
    const rangePattern = /^(\d+(-\d+)?)(,\d+(-\d+)?)*$/;
    if (!rangePattern.test(range)) return false;

    // Check if the ranges are valid (start <= end)
    const ranges = range.split(',');
    for (const r of ranges) {
      if (r.includes('-')) {
        const [start, end] = r.split('-').map(Number);
        if (start > end) return false;
        if (file?.pageCount && (start > file.pageCount || end > file.pageCount)) return false;
      } else {
        const page = Number(r);
        if (file?.pageCount && page > file.pageCount) return false;
      }
    }

    return true;
  };

  const handleSplitPDF = async () => {
    if (!file) {
      setError("Please add a PDF file to split.");
      return;
    }

    if (splitMode === 'range' && !extractAllPages && !pageRange.trim()) {
      setError("Please specify a page range or select 'Extract all pages'.");
      return;
    }

    if (splitMode === 'range' && !extractAllPages && !validatePageRange(pageRange)) {
      setError("Invalid page range format. Use comma-separated page numbers or ranges (e.g., '1-3,5,7-9').");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);
    setSplitPdfUrls([]);

    try {
      // Determine which pages to extract
      let pagesToExtract: number[] = [];
      const pageCount = file.pageCount || 0;

      if (splitMode === 'all' || extractAllPages) {
        // Extract all pages individually
        pagesToExtract = Array.from({ length: pageCount }, (_, i) => i);
      } else {
        // Extract specific pages based on the range
        const ranges = pageRange.split(',');

        for (const range of ranges) {
          if (range.includes('-')) {
            const [start, end] = range.split('-').map(num => parseInt(num.trim()) - 1); // Convert to 0-based index
            for (let i = start; i <= end; i++) {
              if (i >= 0 && i < pageCount && !pagesToExtract.includes(i)) {
                pagesToExtract.push(i);
              }
            }
          } else {
            const pageIndex = parseInt(range.trim()) - 1; // Convert to 0-based index
            if (pageIndex >= 0 && pageIndex < pageCount && !pagesToExtract.includes(pageIndex)) {
              pagesToExtract.push(pageIndex);
            }
          }
        }
      }

      // Use the utility function to split the PDF
      const blobs = await splitPDF(
        file.file,
        pagesToExtract,
        (progress) => setProgress(progress)
      );

      // Create URLs for each blob
      const urls = blobs.map(blob => URL.createObjectURL(blob));

      setSplitPdfUrls(urls);
      toast.success("Success!", {
        description: `Your PDF has been split into ${urls.length} files.`
      });
    } catch (err) {
      console.error('Error splitting PDF:', err);
      setError("An error occurred while splitting the PDF. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadSplitPDF = (url: string, index: number) => {
    if (!file) return;

    const fileName = `${file.name.replace('.pdf', '')}_page_${index + 1}.pdf`;

    // Create a blob from the URL and download it
    fetch(url)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName));
  };

  const downloadAllPDFs = () => {
    if (!file) return;

    // Download each file individually with a slight delay to avoid browser issues
    splitPdfUrls.forEach((url, index) => {
      setTimeout(() => {
        downloadSplitPDF(url, index);
      }, index * 500); // Stagger downloads to avoid browser issues
    });

    toast.success("Downloading all files", {
      description: `${splitPdfUrls.length} files will be downloaded.`
    });
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf"
            className="hidden"
          />

          <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop a PDF File Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={handleBrowseClick}
            variant="outline"
            className="border-purple-300 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20"
          >
            <Upload className="mr-2 h-4 w-4" />
            Browse Files
          </Button>
        </div>
      )}

      {/* Selected File */}
      {file && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Selected PDF</h3>
            <Button
              onClick={removeFile}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-red-500"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <FileText className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(file.size)} • {file.pageCount || '...'} pages
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Split Options */}
      {file && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Split Options</h3>
          </div>
          <div className="p-4">
            <Tabs defaultValue="range" onValueChange={(value) => setSplitMode(value as 'range' | 'all')}>
              <TabsList className="mb-4">
                <TabsTrigger value="range">Split by Range</TabsTrigger>
                <TabsTrigger value="all">Extract All Pages</TabsTrigger>
              </TabsList>

              <TabsContent value="range" className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="extractAll"
                      checked={extractAllPages}
                      onCheckedChange={(checked) => setExtractAllPages(checked as boolean)}
                    />
                    <Label htmlFor="extractAll">Extract all pages individually</Label>
                  </div>

                  {!extractAllPages && (
                    <div className="space-y-2">
                      <Label htmlFor="pageRange">Page Range</Label>
                      <Input
                        id="pageRange"
                        placeholder="e.g., 1-3,5,7-9"
                        value={pageRange}
                        onChange={(e) => setPageRange(e.target.value)}
                        disabled={extractAllPages}
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Specify page numbers and/or ranges separated by commas.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="all">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Each page of your PDF will be extracted as a separate PDF file.
                </p>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Processing...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Split Results */}
      {splitPdfUrls.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Split Results</h3>
            <Button
              onClick={downloadAllPDFs}
              variant="outline"
              size="sm"
              className="text-green-600 border-green-300 hover:bg-green-50 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-900/20"
            >
              <Download className="h-4 w-4 mr-1" />
              Download All
            </Button>
          </div>
          <div className="p-4">
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {splitPdfUrls.map((url, index) => (
                <div key={index} className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                      <FileText className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Page {index + 1}
                    </span>
                  </div>
                  <Button
                    onClick={() => downloadSplitPDF(url, index)}
                    variant="ghost"
                    size="sm"
                    className="text-gray-500 hover:text-green-500"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center pt-4">
        {splitPdfUrls.length === 0 && (
          <Button
            onClick={handleSplitPDF}
            disabled={!file || isProcessing}
            className="bg-purple-500 hover:bg-purple-600 text-white"
            size="lg"
          >
            <Scissors className="mr-2 h-5 w-5" />
            Split PDF
          </Button>
        )}
      </div>
    </div>
  );
}
