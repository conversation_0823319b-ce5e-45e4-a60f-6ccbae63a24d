'use client';

import { useState, useRef } from 'react';
import { FileLock, Upload, X, AlertCircle, Download, Lock, EyeOff, Eye, FileWarning } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { formatFileSize, downloadBlob, protectPDF } from '@/lib/pdf-utils';

interface PDFFile {
  id: string;
  name: string;
  size: number;
  file: File;
}

export function PDFProtectTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [protectedPdfUrl, setProtectedPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [password, setPassword] = useState('');
  const [ownerPassword, setOwnerPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showOwnerPassword, setShowOwnerPassword] = useState(false);
  const [permissions, setPermissions] = useState({
    printing: true,
    modifying: false,
    copying: false,
    annotating: false,
    fillingForms: true,
    contentAccessibility: true,
    documentAssembly: false,
  });
  const [useOwnerPassword, setUseOwnerPassword] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    setFile({
      id: `file-${Date.now()}`,
      name: selectedFile.name,
      size: selectedFile.size,
      file: selectedFile
    });
    
    setError(null);
    setProtectedPdfUrl(null);
    
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      
      if (droppedFile.type !== 'application/pdf') {
        toast.error("Invalid file type", {
          description: "Only PDF files are allowed."
        });
        return;
      }

      setFile({
        id: `file-${Date.now()}`,
        name: droppedFile.name,
        size: droppedFile.size,
        file: droppedFile
      });
      
      setError(null);
      setProtectedPdfUrl(null);
    }
  };

  const removeFile = () => {
    setFile(null);
    setProtectedPdfUrl(null);
    setError(null);
  };

  const handleProtectPDF = async () => {
    if (!file) {
      setError("Please add a PDF file to protect.");
      return;
    }

    if (!password) {
      setError("Please enter a password.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // Use the protectPDF utility function
      const protectedPdfBlob = await protectPDF(
        file.file,
        password,
        useOwnerPassword ? ownerPassword : undefined,
        permissions,
        (progress) => setProgress(progress)
      );

      // Create a URL for the blob
      const url = URL.createObjectURL(protectedPdfBlob);

      setProtectedPdfUrl(url);
      toast.success("Success!", {
        description: "Your PDF has been password protected."
      });
    } catch (err) {
      console.error('Error protecting PDF:', err);
      setError("An error occurred while protecting the PDF. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadProtectedPDF = () => {
    if (!protectedPdfUrl || !file) return;

    // Create a filename based on the original file name
    const baseFileName = file.name.replace('.pdf', '');
    const fileName = `${baseFileName}_protected.pdf`;

    // Create a blob from the URL and download it
    fetch(protectedPdfUrl)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName));
  };

  const handlePermissionChange = (permission: keyof typeof permissions) => {
    setPermissions(prev => ({
      ...prev,
      [permission]: !prev[permission]
    }));
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf"
            className="hidden"
          />

          <FileLock className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop PDF File Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={() => fileInputRef.current?.click()}
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            <Upload className="mr-2 h-4 w-4" />
            Select PDF File
          </Button>
        </div>
      )}

      {/* File Display */}
      {file && (
        <div className="border rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-full">
                <FileLock className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{file.name}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">{formatFileSize(file.size)}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={removeFile}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Password Settings */}
          <div className="space-y-4 pt-2">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Password Protection</h3>
            
            <div className="space-y-4">
              {/* User Password */}
              <div className="space-y-2">
                <Label htmlFor="password">User Password (required for opening the document)</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter password"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Owner Password Option */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="useOwnerPassword"
                  checked={useOwnerPassword}
                  onCheckedChange={() => setUseOwnerPassword(!useOwnerPassword)}
                />
                <Label htmlFor="useOwnerPassword">Use different owner password</Label>
              </div>

              {/* Owner Password */}
              {useOwnerPassword && (
                <div className="space-y-2">
                  <Label htmlFor="ownerPassword">Owner Password (required for changing permissions)</Label>
                  <div className="relative">
                    <Input
                      id="ownerPassword"
                      type={showOwnerPassword ? "text" : "password"}
                      value={ownerPassword}
                      onChange={(e) => setOwnerPassword(e.target.value)}
                      placeholder="Enter owner password"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full"
                      onClick={() => setShowOwnerPassword(!showOwnerPassword)}
                    >
                      {showOwnerPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <h4 className="text-md font-medium text-gray-800 dark:text-gray-200">Permissions</h4>
              <p className="text-xs text-gray-500 dark:text-gray-400">Set what users are allowed to do with the document:</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="printing"
                    checked={permissions.printing}
                    onCheckedChange={() => handlePermissionChange('printing')}
                  />
                  <Label htmlFor="printing">Allow printing</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="modifying"
                    checked={permissions.modifying}
                    onCheckedChange={() => handlePermissionChange('modifying')}
                  />
                  <Label htmlFor="modifying">Allow modifying content</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="copying"
                    checked={permissions.copying}
                    onCheckedChange={() => handlePermissionChange('copying')}
                  />
                  <Label htmlFor="copying">Allow copying text/images</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="annotating"
                    checked={permissions.annotating}
                    onCheckedChange={() => handlePermissionChange('annotating')}
                  />
                  <Label htmlFor="annotating">Allow annotations</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="fillingForms"
                    checked={permissions.fillingForms}
                    onCheckedChange={() => handlePermissionChange('fillingForms')}
                  />
                  <Label htmlFor="fillingForms">Allow filling forms</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="contentAccessibility"
                    checked={permissions.contentAccessibility}
                    onCheckedChange={() => handlePermissionChange('contentAccessibility')}
                  />
                  <Label htmlFor="contentAccessibility">Allow screen readers</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="documentAssembly"
                    checked={permissions.documentAssembly}
                    onCheckedChange={() => handlePermissionChange('documentAssembly')}
                  />
                  <Label htmlFor="documentAssembly">Allow document assembly</Label>
                </div>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Progress Bar */}
          {isProcessing && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>Processing...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-2">
            {!protectedPdfUrl ? (
              <Button
                onClick={handleProtectPDF}
                disabled={isProcessing || !file || !password}
                className="bg-red-500 hover:bg-red-600 text-white flex-1"
              >
                <Lock className="mr-2 h-4 w-4" />
                {isProcessing ? "Encrypting PDF..." : "Protect PDF with Password"}
              </Button>
            ) : (
              <Button
                onClick={downloadProtectedPDF}
                className="bg-green-500 hover:bg-green-600 text-white flex-1"
              >
                <Download className="mr-2 h-4 w-4" />
                Download Protected PDF
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Warning About Encryption */}
      <div className="rounded-lg border border-amber-200 bg-amber-50 dark:border-amber-900 dark:bg-amber-900/20 p-4">
        <div className="flex items-start">
          <FileWarning className="h-5 w-5 text-amber-600 dark:text-amber-500 mt-0.5 mr-3" />
          <div>
            <h4 className="text-sm font-medium text-amber-800 dark:text-amber-500">Important Information</h4>
            <p className="mt-1 text-sm text-amber-700 dark:text-amber-400">
              Once you encrypt a PDF, you will need the password to open it. If you forget the password, we cannot recover it for you. Keep your password in a safe place.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 