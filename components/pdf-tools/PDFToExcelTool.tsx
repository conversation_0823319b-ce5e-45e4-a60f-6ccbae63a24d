'use client';

import React, { useState, useRef, useCallback } from 'react';
import { FileSpreadsheet, Upload, X, Trash2, Table, File, AlertTriangle, Settings, ExternalLink, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { PDFPreview } from './PDFPreview';
import { Card, CardContent } from '@/components/ui/card';
import {
  TooltipProvider,
} from "@/components/ui/tooltip";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Progress } from "@/components/ui/progress";
import { PDFDocument } from 'pdf-lib';
import type { PDFDocumentProxy } from 'pdfjs-dist';

type PDFFile = {
  file: File;
  previewUrl: string;
  pageCount: number;
  selectedPages: number[];
};

export function PDFToExcelTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const [extractionMode, setExtractionMode] = useState<'auto' | 'structured'>('auto');
  const [conversionProgress, setConversionProgress] = useState(0);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [tableSettings, setTableSettings] = useState({
    includeHeaders: true,
    detectMergedCells: true,
    preserveFormatting: true,
    skipEmptyRows: true,
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file drop
  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];
      handleFileSelection(droppedFile);
    }
  }, []);

  // Handle file selection
  const handleFileSelection = async (selectedFile: File) => {
    if (!selectedFile.name.toLowerCase().endsWith('.pdf')) {
      toast.error('Please select a PDF file');
      return;
    }

    if (selectedFile.size > 50 * 1024 * 1024) { // 50MB limit
      toast.error('File size exceeds 50MB limit');
      return;
    }

    try {
      // Create preview URL
      const previewUrl = URL.createObjectURL(selectedFile);

      // Get page count
      const arrayBuffer = await selectedFile.arrayBuffer();
      const pdfDoc = await PDFDocument.load(arrayBuffer);
      const pageCount = pdfDoc.getPageCount();

      setFile({
        file: selectedFile,
        previewUrl,
        pageCount,
        selectedPages: Array.from({ length: pageCount }, (_, i) => i + 1), // Select all pages by default
      });

      toast.success('PDF uploaded successfully');
    } catch (error) {
      console.error('Error loading PDF:', error);
      toast.error('Failed to load PDF file');
    }
  };

  // Handle file selection via file dialog
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelection(e.target.files[0]);
    }
  };

  // Remove file
  const removeFile = () => {
    if (file?.previewUrl) {
      URL.revokeObjectURL(file.previewUrl);
    }
    setFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Toggle page selection
  const togglePageSelection = (pageNumber: number) => {
    if (!file) return;

    setFile(prev => {
      if (!prev) return prev;

      const isSelected = prev.selectedPages.includes(pageNumber);
      const newSelectedPages = isSelected
        ? prev.selectedPages.filter(p => p !== pageNumber)
        : [...prev.selectedPages, pageNumber].sort((a, b) => a - b);

      return {
        ...prev,
        selectedPages: newSelectedPages
      };
    });
  };

  // Toggle all pages
  const toggleAllPages = (selectAll: boolean) => {
    if (!file) return;

    setFile(prev => {
      if (!prev) return prev;

      return {
        ...prev,
        selectedPages: selectAll
          ? Array.from({ length: prev.pageCount }, (_, i) => i + 1)
          : []
      };
    });
  };

  // Convert PDF to Excel
  const convertToExcel = async () => {
    if (!file) {
      toast.error('Please upload a PDF file first');
      return;
    }

    if (file.selectedPages.length === 0) {
      toast.error('Please select at least one page to convert');
      return;
    }

    setIsConverting(true);
    setConversionProgress(0);

    try {
      const [pdfjs, XLSX] = await Promise.all([
        import('pdfjs-dist'),
        import('xlsx')
      ]);

      // Initialize PDF.js worker
      if (typeof window !== 'undefined' && !pdfjs.GlobalWorkerOptions.workerSrc) {
        pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
      }

      setConversionProgress(10);
      toast.info('Initializing conversion...');

      const arrayBuffer = await file.file.arrayBuffer();
      let pdfDocument: PDFDocumentProxy;

      try {
        const loadingTask = pdfjs.getDocument({ data: arrayBuffer, verbosity: 0 });
        pdfDocument = await loadingTask.promise;
      } catch (error) {
        console.warn('Primary worker failed, trying fallback', error);
        pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;
        const retryLoadingTask = pdfjs.getDocument({ data: arrayBuffer, verbosity: 0 });
        pdfDocument = await retryLoadingTask.promise;
      }

      setConversionProgress(40);
      toast.info('Extracting text...');

      const extractedData: string[][] = [];
      for (const pageNum of file.selectedPages.sort((a, b) => a - b)) {
        const page = await pdfDocument.getPage(pageNum);
        const textContent = await page.getTextContent();
        const textItems = textContent.items.map((item: any) => ({ text: item.str, x: item.transform[4], y: item.transform[5] }));
        textItems.sort((a, b) => b.y - a.y || a.x - b.x);

        if (textItems.length > 0) {
          const rows: string[][] = [];
          let currentRow: string[] = [textItems[0].text];
          let lastY = textItems[0].y;
          for (let i = 1; i < textItems.length; i++) {
            if (Math.abs(textItems[i].y - lastY) < 5) {
              currentRow.push(textItems[i].text);
            } else {
              rows.push(currentRow);
              currentRow = [textItems[i].text];
              lastY = textItems[i].y;
            }
          }
          rows.push(currentRow);
          extractedData.push(...rows);
        }
      }

      setConversionProgress(80);
      toast.info('Generating Excel file...');

      if (extractedData.length === 0) throw new Error('No data could be extracted.');

      const finalData = extractedData.map(row => row.map(cell => cell.trim())).filter(row => row.join('').trim() !== '');
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.aoa_to_sheet(finalData);
      XLSX.utils.book_append_sheet(wb, ws, 'Extracted Data');
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${file.file.name.replace('.pdf', '')}_converted.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      setConversionProgress(100);
      toast.success(`Conversion completed!`);
    } catch (error) {
      console.error('Error converting PDF to Excel:', error);

      let errorMessage = 'Unknown error occurred';

      if (error instanceof Error) {
        if (error.message.includes('worker')) {
          errorMessage = 'Failed to load PDF.js worker. Please check your internet connection and try again.';
        } else if (error.message.includes('timed out')) {
          errorMessage = 'PDF loading timed out. The file may be too large or corrupted.';
        } else if (error.message.includes('Invalid PDF')) {
          errorMessage = 'The file is not a valid PDF document.';
        } else if (error.message.includes('password')) {
          errorMessage = 'This PDF is password-protected. Please unlock it first.';
        } else if (error.message.includes('No data could be extracted')) {
          errorMessage = error.message;
        } else {
          errorMessage = error.message;
        }
      }

      toast.error(`Failed to convert PDF to Excel: ${errorMessage}`);
    } finally {
      setIsConverting(false);
    }
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Upload Section */}
        {!file && (
          <div
            className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-12 text-center hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer"
            onDragOver={(e) => e.preventDefault()}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept=".pdf"
              className="hidden"
              disabled={isConverting}
            />
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-full">
                <FileSpreadsheet className="h-10 w-10 text-green-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-1">
                  Upload a PDF to Convert to Excel
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                  Drag and drop your PDF file here, or click to browse
                </p>
                <Button disabled={isConverting} className="bg-green-600 hover:bg-green-700">
                  <Upload className="h-4 w-4 mr-2" />
                  Select PDF File
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
                Maximum file size: 50MB
              </p>
            </div>
          </div>
        )}

        {/* PDF Preview and Options */}
        {file && (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                  <File className="h-5 w-5 mr-2 text-green-500" />
                  {file.file.name}
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {file.pageCount} {file.pageCount === 1 ? 'page' : 'pages'} • {(file.file.size / 1024 / 1024).toFixed(2)}MB
                </p>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={removeFile}
                  disabled={isConverting}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  <span className="hidden sm:inline">Remove</span>
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                >
                  <Settings className="h-4 w-4 mr-1" />
                  <span className="hidden sm:inline">Options</span>
                </Button>

                <Button
                  size="sm"
                  onClick={convertToExcel}
                  disabled={isConverting || file.selectedPages.length === 0}
                  className="bg-green-600 hover:bg-green-700 min-w-[130px]"
                >
                  {isConverting ? (
                    <>
                      <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Converting...
                    </>
                  ) : (
                    <>
                      <Table className="h-4 w-4 mr-1" />
                      Convert to Excel
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Conversion progress */}
            {isConverting && (
              <div className="space-y-2">
                <Progress value={conversionProgress} className="h-2" />
                <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  Converting PDF to Excel ({conversionProgress}%)
                </p>
              </div>
            )}

            {showAdvancedOptions && (
              <Card>
                <CardContent className="pt-6">
                  <div className="grid gap-6 sm:grid-cols-2">
                    <div>
                      <h3 className="text-sm font-medium mb-3 text-gray-800 dark:text-gray-200">Extraction Method</h3>
                      <Select
                        value={extractionMode}
                        onValueChange={(value: 'auto' | 'structured') => setExtractionMode(value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select extraction method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="auto">Auto-detect Tables</SelectItem>
                          <SelectItem value="structured">Structured Data (Better for Forms)</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        Choose how to extract data from your PDF
                      </p>
                    </div>

                    <div className="space-y-3">
                      <h3 className="text-sm font-medium text-gray-800 dark:text-gray-200">Table Options</h3>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeHeaders"
                            checked={tableSettings.includeHeaders}
                            onCheckedChange={(checked) =>
                              setTableSettings(prev => ({...prev, includeHeaders: checked === true}))
                            }
                          />
                          <Label htmlFor="includeHeaders">Use first row as headers</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="detectMergedCells"
                            checked={tableSettings.detectMergedCells}
                            onCheckedChange={(checked) =>
                              setTableSettings(prev => ({...prev, detectMergedCells: checked === true}))
                            }
                          />
                          <Label htmlFor="detectMergedCells">Detect merged cells</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="preserveFormatting"
                            checked={tableSettings.preserveFormatting}
                            onCheckedChange={(checked) =>
                              setTableSettings(prev => ({...prev, preserveFormatting: checked === true}))
                            }
                          />
                          <Label htmlFor="preserveFormatting">Preserve formatting when possible</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="skipEmptyRows"
                            checked={tableSettings.skipEmptyRows}
                            onCheckedChange={(checked) =>
                              setTableSettings(prev => ({...prev, skipEmptyRows: checked === true}))
                            }
                          />
                          <Label htmlFor="skipEmptyRows">Skip empty rows</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <Tabs defaultValue="preview">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="preview">PDF Preview</TabsTrigger>
                <TabsTrigger value="pages">Page Selection</TabsTrigger>
              </TabsList>

              <TabsContent value="preview" className="mt-4">
                <PDFPreview fileUrl={file.previewUrl} />
              </TabsContent>

              <TabsContent value="pages" className="mt-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                        Select pages to convert
                      </h3>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleAllPages(true)}
                          disabled={file.selectedPages.length === file.pageCount}
                        >
                          <Check className="h-3 w-3 mr-1" />
                          Select All
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleAllPages(false)}
                          disabled={file.selectedPages.length === 0}
                        >
                          <X className="h-3 w-3 mr-1" />
                          Clear All
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
                      {Array.from({ length: file.pageCount }, (_, i) => i + 1).map(pageNumber => (
                        <div
                          key={pageNumber}
                          className={`border rounded-md p-2 text-center cursor-pointer transition-colors ${
                            file.selectedPages.includes(pageNumber)
                              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                              : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                          }`}
                          onClick={() => togglePageSelection(pageNumber)}
                        >
                          <div className="font-medium text-sm">
                            {file.selectedPages.includes(pageNumber) && (
                              <Check className="h-3 w-3 inline-block mr-1 text-green-500" />
                            )}
                            Page {pageNumber}
                          </div>
                        </div>
                      ))}
                    </div>

                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
                      {file.selectedPages.length} of {file.pageCount} pages selected
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* Note about accuracy */}
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
            <div className="space-y-1">
              <h3 className="font-medium text-amber-800 dark:text-amber-300 text-sm">
                About PDF to Excel Conversion
              </h3>
              <p className="text-xs text-amber-700 dark:text-amber-400">
                The accuracy of the conversion depends on the structure and quality of the original PDF.
                Well-structured tables with clear borders typically yield the best results.
                After conversion, we recommend reviewing the Excel file to ensure data accuracy.
              </p>
              <div className="pt-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="link" size="sm" className="text-amber-700 dark:text-amber-400 p-0 h-auto text-xs underline">
                      Learn more about PDF extraction <ExternalLink className="h-3 w-3 ml-1" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Tips for Best PDF to Excel Conversion</DialogTitle>
                      <DialogDescription>
                        Get the most accurate results from your PDF to Excel conversions
                      </DialogDescription>
                    </DialogHeader>
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="item-1">
                        <AccordionTrigger>What types of PDFs work best?</AccordionTrigger>
                        <AccordionContent>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Digitally created PDFs with well-defined table structures work best.
                            PDFs with tables that have clear borders, consistent column spacing, and proper
                            text formatting will yield the most accurate results.
                          </p>
                        </AccordionContent>
                      </AccordionItem>
                      <AccordionItem value="item-2">
                        <AccordionTrigger>How to improve conversion accuracy</AccordionTrigger>
                        <AccordionContent>
                          <ul className="list-disc pl-5 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                            <li>Use the &apos;Structured Data&apos; extraction method for forms and simple tables</li>
                            <li>Use &apos;Auto-detect Tables&apos; for complex tables with borders</li>
                            <li>Select only the pages containing tables to avoid unnecessary data</li>
                            <li>For scanned PDFs, consider using OCR processing first</li>
                          </ul>
                        </AccordionContent>
                      </AccordionItem>
                      <AccordionItem value="item-3">
                        <AccordionTrigger>What to check after conversion</AccordionTrigger>
                        <AccordionContent>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Always review the converted Excel file to ensure:
                          </p>
                          <ul className="list-disc pl-5 space-y-1 mt-2 text-sm text-gray-600 dark:text-gray-400">
                            <li>All tables and data were properly extracted</li>
                            <li>Column headers are correct</li>
                            <li>Data is aligned in the right columns</li>
                            <li>No text is split incorrectly across cells</li>
                            <li>Number formatting is preserved where appropriate</li>
                          </ul>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}
