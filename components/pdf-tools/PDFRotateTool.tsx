'use client';

import { useState, useRef, useEffect } from 'react';
import { FileText, Upload, Trash2, AlertCircle, Download, RotateCw, RotateCcw, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { getPDFPageCount, rotatePDF, formatFileSize, downloadBlob } from '@/lib/pdf-utils';
import { PDFPreview } from './PDFPreview';

interface PDFFile {
  id: string;
  name: string;
  size: number;
  file: File;
  pageCount?: number;
}

type RotationAngle = 90 | 180 | 270;
type RotationMode = 'single' | 'continuous';

export function PDFRotateTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [rotatedPdfUrl, setRotatedPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [rotationAngle, setRotationAngle] = useState<RotationAngle>(90);
  const [rotationMode, setRotationMode] = useState<RotationMode>('single');
  const [rotationCount, setRotationCount] = useState(0);
  const [totalRotation, setTotalRotation] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFile(selectedFiles[0]);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFile = async (selectedFile: File) => {
    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    try {
      // Get actual page count using our utility function
      const pageCount = await getPDFPageCount(selectedFile);

      const newFile: PDFFile = {
        id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: selectedFile.name,
        size: selectedFile.size,
        file: selectedFile,
        pageCount: pageCount
      };

      setFile(newFile);
      setRotatedPdfUrl(null);
      setError(null);
    } catch (err) {
      console.error('Error loading PDF:', err);
      toast.error("Error loading PDF", {
        description: "The selected file could not be processed. Please try another PDF file."
      });
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    // Only take the first file for rotation
    addFile(droppedFiles[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = () => {
    setFile(null);
    setRotatedPdfUrl(null);
    setError(null);
    setRotationCount(0);
    setTotalRotation(0);
  };

  const handleRotatePDF = async () => {
    if (!file) {
      setError("Please add a PDF file to rotate.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    // For continuous mode, we keep the current rotated PDF as the source
    // For single mode, we always start from the original file
    const sourceFile = rotationMode === 'continuous' && rotatedPdfUrl
      ? await fetch(rotatedPdfUrl).then(res => res.blob()).then(blob => new File([blob], file.name, { type: 'application/pdf' }))
      : file.file;

    try {
      // Use the utility function to rotate the PDF
      const rotatedPdfBlob = await rotatePDF(
        sourceFile,
        rotationAngle,
        [], // Rotate all pages
        (progress) => setProgress(progress)
      );

      // Revoke previous URL to prevent memory leaks
      if (rotatedPdfUrl) {
        URL.revokeObjectURL(rotatedPdfUrl);
      }

      // Create a URL for the blob
      const url = URL.createObjectURL(rotatedPdfBlob);

      // Update rotation count and total rotation
      const newRotationCount = rotationCount + 1;
      setRotationCount(newRotationCount);

      // Calculate total rotation angle
      const newTotalRotation = rotationMode === 'continuous'
        ? (totalRotation + rotationAngle) % 360
        : rotationAngle;
      setTotalRotation(newTotalRotation);

      setRotatedPdfUrl(url);
      toast.success("Success!", {
        description: rotationMode === 'continuous'
          ? `Your PDF has been rotated ${newRotationCount} times (${newTotalRotation}° total).`
          : `Your PDF has been rotated by ${rotationAngle}°.`
      });
    } catch (err) {
      console.error('Error rotating PDF:', err);
      setError("An error occurred while rotating the PDF. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadRotatedPDF = () => {
    if (!rotatedPdfUrl || !file) return;

    const fileName = rotationMode === 'continuous'
      ? file.name.replace('.pdf', `_rotated_${totalRotation}.pdf`)
      : file.name.replace('.pdf', `_rotated_${rotationAngle}.pdf`);

    // Create a blob from the URL and download it
    fetch(rotatedPdfUrl)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName));
  };

  const resetRotation = () => {
    setRotationCount(0);
    setTotalRotation(0);
    setRotatedPdfUrl(null);
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf"
            className="hidden"
          />

          <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop a PDF File Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={handleBrowseClick}
            variant="outline"
            className="border-orange-300 dark:border-orange-700 hover:bg-orange-50 dark:hover:bg-orange-900/20"
          >
            <Upload className="mr-2 h-4 w-4" />
            Browse Files
          </Button>
        </div>
      )}

      {/* Selected File */}
      {file && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Selected PDF</h3>
            <Button
              onClick={removeFile}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-red-500"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
                <FileText className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(file.size)} • {file.pageCount} pages
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* PDF Preview and Rotation Options */}
      {file && !rotatedPdfUrl && (
        <div className="space-y-6">
          {/* PDF Preview */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="font-medium text-gray-700 dark:text-gray-300">PDF Preview</h3>
            </div>
            <div className="p-4">
              <div className="flex flex-col md:flex-row items-center justify-center gap-6">
                <div className="flex flex-col items-center">
                  <div className="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Original</div>
                  <PDFPreview
                    file={file.file}
                    width={250}
                    height={350}
                    className="border border-gray-200 dark:border-gray-700"
                  />
                </div>

                <div className="flex items-center justify-center">
                  <ArrowRight className="h-8 w-8 text-gray-400 dark:text-gray-600" />
                </div>

                <div className="flex flex-col items-center">
                  <div className="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">After Rotation ({rotationAngle}°)</div>
                  <PDFPreview
                    file={file.file}
                    rotation={rotationAngle}
                    width={250}
                    height={350}
                    className="border border-gray-200 dark:border-gray-700"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Rotation Options */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="font-medium text-gray-700 dark:text-gray-300">Rotation Options</h3>
            </div>
            <div className="p-4 space-y-6">
              {/* Rotation Angle */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Rotation Angle</h4>
                <RadioGroup
                  value={rotationAngle.toString()}
                  onValueChange={(value) => setRotationAngle(parseInt(value) as RotationAngle)}
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="90" id="r90" />
                    <Label htmlFor="r90" className="flex items-center">
                      <RotateCw className="h-4 w-4 mr-2" />
                      Rotate 90° Clockwise
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="180" id="r180" />
                    <Label htmlFor="r180" className="flex items-center">
                      <RotateCw className="h-4 w-4 mr-2" />
                      Rotate 180°
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="270" id="r270" />
                    <Label htmlFor="r270" className="flex items-center">
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Rotate 90° Counterclockwise
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Rotation Mode */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Rotation Mode</h4>
                <RadioGroup
                  value={rotationMode}
                  onValueChange={(value) => setRotationMode(value as RotationMode)}
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="single" id="single" />
                    <Label htmlFor="single" className="flex items-center">
                      Single Rotation (Rotate Once)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="continuous" id="continuous" />
                    <Label htmlFor="continuous" className="flex items-center">
                      Continuous Rotation (Keep Rotating)
                    </Label>
                  </div>
                </RadioGroup>

                {rotationMode === 'continuous' && rotationCount > 0 && (
                  <div className="mt-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-md">
                    <p className="text-xs text-orange-700 dark:text-orange-300">
                      Current rotation: {rotationCount} times ({totalRotation}° total)
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Processing...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Rotated PDF Preview */}
      {rotatedPdfUrl && file && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Rotated PDF</h3>
          </div>
          <div className="p-4">
            <div className="flex flex-col items-center">
              <div className="mb-4 text-center">
                {rotationMode === 'continuous' ? (
                  <>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                      Your PDF has been rotated {rotationCount} {rotationCount === 1 ? 'time' : 'times'}.
                    </p>
                    <p className="text-sm font-medium text-orange-600 dark:text-orange-400 mb-2">
                      Total rotation: {totalRotation}°
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500">
                      You can continue rotating or download the current result.
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                      Your PDF has been successfully rotated by {rotationAngle}°.
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500">
                      Click the button below to download your rotated PDF.
                    </p>
                  </>
                )}
              </div>

              <div className="mb-4">
                <iframe
                  src={rotatedPdfUrl}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg"
                  width="300"
                  height="400"
                  title="Rotated PDF Preview"
                />
              </div>

              {rotationMode === 'continuous' && (
                <div className="mb-4 flex space-x-4">
                  <Button
                    onClick={resetRotation}
                    variant="outline"
                    size="sm"
                    className="text-gray-600 dark:text-gray-300"
                  >
                    <RotateCcw className="mr-2 h-4 w-4" />
                    Reset Rotation
                  </Button>
                  <Button
                    onClick={handleRotatePDF}
                    variant="outline"
                    size="sm"
                    className="text-orange-600 dark:text-orange-400"
                    disabled={isProcessing}
                  >
                    <RotateCw className="mr-2 h-4 w-4" />
                    Rotate Again
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center pt-4">
        {rotatedPdfUrl ? (
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={downloadRotatedPDF}
              className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white"
              size="lg"
            >
              <Download className="mr-2 h-5 w-5" />
              Download Rotated PDF
            </Button>

            {rotationMode === 'continuous' && (
              <Button
                onClick={handleRotatePDF}
                disabled={isProcessing}
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
                size="lg"
              >
                <RotateCw className="mr-2 h-5 w-5" />
                Rotate Again ({rotationAngle}°)
              </Button>
            )}
          </div>
        ) : (
          <Button
            onClick={handleRotatePDF}
            disabled={!file || isProcessing}
            className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white"
            size="lg"
          >
            <RotateCw className="mr-2 h-5 w-5" />
            {rotationMode === 'continuous' ? 'Start Rotating PDF' : 'Rotate PDF'}
          </Button>
        )}
      </div>
    </div>
  );
}
