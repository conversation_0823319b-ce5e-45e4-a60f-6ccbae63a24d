'use client';

import dynamic from 'next/dynamic';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { FileText, FileKey, FileSearch, FileSliders, FileX } from 'lucide-react';
import { useState, useEffect } from 'react';

// Dynamic import with SSR disabled - this is allowed in client components
const PDFToolsGridWrapper = dynamic(
  () => import('@/components/pdf-tools/PDFToolsGridWrapper'),
  { ssr: false }
);

// Define the PDF tool interfaces
interface PDFTool {
  name: string;
  description: string;
  iconName: string;
  href: string;
  color: string;
  bgColor: string;
  borderColor: string;
}

interface PDFToolCategory {
  category: string;
  tools: PDFTool[];
}

export default function PDFToolsPageClient() {
  // We'll use an effect to initialize the component on the client
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Define the PDF tools
  const pdfTools = [
    {
      category: "Basic PDF Operations",
      tools: [
        {
          name: "Merge PDF",
          description: "Combine multiple PDFs into one document",
          iconName: "FilePlus",
          href: "/pdf-tools/merge",
          color: "text-blue-500",
          bgColor: "bg-blue-50 dark:bg-blue-900/20",
          borderColor: "border-blue-200 dark:border-blue-800",
        },
        {
          name: "Split PDF",
          description: "Extract pages from a PDF into separate files",
          iconName: "FileText",
          href: "/pdf-tools/split",
          color: "text-purple-500",
          bgColor: "bg-purple-50 dark:bg-purple-900/20",
          borderColor: "border-purple-200 dark:border-purple-800",
        },
        {
          name: "Compress PDF",
          description: "Reduce PDF file size while maintaining quality",
          iconName: "FileDown",
          href: "/pdf-tools/compress",
          color: "text-green-500",
          bgColor: "bg-green-50 dark:bg-green-900/20",
          borderColor: "border-green-200 dark:border-green-800",
        },
        {
          name: "Rotate PDF",
          description: "Change the orientation of PDF pages",
          iconName: "RotateCw",
          href: "/pdf-tools/rotate",
          color: "text-orange-500",
          bgColor: "bg-orange-50 dark:bg-orange-900/20",
          borderColor: "border-orange-200 dark:border-orange-800",
        },
      ]
    },
    {
      category: "PDF Conversion",
      tools: [
        {
          name: "PDF to Word",
          description: "Convert PDF to editable Word documents",
          iconName: "FileOutput",
          href: "/pdf-tools/pdf-to-word",
          color: "text-blue-500",
          bgColor: "bg-blue-50 dark:bg-blue-900/20",
          borderColor: "border-blue-200 dark:border-blue-800",
        },
        {
          name: "Word to PDF",
          description: "Convert Word documents to PDF format",
          iconName: "FileInput",
          href: "/pdf-tools/word-to-pdf",
          color: "text-indigo-500",
          bgColor: "bg-indigo-50 dark:bg-indigo-900/20",
          borderColor: "border-indigo-200 dark:border-indigo-800",
        },
        {
          name: "PDF to JPG",
          description: "Convert PDF pages to JPG images",
          iconName: "FileImage",
          href: "/pdf-tools/pdf-to-jpg",
          color: "text-pink-500",
          bgColor: "bg-pink-50 dark:bg-pink-900/20",
          borderColor: "border-pink-200 dark:border-pink-800",
        },
        {
          name: "JPG to PDF",
          description: "Convert JPG images to PDF format",
          iconName: "FileUp",
          href: "/pdf-tools/jpg-to-pdf",
          color: "text-red-500",
          bgColor: "bg-red-50 dark:bg-red-900/20",
          borderColor: "border-red-200 dark:border-red-800",
        },
        {
          name: "PDF to Excel",
          description: "Extract tables from PDF to Excel spreadsheets",
          iconName: "FileSpreadsheet",
          href: "/pdf-tools/pdf-to-excel",
          color: "text-green-500",
          bgColor: "bg-green-50 dark:bg-green-900/20",
          borderColor: "border-green-200 dark:border-green-800",
        },
        {
          name: "Excel to PDF",
          description: "Convert Excel spreadsheets to PDF format",
          iconName: "FilePieChart",
          href: "/pdf-tools/excel-to-pdf",
          color: "text-emerald-500",
          bgColor: "bg-emerald-50 dark:bg-emerald-900/20",
          borderColor: "border-emerald-200 dark:border-emerald-800",
        },
      ]
    },
    {
      category: "Advanced PDF Tools",
      tools: [
        {
          name: "Protect PDF",
          description: "Add password protection to your PDF files",
          iconName: "FileLock",
          href: "/pdf-tools/protect",
          color: "text-red-500",
          bgColor: "bg-red-50 dark:bg-red-900/20",
          borderColor: "border-red-200 dark:border-red-800",
        },
        {
          name: "Unlock PDF",
          description: "Remove password protection from PDF files",
          iconName: "FileKey",
          href: "/pdf-tools/unlock",
          color: "text-yellow-500",
          bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
          borderColor: "border-yellow-200 dark:border-yellow-800",
        },
        {
          name: "OCR PDF",
          description: "Make scanned PDFs searchable with text recognition",
          iconName: "FileSearch",
          href: "/pdf-tools/ocr",
          color: "text-purple-500",
          bgColor: "bg-purple-50 dark:bg-purple-900/20",
          borderColor: "border-purple-200 dark:border-purple-800",
        },
        {
          name: "Organize PDF",
          description: "Rearrange, delete, or add pages to your PDF",
          iconName: "FileSliders",
          href: "/pdf-tools/organize",
          color: "text-blue-500",
          bgColor: "bg-blue-50 dark:bg-blue-900/20",
          borderColor: "border-blue-200 dark:border-blue-800",
        },
        {
          name: "Repair PDF",
          description: "Fix corrupted PDF files and recover data",
          iconName: "FileWarning",
          href: "/pdf-tools/repair",
          color: "text-amber-500",
          bgColor: "bg-amber-50 dark:bg-amber-900/20",
          borderColor: "border-amber-200 dark:border-amber-800",
        },
        {
          name: "Watermark PDF",
          description: "Add text or image watermarks to your PDF",
          iconName: "FileX",
          href: "/pdf-tools/watermark",
          color: "text-teal-500",
          bgColor: "bg-teal-50 dark:bg-teal-900/20",
          borderColor: "border-teal-200 dark:border-teal-800",
        },
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - More Compact */}
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-6 overflow-hidden">
            <div className="max-w-5xl mx-auto px-4 py-4 flex flex-col sm:flex-row items-center justify-between relative">
              <div className="text-center sm:text-left mb-2 sm:mb-0">
                <h1 className="text-2xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  PDF Tools
                </h1>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  Free tools to work with PDFs in one place
                </p>
              </div>
              <div className="flex flex-wrap gap-2 justify-center">
                <div className="px-3 py-1 bg-white/70 dark:bg-gray-800/70 rounded-full text-xs font-medium text-blue-600 dark:text-blue-400 shadow-sm border border-blue-100 dark:border-blue-900/50">
                  100% Free
                </div>
                <div className="px-3 py-1 bg-white/70 dark:bg-gray-800/70 rounded-full text-xs font-medium text-purple-600 dark:text-purple-400 shadow-sm border border-purple-100 dark:border-purple-900/50">
                  No Sign-up
                </div>
                <div className="px-3 py-1 bg-white/70 dark:bg-gray-800/70 rounded-full text-xs font-medium text-pink-600 dark:text-pink-400 shadow-sm border border-pink-100 dark:border-pink-900/50">
                  Privacy-Focused
                </div>
              </div>
            </div>
          </section>

          {/* Tools Grid - Only render when on client */}
          {isClient && <PDFToolsGridWrapper pdfTools={pdfTools} />}

          {/* Coming Soon Section - More Compact */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-5 mb-8 shadow-lg border border-blue-100/50 dark:border-blue-900/30 relative overflow-hidden">
            <div className="absolute top-0 right-0 -mt-10 -mr-10 w-40 h-40 bg-blue-400/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 -mb-10 -ml-10 w-40 h-40 bg-indigo-400/10 rounded-full blur-3xl"></div>

            <div className="relative z-10">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 gap-2">
                <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">More PDF Tools Coming Soon!</h2>
                <div className="px-2 py-1 bg-blue-100/70 dark:bg-blue-900/30 rounded-full text-xs font-medium text-blue-700 dark:text-blue-300 shadow-sm">
                  <span className="flex items-center gap-1">
                    <FileText className="w-3 h-3" /> Stay tuned!
                  </span>
                </div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 max-w-3xl">
                We&apos;re constantly working on adding more features to make your document workflow even easier.
              </p>
              <div className="flex flex-wrap gap-2">
                <div className="px-2 py-1 bg-white/70 dark:bg-gray-800/70 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300 shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:scale-105 transition-transform duration-300 hover:shadow-md">
                  <span className="flex items-center gap-1">
                    <FileText className="w-3 h-3" /> PDF Editor
                  </span>
                </div>
                <div className="px-2 py-1 bg-white/70 dark:bg-gray-800/70 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300 shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:scale-105 transition-transform duration-300 hover:shadow-md">
                  <span className="flex items-center gap-1">
                    <FileKey className="w-3 h-3" /> Form Filling
                  </span>
                </div>
                <div className="px-2 py-1 bg-white/70 dark:bg-gray-800/70 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300 shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:scale-105 transition-transform duration-300 hover:shadow-md">
                  <span className="flex items-center gap-1">
                    <FileSearch className="w-3 h-3" /> PDF Comparison
                  </span>
                </div>
                <div className="px-2 py-1 bg-white/70 dark:bg-gray-800/70 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300 shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:scale-105 transition-transform duration-300 hover:shadow-md">
                  <span className="flex items-center gap-1">
                    <FileX className="w-3 h-3" /> PDF Redaction
                  </span>
                </div>
                <div className="px-2 py-1 bg-white/70 dark:bg-gray-800/70 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300 shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:scale-105 transition-transform duration-300 hover:shadow-md">
                  <span className="flex items-center gap-1">
                    <FileSliders className="w-3 h-3" /> Page Numbering
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
} 