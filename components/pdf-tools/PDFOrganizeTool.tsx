'use client';

import { useRef, useState } from 'react';
import {
  FileText,
  Trash2,
  Download,
  Plus,
  ArrowUp,
  ArrowDown,
  X,
  RotateCw,
  Copy,
  AlertCircle,
  FilePlus,
  FileIcon
} from 'lucide-react';
import { toast } from 'sonner';
import { PDFDocument } from 'pdf-lib';

import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PDFPreview } from './PDFPreview';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { getPDFPageCount, formatFileSize } from '@/lib/pdf-utils';
import { PDFFile } from '@/types/pdf';

interface PDFPage {
  id: string;
  pageNumber: number;
  fileId: string;
  fileName: string;
  rotation: number;
}

export function PDFOrganizeTool() {
  const [files, setFiles] = useState<PDFFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [organizedPdfUrl, setOrganizedPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [pages, setPages] = useState<PDFPage[]>([]);
  const [activeTab, setActiveTab] = useState<string>('pages');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFile(selectedFiles[0]);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFile = async (selectedFile: File) => {
    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    // Validate file size before attempting to load
    if (selectedFile.size === 0) {
      toast.error("Invalid PDF file", {
        description: "The PDF file is empty."
      });
      return;
    }

    // Check if file size is too large (over 100MB)
    if (selectedFile.size > 100 * 1024 * 1024) {
      toast.error("File too large", {
        description: "Maximum file size is 100MB."
      });
      return;
    }

    setIsProcessing(true);
    setProgress(10);
    setError(null);

    try {
      // Get actual page count using our utility function
      const pageCount = await getPDFPageCount(selectedFile).catch(error => {
        console.error('Error getting page count:', error);
        throw new Error('Failed to read PDF structure. The file may be corrupted or password-protected.');
      });

      setProgress(50);

      const fileId = `file-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const newFile: PDFFile = {
        id: fileId,
        name: selectedFile.name,
        size: selectedFile.size,
        file: selectedFile,
        pageCount: pageCount
      };

      // Add the file to our files array
      setFiles(prev => [...prev, newFile]);

      // Create page entries for each page in the document
      const newPages = Array.from({ length: pageCount }, (_, i) => ({
        id: `page-${fileId}-${i}`,
        pageNumber: i + 1,
        fileId: fileId,
        fileName: selectedFile.name,
        rotation: 0
      }));

      // Add these pages to our pages array
      setPages(prev => [...prev, ...newPages]);

      setOrganizedPdfUrl(null);
      setError(null);
      setProgress(100);

      toast.success("PDF loaded successfully", {
        description: `Added ${pageCount} page${pageCount !== 1 ? 's' : ''} from ${selectedFile.name}`
      });
    } catch (err) {
      console.error('Error loading PDF:', err);
      const errorMessage = err instanceof Error ? err.message : "The selected file could not be processed.";

      setError(errorMessage);
      toast.error("Error loading PDF", {
        description: errorMessage
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    // Only take the first file for organization
    addFile(droppedFiles[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setPages(prev => prev.filter(page => page.fileId !== fileId));

    if (files.length <= 1) {
      setOrganizedPdfUrl(null);
    }
  };

  const removePage = (pageId: string) => {
    setPages(prev => prev.filter(page => page.id !== pageId));
  };

  const movePageUp = (pageId: string) => {
    setPages(prev => {
      const index = prev.findIndex(page => page.id === pageId);
      if (index <= 0) return prev;

      const newPages = [...prev];
      const temp = newPages[index];
      newPages[index] = newPages[index - 1];
      newPages[index - 1] = temp;

      return newPages;
    });
  };

  const movePageDown = (pageId: string) => {
    setPages(prev => {
      const index = prev.findIndex(page => page.id === pageId);
      if (index < 0 || index >= prev.length - 1) return prev;

      const newPages = [...prev];
      const temp = newPages[index];
      newPages[index] = newPages[index + 1];
      newPages[index + 1] = temp;

      return newPages;
    });
  };

  const rotatePage = (pageId: string) => {
    setPages(prev => {
      return prev.map(page => {
        if (page.id === pageId) {
          return {
            ...page,
            rotation: (page.rotation + 90) % 360
          };
        }
        return page;
      });
    });
  };

  const duplicatePage = (pageId: string) => {
    setPages(prev => {
      const index = prev.findIndex(page => page.id === pageId);
      if (index < 0) return prev;

      const pageToClone = prev[index];
      const newPage = {
        ...pageToClone,
        id: `page-${pageToClone.fileId}-${Date.now()}`
      };

      const newPages = [...prev];
      newPages.splice(index + 1, 0, newPage);

      return newPages;
    });
  };

  const handleOrganizePDF = async () => {
    if (pages.length === 0) {
      setError("Please add at least one PDF page to organize.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // Create a new PDF document
      const pdfDoc = await PDFDocument.create();

      // Track progress
      let completedPages = 0;

      // For each page in our ordered list
      for (const page of pages) {
        // Find the file this page belongs to
        const file = files.find(f => f.id === page.fileId);
        if (!file) continue;

        // Load the source PDF
        const sourcePdfBytes = await file.file.arrayBuffer();
        const sourcePdf = await PDFDocument.load(sourcePdfBytes);

        // Copy the page (0-indexed in pdf-lib)
        const [copiedPage] = await pdfDoc.copyPages(sourcePdf, [page.pageNumber - 1]);

        // If page has rotation, apply it
        if (page.rotation !== 0) {
          // Apply rotation using degrees
          const rotationAngle = page.rotation;
          switch (rotationAngle) {
            case 90:
              copiedPage.setRotation({
                angle: 90,
                type: 'degrees'
              } as any); // Type assertion to bypass type checking
              break;
            case 180:
              copiedPage.setRotation({
                angle: 180,
                type: 'degrees'
              } as any);
              break;
            case 270:
              copiedPage.setRotation({
                angle: 270,
                type: 'degrees'
              } as any);
              break;
          }
        }

        // Add the page to our new document
        pdfDoc.addPage(copiedPage);

        // Update progress
        completedPages++;
        setProgress(Math.round((completedPages / pages.length) * 100));
      }

      // Save the organized PDF
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      setOrganizedPdfUrl(url);

      toast.success("PDF Organization Complete", {
        description: "Your PDF has been organized successfully."
      });
    } catch (err) {
      console.error('Error organizing PDF:', err);
      setError("An error occurred while organizing the PDF. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadOrganizedPDF = () => {
    if (!organizedPdfUrl) return;

    const link = document.createElement('a');
    link.href = organizedPdfUrl;
    link.download = "organized_document.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileById = (fileId: string) => {
    return files.find(file => file.id === fileId);
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center ${
          isDragging
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-700'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf"
          className="hidden"
        />

        <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

        <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
          Drag & Drop PDF Files Here
        </h3>

        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Or click the button below to select files
        </p>

        <Button
          onClick={handleBrowseClick}
          variant="outline"
          className="border-blue-300 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20"
        >
          <FilePlus className="mr-2 h-4 w-4" />
          Add PDF
        </Button>
      </div>

      {/* Tabs for Organization */}
      {files.length > 0 && (
        <Tabs defaultValue="pages" onValueChange={setActiveTab} value={activeTab}>
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="pages">Organize Pages</TabsTrigger>
            <TabsTrigger value="files">Manage Files</TabsTrigger>
          </TabsList>

          {/* Pages Tab */}
          <TabsContent value="pages" className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-gray-700 dark:text-gray-300">Pages</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Drag pages to reorder, rotate, or delete them
                </p>
              </div>

              <ScrollArea className="h-[600px]">
                <div className="p-4 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {pages.map((page, index) => {
                    const file = getFileById(page.fileId);
                    if (!file) return null;

                    return (
                      <div
                        key={page.id}
                        className="relative bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 shadow-sm group"
                      >
                        <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 rounded-full bg-black/30 text-white hover:bg-black/50"
                            onClick={() => removePage(page.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>

                        <div
                          className="aspect-[3/4] relative overflow-hidden"
                          style={{ transform: `rotate(${page.rotation}deg)` }}
                        >
                          <PDFPreview
                            file={file.file}
                            pageNumber={page.pageNumber}
                            rotation={page.rotation}
                            className="w-full h-full"
                          />
                        </div>

                        <div className="p-2 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate text-center">
                            Page {page.pageNumber} from {page.fileName}
                          </p>

                          <div className="flex justify-center mt-2 space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => movePageUp(page.id)}
                              disabled={index === 0}
                            >
                              <ArrowUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => movePageDown(page.id)}
                              disabled={index === pages.length - 1}
                            >
                              <ArrowDown className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => rotatePage(page.id)}
                            >
                              <RotateCw className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => duplicatePage(page.id)}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          {/* Files Tab */}
          <TabsContent value="files" className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-gray-700 dark:text-gray-300">Files</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Manage your uploaded PDF files
                </p>
              </div>

              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {files.map(file => (
                  <div key={file.id} className="p-4 flex items-center">
                    <div className="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                      <FileIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>

                    <div className="flex-1 min-w-0 ml-3">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {formatFileSize(file.size)} • {file.pageCount} pages
                      </p>
                    </div>

                    <div className="ml-4 flex-shrink-0">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-gray-500 mr-2"
                          >
                            Preview
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                          <DialogHeader>
                            <DialogTitle>{file.name}</DialogTitle>
                            <DialogDescription>
                              {formatFileSize(file.size)} • {file.pageCount} pages
                            </DialogDescription>
                          </DialogHeader>
                          <div className="h-[500px] overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
                            <PDFPreview
                              file={file.file}
                              className="w-full h-full"
                            />
                          </div>
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-500 hover:text-red-500"
                        onClick={() => removeFile(file.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  onClick={handleBrowseClick}
                  variant="outline"
                  size="sm"
                  className="w-full border-blue-300 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Another PDF
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Organizing PDF...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Action Buttons */}
      {files.length > 0 && pages.length > 0 && !organizedPdfUrl && !isProcessing && (
        <div className="flex justify-end">
          <Button
            onClick={handleOrganizePDF}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <FileText className="mr-2 h-4 w-4" />
            Create Organized PDF
          </Button>
        </div>
      )}

      {/* Download Button */}
      {organizedPdfUrl && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 flex flex-col sm:flex-row justify-between items-center gap-4">
          <div>
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-1">PDF Organization Complete!</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Your PDF has been organized with {pages.length} pages.
            </p>
          </div>
          <Button onClick={downloadOrganizedPDF} className="bg-blue-600 hover:bg-blue-700 min-w-[140px]">
            <Download className="mr-2 h-4 w-4" />
            Download PDF
          </Button>
        </div>
      )}
    </div>
  );
}
