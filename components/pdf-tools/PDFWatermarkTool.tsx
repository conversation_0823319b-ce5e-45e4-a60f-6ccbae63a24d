'use client';

import React, { useState, useRef, useCallback } from 'react';
import { PDFDocument, rgb, StandardFonts, degrees } from 'pdf-lib';
import Image from 'next/image';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Slider } from '../ui/slider';
import { Label } from '../ui/label';
import { FileUploader } from '@/components/FileUploader';
import { PDFPreview } from './PDFPreview';
import { Switch } from '../ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { AlertCircle, Download, FileImage, FileText } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Progress } from '../ui/progress';

const PDFWatermarkTool = () => {
  // File state
  const [file, setFile] = useState<File | null>(null);
  const [processedPdfUrl, setProcessedPdfUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  // Watermark type and settings
  const [watermarkType, setWatermarkType] = useState<'text' | 'image'>('text');
  const [watermarkText, setWatermarkText] = useState('CONFIDENTIAL');
  const [textColor, setTextColor] = useState('#FF0000');
  const [fontSize, setFontSize] = useState(50);
  const [opacity, setOpacity] = useState(30);
  const [rotation, setRotation] = useState(45);
  const [position, setPosition] = useState('center');
  const [imageWatermark, setImageWatermark] = useState<File | null>(null);
  const [imageWatermarkPreview, setImageWatermarkPreview] = useState<string | null>(null);
  const [allPages, setAllPages] = useState(true);
  const [pageRange, setPageRange] = useState('');

  const imageInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileSelected = useCallback((selectedFile: File) => {
    // Validate file type
    if (selectedFile.type !== 'application/pdf' && !selectedFile.name.toLowerCase().endsWith('.pdf')) {
      setError('Invalid file type. Please select a PDF file.');
      return;
    }

    // Validate file size
    if (selectedFile.size === 0) {
      setError('The PDF file is empty.');
      return;
    }

    if (selectedFile.size > 50 * 1024 * 1024) { // 50MB limit
      setError('File size exceeds the 50MB limit.');
      return;
    }

    console.log('PDF file selected:', selectedFile.name, 'Size:', selectedFile.size);
    setFile(selectedFile);
    setProcessedPdfUrl(null);
    setError(null);
  }, []);

  // Handle watermark image selection
  const handleWatermarkImageSelected = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const imageFile = e.target.files[0];

      // Validate image type
      if (!imageFile.type.startsWith('image/')) {
        setError('Please select a valid image file (JPEG, PNG).');
        return;
      }

      // Validate image size (max 5MB)
      if (imageFile.size > 5 * 1024 * 1024) {
        setError('Image size exceeds the 5MB limit.');
        return;
      }

      setImageWatermark(imageFile);
      setError(null);

      // Create image preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImageWatermarkPreview(e.target?.result as string);
      };
      reader.onerror = () => {
        setError('Failed to read the image file.');
        setImageWatermark(null);
        setImageWatermarkPreview(null);
      };
      reader.readAsDataURL(imageFile);
    }
  };

  // Reset the form
  const handleReset = () => {
    setFile(null);
    setProcessedPdfUrl(null);
    setError(null);
    setProgress(0);
  };

  // Process PDF with watermark
  const handleAddWatermark = async () => {
    if (!file) {
      setError('Please upload a PDF file first.');
      return;
    }

    if (watermarkType === 'image' && !imageWatermark) {
      setError('Please upload a watermark image.');
      return;
    }

    try {
      setIsProcessing(true);
      setProgress(10);
      setError(null);

      // Read the PDF file
      const fileArrayBuffer = await file.arrayBuffer();
      const pdfDoc = await PDFDocument.load(fileArrayBuffer);
      const pages = pdfDoc.getPages();

      setProgress(30);

      // Determine which pages to watermark
      let pagesToWatermark: number[] = [];
      if (allPages) {
        pagesToWatermark = Array.from({ length: pages.length }, (_, i) => i);
      } else if (pageRange) {
        const ranges = pageRange.split(',').map(range => range.trim());
        for (const range of ranges) {
          if (range.includes('-')) {
            const [start, end] = range.split('-').map(num => parseInt(num.trim()));
            if (!isNaN(start) && !isNaN(end) && start > 0 && end <= pages.length) {
              for (let i = start - 1; i < end; i++) {
                pagesToWatermark.push(i);
              }
            }
          } else {
            const pageNum = parseInt(range);
            if (!isNaN(pageNum) && pageNum > 0 && pageNum <= pages.length) {
              pagesToWatermark.push(pageNum - 1);
            }
          }
        }
      }

      if (pagesToWatermark.length === 0) {
        pagesToWatermark = Array.from({ length: pages.length }, (_, i) => i);
      }

      setProgress(40);

      // Add watermark to selected pages
      if (watermarkType === 'text') {
        // Text watermark
        const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

        // Convert hex color to RGB
        const hexToRgb = (hex: string) => {
          const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
          return result ? {
            r: parseInt(result[1], 16) / 255,
            g: parseInt(result[2], 16) / 255,
            b: parseInt(result[3], 16) / 255
          } : { r: 1, g: 0, b: 0 };
        };

        const color = hexToRgb(textColor);

        for (const pageIndex of pagesToWatermark) {
          const page = pages[pageIndex];
          const { width, height } = page.getSize();

          // Calculate position
          let x = width / 2;
          let y = height / 2;

          if (position === 'topLeft') {
            x = width * 0.1;
            y = height * 0.9;
          } else if (position === 'topRight') {
            x = width * 0.9;
            y = height * 0.9;
          } else if (position === 'bottomLeft') {
            x = width * 0.1;
            y = height * 0.1;
          } else if (position === 'bottomRight') {
            x = width * 0.9;
            y = height * 0.1;
          }

          page.drawText(watermarkText, {
            x,
            y,
            size: fontSize,
            font,
            color: rgb(color.r, color.g, color.b),
            opacity: opacity / 100,
            rotate: degrees(rotation),
            xSkew: degrees(0),
            ySkew: degrees(0),
          });
        }
      } else {
        // Image watermark
        if (!imageWatermark) return;

        const imageBytes = await imageWatermark.arrayBuffer();
        let image;

        // Check image format and embed accordingly
        if (imageWatermark.type === 'image/png') {
          image = await pdfDoc.embedPng(imageBytes);
        } else if (imageWatermark.type === 'image/jpeg' || imageWatermark.type === 'image/jpg') {
          image = await pdfDoc.embedJpg(imageBytes);
        } else {
          throw new Error('Unsupported image format. Please use PNG or JPG.');
        }

        const imgDims = image.scale(1);

        for (const pageIndex of pagesToWatermark) {
          const page = pages[pageIndex];
          const { width, height } = page.getSize();

          // Calculate position
          let x = width / 2 - imgDims.width / 2;
          let y = height / 2 - imgDims.height / 2;

          if (position === 'topLeft') {
            x = width * 0.1;
            y = height * 0.9 - imgDims.height;
          } else if (position === 'topRight') {
            x = width * 0.9 - imgDims.width;
            y = height * 0.9 - imgDims.height;
          } else if (position === 'bottomLeft') {
            x = width * 0.1;
            y = height * 0.1;
          } else if (position === 'bottomRight') {
            x = width * 0.9 - imgDims.width;
            y = height * 0.1;
          }

          page.drawImage(image, {
            x,
            y,
            width: imgDims.width,
            height: imgDims.height,
            opacity: opacity / 100,
            rotate: degrees(rotation),
          });
        }
      }

      setProgress(80);

      // Save the PDF
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      setProcessedPdfUrl(url);
      setProgress(100);
    } catch (err) {
      console.error('Error adding watermark:', err);

      // Provide more detailed error messages
      let errorMessage = 'Failed to add watermark';

      if (err instanceof Error) {
        console.log('Error type:', err.name, 'Message:', err.message);
        errorMessage = `Failed to add watermark: ${err.message}`;
      } else {
        errorMessage = `Failed to add watermark: ${String(err)}`;
      }

      setError(errorMessage);
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  };

  // Download processed PDF
  const handleDownload = () => {
    if (processedPdfUrl) {
      const link = document.createElement('a');
      link.href = processedPdfUrl;
      link.download = `watermarked_${file?.name || 'document.pdf'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="p-4 border rounded-lg bg-background">
            <h3 className="text-lg font-medium mb-3">Upload PDF</h3>
            <FileUploader
              onFilesSelected={(files: File[]) => files.length > 0 && handleFileSelected(files[0])}
              accept=".pdf"
              multiple={false}
              maxSizeInMB={50}
              icon={<FileText className="h-8 w-8 text-muted-foreground" />}
              buttonText="Upload PDF"
              dragActiveText="Drop your PDF here"
              dragInactiveText="Upload PDF file"
            />
          </div>

          {file && (
            <div className="p-4 border rounded-lg bg-background">
              <h3 className="text-lg font-medium mb-3">Watermark Settings</h3>

              <Tabs value={watermarkType} onValueChange={(v) => setWatermarkType(v as 'text' | 'image')} className="mb-4">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="text">Text Watermark</TabsTrigger>
                  <TabsTrigger value="image">Image Watermark</TabsTrigger>
                </TabsList>

                <TabsContent value="text" className="space-y-4 mt-4">
                  <div>
                    <Label htmlFor="watermarkText">Watermark Text</Label>
                    <Input
                      id="watermarkText"
                      value={watermarkText}
                      onChange={(e) => setWatermarkText(e.target.value)}
                      placeholder="Enter watermark text"
                    />
                  </div>

                  <div>
                    <Label htmlFor="textColor">Text Color</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="textColor"
                        type="color"
                        value={textColor}
                        onChange={(e) => setTextColor(e.target.value)}
                        className="w-12 h-10 p-1"
                      />
                      <span className="text-sm">{textColor}</span>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="fontSize">Font Size: {fontSize}</Label>
                    <Slider
                      id="fontSize"
                      min={12}
                      max={100}
                      step={1}
                      value={[fontSize]}
                      onValueChange={(values) => setFontSize(values[0])}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="image" className="space-y-4 mt-4">
                  <div>
                    <Label htmlFor="watermarkImage">Watermark Image</Label>
                    <div className="flex flex-col gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        className="w-full"
                        onClick={() => imageInputRef.current?.click()}
                      >
                        <FileImage className="mr-2 h-4 w-4" />
                        Select Image
                      </Button>
                      <Input
                        id="watermarkImage"
                        type="file"
                        accept="image/png,image/jpeg"
                        onChange={handleWatermarkImageSelected}
                        className="hidden"
                        ref={imageInputRef}
                      />
                    </div>

                    {imageWatermarkPreview && (
                      <div className="mt-2">
                        <div className="border rounded-md p-2">
                          <div className="relative h-24 w-full flex items-center justify-center">
                            <Image
                              src={imageWatermarkPreview}
                              alt="Watermark preview"
                              className="object-contain"
                              fill
                              sizes="(max-width: 768px) 100vw, 300px"
                              unoptimized
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="opacity">Opacity: {opacity}%</Label>
                  <Slider
                    id="opacity"
                    min={5}
                    max={100}
                    step={1}
                    value={[opacity]}
                    onValueChange={(values) => setOpacity(values[0])}
                  />
                </div>

                <div>
                  <Label htmlFor="rotation">Rotation: {rotation}°</Label>
                  <Slider
                    id="rotation"
                    min={0}
                    max={360}
                    step={5}
                    value={[rotation]}
                    onValueChange={(values) => setRotation(values[0])}
                  />
                </div>

                <div>
                  <Label htmlFor="position">Position</Label>
                  <Select value={position} onValueChange={setPosition}>
                    <SelectTrigger id="position">
                      <SelectValue placeholder="Select position" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="center">Center</SelectItem>
                      <SelectItem value="topLeft">Top Left</SelectItem>
                      <SelectItem value="topRight">Top Right</SelectItem>
                      <SelectItem value="bottomLeft">Bottom Left</SelectItem>
                      <SelectItem value="bottomRight">Bottom Right</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <Switch
                      id="allPages"
                      checked={allPages}
                      onCheckedChange={setAllPages}
                    />
                    <Label htmlFor="allPages">Apply to all pages</Label>
                  </div>

                  {!allPages && (
                    <div>
                      <Label htmlFor="pageRange">Page Range</Label>
                      <Input
                        id="pageRange"
                        value={pageRange}
                        onChange={(e) => setPageRange(e.target.value)}
                        placeholder="e.g., 1-3, 5, 7-9"
                        disabled={allPages}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Specify pages or ranges separated by commas (e.g., 1-3, 5, 7-9)
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-4 flex gap-2">
                <Button
                  onClick={handleAddWatermark}
                  disabled={isProcessing || !file || (watermarkType === 'image' && !imageWatermark)}
                  className="flex-1"
                >
                  {isProcessing ? 'Processing...' : 'Add Watermark'}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleReset}
                  disabled={isProcessing}
                >
                  Reset
                </Button>
              </div>

              {isProcessing && (
                <Progress value={progress} className="mt-4" />
              )}
            </div>
          )}
        </div>

        <div className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {(file || processedPdfUrl) && (
            <div className="border rounded-lg p-4 bg-background">
              <h3 className="text-lg font-medium mb-3">
                {processedPdfUrl ? 'Watermarked PDF' : 'PDF Preview'}
              </h3>

              <div className="mb-4 aspect-[3/4] bg-muted/20">
                {processedPdfUrl ? (
                  <PDFPreview
                    fileUrl={processedPdfUrl}
                    className="w-full h-full"
                  />
                ) : file ? (
                  <PDFPreview
                    file={file}
                    className="w-full h-full"
                  />
                ) : null}
              </div>

              {processedPdfUrl && (
                <Button
                  onClick={handleDownload}
                  className="w-full"
                  variant="default"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download Watermarked PDF
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PDFWatermarkTool;