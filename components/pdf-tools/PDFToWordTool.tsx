'use client';

import { useState, useRef } from 'react';
import { FileText, Upload, Trash2, AlertCircle, Download, FileType } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { pdfToWord, formatFileSize, downloadBlob, getPDFPageCount } from '@/lib/pdf-utils';

interface PDFFile {
  id: string;
  name: string;
  size: number;
  file: File;
  pageCount?: number;
}

export function PDFToWordTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [wordDocUrl, setWordDocUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFile(selectedFiles[0]);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFile = async (selectedFile: File) => {
    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    try {
      // Get actual page count using our utility function
      const pageCount = await getPDFPageCount(selectedFile);

      const newFile: PDFFile = {
        id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: selectedFile.name,
        size: selectedFile.size,
        file: selectedFile,
        pageCount: pageCount
      };

      setFile(newFile);
      setWordDocUrl(null);
      setError(null);
    } catch (err) {
      console.error('Error loading PDF:', err);
      toast.error("Error loading PDF", {
        description: "The selected file could not be processed. Please try another PDF file."
      });
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    // Only take the first file for conversion
    addFile(droppedFiles[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = () => {
    setFile(null);
    setWordDocUrl(null);
    setError(null);
  };

  const handleConvertToWord = async () => {
    if (!file) {
      setError("Please add a PDF file to convert.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);
    setWordDocUrl(null);

    try {
      // Use the utility function to convert PDF to Word
      const wordBlob = await pdfToWord(
        file.file,
        (progress) => setProgress(progress)
      );
      
      // Create a URL for the blob
      const url = URL.createObjectURL(wordBlob);
      
      setWordDocUrl(url);
      toast.success("Success!", {
        description: `Your PDF has been converted to a Word document.`
      });
    } catch (err) {
      console.error('Error converting PDF to Word:', err);
      setError("An error occurred while converting the PDF. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadWordDoc = () => {
    if (!wordDocUrl || !file) return;
    
    const fileName = file.name.replace('.pdf', '.docx');
    
    // Create a blob from the URL and download it
    fetch(wordDocUrl)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName));
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf"
            className="hidden"
          />

          <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop a PDF File Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={handleBrowseClick}
            variant="outline"
            className="border-blue-300 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            <Upload className="mr-2 h-4 w-4" />
            Browse Files
          </Button>
        </div>
      )}

      {/* Selected File */}
      {file && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Selected PDF</h3>
            <Button
              onClick={removeFile}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-red-500"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(file.size)} • {file.pageCount} pages
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Conversion Notes */}
      {file && !wordDocUrl && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Conversion Notes</h3>
          </div>
          <div className="p-4">
            <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
              <p>
                <strong>Text Extraction:</strong> Our converter will extract all text content from your PDF and preserve basic formatting.
              </p>
              <p>
                <strong>Formatting Limitations:</strong> Complex layouts, tables, and images may not be perfectly preserved in the converted document.
              </p>
              <p>
                <strong>Best Results:</strong> PDFs with simple text layouts and minimal complex formatting will convert most accurately.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Processing...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center pt-4">
        {wordDocUrl ? (
          <Button
            onClick={downloadWordDoc}
            className="bg-blue-500 hover:bg-blue-600 text-white"
            size="lg"
          >
            <Download className="mr-2 h-5 w-5" />
            Download Word Document
          </Button>
        ) : (
          <Button
            onClick={handleConvertToWord}
            disabled={!file || isProcessing}
            className="bg-blue-500 hover:bg-blue-600 text-white"
            size="lg"
          >
            <FileType className="mr-2 h-5 w-5" />
            Convert to Word
          </Button>
        )}
      </div>
    </div>
  );
}
