'use client';

import { PDFToolsGrid } from './PDFToolsGrid';

interface PDFTool {
  name: string;
  description: string;
  iconName: string;
  href: string;
  color: string;
  bgColor: string;
  borderColor: string;
}

interface PDFToolCategory {
  category: string;
  tools: PDFTool[];
}

interface PDFToolsGridWrapperProps {
  pdfTools: PDFToolCategory[];
}

export default function PDFToolsGridWrapper({ pdfTools }: PDFToolsGridWrapperProps) {
  return <PDFToolsGrid pdfTools={pdfTools} />;
}
