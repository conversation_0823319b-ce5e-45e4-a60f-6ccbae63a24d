'use client';

import { useState, useRef, useEffect } from 'react';
import { FileText, Upload, X, Trash2, AlertCircle, Download, Minimize, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { compressPDF, formatFileSize, downloadBlob } from '@/lib/pdf-utils';

interface PDFFile {
  id: string;
  name: string;
  size: number;
  file: File;
}

type CompressionLevel = 'low' | 'medium' | 'high' | 'very-high' | 'custom';

export function PDFCompressTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [compressedPdfUrl, setCompressedPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [compressionLevel, setCompressionLevel] = useState<CompressionLevel>('medium');
  const [customCompressionValue, setCustomCompressionValue] = useState<number[]>([50]);
  const [compressionStats, setCompressionStats] = useState<{ originalSize: number; compressedSize: number; reduction: number } | null>(null);
  const [compressionWarning, setCompressionWarning] = useState<string | null>(null);
  const [isBrowserCompatible, setIsBrowserCompatible] = useState<boolean>(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check browser compatibility on component mount
  useEffect(() => {
    // Test if the browser supports basic PDF operations
    const checkBrowserCompatibility = () => {
      try {
        // Check if Blob is supported
        if (typeof Blob === 'undefined') {
          setIsBrowserCompatible(false);
          return;
        }

        // Check if ArrayBuffer is supported
        if (typeof ArrayBuffer === 'undefined') {
          setIsBrowserCompatible(false);
          return;
        }

        // All checks passed
        setIsBrowserCompatible(true);
      } catch (error) {
        console.error('Browser compatibility check failed:', error);
        setIsBrowserCompatible(false);
      }
    };

    checkBrowserCompatibility();
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFile(selectedFiles[0]);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFile = (selectedFile: File) => {
    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    const newFile: PDFFile = {
      id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: selectedFile.name,
      size: selectedFile.size,
      file: selectedFile
    };

    setFile(newFile);
    setCompressedPdfUrl(null);
    setCompressionStats(null);
    setError(null);
    setCompressionWarning(null);

    // Add size warning for very small files
    if (selectedFile.size < 100000) { // 100KB
      setCompressionWarning("This PDF is already small in size. Compression might have limited effect.");
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    // Only take the first file for compression
    addFile(droppedFiles[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = () => {
    setFile(null);
    setCompressedPdfUrl(null);
    setCompressionStats(null);
    setError(null);
    setCompressionWarning(null);
  };

  const getCompressionQuality = (): number => {
    switch (compressionLevel) {
      case 'low': return 0.8;
      case 'medium': return 0.6;
      case 'high': return 0.4;
      case 'very-high': return 0.2;
      case 'custom': return 1 - (customCompressionValue[0] / 100);
      default: return 0.6;
    }
  };

  const handleCompressPDF = async () => {
    if (!file) return;

    try {
      setIsProcessing(true);
      setProgress(0);
      setError(null);
      setCompressionWarning(null);
      setCompressedPdfUrl(null);
      setCompressionStats(null);

      setProgress(5); // Initial progress

      const compressedBlob = await compressPDF(
        file.file,
        getCompressionQuality(),
        (progress: number) => {
          // The compression function provides progress from 0-100
          // We'll map this to the 5-95% range
          setProgress(5 + Math.floor(progress * 0.9));
        }
      );
      const compressedBytes = await compressedBlob.arrayBuffer();
      const originalSize = file.size;
      const compressedSize = compressedBlob.size;
      const reductionAchieved = (1 - compressedSize / originalSize) * 100;

      setProgress(95);

      // Create a URL for the compressed PDF
      const blob = new Blob([compressedBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      // Get the file sizes for display
      const newSize = blob.size;
      const oldSize = file.size;
      const percentReduction = ((oldSize - newSize) / oldSize * 100).toFixed(1);

      setCompressedPdfUrl(url);
      setCompressionStats({
        originalSize,
        compressedSize,
        reduction: reductionAchieved
      });
      setCompressionWarning(reductionAchieved < 10 ? "This PDF didn't compress well. It may already be optimized or consist mostly of text content." : null);
      setProgress(100);

      // Small delay to show the completion progress
      setTimeout(() => {
        setIsProcessing(false);
      }, 500);

    } catch (error) {
      console.error('Error compressing PDF:', error);
      setError(error instanceof Error ? error.message : 'Failed to compress the PDF. Please try again with a different file.');
      setIsProcessing(false);
    }
  };

  const downloadCompressedPDF = () => {
    if (!compressedPdfUrl || !file) return;

    const fileName = file.name.replace('.pdf', '_compressed.pdf');

    // Create a blob from the URL and download it
    fetch(compressedPdfUrl)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName))
      .catch(err => {
        console.error('Error downloading compressed PDF:', err);
        setError('Failed to download the compressed PDF. Please try again.');
      });
  };

  const renderCompressionOptions = () => (
    <div className="p-4 space-y-4">
      <div className="text-sm text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 p-3 rounded-md flex items-start">
        <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
        <div>
          <p className="mb-2">Higher compression levels may reduce image quality and text clarity. Choose the appropriate level based on your needs.</p>
          <ul className="list-disc list-inside text-xs">
            <li>For important documents, choose Low compression</li>
            <li>For general use, Medium is recommended</li>
            <li>For storage optimization, use High or Very High</li>
          </ul>
        </div>
      </div>

      <RadioGroup
        value={compressionLevel}
        onValueChange={(value) => setCompressionLevel(value as CompressionLevel)}
        className="space-y-4"
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="low" id="low" />
          <Label htmlFor="low">Low Compression (Best Quality)</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="medium" id="medium" />
          <Label htmlFor="medium">Medium Compression (Good Quality)</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="high" id="high" />
          <Label htmlFor="high">High Compression (Smaller Size)</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="very-high" id="very-high" />
          <Label htmlFor="very-high">Very High Compression (Smallest Size)</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="custom" id="custom" />
          <Label htmlFor="custom">Custom Compression</Label>
        </div>

        {compressionLevel === 'custom' && (
          <div className="pl-6 pt-2">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>Better Quality</span>
                  <span>Compression Level: {customCompressionValue[0]}%</span>
                  <span>Smaller Size</span>
                </div>
                <Slider
                  value={customCompressionValue}
                  onValueChange={setCustomCompressionValue}
                  max={100}
                  step={5}
                  className="w-full"
                />
              </div>

              <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-md text-xs text-gray-600 dark:text-gray-400">
                <p className="font-medium mb-1">Expected results at {customCompressionValue[0]}% compression:</p>
                <ul className="list-disc list-inside space-y-1">
                  {customCompressionValue[0] <= 20 && (
                    <li>Minimal compression, preserves nearly all quality</li>
                  )}
                  {customCompressionValue[0] > 20 && customCompressionValue[0] <= 40 && (
                    <li>Light compression, good for documents with images</li>
                  )}
                  {customCompressionValue[0] > 40 && customCompressionValue[0] <= 60 && (
                    <li>Medium compression, balanced quality and size</li>
                  )}
                  {customCompressionValue[0] > 60 && customCompressionValue[0] <= 80 && (
                    <li>High compression, may affect image quality</li>
                  )}
                  {customCompressionValue[0] > 80 && (
                    <li>Very high compression, significant quality reduction</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        )}
      </RadioGroup>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Browser Compatibility Warning */}
      {!isBrowserCompatible && (
        <Alert className="bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800">
          <Info className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertDescription className="text-amber-600 dark:text-amber-400">
            Your browser may have limited support for PDF compression. For best results, use Chrome, Firefox, or Edge.
          </AlertDescription>
        </Alert>
      )}

      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf"
            className="hidden"
          />

          <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop a PDF File Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={handleBrowseClick}
            variant="outline"
            className="border-green-300 dark:border-green-700 hover:bg-green-50 dark:hover:bg-green-900/20"
          >
            <Upload className="mr-2 h-4 w-4" />
            Browse Files
          </Button>
        </div>
      )}

      {/* Selected File */}
      {file && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Selected PDF</h3>
            <Button
              onClick={removeFile}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-red-500"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                <FileText className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(file.size)}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Compression Options */}
      {file && !compressedPdfUrl && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Compression Options</h3>
          </div>
          {renderCompressionOptions()}
        </div>
      )}

      {/* Compression Results */}
      {compressionStats && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Compression Results</h3>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <p className="text-xs text-gray-500 dark:text-gray-400">Original Size</p>
                <p className="text-lg font-semibold text-gray-800 dark:text-gray-200">{formatFileSize(compressionStats.originalSize)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-500 dark:text-gray-400">Compressed Size</p>
                <p className="text-lg font-semibold text-gray-800 dark:text-gray-200">{formatFileSize(compressionStats.compressedSize)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-500 dark:text-gray-400">Reduction</p>
                <p className="text-lg font-semibold text-green-600 dark:text-green-400">{compressionStats.reduction}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Warning Message */}
      {compressionWarning && !error && (
        <Alert className="bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800">
          <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertDescription className="text-amber-600 dark:text-amber-400">{compressionWarning}</AlertDescription>
        </Alert>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Processing...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center pt-4">
        {compressedPdfUrl ? (
          <Button
            onClick={downloadCompressedPDF}
            className="bg-green-500 hover:bg-green-600 text-white"
            size="lg"
          >
            <Download className="mr-2 h-5 w-5" />
            Download Compressed PDF
          </Button>
        ) : (
          <Button
            onClick={handleCompressPDF}
            disabled={!file || isProcessing}
            className="bg-green-500 hover:bg-green-600 text-white"
            size="lg"
          >
            <Minimize className="mr-2 h-5 w-5" />
            Compress PDF
          </Button>
        )}
      </div>
    </div>
  );
}
