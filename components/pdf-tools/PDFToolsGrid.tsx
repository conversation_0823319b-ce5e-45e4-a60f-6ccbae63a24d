'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Star, ChevronDown, ChevronUp, Pin, FileUp, FilePlus, FileText, FileImage, FileDown, RotateCw, FileOutput, FileInput, FilePieChart, FileSpreadsheet, FileSliders, FileX, FileLock, FileKey, FileSearch, FileWarning } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface PDFTool {
  name: string;
  description: string;
  iconName: string;
  href: string;
  color: string;
  bgColor: string;
  borderColor: string;
}

interface PDFToolCategory {
  category: string;
  tools: PDFTool[];
}

interface PDFToolsGridProps {
  pdfTools: PDFToolCategory[];
}

// Map of icon names to components
const iconMap = {
  FileUp,
  FilePlus,
  FileText,
  FileImage,
  FileDown,
  RotateCw,
  FileOutput,
  FileInput,
  <PERSON>PieChart,
  FileSpreadsheet,
  FileSliders,
  FileX,
  FileLock,
  FileKey,
  FileSearch,
  FileWarning
};

export function PDFToolsGrid({ pdfTools }: PDFToolsGridProps) {
  // State for pinned tools
  const [pinnedTools, setPinnedTools] = useState<PDFTool[]>([]);
  
  // State for collapsed sections
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({});
  
  // Load preferences from local storage on component mount
  useEffect(() => {
    const storedPinnedTools = localStorage.getItem('pinnedPDFTools');
    const storedCollapsedSections = localStorage.getItem('collapsedPDFSections');
    
    if (storedPinnedTools) {
      try {
        setPinnedTools(JSON.parse(storedPinnedTools));
      } catch (error) {
        console.error('Error loading pinned tools:', error);
      }
    }
    
    if (storedCollapsedSections) {
      try {
        setCollapsedSections(JSON.parse(storedCollapsedSections));
      } catch (error) {
        console.error('Error loading collapsed sections:', error);
      }
    }
  }, []);
  
  // Save preferences to local storage when they change
  useEffect(() => {
    localStorage.setItem('pinnedPDFTools', JSON.stringify(pinnedTools));
  }, [pinnedTools]);
  
  useEffect(() => {
    localStorage.setItem('collapsedPDFSections', JSON.stringify(collapsedSections));
  }, [collapsedSections]);
  
  // Toggle pinned status of a tool
  const togglePinned = (tool: PDFTool) => {
    if (isPinned(tool)) {
      setPinnedTools(pinnedTools.filter(t => t.href !== tool.href));
    } else {
      setPinnedTools([...pinnedTools, tool]);
    }
  };
  
  // Check if a tool is pinned
  const isPinned = (tool: PDFTool) => {
    return pinnedTools.some(t => t.href === tool.href);
  };
  
  // Toggle collapsed status of a section
  const toggleCollapsed = (category: string) => {
    setCollapsedSections({
      ...collapsedSections,
      [category]: !collapsedSections[category]
    });
  };
  
  // Check if a section is collapsed
  const isCollapsed = (category: string) => {
    return collapsedSections[category] === true;
  };
  
  // Render a single tool card
  const renderToolCard = (tool: PDFTool, isPinnable: boolean = true) => {
    // Get the icon component from the iconName
    const IconComponent = iconMap[tool.iconName as keyof typeof iconMap] || FileText;
    
    return (
      <div className="relative group">
        <Link href={tool.href}>
          <div className={`p-4 rounded-lg border ${tool.borderColor} hover:shadow-md hover:-translate-y-1 transition-all duration-300 h-full bg-white dark:bg-gray-800 group relative overflow-hidden`}>
            {/* Background decoration */}
            <div className="absolute inset-0 bg-gradient-to-br from-transparent to-gray-100 dark:to-gray-800/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div className="relative">
              <div className="flex items-center gap-3 mb-2">
                <div className={`w-10 h-10 rounded-lg ${tool.bgColor} flex items-center justify-center shadow-sm`}>
                  <IconComponent className={`w-5 h-5 ${tool.color}`} />
                </div>
                <h3 className="text-base font-semibold text-gray-800 dark:text-gray-200 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">{tool.name}</h3>
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-400">{tool.description}</p>
            </div>
          </div>
        </Link>
        
        {isPinnable && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-1 right-1 h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.preventDefault();
                    togglePinned(tool);
                  }}
                >
                  <Star className={`h-4 w-4 ${isPinned(tool) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-400'}`} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isPinned(tool) ? 'Unpin from favorites' : 'Pin to favorites'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-10 mb-10 px-2">
      {/* Pinned Tools Section */}
      {pinnedTools.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <div className="h-1 w-6 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full"></div>
            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 bg-gradient-to-r from-yellow-500 to-amber-500 bg-clip-text text-transparent flex items-center gap-2">
              <Pin className="h-4 w-4" /> Favorites
            </h2>
            <div className="h-1 flex-grow bg-gradient-to-r from-amber-500 to-transparent rounded-full"></div>
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
            {pinnedTools.map((tool, index) => (
              <div key={`pinned-${index}`}>
                {renderToolCard(tool, false)}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Regular Categories */}
      {pdfTools.map((category, index) => (
        <div key={index} className="space-y-4">
          <div 
            className="flex items-center gap-2 mb-3 cursor-pointer" 
            onClick={() => toggleCollapsed(category.category)}
          >
            <div className="h-1 w-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 bg-gradient-to-r from-gray-800 to-gray-600 dark:from-gray-200 dark:to-gray-400 bg-clip-text text-transparent flex items-center gap-2">
              {category.category}
              {isCollapsed(category.category) ? (
                <ChevronDown className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronUp className="h-5 w-5 text-gray-400" />
              )}
            </h2>
            <div className="h-1 flex-grow bg-gradient-to-r from-purple-500 to-transparent rounded-full"></div>
          </div>
          
          {!isCollapsed(category.category) && (
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
              {category.tools.map((tool, toolIndex) => (
                <div key={toolIndex}>
                  {renderToolCard(tool)}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
