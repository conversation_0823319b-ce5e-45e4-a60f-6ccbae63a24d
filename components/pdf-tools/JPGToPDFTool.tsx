'use client';

import { useState, useRef } from 'react';
import { FileText, Upload, Trash2, AlertCircle, Download, FileImage, FilePlus, ArrowUp, ArrowDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { jpgToPdf, formatFileSize, downloadBlob } from '@/lib/pdf-utils';

interface ImageFile {
  id: string;
  name: string;
  size: number;
  file: File;
  preview: string;
}

export function JPGToPDFTool() {
  const [files, setFiles] = useState<ImageFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFiles(Array.from(selectedFiles));
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFiles = async (selectedFiles: File[]) => {
    const validImageTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    const validFiles = selectedFiles.filter(file => validImageTypes.includes(file.type));
    
    if (validFiles.length === 0) {
      toast.error("Invalid file type", {
        description: "Only JPG and PNG images are allowed."
      });
      return;
    }

    if (validFiles.length !== selectedFiles.length) {
      toast.warning("Some files were skipped", {
        description: "Only JPG and PNG images are supported."
      });
    }

    const newFiles: ImageFile[] = await Promise.all(
      validFiles.map(async (file) => {
        const preview = URL.createObjectURL(file);
        return {
          id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          name: file.name,
          size: file.size,
          file: file,
          preview: preview
        };
      })
    );

    setFiles(prev => [...prev, ...newFiles]);
    setPdfUrl(null);
    setError(null);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    addFiles(Array.from(droppedFiles));
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = (id: string) => {
    setFiles(prev => {
      const updatedFiles = prev.filter(file => file.id !== id);
      // If all files are removed, reset the PDF URL
      if (updatedFiles.length === 0) {
        setPdfUrl(null);
      }
      return updatedFiles;
    });
  };

  const moveFileUp = (index: number) => {
    if (index === 0) return;
    
    setFiles(prev => {
      const newFiles = [...prev];
      [newFiles[index - 1], newFiles[index]] = [newFiles[index], newFiles[index - 1]];
      return newFiles;
    });
  };

  const moveFileDown = (index: number) => {
    if (index === files.length - 1) return;
    
    setFiles(prev => {
      const newFiles = [...prev];
      [newFiles[index], newFiles[index + 1]] = [newFiles[index + 1], newFiles[index]];
      return newFiles;
    });
  };

  const clearAllFiles = () => {
    // Revoke all object URLs to prevent memory leaks
    files.forEach(file => URL.revokeObjectURL(file.preview));
    setFiles([]);
    setPdfUrl(null);
    setError(null);
  };

  const handleCreatePDF = async () => {
    if (files.length === 0) {
      setError("Please add at least one image to convert.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);
    setPdfUrl(null);

    try {
      // Use the utility function to convert images to PDF
      const pdfBlob = await jpgToPdf(
        files.map(file => file.file),
        (progress) => setProgress(progress)
      );
      
      // Create a URL for the blob
      const url = URL.createObjectURL(pdfBlob);
      
      setPdfUrl(url);
      toast.success("Success!", {
        description: `Your images have been converted to a PDF file.`
      });
    } catch (err) {
      console.error('Error converting images to PDF:', err);
      setError("An error occurred while creating the PDF. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadPDF = () => {
    if (!pdfUrl) return;
    
    // Create a filename based on the first image's name or a default name
    const baseFileName = files.length > 0 
      ? files[0].name.replace(/\.(jpg|jpeg|png)$/i, '') 
      : 'converted';
    const fileName = `${baseFileName}.pdf`;
    
    // Create a blob from the URL and download it
    fetch(pdfUrl)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName));
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center ${
          isDragging
            ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20'
            : 'border-gray-300 dark:border-gray-700'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".jpg,.jpeg,.png"
          multiple
          className="hidden"
        />

        <FileImage className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

        <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
          Drag & Drop Images Here
        </h3>

        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Or click the button below to select files
        </p>

        <Button
          onClick={handleBrowseClick}
          variant="outline"
          className="border-indigo-300 dark:border-indigo-700 hover:bg-indigo-50 dark:hover:bg-indigo-900/20"
        >
          <Upload className="mr-2 h-4 w-4" />
          Browse Files
        </Button>
      </div>

      {/* Selected Files */}
      {files.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Selected Images ({files.length})</h3>
            <Button
              onClick={clearAllFiles}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-red-500"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          </div>
          <div className="p-4">
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {files.map((file, index) => (
                <div key={file.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50">
                  <div className="w-12 h-12 rounded-lg bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center overflow-hidden">
                    <img 
                      src={file.preview} 
                      alt={file.name} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      onClick={() => moveFileUp(index)}
                      variant="ghost"
                      size="sm"
                      disabled={index === 0}
                      className="text-gray-500 hover:text-indigo-500 h-8 w-8 p-0"
                    >
                      <ArrowUp className="h-4 w-4" />
                    </Button>
                    <Button
                      onClick={() => moveFileDown(index)}
                      variant="ghost"
                      size="sm"
                      disabled={index === files.length - 1}
                      className="text-gray-500 hover:text-indigo-500 h-8 w-8 p-0"
                    >
                      <ArrowDown className="h-4 w-4" />
                    </Button>
                    <Button
                      onClick={() => removeFile(file.id)}
                      variant="ghost"
                      size="sm"
                      className="text-gray-500 hover:text-red-500 h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Processing...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center pt-4">
        {pdfUrl ? (
          <Button
            onClick={downloadPDF}
            className="bg-indigo-500 hover:bg-indigo-600 text-white"
            size="lg"
          >
            <Download className="mr-2 h-5 w-5" />
            Download PDF
          </Button>
        ) : (
          <Button
            onClick={handleCreatePDF}
            disabled={files.length === 0 || isProcessing}
            className="bg-indigo-500 hover:bg-indigo-600 text-white"
            size="lg"
          >
            <FilePlus className="mr-2 h-5 w-5" />
            Create PDF
          </Button>
        )}
      </div>
    </div>
  );
}
