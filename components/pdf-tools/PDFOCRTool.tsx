'use client';

import { useRef, useState } from 'react';
import {
  FileText,
  Upload,
  Trash2,
  Download,
  Languages,
  AlertCircle,
  Settings2
} from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';

import { getPDFPageCount, formatFileSize } from '@/lib/pdf-utils';
import { PDFFile } from '@/types/pdf';

// PDFLib for OCR would go here in a real implementation
// import { createWorker } from 'tesseract.js';

export function PDFOCRTool() {
  const [file, setFile] = useState<PDFFile | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [processedPdfUrl, setProcessedPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [language, setLanguage] = useState<string>('eng');
  const [quality, setQuality] = useState<number[]>([2]);
  const [enhanceText, setEnhanceText] = useState<boolean>(true);
  const [ocrMode, setOcrMode] = useState<'page' | 'text'>('page');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    addFile(selectedFiles[0]);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFile = async (selectedFile: File) => {
    if (selectedFile.type !== 'application/pdf') {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
      return;
    }

    try {
      // Get actual page count using our utility function
      const pageCount = await getPDFPageCount(selectedFile);

      const newFile: PDFFile = {
        id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: selectedFile.name,
        size: selectedFile.size,
        file: selectedFile,
        pageCount: pageCount
      };

      setFile(newFile);
      setProcessedPdfUrl(null);
      setError(null);
    } catch (err) {
      console.error('Error loading PDF:', err);
      toast.error("Error loading PDF", {
        description: "The selected file could not be processed. Please try another PDF file."
      });
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    // Only take the first file for OCR
    addFile(droppedFiles[0]);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = () => {
    setFile(null);
    setProcessedPdfUrl(null);
    setError(null);
  };

  const handleOcrPDF = async () => {
    if (!file) {
      setError("Please add a PDF file to process.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // Simulate OCR processing with a delay
      // In a real implementation, this would use a library like Tesseract.js or a backend service
      for (let i = 0; i <= 100; i += 5) {
        setProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Simulate creating a searchable PDF
      // In a real implementation, this would combine the OCR results with the original PDF
      const processedPdfBlob = new Blob([await file.file.arrayBuffer()], { type: 'application/pdf' });
      const url = URL.createObjectURL(processedPdfBlob);
      setProcessedPdfUrl(url);

      toast.success("OCR Completed", {
        description: "Your PDF has been processed successfully."
      });

    } catch (err) {
      console.error('Error processing PDF:', err);
      setError("An error occurred while processing the PDF. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadProcessedPDF = () => {
    if (!processedPdfUrl || !file) return;

    const link = document.createElement('a');
    link.href = processedPdfUrl;
    const fileName = file.name.replace(/\.pdf$/i, '_ocr.pdf');
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const qualityOptions = [
    { value: 1, label: 'Fast (Lower Quality)' },
    { value: 2, label: 'Standard' },
    { value: 3, label: 'Precise (Slower)' },
  ];

  const languages = [
    { value: 'eng', label: 'English' },
    { value: 'spa', label: 'Spanish' },
    { value: 'fra', label: 'French' },
    { value: 'deu', label: 'German' },
    { value: 'ita', label: 'Italian' },
    { value: 'por', label: 'Portuguese' },
    { value: 'rus', label: 'Russian' },
    { value: 'chi_sim', label: 'Chinese (Simplified)' },
    { value: 'jpn', label: 'Japanese' },
    { value: 'kor', label: 'Korean' },
  ];

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      {!file && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging
              ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
              : 'border-gray-300 dark:border-gray-700'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf"
            className="hidden"
            data-testid="file-input"
          />

          <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

          <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
            Drag & Drop a PDF File Here
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Or click the button below to select a file
          </p>

          <Button
            onClick={handleBrowseClick}
            variant="outline"
            className="border-purple-300 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20"
          >
            <Upload className="mr-2 h-4 w-4" />
            Browse Files
          </Button>
        </div>
      )}

      {/* Selected File */}
      {file && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">Selected PDF</h3>
            <Button
              onClick={removeFile}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-red-500"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <FileText className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(file.size)} • {file.pageCount} pages
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* OCR Options */}
      {file && !processedPdfUrl && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 className="font-medium text-gray-700 dark:text-gray-300">OCR Settings</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              className="text-gray-500"
            >
              <Settings2 className="h-4 w-4 mr-1" />
              {showAdvancedOptions ? 'Hide' : 'Show'} Advanced
            </Button>
          </div>
          <div className="p-4 space-y-6">
            {/* Basic Settings */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="language">Language</Label>
                <Select value={language} onValueChange={setLanguage}>
                  <SelectTrigger id="language" className="w-full">
                    <SelectValue placeholder="Select Language" />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map(lang => (
                      <SelectItem key={lang.value} value={lang.value}>
                        {lang.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Select the primary language of your document for better OCR accuracy.
                </p>
              </div>

              <div className="space-y-2">
                <Label>OCR Mode</Label>
                <Tabs value={ocrMode} onValueChange={(value) => setOcrMode(value as 'page' | 'text')}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="page">Searchable PDF</TabsTrigger>
                    <TabsTrigger value="text">Extract Text</TabsTrigger>
                  </TabsList>
                  <TabsContent value="page" className="pt-2">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Creates a searchable PDF that looks identical to the original but with selectable text.
                    </p>
                  </TabsContent>
                  <TabsContent value="text" className="pt-2">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Extracts text from the document into a separate text file.
                    </p>
                  </TabsContent>
                </Tabs>
              </div>

              <div className="space-y-2">
                <Label>Recognition Quality</Label>
                <div className="pt-2">
                  <Slider
                    value={quality}
                    onValueChange={setQuality}
                    max={3}
                    min={1}
                    step={1}
                  />
                  <div className="flex justify-between mt-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">Fast</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {quality[0] === 1 ? 'Fast' : quality[0] === 2 ? 'Standard' : 'Precise'}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">Precise</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Advanced Settings */}
            {showAdvancedOptions && (
              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Advanced Options</h4>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="enhanceText">Enhance Text Clarity</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Improves recognition for blurry or low-contrast text
                      </p>
                    </div>
                    <Switch
                      id="enhanceText"
                      checked={enhanceText}
                      onCheckedChange={setEnhanceText}
                    />
                  </div>

                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="item-1">
                      <AccordionTrigger className="text-sm">Additional Processing Options</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2 pt-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="deskew" />
                            <Label htmlFor="deskew">Deskew pages</Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox id="multiColumn" />
                            <Label htmlFor="multiColumn">Detect multiple columns</Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox id="tables" />
                            <Label htmlFor="tables">Recognize tables</Label>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Processing OCR...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Action Buttons */}
      {file && !processedPdfUrl && !isProcessing && (
        <div className="flex justify-end">
          <Button
            onClick={handleOcrPDF}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Languages className="mr-2 h-4 w-4" />
            Process with OCR
          </Button>
        </div>
      )}

      {/* Download Button */}
      {processedPdfUrl && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 flex flex-col sm:flex-row justify-between items-center gap-4">
          <div>
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-1">Processing Complete!</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Your PDF is now searchable and the text is selectable.
            </p>
          </div>
          <Button onClick={downloadProcessedPDF} className="bg-purple-600 hover:bg-purple-700 min-w-[140px]">
            <Download className="mr-2 h-4 w-4" />
            Download PDF
          </Button>
        </div>
      )}
    </div>
  );
}
