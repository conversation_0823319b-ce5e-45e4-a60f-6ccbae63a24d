'use client';

import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// Dynamically import PDFRepairTool with SSR disabled
const PDFRepairTool = dynamic(() => import('../PDFRepairTool').then(mod => ({ default: mod.PDFRepairTool })), {
  ssr: false,
  loading: () => (
    <div className="w-full h-64 flex items-center justify-center">
      <Loader2 className="h-8 w-8 text-amber-500 animate-spin" />
    </div>
  )
});

export default function PDFRepairToolClient() {
  return <PDFRepairTool />;
} 