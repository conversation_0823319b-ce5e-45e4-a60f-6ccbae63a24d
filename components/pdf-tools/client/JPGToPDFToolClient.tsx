'use client';

import dynamic from 'next/dynamic';
import { FileUp } from 'lucide-react';

// Use dynamic import with SSR disabled
const JPGToPDFTool = dynamic(
  () => import('../JPGToPDFTool').then(mod => mod.JPGToPDFTool),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mb-4">
          <FileUp className="h-8 w-8 text-red-600 dark:text-red-400" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">Loading JPG to PDF Tool...</h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Please wait while we initialize the conversion tool.
        </p>
      </div>
    )
  }
);

export function JPGToPDFToolClient() {
  return <JPGToPDFTool />;
}
