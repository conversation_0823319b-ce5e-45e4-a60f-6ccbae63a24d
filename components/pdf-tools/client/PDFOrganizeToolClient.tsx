'use client';

import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// Dynamically import PDFOrganizeTool with SSR disabled
const PDFOrganizeTool = dynamic(() => import('../PDFOrganizeTool').then(mod => ({ default: mod.PDFOrganizeTool })), {
  ssr: false,
  loading: () => (
    <div className="w-full h-64 flex items-center justify-center">
      <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
    </div>
  )
});

export default function PDFOrganizeToolClient() {
  return <PDFOrganizeTool />;
} 