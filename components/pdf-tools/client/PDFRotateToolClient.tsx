'use client';

import dynamic from 'next/dynamic';
import { RotateCw } from 'lucide-react';

// Use dynamic import with SSR disabled
const PDFRotateTool = dynamic(
  () => import('../PDFRotateTool').then(mod => mod.PDFRotateTool),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center mb-4">
          <RotateCw className="h-8 w-8 text-orange-600 dark:text-orange-400" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">Loading PDF Rotation Tool...</h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Please wait while we initialize the rotation tool.
        </p>
      </div>
    )
  }
);

export function PDFRotateToolClient() {
  return <PDFRotateTool />;
}
