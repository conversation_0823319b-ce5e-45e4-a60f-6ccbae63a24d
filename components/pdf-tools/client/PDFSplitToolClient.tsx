'use client';

import dynamic from 'next/dynamic';
import { FileText, Scissors } from 'lucide-react';

// Use dynamic import with SSR disabled
const PDFSplitTool = dynamic(
  () => import('../PDFSplitTool').then(mod => mod.PDFSplitTool),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center mb-4">
          <Scissors className="h-8 w-8 text-purple-600 dark:text-purple-400" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">Loading PDF Split Tool...</h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Please wait while we initialize the splitting tool.
        </p>
      </div>
    )
  }
);

export function PDFSplitToolClient() {
  return <PDFSplitTool />;
}
