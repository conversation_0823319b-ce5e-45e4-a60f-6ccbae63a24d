'use client';

import dynamic from 'next/dynamic';
import { FilePlus } from 'lucide-react';

// Use dynamic import with SSR disabled
const PDFMergeTool = dynamic(
  () => import('../PDFMergeTool').then(mod => mod.PDFMergeTool),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mb-4">
          <FilePlus className="h-8 w-8 text-blue-600 dark:text-blue-400" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">Loading PDF Merge Tool...</h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Please wait while we initialize the merging tool.
        </p>
      </div>
    )
  }
);

export function PDFMergeToolClient() {
  return <PDFMergeTool />;
}
