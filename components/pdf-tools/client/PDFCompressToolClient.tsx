'use client';

import dynamic from 'next/dynamic';
import { FileDown, Minimize } from 'lucide-react';

// Use dynamic import with SSR disabled
const PDFCompressTool = dynamic(
  () => import('../PDFCompressTool').then(mod => mod.PDFCompressTool),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mb-4">
          <Minimize className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">Loading PDF Compression Tool...</h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Please wait while we initialize the compression tool.
        </p>
      </div>
    )
  }
);

export function PDFCompressToolClient() {
  return <PDFCompressTool />;
}
