'use client';

import dynamic from 'next/dynamic';
import { FileImage } from 'lucide-react';

// Use dynamic import with SSR disabled
const PDFToJPGTool = dynamic(
  () => import('../PDFToJPGTool').then(mod => mod.PDFToJPGTool),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 rounded-full bg-pink-100 dark:bg-pink-900/30 flex items-center justify-center mb-4">
          <FileImage className="h-8 w-8 text-pink-600 dark:text-pink-400" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">Loading PDF to JPG Tool...</h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Please wait while we initialize the conversion tool.
        </p>
      </div>
    )
  }
);

export function PDFToJPGToolClient() {
  return <PDFToJPGTool />;
}
