'use client';

import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// Dynamically import PDFOCRTool with SSR disabled
const PDFOCRTool = dynamic(() => import('../PDFOCRTool').then(mod => ({ default: mod.PDFOCRTool })), {
  ssr: false,
  loading: () => (
    <div className="w-full h-64 flex items-center justify-center">
      <Loader2 className="h-8 w-8 text-purple-500 animate-spin" />
    </div>
  )
});

export default function PDFOCRToolClient() {
  return <PDFOCRTool />;
} 