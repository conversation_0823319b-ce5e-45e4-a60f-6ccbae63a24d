'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { Skeleton } from '../../ui/skeleton';

// Dynamically import the PDF tool with SSR disabled
const PDFWatermarkTool = dynamic(
  () => import('../PDFWatermarkTool'),
  {
    ssr: false,
    loading: () => (
      <div className="space-y-4">
        <Skeleton className="h-[300px] w-full rounded-lg" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
    ),
  }
);

const PDFWatermarkToolClient = () => {
  return <PDFWatermarkTool />;
};

export default PDFWatermarkToolClient; 