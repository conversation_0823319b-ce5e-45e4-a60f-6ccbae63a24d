'use client';

import { useState, useRef } from 'react';
import { FilePlus, Upload, X, ArrowDown, ArrowUp, Trash2, FileText, AlertCircle, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { mergePDFs, formatFileSize, downloadBlob } from '@/lib/pdf-utils';

interface PDFFile {
  id: string;
  name: string;
  size: number;
  file: File;
}

export function PDFMergeTool() {
  const [files, setFiles] = useState<PDFFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [mergedPdfUrl, setMergedPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles) return;

    addFiles(selectedFiles);
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const addFiles = (selectedFiles: FileList) => {
    const newFiles: PDFFile[] = [];
    let hasNonPdfFile = false;

    Array.from(selectedFiles).forEach(file => {
      if (file.type !== 'application/pdf') {
        hasNonPdfFile = true;
        return;
      }

      newFiles.push({
        id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: file.name,
        size: file.size,
        file
      });
    });

    if (hasNonPdfFile) {
      toast.error("Invalid file type", {
        description: "Only PDF files are allowed."
      });
    }

    setFiles(prev => [...prev, ...newFiles]);
    setError(null);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files.length > 0) {
      addFiles(e.dataTransfer.files);
    }
  };

  const removeFile = (id: string) => {
    setFiles(files.filter(file => file.id !== id));
    setMergedPdfUrl(null);
  };

  const moveFile = (id: string, direction: 'up' | 'down') => {
    const index = files.findIndex(file => file.id === id);
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === files.length - 1)
    ) {
      return;
    }

    const newFiles = [...files];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    // Swap the files
    [newFiles[index], newFiles[newIndex]] = [newFiles[newIndex], newFiles[index]];

    setFiles(newFiles);
    setMergedPdfUrl(null);
  };

  const clearAllFiles = () => {
    setFiles([]);
    setMergedPdfUrl(null);
    setError(null);
  };

  // Using formatFileSize from pdf-utils.ts

  const handleMergePDFs = async () => {
    if (files.length === 0) {
      setError("Please add at least one PDF file to merge.");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // Use the utility function to merge PDFs
      const mergedPdfBlob = await mergePDFs(
        files.map(file => file.file),
        (progress) => setProgress(progress)
      );

      // Create a URL for the blob
      const url = URL.createObjectURL(mergedPdfBlob);

      setMergedPdfUrl(url);
      toast.success("Success!", {
        description: "Your PDFs have been merged successfully."
      });
    } catch (err) {
      console.error('Error merging PDFs:', err);
      setError("An error occurred while merging the PDFs. Please try again.");
    } finally {
      setIsProcessing(false);
      setProgress(100);
    }
  };

  const downloadMergedPDF = () => {
    if (!mergedPdfUrl) return;

    // Create a filename based on the first file's name
    const baseFileName = files.length > 0 ? files[0].name.replace('.pdf', '') : 'document';
    const fileName = `${baseFileName}_merged.pdf`;

    // Create a blob from the URL and download it
    fetch(mergedPdfUrl)
      .then(res => res.blob())
      .then(blob => downloadBlob(blob, fileName));
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center ${
          isDragging
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-700'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf"
          multiple
          className="hidden"
        />

        <FilePlus className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />

        <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
          Drag & Drop PDF Files Here
        </h3>

        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Or click the button below to select files
        </p>

        <Button
          onClick={() => fileInputRef.current?.click()}
          className="bg-blue-500 hover:bg-blue-600 text-white"
        >
          <Upload className="mr-2 h-4 w-4" />
          Select PDF Files
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">
              {files.length} {files.length === 1 ? 'File' : 'Files'} Selected
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllFiles}
              className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Clear All
            </Button>
          </div>

          <div className="border rounded-lg overflow-hidden">
            <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 border-b">
              <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                <div className="col-span-6">Filename</div>
                <div className="col-span-3">Size</div>
                <div className="col-span-3 text-right">Actions</div>
              </div>
            </div>

            <div className="divide-y">
              {files.map((file, index) => (
                <div key={file.id} className="px-4 py-3 bg-white dark:bg-gray-900">
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-6 flex items-center">
                      <FileText className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" />
                      <span className="text-sm text-gray-700 dark:text-gray-300 truncate">{file.name}</span>
                    </div>
                    <div className="col-span-3">
                      <span className="text-sm text-gray-500 dark:text-gray-400">{formatFileSize(file.size)}</span>
                    </div>
                    <div className="col-span-3 flex justify-end space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => moveFile(file.id, 'up')}
                        disabled={index === 0}
                        className="h-8 w-8 p-0"
                      >
                        <ArrowUp className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => moveFile(file.id, 'down')}
                        disabled={index === files.length - 1}
                        className="h-8 w-8 p-0"
                      >
                        <ArrowDown className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Processing Status */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-500 dark:text-gray-400">Merging PDFs...</span>
            <span className="text-gray-700 dark:text-gray-300">{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center pt-4">
        {mergedPdfUrl ? (
          <Button
            onClick={downloadMergedPDF}
            className="bg-green-500 hover:bg-green-600 text-white"
            size="lg"
          >
            <Download className="mr-2 h-5 w-5" />
            Download Merged PDF
          </Button>
        ) : (
          <Button
            onClick={handleMergePDFs}
            disabled={files.length === 0 || isProcessing}
            className="bg-blue-500 hover:bg-blue-600 text-white"
            size="lg"
          >
            <FilePlus className="mr-2 h-5 w-5" />
            Merge PDFs
          </Button>
        )}
      </div>
    </div>
  );
}
