'use client';

import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Progress } from "@/components/ui/progress";
import { Heart, Copy, RefreshCw } from 'lucide-react';
import { Label } from '@/components/ui/label';

// Constants
const BASE_COOLDOWN_TIME = 15000; // 15 seconds base cooldown
const EXTRA_COOLDOWN_TIME = 5000; // 5 seconds extra per threshold
const USAGE_THRESHOLD = 5; // Number of uses before increasing cooldown
const WORD_LIMIT = 50;

// Animation definitions
const animations = [
  'animate-reveal-left',
  'animate-reveal-right',
  'animate-fade-up',
  'animate-typewriter',
  'animate-bounce-in'
];

interface PickupLineResponse {
  pickupLines: string[];
}

async function generatePickupLine(context: string, style: string, fromGender: string, toGender: string): Promise<PickupLineResponse> {
  const response = await fetch('/api/pickup-line', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ context, style, fromGender, toGender }),
  });

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to generate pickup line');
  }

  return response.json();
}

export function PickupLineGenerator() {
  // State management
  const [context, setContext] = useState('');
  const [style, setStyle] = useState('funny');
  const [fromGender, setFromGender] = useState('no-preference');
  const [toGender, setToGender] = useState('no-preference');
  const [output, setOutput] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [lastRequestTime, setLastRequestTime] = useState(0);
  const [cooldownRemaining, setCooldownRemaining] = useState(0);
  const [usageCount, setUsageCount] = useState(0);
  const [currentAnimation, setCurrentAnimation] = useState('');
  const [animationKey, setAnimationKey] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout>();

  // Animation effect
  useEffect(() => {
    if (output) {
      setCurrentAnimation(''); // Clear current animation
      setAnimationKey(prev => prev + 1); // Force re-render

      // Need to wait for next frame to apply new animation
      requestAnimationFrame(() => {
        const newIndex = Math.floor(Math.random() * animations.length);
        setCurrentAnimation(animations[newIndex]);
      });
    }
  }, [output]);

  // Cooldown management
  const getCurrentCooldownTime = () => {
    const extraTime = Math.floor(Math.max(0, usageCount - USAGE_THRESHOLD) * EXTRA_COOLDOWN_TIME);
    return BASE_COOLDOWN_TIME + extraTime;
  };

  useEffect(() => {
    if (cooldownRemaining > 0) {
      intervalRef.current = setInterval(() => {
        const remaining = Math.max(0, getCurrentCooldownTime() - (Date.now() - lastRequestTime));
        setCooldownRemaining(remaining);
        if (remaining === 0) {
          clearInterval(intervalRef.current);
        }
      }, 100);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [lastRequestTime, cooldownRemaining, usageCount]);

  // Word count effect
  useEffect(() => {
    const words = context.trim().split(/\s+/);
    setWordCount(context.trim() ? words.length : 0);
  }, [context]);

  // Submit handler
  const handleSubmit = async () => {
    if (!context.trim()) {
      toast.error('Please enter some context for the pickup line');
      return;
    }

    if (wordCount > WORD_LIMIT) {
      toast.error(`Text exceeds ${WORD_LIMIT} words limit`);
      return;
    }

    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    const currentCooldownTime = getCurrentCooldownTime();

    if (timeSinceLastRequest < currentCooldownTime) {
      const remainingTime = Math.ceil((currentCooldownTime - timeSinceLastRequest) / 1000);
      setError(`Please wait ${remainingTime} seconds before making another request`);
      setCooldownRemaining(currentCooldownTime - timeSinceLastRequest);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const result = await generatePickupLine(context, style, fromGender, toGender);
      setOutput(result.pickupLines.join('\n\n'));
      setLastRequestTime(now);
      setUsageCount(prev => prev + 1);
      setCooldownRemaining(currentCooldownTime);

      if (usageCount === USAGE_THRESHOLD - 1) {
        toast.warning('Next use will increase cooldown time by 5 seconds');
      } else if (usageCount >= USAGE_THRESHOLD) {
        const extraTime = Math.floor((usageCount - USAGE_THRESHOLD + 1) * 5);
        toast.warning(`Cooldown time increased to ${15 + extraTime} seconds due to frequent use`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const getCooldownDisplay = () => {
    if (cooldownRemaining <= 0) return 'Generate';
    return `Wait ${Math.ceil(cooldownRemaining / 1000)}s`;
  };

  return (
    <div className="grid gap-6 p-6">
      {/* Error Message Display */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4 relative">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {error.includes('cooldown') || error.includes('queue') ? '⏳' : '⚠️'}
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Input Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="context" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Context or Interests
          </Label>
          <div className="flex items-center gap-4">
            <span className={`text-sm ${wordCount > WORD_LIMIT ? 'text-red-500' : 'text-gray-500'}`}>
              {wordCount}/{WORD_LIMIT} words
            </span>
            <span className="text-sm text-gray-500">
              Uses: {usageCount}
            </span>
          </div>
        </div>
        <Textarea
          id="context"
          value={context}
          onChange={(e) => setContext(e.target.value)}
          placeholder="Enter interests or context (e.g., 'loves coffee, enjoys hiking, works in tech')"
          className="min-h-[100px] transition-all duration-200 focus:shadow-lg"
        />
      </div>

      {/* Style and Gender Selection */}
      <div className="flex flex-wrap justify-center items-end gap-4 w-full max-w-4xl mx-auto">
        <div className="w-40">
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Style</Label>
          <Select
            value={style}
            onValueChange={setStyle}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select style" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="funny">Funny 😄</SelectItem>
              <SelectItem value="romantic">Romantic 💖</SelectItem>
              <SelectItem value="nerdy">Nerdy 🤓</SelectItem>
              <SelectItem value="cheesy">Cheesy 🧀</SelectItem>
              <SelectItem value="clever">Clever 🧠</SelectItem>
              <SelectItem value="smooth">Smooth 😎</SelectItem>
              <SelectItem value="cute">Cute 🥰</SelectItem>
              <SelectItem value="playful">Playful 🎮</SelectItem>
              <SelectItem value="intellectual">Intellectual 📚</SelectItem>
              <SelectItem value="quirky">Quirky 🎪</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="w-40">
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Gender</Label>
          <Select value={fromGender} onValueChange={setFromGender}>
            <SelectTrigger>
              <SelectValue placeholder="Select gender" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="no-preference">No Preference</SelectItem>
              <SelectItem value="male">Male</SelectItem>
              <SelectItem value="female">Female</SelectItem>
              <SelectItem value="non-binary">Non-Binary</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="w-40">
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">To Gender</Label>
          <Select value={toGender} onValueChange={setToGender}>
            <SelectTrigger>
              <SelectValue placeholder="Select gender" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="no-preference">No Preference</SelectItem>
              <SelectItem value="male">Male</SelectItem>
              <SelectItem value="female">Female</SelectItem>
              <SelectItem value="non-binary">Non-Binary</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="w-40">
          <Button
            onClick={handleSubmit}
            disabled={isLoading || !context.trim() || wordCount > WORD_LIMIT || cooldownRemaining > 0}
            className="w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600"
          >
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Heart className="mr-2 h-4 w-4" />
                {getCooldownDisplay()}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Output Section */}
      {output && (
        <Card className="relative p-6 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-pink-50 to-rose-50 dark:from-gray-800 dark:to-gray-700 opacity-50" />
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Heart className="h-5 w-5 text-rose-500 animate-pulse" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Your Pickup Line</h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(output);
                  toast.success('Copied to clipboard!');
                }}
                className="hover:bg-rose-100 dark:hover:bg-gray-600"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <div
              key={animationKey}
              className={`text-lg text-gray-800 dark:text-gray-200 leading-relaxed ${currentAnimation}`}
            >
              {output}
            </div>
          </div>
        </Card>
      )}

      <style jsx global>{`
        .animate-reveal-left {
          opacity: 0;
          animation: revealLeft 0.8s ease-out forwards;
        }

        .animate-reveal-right {
          opacity: 0;
          animation: revealRight 0.8s ease-out forwards;
        }

        .animate-fade-up {
          opacity: 0;
          animation: fadeUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .animate-typewriter {
          opacity: 0;
          width: 0;
          white-space: nowrap;
          overflow: hidden;
          animation: typewriter 1.5s steps(40) forwards;
        }

        .animate-bounce-in {
          opacity: 0;
          animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
        }

        @keyframes revealLeft {
          0% {
            opacity: 0;
            transform: translateX(-20px);
          }
          100% {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes revealRight {
          0% {
            opacity: 0;
            transform: translateX(20px);
          }
          100% {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes fadeUp {
          0% {
            opacity: 0;
            transform: translateY(20px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes typewriter {
          0% {
            opacity: 1;
            width: 0;
          }
          100% {
            opacity: 1;
            width: 100%;
          }
        }

        @keyframes bounceIn {
          0% {
            opacity: 0;
            transform: scale(0.3);
          }
          50% {
            opacity: 0.8;
            transform: scale(1.1);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
    </div>
  );
}
