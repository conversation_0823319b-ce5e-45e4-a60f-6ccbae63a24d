import { QrCode, Palette, Download, Share2, History, Settings2 } from "lucide-react";

export function QRGuide() {
  return (
    <div className="space-y-8 mt-16">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          How to Use QR Code Generator
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Create, customize, and track your QR codes with our comprehensive features
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
          <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-4">
            <QrCode className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2 text-blue-600 dark:text-blue-400">Create QR Code</h3>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
            <li>Enter your URL or text</li>
            <li>Choose QR code type</li>
            <li>Preview in real-time</li>
            <li>Sign in to save codes</li>
            <li>Track QR code usage</li>
          </ul>
        </div>

        <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
          <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-4">
            <Palette className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2 text-purple-600 dark:text-purple-400">Customize Design</h3>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
            <li>Choose custom colors</li>
            <li>Adjust QR code size</li>
            <li>Set error correction level</li>
            <li>Change background color</li>
            <li>Preview changes instantly</li>
          </ul>
        </div>

        <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
          <div className="w-12 h-12 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-4">
            <Share2 className="w-6 h-6 text-pink-600 dark:text-pink-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2 text-pink-600 dark:text-pink-400">Share & Track</h3>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
            <li>Download as PNG/SVG</li>
            <li>Track scan analytics</li>
            <li>Share QR codes easily</li>
            <li>View usage statistics</li>
            <li>Manage saved codes</li>
          </ul>
        </div>
      </div>
    </div>
  );
}