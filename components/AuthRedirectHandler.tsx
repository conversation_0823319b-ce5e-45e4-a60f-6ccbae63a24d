'use client';

import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';

export function AuthRedirectHandler() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Only handle redirect if user just signed in and we have a redirect parameter
    if (!loading && user) {
      const redirect = searchParams.get('redirect');
      if (redirect) {
        console.log('Auth redirect handler: redirecting to', redirect);
        // Use window.location for more reliable redirect
        window.location.href = decodeURIComponent(redirect);
      }
    }
  }, [user, loading, searchParams]);

  return null; // This component doesn't render anything
}
