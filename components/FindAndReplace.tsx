"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Copy, Search, Replace, Download, Info, ChevronDown, ChevronUp } from "lucide-react";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

export function FindAndReplace() {
  const [inputText, setInputText] = useState("");
  const [outputText, setOutputText] = useState("");
  const [findText, setFindText] = useState("");
  const [replaceText, setReplaceText] = useState("");
  const [highlightedResult, setHighlightedResult] = useState("");
  const [matchCount, setMatchCount] = useState(0);
  const [isCaseSensitive, setIsCaseSensitive] = useState(false);
  const [showFullOutput, setShowFullOutput] = useState(false);
  const outputRef = useRef<HTMLDivElement>(null);
  const [isOutputOverflowing, setIsOutputOverflowing] = useState(false);

  // Function to highlight matched text
  const highlightMatches = (text: string, searchTerm: string) => {
    if (!searchTerm) return text;
    try {
      const flags = isCaseSensitive ? 'g' : 'gi';
      const regex = new RegExp(searchTerm, flags);
      return text.replace(regex, match => `<span class="bg-yellow-200 dark:bg-yellow-500/50">${match}</span>`);
    } catch (e) {
      return text;
    }
  };

  // Update output text when input changes
  useEffect(() => {
    setOutputText(inputText);
  }, [inputText]);

  // Update highlighting when text or search term changes
  useEffect(() => {
    if (outputText === "" || findText === "") {
      setHighlightedResult(outputText);
      setMatchCount(0);
      return;
    }

    try {
      const flags = isCaseSensitive ? 'g' : 'gi';
      const regex = new RegExp(findText, flags);
      const matches = outputText.match(regex);
      setMatchCount(matches ? matches.length : 0);
      setHighlightedResult(highlightMatches(outputText, findText));
    } catch (error) {
      setHighlightedResult(outputText);
      setMatchCount(0);
    }
  }, [outputText, findText, isCaseSensitive]);

  useEffect(() => {
    if (outputRef.current) {
      const container = outputRef.current.querySelector('div');
      if (container) {
        const hasOverflow = container.scrollHeight > container.clientHeight;
        setIsOutputOverflowing(hasOverflow);
      }
    }
  }, [outputText, highlightedResult]);

  const handleInputChange = (value: string) => {
    setInputText(value);
  };

  const handleReplace = () => {
    if (!findText) {
      toast.error("Please enter text to find");
      return;
    }

    try {
      const flags = isCaseSensitive ? 'g' : 'gi';
      const regex = new RegExp(findText, flags);
      const newText = outputText.replace(regex, replaceText);
      setOutputText(newText);
      toast.success("Text replaced successfully");
    } catch (error) {
      toast.error("Invalid search pattern");
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(outputText);
      toast.success("Text copied to clipboard successfully");
    } catch (err) {
      toast.error("Failed to copy text to clipboard");
    }
  };

  const downloadText = () => {
    try {
      const blob = new Blob([outputText], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'converted-text.txt';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      toast.success("File downloaded successfully");
    } catch (error) {
      toast.error("Failed to download file");
    }
  };

  const countStats = (text: string) => {
    const trimmedText = text.trim();
    return {
      characters: text.length,
      words: trimmedText === "" ? 0 : trimmedText.split(/\s+/).filter(Boolean).length,
      sentences: trimmedText === "" ? 0 : trimmedText.split(/[.!?]+/).filter(Boolean).length,
      lines: trimmedText === "" ? 0 : trimmedText.split(/\n/).length,
    };
  };

  const stats = countStats(outputText);

  // Add remaining words calculation
  const getRemainingWords = () => {
    if (!outputText) return 0;
    const totalWords = outputText.trim().split(/\s+/).filter(Boolean).length;
    const visibleWords = showFullOutput ? totalWords : 500;
    return Math.max(0, totalWords - visibleWords);
  };

  return (
    <TooltipProvider>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          <div className="grid gap-8 md:grid-cols-2">
            {/* Input Section */}
            <Card className="p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="text" className="text-lg font-semibold">Text</Label>
                </div>
                <Textarea
                  id="text"
                  value={inputText}
                  onChange={(e) => handleInputChange(e.target.value)}
                  placeholder="Enter your text here..."
                  className="min-h-[200px] resize-none"
                />

                {/* Find and Replace Section */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="findText" className="text-sm">Find:</Label>
                    <div className="flex gap-2">
                      <Input
                        id="findText"
                        value={findText}
                        onChange={(e) => setFindText(e.target.value)}
                        placeholder="Text to find"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="replaceText" className="text-sm">Replace with:</Label>
                    <div className="flex gap-2">
                      <Input
                        id="replaceText"
                        value={replaceText}
                        onChange={(e) => setReplaceText(e.target.value)}
                        placeholder="Replacement text"
                      />
                      <Button 
                        variant="secondary"
                        onClick={handleReplace}
                        className="shrink-0 bg-purple-500 hover:bg-purple-600 text-white"
                      >
                        <Replace className="h-4 w-4 mr-2" />
                        Replace
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <TooltipProvider>
                      <div className="flex items-center gap-2">
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={isCaseSensitive}
                            onChange={(e) => setIsCaseSensitive(e.target.checked)}
                            className="w-4 h-4 rounded border-gray-300"
                          />
                          <span className="text-sm">Case sensitive</span>
                        </label>
                        <Dialog>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DialogTrigger asChild>
                                <button className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                                  <Info className="h-4 w-4 text-gray-500" />
                                </button>
                              </DialogTrigger>
                            </TooltipTrigger>
                            <TooltipContent side="top" className="p-2">
                              <p>Click for case sensitivity examples</p>
                            </TooltipContent>
                          </Tooltip>
                          <DialogContent className="sm:max-w-[425px]">
                            <DialogHeader>
                              <DialogTitle className="flex items-center gap-2">
                                <Info className="h-5 w-5 text-blue-500" />
                                Case Sensitivity
                              </DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <h4 className="font-medium mb-2 text-blue-600">When enabled:</h4>
                                <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3 space-y-2 text-sm">
                                  <p>• "Hello" ≠ "hello"</p>
                                  <p>• "WORLD" ≠ "world"</p>
                                  <p>• "JavaScript" only matches "JavaScript"</p>
                                </div>
                              </div>
                              <div>
                                <h4 className="font-medium mb-2 text-blue-600">When disabled:</h4>
                                <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3 space-y-2 text-sm">
                                  <p>• "hello" matches "Hello", "HELLO", "hello"</p>
                                  <p>• "world" matches "WORLD", "World", "world"</p>
                                </div>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                Toggle this option when you need exact case matching.
                              </p>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </TooltipProvider>
                  </div>
                </div>

                {/* Stats Section */}
                {inputText && (
                  <Card className="p-4 mt-4">
                    <div className="space-y-4">
                      {/* Match count */}
                      {matchCount > 0 && (
                        <div className="text-center">
                          <Badge variant="secondary" className="mb-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                            Matches Found
                          </Badge>
                          <p className="text-2xl font-bold text-green-600">{matchCount}</p>
                        </div>
                      )}
                      
                      {/* Stats grid */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {Object.entries(countStats(outputText)).map(([key, value]) => {
                          type StatKey = 'characters' | 'words' | 'sentences' | 'lines';
                          
                          const colorMap: Record<StatKey, { badge: string; text: string }> = {
                            characters: {
                              badge: "text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400",
                              text: "text-blue-600"
                            },
                            words: {
                              badge: "text-purple-600 bg-purple-100 dark:bg-purple-900/30 dark:text-purple-400",
                              text: "text-purple-600"
                            },
                            sentences: {
                              badge: "text-pink-600 bg-pink-100 dark:bg-pink-900/30 dark:text-pink-400",
                              text: "text-pink-600"
                            },
                            lines: {
                              badge: "text-orange-600 bg-orange-100 dark:bg-orange-900/30 dark:text-orange-400",
                              text: "text-orange-600"
                            }
                          };

                          const colors = colorMap[key as StatKey] || {
                            badge: "text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400",
                            text: "text-gray-600"
                          };

                          return (
                            <div key={key} className="text-center">
                              <Badge 
                                variant="secondary" 
                                className={`mb-1 capitalize ${colors.badge}`}
                              >
                                {key}
                              </Badge>
                              <p className={`text-2xl font-bold ${colors.text}`}>
                                {value}
                              </p>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </Card>
                )}
              </div>
            </Card>

            {/* Output Section */}
            <Card className="p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-lg font-semibold">Result</Label>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline"
                      size="sm" 
                      onClick={copyToClipboard}
                      className="bg-blue-500 hover:bg-blue-600 text-white"
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                    <Button 
                      variant="outline"
                      size="sm" 
                      onClick={downloadText}
                      className="bg-green-500 hover:bg-green-600 text-white"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
                <div className="relative">
                  <div className="relative">
                    <div 
                      ref={outputRef}
                      className="relative overflow-hidden transition-all duration-300"
                    >
                      <div
                        className={`min-h-[500px] p-4 bg-slate-50 dark:bg-gray-800 rounded-t-md whitespace-pre-wrap ${
                          showFullOutput ? '' : 'max-h-[800px]'
                        }`}
                        dangerouslySetInnerHTML={{ __html: highlightedResult }}
                      />
                    </div>
                    {!showFullOutput && (
                      <>
                        <div className="absolute bottom-14 left-0 right-0 h-40 bg-gradient-to-t from-white/95 dark:from-gray-900/95 to-transparent pointer-events-none" />
                        <div className="absolute bottom-32 left-0 right-0 flex flex-col items-center justify-center pointer-events-none">
                          <div className="bg-blue-500 dark:bg-blue-600 px-4 py-2 rounded-full text-sm text-white shadow-lg">
                            <div className="flex items-center gap-2">
                              <ChevronDown className="h-4 w-4 animate-bounce" />
                              <span>{getRemainingWords()} more words below</span>
                              <ChevronDown className="h-4 w-4 animate-bounce" />
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                  <div className="relative bg-white dark:bg-gray-900 pt-2 pb-1 px-1 rounded-b-lg border-t border-gray-200 dark:border-gray-700 shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] flex justify-center">
                    <Button
                      variant="default"
                      size="lg"
                      className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white flex items-center justify-center gap-2 group shadow-lg text-lg font-medium px-8"
                      onClick={() => setShowFullOutput(!showFullOutput)}
                    >
                      {showFullOutput ? (
                        <>
                          <ChevronUp className="h-6 w-6 group-hover:-translate-y-0.5 transition-transform" />
                          Show Less Content
                        </>
                      ) : (
                        <>
                          <ChevronDown className="h-6 w-6 group-hover:translate-y-0.5 transition-transform" />
                          Show More Content
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}