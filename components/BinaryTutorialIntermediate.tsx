'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { 
  Calculator, 
  Code, 
  Zap,
  Plus,
  Minus,
  X,
  GitCommitVertical,
  GitMerge,
  GitPullRequestClosed,
  CircleOff,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';
import {
  binaryAdd,
  binarySubtract,
  binaryMultiply,
  binaryDivide,
  binaryAND,
  binaryOR,
  binaryXOR,
  binaryNOT,
  getBinaryAdditionSteps,
  isValidBinary
} from '@/lib/binaryUtils';

export function BinaryTutorialIntermediate() {
  const [arithmeticInputs, setArithmeticInputs] = useState({ a: '1010', b: '0110' });
  const [bitwiseInputs, setBitwiseInputs] = useState({ a: '1100', b: '1010' });
  const [selectedOperation, setSelectedOperation] = useState<string>('add');

  const arithmeticOperations = [
    { id: 'add', name: 'Addition', icon: Plus, symbol: '+', func: binaryAdd },
    { id: 'subtract', name: 'Subtraction', icon: Minus, symbol: '-', func: binarySubtract },
    { id: 'multiply', name: 'Multiplication', icon: X, symbol: '×', func: binaryMultiply },
    { id: 'divide', name: 'Division', icon: Calculator, symbol: '÷', func: binaryDivide },
  ];

  const bitwiseOperations = [
    { id: 'and', name: 'AND', icon: GitCommitVertical, symbol: '&', func: binaryAND },
    { id: 'or', name: 'OR', icon: GitMerge, symbol: '|', func: binaryOR },
    { id: 'xor', name: 'XOR', icon: GitPullRequestClosed, symbol: '^', func: binaryXOR },
    { id: 'not', name: 'NOT', icon: CircleOff, symbol: '~', func: binaryNOT },
  ];

  const programmingExamples = [
    {
      language: 'JavaScript',
      title: 'Bitwise Operations',
      code: `// Bitwise AND
let result = 12 & 10;  // 1100 & 1010 = 1000 (8)

// Bitwise OR
let result = 12 | 10;  // 1100 | 1010 = 1110 (14)

// Left shift (multiply by 2)
let doubled = 5 << 1;  // 101 << 1 = 1010 (10)

// Right shift (divide by 2)
let halved = 10 >> 1;  // 1010 >> 1 = 101 (5)`
    },
    {
      language: 'Python',
      title: 'Binary Conversion',
      code: `# Convert to binary
binary = bin(42)  # '0b101010'

# Convert from binary
decimal = int('101010', 2)  # 42

# Bitwise operations
result = 12 & 10  # AND: 8
result = 12 | 10  # OR: 14
result = 12 ^ 10  # XOR: 6`
    },
    {
      language: 'C++',
      title: 'Bit Manipulation',
      code: `#include <bitset>

// Set a bit
int setBit(int num, int pos) {
    return num | (1 << pos);
}

// Clear a bit
int clearBit(int num, int pos) {
    return num & ~(1 << pos);
}

// Toggle a bit
int toggleBit(int num, int pos) {
    return num ^ (1 << pos);
}`
    }
  ];

  const performArithmetic = (operation: string) => {
    const { a, b } = arithmeticInputs;
    
    if (!isValidBinary(a) || !isValidBinary(b)) {
      toast.error('Please enter valid binary numbers');
      return null;
    }

    try {
      const op = arithmeticOperations.find(o => o.id === operation);
      if (!op) return null;
      
      return op.func(a, b);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Calculation error');
      return null;
    }
  };

  const performBitwise = (operation: string) => {
    const { a, b } = bitwiseInputs;
    
    if (!isValidBinary(a) || (operation !== 'not' && !isValidBinary(b))) {
      toast.error('Please enter valid binary numbers');
      return null;
    }

    try {
      const op = bitwiseOperations.find(o => o.id === operation);
      if (!op) return null;
      
      return operation === 'not' ? op.func(a) : op.func(a, b);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Calculation error');
      return null;
    }
  };

  const BitwiseVisualizer = ({ operation }: { operation: string }) => {
    const { a, b } = bitwiseInputs;
    const maxLen = Math.max(a.length, b.length);
    const paddedA = a.padStart(maxLen, '0');
    const paddedB = b.padStart(maxLen, '0');
    const result = performBitwise(operation);
    const paddedResult = result ? result.padStart(maxLen, '0') : '';

    return (
      <div className="space-y-4">
        <div className="font-mono text-center space-y-2">
          <div className="flex justify-center space-x-2">
            <span className="text-gray-500">A:</span>
            {paddedA.split('').map((bit, i) => (
              <span key={i} className={`w-8 h-8 flex items-center justify-center rounded ${
                bit === '1' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'
              }`}>
                {bit}
              </span>
            ))}
          </div>
          
          {operation !== 'not' && (
            <div className="flex justify-center space-x-2">
              <span className="text-gray-500">B:</span>
              {paddedB.split('').map((bit, i) => (
                <span key={i} className={`w-8 h-8 flex items-center justify-center rounded ${
                  bit === '1' ? 'bg-green-500 text-white' : 'bg-gray-200 dark:bg-gray-700'
                }`}>
                  {bit}
                </span>
              ))}
            </div>
          )}
          
          <div className="text-center text-gray-500">
            {bitwiseOperations.find(op => op.id === operation)?.symbol}
          </div>
          
          {result && (
            <div className="flex justify-center space-x-2">
              <span className="text-gray-500">Result:</span>
              {paddedResult.split('').map((bit, i) => (
                <span key={i} className={`w-8 h-8 flex items-center justify-center rounded ${
                  bit === '1' ? 'bg-purple-500 text-white' : 'bg-gray-200 dark:bg-gray-700'
                }`}>
                  {bit}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="arithmetic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="arithmetic">Binary Arithmetic</TabsTrigger>
          <TabsTrigger value="bitwise">Bitwise Operations</TabsTrigger>
          <TabsTrigger value="programming">Programming Examples</TabsTrigger>
        </TabsList>

        <TabsContent value="arithmetic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Binary Arithmetic Operations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium mb-2">First Number (Binary)</label>
                  <Input
                    value={arithmeticInputs.a}
                    onChange={(e) => setArithmeticInputs(prev => ({ ...prev, a: e.target.value }))}
                    placeholder="1010"
                    className="font-mono"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Second Number (Binary)</label>
                  <Input
                    value={arithmeticInputs.b}
                    onChange={(e) => setArithmeticInputs(prev => ({ ...prev, b: e.target.value }))}
                    placeholder="0110"
                    className="font-mono"
                  />
                </div>
              </div>

              <div className="grid gap-2 md:grid-cols-4">
                {arithmeticOperations.map((op) => {
                  const Icon = op.icon;
                  const result = performArithmetic(op.id);
                  
                  return (
                    <Card key={op.id} className="p-4">
                      <div className="text-center space-y-2">
                        <Icon className="h-6 w-6 mx-auto text-blue-600" />
                        <div className="font-semibold">{op.name}</div>
                        <div className="font-mono text-sm">
                          {arithmeticInputs.a} {op.symbol} {arithmeticInputs.b}
                        </div>
                        <div className="font-mono font-bold text-lg">
                          {result || 'Invalid'}
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>

              {/* Addition Steps */}
              {selectedOperation === 'add' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Step-by-Step Addition</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {(() => {
                        try {
                          const steps = getBinaryAdditionSteps(arithmeticInputs.a, arithmeticInputs.b);
                          return steps.map((step, index) => (
                            <div key={index} className="flex items-center gap-4 font-mono text-sm">
                              <Badge variant="outline">Step {index + 1}</Badge>
                              <span>{step.bit1} + {step.bit2} + {step.carry} = {step.sum} → {step.result}</span>
                            </div>
                          ));
                        } catch {
                          return <p className="text-gray-500">Enter valid binary numbers to see steps</p>;
                        }
                      })()}
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bitwise" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Bitwise Operations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium mb-2">First Number (Binary)</label>
                  <Input
                    value={bitwiseInputs.a}
                    onChange={(e) => setBitwiseInputs(prev => ({ ...prev, a: e.target.value }))}
                    placeholder="1100"
                    className="font-mono"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Second Number (Binary)</label>
                  <Input
                    value={bitwiseInputs.b}
                    onChange={(e) => setBitwiseInputs(prev => ({ ...prev, b: e.target.value }))}
                    placeholder="1010"
                    className="font-mono"
                  />
                </div>
              </div>

              <div className="grid gap-2 md:grid-cols-4">
                {bitwiseOperations.map((op) => {
                  const Icon = op.icon;
                  const result = performBitwise(op.id);
                  
                  return (
                    <Card key={op.id} className="p-4 cursor-pointer hover:shadow-md transition-shadow">
                      <div className="text-center space-y-2">
                        <Icon className="h-6 w-6 mx-auto text-purple-600" />
                        <div className="font-semibold">{op.name}</div>
                        <div className="font-mono text-sm">
                          {op.id === 'not' ? `${op.symbol}${bitwiseInputs.a}` : `${bitwiseInputs.a} ${op.symbol} ${bitwiseInputs.b}`}
                        </div>
                        <div className="font-mono font-bold text-lg">
                          {result || 'Invalid'}
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>

              {/* Visual Bitwise Operation */}
              <Card>
                <CardHeader>
                  <CardTitle>Visual Bitwise Operation</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex gap-2 justify-center">
                      {bitwiseOperations.map((op) => (
                        <Button
                          key={op.id}
                          variant={selectedOperation === op.id ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setSelectedOperation(op.id)}
                        >
                          {op.name}
                        </Button>
                      ))}
                    </div>
                    <BitwiseVisualizer operation={selectedOperation} />
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="programming" className="space-y-6">
          <div className="grid gap-6">
            {programmingExamples.map((example, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    {example.language} - {example.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg overflow-x-auto">
                    <code className="text-sm">{example.code}</code>
                  </pre>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Common Use Cases</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <h4 className="font-semibold">Bit Manipulation</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• Setting/clearing individual bits</li>
                    <li>• Checking if a bit is set</li>
                    <li>• Counting set bits</li>
                    <li>• Finding the rightmost set bit</li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Performance Optimization</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• Fast multiplication/division by powers of 2</li>
                    <li>• Efficient modulo operations</li>
                    <li>• Bitwise flags and permissions</li>
                    <li>• Memory-efficient data structures</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
