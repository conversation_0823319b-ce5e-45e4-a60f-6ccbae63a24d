"use client";
import React, { useState } from "react";
import { Download, <PERSON>, Settings, Video, ChevronDown, ChevronUp, AlertCircle, Music, FileVideo, X } from "lucide-react";
import { toast } from "sonner";

interface VideoFormat {
  itag: string;
  quality: string;
  container: string;
  hasAudio?: boolean;
  hasVideo?: boolean;
  contentLength?: string;
  fps?: number;
  width?: number;
  height?: number;
  audioBitrate?: number;
}

type CodecType = 'AV1' | 'VP9' | 'H.264' | 'unknown';

interface VideoInfo {
  title: string;
  thumbnail: string;
  duration: string;
  formats: VideoFormat[];
}

type DownloadType = "download" | "convert";

interface VideoDownloaderState {
  url: string;
  videoInfo: VideoInfo | null;
  expandedFormat: string | null;
  selectedQuality: { format: string; quality: VideoFormat | null };
  downloading: boolean;
  downloadProgress: number;
  activeTab: DownloadType;
  convertFormat: string;
  convertQuality: string;
  abortController: AbortController | null;
  isLoading: boolean;
}

export default function VideoDownloader() {
  const [state, setState] = useState<VideoDownloaderState>({
    url: "",
    videoInfo: null,
    expandedFormat: null,
    selectedQuality: { format: "", quality: null },
    downloading: false,
    downloadProgress: 0,
    activeTab: "download",
    convertFormat: 'mp3',
    convertQuality: '192',
    abortController: null,
    isLoading: false
  });

  const getCodecName = (format: VideoFormat): CodecType => {
    const itag = parseInt(format.itag);
    // Map common YouTube itags to codec names
    if ([313, 271, 248].includes(itag)) return 'VP9';
    if ([401, 400, 399, 398, 397, 396, 395, 394].includes(itag)) return 'AV1';
    if ([137, 136, 135, 134, 133].includes(itag)) return 'H.264';
    return 'unknown';
  };

  const convertOptions = {
    mp3: {
      label: 'MP3',
      qualities: [
        { value: '64', label: '64 kbps' },
        { value: '128', label: '128 kbps' },
        { value: '192', label: '192 kbps' },
        { value: '256', label: '256 kbps' },
        { value: '320', label: '320 kbps' },
      ],
    },
    m4a: {
      label: 'M4A',
      qualities: [
        { value: '128', label: '128 kbps' },
        { value: '192', label: '192 kbps' },
        { value: '256', label: '256 kbps' },
      ],
    },
  };

  const detectPlatform = (url: string) => {
    if (url.includes('youtube.com') || url.includes('youtu.be')) return 'youtube';
    if (url.includes('vimeo.com')) return 'vimeo';
    if (url.includes('facebook.com') || url.includes('fb.watch')) return 'facebook';
    if (url.includes('instagram.com')) return 'instagram';
    return 'youtube'; // default to youtube
  };

  const fetchVideoInfo = async (url: string, mode: DownloadType) => {
    if (!url) {
      setState(prev => ({ ...prev, isLoading: false }));
      return;
    }

    try {
      const platform = detectPlatform(url);
      const response = await fetch("/api/video/info", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url, platform, mode }),
      });

      if (!response.ok) throw new Error("Failed to fetch video info");

      const data = await response.json();
      setState(prev => ({ 
        ...prev, 
        videoInfo: data,
        selectedQuality: { format: "", quality: null },
        expandedFormat: null,
        isLoading: false
      }));
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch video info");
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleUrlSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!state.url) return;

    setState(prev => ({ 
      ...prev, 
      videoInfo: null,
      isLoading: true 
    }));

    try {
      const platform = detectPlatform(state.url);
      const response = await fetch("/api/video/info", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url: state.url, platform, mode: state.activeTab }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.isProduction) {
          toast.error(data.message);
          setState(prev => ({ ...prev, isLoading: false }));
          return;
        }
        throw new Error(data.error || "Failed to fetch video info");
      }

      setState(prev => ({ 
        ...prev, 
        videoInfo: data,
        selectedQuality: { format: "", quality: null },
        expandedFormat: null,
        isLoading: false
      }));
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch video info");
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleTabChange = async (newTab: DownloadType) => {
    setState(prev => ({ 
      ...prev, 
      activeTab: newTab,
      isLoading: true
    }));
    
    if (state.url && state.videoInfo) {
      await fetchVideoInfo(state.url, newTab);
    } else {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleFormatClick = (format: string) => {
    setState(prev => ({ ...prev, expandedFormat: prev.expandedFormat === format ? null : format }));
  };

  const handleQualitySelect = (format: string, quality: VideoFormat) => {
    setState(prev => ({ ...prev, selectedQuality: { format, quality } }));
  };

  const handleDownloadClick = async () => {
    if (state.activeTab === "convert") {
      // For convert tab, use the first available audio format
      const audioFormats = state.videoInfo?.formats.filter(f => f.hasAudio && !f.hasVideo) || [];
      if (audioFormats.length > 0) {
        await handleDownload(audioFormats[0], "convert" as const);
      } else {
        toast.error("No suitable audio format found for conversion");
      }
    } else {
      // For download tab, use the selected quality
      if (state.selectedQuality.quality) {
        await handleDownload(state.selectedQuality.quality, "download" as const);
      }
    }
  };

  const handleDownload = async (format: VideoFormat | null, type: "download" | "convert") => {
    if (!format || !state.videoInfo) return;

    const abortController = new AbortController();
    setState(prev => ({ 
      ...prev, 
      downloading: true, 
      downloadProgress: 0,
      abortController 
    }));

    try {
      const response = await fetch("/api/video/download", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          url: state.url,
          itag: format.itag,
          title: state.videoInfo.title,
          type,
          convertFormat: state.convertFormat,
          convertQuality: state.convertQuality,
        }),
        signal: abortController.signal
      });

      if (!response.ok) {
        const data = await response.json();
        if (data.isProduction) {
          toast.error(data.message);
          return;
        }
        throw new Error("Download failed");
      }

      const body = response.body;
      if (!body) {
        throw new Error("No response body");
      }

      const contentLength = parseInt(response.headers.get('Content-Length') || '0');
      const chunks: Uint8Array[] = [];
      let receivedLength = 0;

      const reader = body.getReader();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) break;
          
          chunks.push(value);
          receivedLength += value.length;
          
          if (contentLength) {
            const progress = Math.round((receivedLength / contentLength) * 100);
            setState(prev => ({ ...prev, downloadProgress: progress }));
          }
        }
      } finally {
        reader.releaseLock();
      }

      // Create and download the blob
      const blob = new Blob(chunks, { type: type === 'convert' ? 'audio/mpeg' : 'video/mp4' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${state.videoInfo.title}.${type === "convert" ? state.convertFormat : format.container}`;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success("Download completed!");
    } catch (error: any) {
      if (error.name === 'AbortError') {
        toast.info("Download cancelled");
        return;
      }
      toast.error(error.message || "Failed to download video");
    } finally {
      setState(prev => ({ 
        ...prev, 
        downloading: false, 
        downloadProgress: 0,
        abortController: null 
      }));
    }
  };

  const groupFormats = (formats: VideoFormat[]) => {
    // First group by resolution
    const resolutionGroups: { [key: string]: VideoFormat[] } = {};
    
    formats.forEach(format => {
      if (!format) return;
      
      const resolution = format.height ? `${format.height}p` : 'Auto';
      if (!resolutionGroups[resolution]) {
        resolutionGroups[resolution] = [];
      }
      resolutionGroups[resolution].push(format);
    });

    // Sort resolutions in descending order
    const sortedResolutions = Object.keys(resolutionGroups).sort((a, b) => {
      const aHeight = parseInt(a) || 0;
      const bHeight = parseInt(b) || 0;
      return bHeight - aHeight;
    });

    // Sort formats within each resolution by quality
    sortedResolutions.forEach(resolution => {
      resolutionGroups[resolution].sort((a, b) => {
        // First sort by codec preference (AV1 > VP9 > H.264)
        const codecOrder: Record<CodecType, number> = {
          'AV1': 3,
          'VP9': 2,
          'H.264': 1,
          'unknown': 0
        };
        
        const aCodec = getCodecName(a);
        const bCodec = getCodecName(b);
        
        if (codecOrder[aCodec] !== codecOrder[bCodec]) {
          return codecOrder[bCodec] - codecOrder[aCodec];
        }
        
        // Then by file size if available
        const aSize = parseInt(a.contentLength?.replace(/[^\d]/g, '') || '0');
        const bSize = parseInt(b.contentLength?.replace(/[^\d]/g, '') || '0');
        if (aSize && bSize && aSize !== bSize) {
          return aSize - bSize;
        }
        
        // Finally by FPS
        return (b.fps || 0) - (a.fps || 0);
      });

      // Remove duplicates (same codec, size, and fps)
      resolutionGroups[resolution] = resolutionGroups[resolution].filter((format, index, self) => 
        index === self.findIndex((f) => (
          getCodecName(f) === getCodecName(format) &&
          f.contentLength === format.contentLength &&
          f.fps === format.fps
        ))
      );
    });

    return { resolutions: sortedResolutions, groups: resolutionGroups };
  };

  const getCodecDescription = (format: VideoFormat): string => {
    const codec = getCodecName(format);
    switch (codec) {
      case 'AV1':
        return 'Best quality with smallest file size (newer devices)';
      case 'VP9':
        return 'Good balance of quality and compatibility';
      case 'H.264':
        return 'Most compatible format (works everywhere)';
      default:
        return 'Standard quality';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  };

  const formatQualityLabel = (format: VideoFormat) => {
    const parts = [];
    
    // Add quality label with codec info
    if (format.quality === 'Standard Quality') {
      const codec = getCodecName(format);
      parts.push(`Standard (${codec})`);
    } else if (format.quality === 'High Efficiency') {
      parts.push('High Efficiency (VP9)');
    } else if (format.quality === 'Best Quality') {
      parts.push('Best Quality (AV1)');
    } else {
      parts.push(format.quality);
    }
    
    // Add file size with proper units
    if (format.contentLength && format.contentLength !== 'Unknown') {
      const formattedSize = formatFileSize(parseInt(format.contentLength));
      parts.push(`Size: ${formattedSize}`);
    }
    
    // Add bitrate if available
    if (format.audioBitrate) {
      parts.push(`${format.audioBitrate}kbps`);
    }
    
    // Add FPS for high frame rate videos
    if (format.fps && format.fps > 30) {
      parts.push(`${format.fps}fps`);
    }

    return parts.join(' • ');
  };

  const getResolutionDescription = (resolution: string): string => {
    const resMap: { [key: string]: string } = {
      '2160p': '4K Ultra HD (2160p)',
      '1440p': 'Quad HD (1440p)',
      '1080p': 'Full HD (1080p)',
      '720p': 'HD (720p)',
      '480p': 'Standard Definition (480p)',
      '360p': 'Low Definition (360p)',
      '240p': 'Very Low Quality (240p)',
      '144p': 'Minimal Quality (144p)'
    };
    return resMap[resolution] || resolution;
  };

  const handleCancelDownload = () => {
    if (state.abortController) {
      state.abortController.abort();
      setState(prev => ({ 
        ...prev, 
        downloading: false, 
        downloadProgress: 0, 
        abortController: null 
      }));
      toast.info("Download cancelled");
    }
  };

  // Add a production environment notice
  const isProduction = process.env.NODE_ENV === 'production' || process.env.VERCEL_ENV === 'production';

  return (
    <div className="space-y-6">
      {isProduction && (
        <div className="p-4 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-500 mt-0.5" />
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              This is a demo version. For full functionality including downloads and conversions, please run the application locally.
              Visit the <a href="https://github.com/yourusername/yourrepo" className="underline hover:text-yellow-900 dark:hover:text-yellow-100">GitHub repository</a> for instructions.
            </p>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-4">
          <button
            onClick={() => handleTabChange("download")}
            disabled={state.isLoading}
            className={`py-2 px-4 -mb-px ${
              state.activeTab === "download"
                ? "border-b-2 border-blue-500 text-blue-600 dark:text-blue-400"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            } font-medium transition-colors duration-200 disabled:opacity-50`}
          >
            <div className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Download</span>
            </div>
          </button>
          <button
            onClick={() => handleTabChange("convert")}
            disabled={state.isLoading}
            className={`py-2 px-4 -mb-px ${
              state.activeTab === "convert"
                ? "border-b-2 border-blue-500 text-blue-600 dark:text-blue-400"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            } font-medium transition-colors duration-200 disabled:opacity-50`}
          >
            <div className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span>Convert</span>
            </div>
          </button>
        </div>
      </div>

      {/* URL Input with loading states */}
      <form onSubmit={handleUrlSubmit} className="space-y-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Link className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="url"
                value={state.url}
                onChange={(e) => setState(prev => ({ ...prev, url: e.target.value }))}
                placeholder="Enter video URL..."
                disabled={state.isLoading}
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              />
            </div>
          </div>
          <button
            type="submit"
            disabled={state.isLoading || !state.url}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center gap-2 whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {state.isLoading ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>Getting Info...</span>
              </>
            ) : (
              <>
                <Video className="w-5 h-5" />
                <span>Get Video Info</span>
              </>
            )}
          </button>
        </div>
      </form>

      {/* Loading state for initial fetch */}
      {state.isLoading && !state.videoInfo && (
        <div className="flex items-center justify-center py-12">
          <div className="flex flex-col items-center gap-4 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
            <div className="w-8 h-8 border-3 border-blue-500 border-t-transparent rounded-full animate-spin" />
            <div className="text-center">
              <p className="font-medium text-blue-500">Fetching video information...</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">This may take a few seconds</p>
            </div>
          </div>
        </div>
      )}

      {/* Legal Notice */}
      <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <div className="flex items-start gap-3">
          <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-500 mt-0.5" />
          <p className="text-sm text-yellow-700 dark:text-yellow-300">
            Please ensure you have the right to download and convert this content. Respect copyright laws and platform terms of service.
          </p>
        </div>
      </div>

      {/* Video Info and Options */}
      <div className="relative">
        {state.videoInfo && (
          <div className={`mt-6 space-y-6 transition-all duration-200 ${state.isLoading ? 'opacity-50 pointer-events-none' : 'opacity-100'}`}>
            {state.isLoading && (
              <div className="absolute inset-0 flex items-center justify-center z-10">
                <div className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
                  <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                  <span className="text-blue-500 font-medium">Loading {state.activeTab === 'convert' ? 'conversion' : 'download'} options...</span>
                </div>
              </div>
            )}
            {/* Rest of your video info content */}
            <div className="flex flex-col md:flex-row gap-6 p-6 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <div className="w-full md:w-1/3">
                <div className="aspect-video rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700">
                  <img
                    src={state.videoInfo.thumbnail}
                    alt={state.videoInfo.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="mt-4">
                  <h3 className="font-medium text-lg">{state.videoInfo.title}</h3>
                  <p className="text-sm text-gray-500">Duration: {state.videoInfo.duration}</p>
                </div>
              </div>

              {/* Format Options */}
              <div className="w-full md:w-2/3 space-y-4">
                {state.activeTab === "convert" ? (
                  <div className="space-y-4">
                    <div className="flex flex-col gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Output Format</label>
                        <div className="flex gap-2">
                          {Object.entries(convertOptions).map(([format, { label }]) => (
                            <button
                              key={format}
                              onClick={() => setState(prev => ({ ...prev, convertFormat: format }))}
                              className={`px-4 py-2 rounded-lg border ${
                                state.convertFormat === format
                                  ? 'bg-blue-500 text-white border-blue-500'
                                  : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800'
                              }`}
                            >
                              {label}
                            </button>
                          ))}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Quality</label>
                        <div className="flex flex-wrap gap-2">
                          {convertOptions[state.convertFormat as keyof typeof convertOptions].qualities.map(({ value, label }) => (
                            <button
                              key={value}
                              onClick={() => setState(prev => ({ ...prev, convertQuality: value }))}
                              className={`px-4 py-2 rounded-lg border ${
                                state.convertQuality === value
                                  ? 'bg-blue-500 text-white border-blue-500'
                                  : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800'
                              }`}
                            >
                              {label}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={handleDownloadClick}
                      disabled={!state.videoInfo || state.downloading}
                      className="w-full py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center gap-2"
                    >
                      {state.downloading ? (
                        <>Converting...</>
                      ) : (
                        <>
                          <Music className="h-5 w-5" />
                          Convert to {state.convertFormat.toUpperCase()}
                        </>
                      )}
                    </button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {state.videoInfo?.formats && (() => {
                      const { resolutions, groups } = groupFormats(state.videoInfo.formats);
                      return resolutions.map(resolution => (
                        <div key={resolution} className="rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                          <button
                            onClick={() => handleFormatClick(resolution)}
                            className="w-full flex items-center justify-between p-4 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          >
                            <div className="flex items-center gap-2">
                              <FileVideo className="w-4 h-4 text-blue-500" />
                              <span className="font-medium">
                                {getResolutionDescription(resolution)}
                                <span className="text-sm text-gray-500 ml-2">
                                  ({groups[resolution].length} options)
                                </span>
                              </span>
                            </div>
                            {state.expandedFormat === resolution ? (
                              <ChevronUp className="w-4 h-4" />
                            ) : (
                              <ChevronDown className="w-4 h-4" />
                            )}
                          </button>

                          {state.expandedFormat === resolution && (
                            <div className="border-t border-gray-200 dark:border-gray-700">
                              <div className="p-4 space-y-3">
                                <div className="grid grid-cols-1 gap-2">
                                  {groups[resolution].map((quality, index) => (
                                    <button
                                      key={index}
                                      onClick={() => handleQualitySelect(resolution, quality)}
                                      className={`p-4 rounded-lg border ${
                                        state.selectedQuality.format === resolution && state.selectedQuality.quality?.itag === quality.itag
                                          ? "border-blue-500 bg-blue-50 dark:bg-blue-900/30"
                                          : "border-gray-200 dark:border-gray-700 hover:border-blue-500"
                                      } transition-all duration-200`}
                                    >
                                      <div className="flex flex-col items-start gap-2">
                                        <span className="text-sm font-medium">
                                          {formatQualityLabel(quality)}
                                        </span>
                                        <div className="text-xs text-gray-500">
                                          {getCodecDescription(quality)}
                                        </div>
                                      </div>
                                    </button>
                                  ))}
                                </div>

                                {state.selectedQuality.format === resolution && (
                                  <div className="flex gap-2">
                                    <button
                                      onClick={handleDownloadClick}
                                      disabled={!state.selectedQuality.quality || state.downloading}
                                      className="flex-1 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center gap-2"
                                    >
                                      {state.downloading ? (
                                        <div className="flex items-center justify-center gap-3 w-full">
                                          <div className="flex-1 h-2.5 bg-blue-700 rounded-full overflow-hidden">
                                            <div
                                              className="h-full bg-white transition-all duration-150 ease-out"
                                              style={{ width: `${state.downloadProgress}%` }}
                                            />
                                          </div>
                                          <div className="flex flex-col items-end min-w-[100px]">
                                            <span className="text-sm font-medium">{state.downloadProgress}%</span>
                                            <span className="text-xs text-gray-500">
                                              {state.downloading && `Size: ${state.videoInfo?.formats.find(f => f.itag === state.selectedQuality.quality?.itag)?.contentLength}`}
                                            </span>
                                          </div>
                                        </div>
                                      ) : (
                                        <>
                                          <Download className="w-4 h-4" />
                                          <span>Download {resolution}</span>
                                        </>
                                      )}
                                    </button>
                                    
                                    {state.downloading && (
                                      <button
                                        onClick={handleCancelDownload}
                                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center justify-center"
                                        title="Cancel Download"
                                      >
                                        <X className="w-4 h-4" />
                                      </button>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      ));
                    })()}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export function VideoDownloaderGuide() {
  return (
    <section className="container mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-[#1C6997] dark:text-[#1C6997] mb-2">
            Video Downloader Guide
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Learn how to download and convert videos effectively
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Video className="h-5 w-5 text-[#1C6997]" />
              <h3 className="font-semibold">Basic Usage</h3>
            </div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Paste video URL</li>
              <li>Select desired format</li>
              <li>Choose quality options</li>
              <li>Click download button</li>
            </ul>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Download className="h-5 w-5 text-[#1C6997]" />
              <h3 className="font-semibold">Supported Platforms</h3>
            </div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>YouTube videos</li>
              <li>Instagram reels</li>
              <li>TikTok videos</li>
              <li>Other social platforms</li>
            </ul>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <FileVideo className="h-5 w-5 text-[#1C6997]" />
              <h3 className="font-semibold">Available Formats</h3>
            </div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>MP4 video format</li>
              <li>WebM video format</li>
              <li>MP3 audio only</li>
              <li>Multiple resolutions</li>
            </ul>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Settings className="h-5 w-5 text-[#1C6997]" />
              <h3 className="font-semibold">Advanced Features</h3>
            </div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Custom quality selection</li>
              <li>Format conversion</li>
              <li>Batch downloading</li>
              <li>Progress tracking</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}