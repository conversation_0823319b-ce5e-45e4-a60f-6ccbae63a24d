import { Card } from "@/components/ui/card";
import { BuyMeCoffeeButton } from "@/components/BuyMeCoffeeButton";
import { LikeButton } from "@/components/LikeButton";
import ClientOnly from "@/components/ClientOnly";

export function SupportSection() {
  return (
    <div className="space-y-6">
      <Card className="p-6 bg-gradient-to-br from-pink-50 to-white dark:from-gray-800 dark:to-gray-700">
        <h3 className="text-lg font-semibold mb-3 text-pink-600 dark:text-pink-400">Support & Feedback</h3>
        <div className="text-gray-600 dark:text-gray-300 space-y-4">
          <div className="space-y-2">
            <p>This tool is part of my commitment to providing free, high-quality text transformation services to everyone. Your support helps me:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>Maintain reliable API services and server infrastructure</li>
              <li>Develop new features and tools</li>
              <li>Keep the service free and accessible</li>
              <li>Improve AI model quality and performance</li>
            </ul>
          </div>
          <div className="grid gap-4 sm:grid-cols-2">
            <a
              href="/contact?type=issue"
              className="flex items-center justify-center gap-2 p-3 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <span>🐛 Report Issues</span>
            </a>
            <a
              href="/contact?type=feature"
              className="flex items-center justify-center gap-2 p-3 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <span>💡 Suggest Features</span>
            </a>
          </div>
          <div className="text-sm space-y-2">
            <p className="font-medium">Why Support?</p>
            <p>While I'm committed to keeping these tools free, running AI services comes with significant costs. Your support through Buy Me a Coffee helps me offset server expenses and enables continuous improvements to benefit all users.</p>
          </div>
        </div>
      </Card>
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-4">
          <ClientOnly>
            <LikeButton />
          </ClientOnly>
          <BuyMeCoffeeButton />
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Your support helps us continue providing free tools
        </p>
      </div>
    </div>
  );
}
