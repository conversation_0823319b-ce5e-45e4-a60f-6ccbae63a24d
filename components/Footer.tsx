'use client';

import { Gith<PERSON>, Twitter, Mail, Globe } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useTheme } from "@/contexts/ThemeContext";
import { cn } from "@/lib/utils";
import { themes } from "@/contexts/ThemeContext";

export function Footer() {
  const { currentTheme } = useTheme();

  // Get theme styles, defaulting to native if currentTheme is invalid
  const themeStyles = themes[currentTheme] || themes.native;

  return (
    <footer className={cn(
      themeStyles.bg,
      themeStyles.text,
      "w-full"
    )}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">Pixel&Code</h3>
            <p className="text-white/80">
              Transform and analyze your text with our powerful conversion tools.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Connect</h3>
            <div className="flex space-x-4">
              <a href="https://github.com/Sonamkhadka" target="_blank" rel="noopener noreferrer">
                <Button variant="ghost" size="icon" className="text-white hover:text-white/80">
                  <Github className="h-5 w-5" />
                </Button>
              </a>
              <a href="https://sonamkhadka.com" target="_blank" rel="noopener noreferrer">
                <Button variant="ghost" size="icon" className="text-white hover:text-white/80">
                  <Globe className="h-5 w-5" />
                </Button>
              </a>
              <Link href="/contact">
                <Button variant="ghost" size="icon" className="text-white hover:text-white/80">
                  <Mail className="h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <p className="text-white/80">
              Have questions? Email us at:<br />
              <a href="mailto:<EMAIL>" className="hover:text-white/60">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
        <div className="mt-8 pt-8 border-t border-white/10 text-center text-white/80">
          <p> {new Date().getFullYear()} Pixel&Code. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}