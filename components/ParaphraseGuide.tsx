import { Card } from "@/components/ui/card";

export function ParaphraseGuide() {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="p-6 bg-gradient-to-br from-blue-50 to-white dark:from-gray-800 dark:to-gray-700">
          <h3 className="text-lg font-semibold mb-3 text-blue-600 dark:text-blue-400">Features</h3>
          <ul className="space-y-2 text-gray-600 dark:text-gray-300">
            <li>• Multiple tone options for versatile writing</li>
            <li>• Up to 100 words per request</li>
            <li>• Real-time word count</li>
            <li>• One-click copy to clipboard</li>
            <li>• Rate limit: 1 request per 10 seconds</li>
          </ul>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-cyan-50 to-white dark:from-gray-800 dark:to-gray-700">
          <h3 className="text-lg font-semibold mb-3 text-cyan-600 dark:text-cyan-400">Available Tones</h3>
          <ul className="space-y-2 text-gray-600 dark:text-gray-300">
            <li>• <span className="font-medium">Formal:</span> Professional and academic writing</li>
            <li>• <span className="font-medium">Informal:</span> Casual and conversational style</li>
            <li>• <span className="font-medium">Creative:</span> Engaging and imaginative expression</li>
            <li>• <span className="font-medium">Professional:</span> Business and corporate communication</li>
            <li>• <span className="font-medium">Simple & Clear:</span> Easy-to-understand language</li>
          </ul>
        </Card>
      </div>

      <Card className="p-6 bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-700">
        <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">How to Use</h3>
        <ol className="space-y-3 text-gray-600 dark:text-gray-300">
          <li className="flex gap-3">
            <span className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-semibold">1</span>
            <span>Enter your text in the input box (maximum 100 words)</span>
          </li>
          <li className="flex gap-3">
            <span className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-semibold">2</span>
            <span>Select your desired tone from the dropdown menu</span>
          </li>
          <li className="flex gap-3">
            <span className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-semibold">3</span>
            <span>Click the "Paraphrase" button to transform your text</span>
          </li>
          <li className="flex gap-3">
            <span className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-semibold">4</span>
            <span>Copy the paraphrased text using the "Copy" button</span>
          </li>
        </ol>
      </Card>

      <Card className="p-6 bg-gradient-to-br from-rose-50 to-white dark:from-gray-800 dark:to-gray-700">
        <h3 className="text-lg font-semibold mb-3 text-rose-600 dark:text-rose-400">Important Notice</h3>
        <div className="text-gray-600 dark:text-gray-300 space-y-2">
          <p>This tool is provided free of charge to help everyone improve their writing. Please respect the following guidelines:</p>
          <ul className="space-y-2 pl-4">
            <li>• Please avoid spamming the tool with repeated requests</li>
            <li>• Wait 10 seconds between requests to allow others to use the service</li>
            <li>• Excessive requests may prevent other users from accessing the tool</li>
            <li>• If you find the tool helpful, consider supporting its continued availability</li>
          </ul>
        </div>
      </Card>
    </div>
  );
}