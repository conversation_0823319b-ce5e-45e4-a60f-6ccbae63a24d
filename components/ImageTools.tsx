"use client";

import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  UploadCloud, Download, Loader2, X, RotateCw, RotateCcw, FlipHorizontal, FlipVertical,
  Crop, ChevronLeft, ChevronRight, Maximize2, FileDown, Settings, Palette,
  Sun, Contrast, Droplets, Sparkles, Undo, Redo, Eye, EyeOff,
  Grid, Smartphone, Monitor, Square, Image as ImageIcon, Zap
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ImageAdjustments {
  brightness: number;
  contrast: number;
  saturation: number;
  blur: number;
  rotation: number;
  flipX: boolean;
  flipY: boolean;
  width: number;
  height: number;
}

interface ImageFile {
  file: File;
  id: string;
  preview: string;
  processed?: string;
  adjustments: ImageAdjustments;
  originalDimensions: { width: number; height: number };
  history: HistoryState[];
  historyIndex: number;
}

interface HistoryState {
  adjustments: ImageAdjustments;
  timestamp: number;
}

export default function ImageTools() {
  // Core state
  const [images, setImages] = useState<ImageFile[]>([]);
  const [currentImageId, setCurrentImageId] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [activeTab, setActiveTab] = useState('basic');

  // Export settings
  const [exportFormat, setExportFormat] = useState<'jpeg' | 'png' | 'webp'>('jpeg');
  const [exportQuality, setExportQuality] = useState(90);
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true);

  // UI state
  const [showPreview, setShowPreview] = useState(true);
  const [batchMode, setBatchMode] = useState(false);

  // Refs
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);

  // Get current image
  const currentImage = images.find(img => img.id === currentImageId);

  // Get current image adjustments
  const currentAdjustments = currentImage?.adjustments || {
    brightness: 0,
    contrast: 0,
    saturation: 0,
    blur: 0,
    rotation: 0,
    flipX: false,
    flipY: false,
    width: 0,
    height: 0
  };

  // Save adjustments to current image's history
  const saveToHistory = useCallback(() => {
    if (!currentImageId) return;

    setImages(prev => prev.map(img => {
      if (img.id === currentImageId) {
        const newState: HistoryState = {
          adjustments: { ...img.adjustments },
          timestamp: Date.now()
        };

        const newHistory = img.history.slice(0, img.historyIndex + 1);
        newHistory.push(newState);

        // Limit history to 50 states
        if (newHistory.length > 50) {
          newHistory.shift();
        }

        return {
          ...img,
          history: newHistory,
          historyIndex: newHistory.length - 1
        };
      }
      return img;
    }));
  }, [currentImageId]);

  // Undo/Redo functions
  const undo = useCallback(() => {
    if (!currentImageId || !currentImage) return;

    if (currentImage.historyIndex > 0) {
      const prevState = currentImage.history[currentImage.historyIndex - 1];
      setImages(prev => prev.map(img =>
        img.id === currentImageId
          ? { ...img, adjustments: prevState.adjustments, historyIndex: img.historyIndex - 1 }
          : img
      ));
    }
  }, [currentImageId, currentImage]);

  const redo = useCallback(() => {
    if (!currentImageId || !currentImage) return;

    if (currentImage.historyIndex < currentImage.history.length - 1) {
      const nextState = currentImage.history[currentImage.historyIndex + 1];
      setImages(prev => prev.map(img =>
        img.id === currentImageId
          ? { ...img, adjustments: nextState.adjustments, historyIndex: img.historyIndex + 1 }
          : img
      ));
    }
  }, [currentImageId, currentImage]);

  // Dropzone setup
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const processFiles = async () => {
      const newImages: ImageFile[] = [];

      for (const file of acceptedFiles) {
        const preview = URL.createObjectURL(file);
        const img = new Image();

        await new Promise<void>((resolve) => {
          img.onload = () => {
            const initialAdjustments: ImageAdjustments = {
              brightness: 0,
              contrast: 0,
              saturation: 0,
              blur: 0,
              rotation: 0,
              flipX: false,
              flipY: false,
              width: img.width,
              height: img.height
            };

            const initialState: HistoryState = {
              adjustments: initialAdjustments,
              timestamp: Date.now()
            };

            newImages.push({
              file,
              id: Math.random().toString(36).substr(2, 9),
              preview,
              adjustments: initialAdjustments,
              originalDimensions: { width: img.width, height: img.height },
              history: [initialState],
              historyIndex: 0
            });

            resolve();
          };
          img.src = preview;
        });
      }

      setImages(prev => [...prev, ...newImages]);

      // Set first image as current if none selected
      if (!currentImageId && newImages.length > 0) {
        setCurrentImageId(newImages[0].id);
      }
    };

    processFiles();
  }, [currentImageId]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.gif', '.bmp', '.tiff']
    },
    multiple: true,
    maxSize: 50 * 1024 * 1024 // 50MB limit
  });

  // Apply adjustments to canvas
  const applyAdjustments = useCallback((canvas: HTMLCanvasElement, sourceImage: HTMLImageElement, adjustments: ImageAdjustments) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = adjustments.width;
    canvas.height = adjustments.height;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Save context state
    ctx.save();

    // Apply transformations
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.rotate((adjustments.rotation * Math.PI) / 180);
    ctx.scale(adjustments.flipX ? -1 : 1, adjustments.flipY ? -1 : 1);

    // Apply filters
    const filters = [];
    if (adjustments.brightness !== 0) filters.push(`brightness(${100 + adjustments.brightness}%)`);
    if (adjustments.contrast !== 0) filters.push(`contrast(${100 + adjustments.contrast}%)`);
    if (adjustments.saturation !== 0) filters.push(`saturate(${100 + adjustments.saturation}%)`);
    if (adjustments.blur > 0) filters.push(`blur(${adjustments.blur}px)`);

    ctx.filter = filters.join(' ') || 'none';

    // Draw image
    ctx.drawImage(
      sourceImage,
      -adjustments.width / 2,
      -adjustments.height / 2,
      adjustments.width,
      adjustments.height
    );

    // Restore context state
    ctx.restore();
  }, []);

  // Update adjustment value for current image
  const updateAdjustment = useCallback((key: keyof ImageAdjustments, value: any) => {
    if (!currentImageId) return;

    setImages(prev => prev.map(img =>
      img.id === currentImageId
        ? { ...img, adjustments: { ...img.adjustments, [key]: value } }
        : img
    ));
  }, [currentImageId]);

  // Handle dimension changes with aspect ratio
  const handleDimensionChange = useCallback((type: 'width' | 'height', value: number) => {
    if (!currentImage) return;

    const aspectRatio = currentImage.originalDimensions.width / currentImage.originalDimensions.height;

    if (maintainAspectRatio) {
      if (type === 'width') {
        updateAdjustment('width', value);
        updateAdjustment('height', Math.round(value / aspectRatio));
      } else {
        updateAdjustment('height', value);
        updateAdjustment('width', Math.round(value * aspectRatio));
      }
    } else {
      updateAdjustment(type, value);
    }
  }, [currentImage, maintainAspectRatio, updateAdjustment]);

  // Export single image
  const exportImage = useCallback(async (imageFile: ImageFile) => {
    if (!canvasRef.current) return;

    setProcessing(true);

    try {
      const img = new Image();
      img.onload = () => {
        const canvas = canvasRef.current!;
        applyAdjustments(canvas, img, imageFile.adjustments);

        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `edited-${imageFile.file.name.replace(/\.[^/.]+$/, '')}.${exportFormat === 'jpeg' ? 'jpg' : exportFormat}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          }
          setProcessing(false);
        }, `image/${exportFormat}`, exportQuality / 100);
      };
      img.src = imageFile.preview;
    } catch (error) {
      console.error('Export failed:', error);
      setProcessing(false);
    }
  }, [applyAdjustments, exportFormat, exportQuality]);

  // Export all images (batch)
  const exportAllImages = useCallback(async () => {
    if (images.length === 0) return;

    setProcessing(true);
    setProgress(0);

    for (let i = 0; i < images.length; i++) {
      const imageFile = images[i];

      // Create a promise for each image export
      await new Promise<void>((resolve) => {
        const img = new Image();
        img.onload = () => {
          const canvas = canvasRef.current!;
          applyAdjustments(canvas, img, imageFile.adjustments);

          canvas.toBlob((blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `edited-${imageFile.file.name.replace(/\.[^/.]+$/, '')}.${exportFormat === 'jpeg' ? 'jpg' : exportFormat}`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            }
            resolve();
          }, `image/${exportFormat}`, exportQuality / 100);
        };
        img.src = imageFile.preview;
      });

      setProgress(((i + 1) / images.length) * 100);

      // Small delay to prevent browser freezing
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    setProcessing(false);
    setProgress(0);
  }, [images, applyAdjustments, exportFormat, exportQuality]);

  // Reset all adjustments for current image
  const resetAdjustments = useCallback(() => {
    if (!currentImage || !currentImageId) return;

    const resetState: ImageAdjustments = {
      brightness: 0,
      contrast: 0,
      saturation: 0,
      blur: 0,
      rotation: 0,
      flipX: false,
      flipY: false,
      width: currentImage.originalDimensions.width,
      height: currentImage.originalDimensions.height
    };

    setImages(prev => prev.map(img =>
      img.id === currentImageId
        ? { ...img, adjustments: resetState }
        : img
    ));

    saveToHistory();
  }, [currentImage, currentImageId, saveToHistory]);

  // Social media presets
  const socialPresets = [
    { name: 'Instagram Post', width: 1080, height: 1080, icon: Square },
    { name: 'Instagram Story', width: 1080, height: 1920, icon: Smartphone },
    { name: 'Facebook Post', width: 1200, height: 630, icon: Monitor },
    { name: 'Twitter Header', width: 1500, height: 500, icon: Monitor },
    { name: 'YouTube Thumbnail', width: 1280, height: 720, icon: Monitor },
    { name: 'LinkedIn Post', width: 1200, height: 627, icon: Monitor }
  ];

  // Apply preset dimensions
  const applyPreset = useCallback((width: number, height: number) => {
    updateAdjustment('width', width);
    updateAdjustment('height', height);
    saveToHistory();
  }, [updateAdjustment, saveToHistory]);

  // Apply filter presets
  const applyFilter = useCallback((filterName: string) => {
    if (!currentImageId || !currentImage) return;

    let newAdjustments = { ...currentImage.adjustments };

    switch (filterName) {
      case 'vintage':
        newAdjustments = { ...newAdjustments, brightness: -10, contrast: 15, saturation: -20 };
        break;
      case 'bright':
        newAdjustments = { ...newAdjustments, brightness: 20, contrast: 10 };
        break;
      case 'dramatic':
        newAdjustments = { ...newAdjustments, contrast: 30, saturation: 15 };
        break;
      case 'soft':
        newAdjustments = { ...newAdjustments, blur: 1, brightness: 5 };
        break;
      case 'bw':
        newAdjustments = { ...newAdjustments, saturation: -100 };
        break;
      default:
        break;
    }

    setImages(prev => prev.map(img =>
      img.id === currentImageId
        ? { ...img, adjustments: newAdjustments }
        : img
    ));

    saveToHistory();
  }, [currentImageId, currentImage, saveToHistory]);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  // Remove image
  const removeImage = useCallback((imageId: string) => {
    setImages(prev => {
      const filtered = prev.filter(img => img.id !== imageId);

      // Clean up URL
      const imageToRemove = prev.find(img => img.id === imageId);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }

      // Update current image if needed
      if (imageId === currentImageId) {
        if (filtered.length > 0) {
          setCurrentImageId(filtered[0].id);
        } else {
          setCurrentImageId(null);
        }
      }

      return filtered;
    });
  }, [currentImageId]);

  // Real-time preview effect
  useEffect(() => {
    if (!currentImage || !previewCanvasRef.current) return;

    const img = new Image();
    img.onload = () => {
      applyAdjustments(previewCanvasRef.current!, img, currentImage.adjustments);
    };
    img.src = currentImage.preview;
  }, [currentImage, applyAdjustments]);

  return (
    <div className="space-y-6">
      {/* Upload Zone */}
      <Card className="border-2 border-dashed border-purple-200 dark:border-purple-800 bg-gradient-to-br from-purple-50/50 to-indigo-50/50 dark:from-purple-950/20 dark:to-indigo-950/20">
        <CardContent className="p-8">
          <div
            {...getRootProps()}
            className={`
              text-center cursor-pointer transition-all duration-300
              ${isDragActive ? 'scale-105 opacity-80' : 'hover:scale-102'}
            `}
          >
            <input {...getInputProps()} />
            <div className="space-y-4">
              <div className="mx-auto w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-full flex items-center justify-center">
                <UploadCloud className="w-8 h-8 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {isDragActive ? 'Drop images here' : 'Upload your images'}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Drag and drop images here, or click to browse
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  Supports: JPEG, PNG, WebP, GIF, BMP, TIFF (up to 50MB each)
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Image Gallery */}
      {images.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <ImageIcon className="w-5 h-5 text-purple-500" />
                Images ({images.length})
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setBatchMode(!batchMode)}
                  className="text-purple-600 border-purple-200 hover:bg-purple-50"
                >
                  <Zap className="w-4 h-4 mr-1" />
                  {batchMode ? 'Single Mode' : 'Batch Mode'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setImages([])}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <X className="w-4 h-4 mr-1" />
                  Clear All
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 gap-3">
              {images.map((image) => (
                <div
                  key={image.id}
                  className={`
                    relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all
                    ${currentImageId === image.id
                      ? 'border-purple-500 ring-2 ring-purple-200 dark:ring-purple-800'
                      : 'border-gray-200 dark:border-gray-700 hover:border-purple-300'
                    }
                  `}
                  onClick={() => {
                    setCurrentImageId(image.id);
                    loadImageDimensions(image);
                  }}
                >
                  <img
                    src={image.preview}
                    alt="Preview"
                    className="w-full h-20 object-cover"
                  />
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeImage(image.id);
                    }}
                    className="absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center hover:bg-red-600"
                  >
                    <X className="w-3 h-3" />
                  </button>
                  <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 truncate">
                    {image.file.name}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Editor */}
      {currentImage && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Preview Section */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Eye className="w-5 h-5 text-purple-500" />
                    Preview
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={undo}
                      disabled={!currentImage || currentImage.historyIndex <= 0}
                      className="text-purple-600 border-purple-200 hover:bg-purple-50"
                    >
                      <Undo className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={redo}
                      disabled={!currentImage || currentImage.historyIndex >= currentImage.history.length - 1}
                      className="text-purple-600 border-purple-200 hover:bg-purple-50"
                    >
                      <Redo className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={resetAdjustments}
                      className="text-purple-600 border-purple-200 hover:bg-purple-50"
                    >
                      Reset
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="relative bg-gray-50 dark:bg-gray-900 rounded-lg overflow-hidden">
                  <canvas
                    ref={previewCanvasRef}
                    className="max-w-full max-h-96 mx-auto block"
                    style={{ imageRendering: 'high-quality' }}
                  />
                  <canvas ref={canvasRef} className="hidden" />
                </div>

                {/* Image Info */}
                <div className="mt-4 p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Size:</span>
                      <span className="ml-1 font-medium">{formatFileSize(currentImage.file.size)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Type:</span>
                      <span className="ml-1 font-medium">{currentImage.file.type.split('/')[1].toUpperCase()}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Dimensions:</span>
                      <span className="ml-1 font-medium">{currentAdjustments.width} × {currentAdjustments.height}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Format:</span>
                      <span className="ml-1 font-medium">{exportFormat.toUpperCase()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tools Panel */}
          <div className="space-y-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Settings className="w-5 h-5 text-purple-500" />
                  Tools
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic" className="text-xs">Basic</TabsTrigger>
                    <TabsTrigger value="adjust" className="text-xs">Adjust</TabsTrigger>
                    <TabsTrigger value="filters" className="text-xs">Filters</TabsTrigger>
                    <TabsTrigger value="export" className="text-xs">Export</TabsTrigger>
                  </TabsList>

                  {/* Basic Tools */}
                  <TabsContent value="basic" className="space-y-4 mt-4">
                    {/* Resize */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium flex items-center gap-2">
                        <Maximize2 className="w-4 h-4 text-purple-500" />
                        Resize
                      </Label>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="width" className="text-xs">Width</Label>
                          <Input
                            id="width"
                            type="number"
                            value={currentAdjustments.width}
                            onChange={(e) => handleDimensionChange('width', Number(e.target.value))}
                            className="h-8"
                          />
                        </div>
                        <div>
                          <Label htmlFor="height" className="text-xs">Height</Label>
                          <Input
                            id="height"
                            type="number"
                            value={currentAdjustments.height}
                            onChange={(e) => handleDimensionChange('height', Number(e.target.value))}
                            className="h-8"
                          />
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={maintainAspectRatio}
                          onCheckedChange={setMaintainAspectRatio}
                          className="data-[state=checked]:bg-purple-500"
                        />
                        <Label className="text-xs">Lock aspect ratio</Label>
                      </div>
                    </div>

                    {/* Social Media Presets */}
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-600 dark:text-gray-400">Quick Presets</Label>
                      <div className="grid grid-cols-2 gap-1">
                        {socialPresets.slice(0, 4).map((preset) => {
                          const IconComponent = preset.icon;
                          return (
                            <Button
                              key={preset.name}
                              variant="outline"
                              size="sm"
                              onClick={() => applyPreset(preset.width, preset.height)}
                              className="h-8 text-xs justify-start"
                            >
                              <IconComponent className="w-3 h-3 mr-1" />
                              {preset.name.split(' ')[0]}
                            </Button>
                          );
                        })}
                      </div>
                    </div>

                    {/* Transform */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium flex items-center gap-2">
                        <RotateCw className="w-4 h-4 text-purple-500" />
                        Transform
                      </Label>
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Rotate</span>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              updateAdjustment('rotation', currentAdjustments.rotation - 90);
                              saveToHistory();
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <RotateCcw className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              updateAdjustment('rotation', currentAdjustments.rotation + 90);
                              saveToHistory();
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <RotateCw className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Flip</span>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              updateAdjustment('flipX', !currentAdjustments.flipX);
                              saveToHistory();
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <FlipHorizontal className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              updateAdjustment('flipY', !currentAdjustments.flipY);
                              saveToHistory();
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <FlipVertical className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  {/* Adjustments Tab */}
                  <TabsContent value="adjust" className="space-y-4 mt-4">
                    {/* Brightness */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm flex items-center gap-2">
                          <Sun className="w-4 h-4 text-yellow-500" />
                          Brightness
                        </Label>
                        <span className="text-xs text-gray-500">{currentAdjustments.brightness}</span>
                      </div>
                      <Slider
                        value={[currentAdjustments.brightness]}
                        onValueChange={([value]) => updateAdjustment('brightness', value)}
                        onValueCommit={() => saveToHistory()}
                        min={-100}
                        max={100}
                        step={1}
                        className="w-full"
                      />
                    </div>

                    {/* Contrast */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm flex items-center gap-2">
                          <Contrast className="w-4 h-4 text-gray-600" />
                          Contrast
                        </Label>
                        <span className="text-xs text-gray-500">{currentAdjustments.contrast}</span>
                      </div>
                      <Slider
                        value={[currentAdjustments.contrast]}
                        onValueChange={([value]) => updateAdjustment('contrast', value)}
                        onValueCommit={() => saveToHistory()}
                        min={-100}
                        max={100}
                        step={1}
                        className="w-full"
                      />
                    </div>

                    {/* Saturation */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm flex items-center gap-2">
                          <Droplets className="w-4 h-4 text-blue-500" />
                          Saturation
                        </Label>
                        <span className="text-xs text-gray-500">{currentAdjustments.saturation}</span>
                      </div>
                      <Slider
                        value={[currentAdjustments.saturation]}
                        onValueChange={([value]) => updateAdjustment('saturation', value)}
                        onValueCommit={() => saveToHistory()}
                        min={-100}
                        max={100}
                        step={1}
                        className="w-full"
                      />
                    </div>

                    {/* Blur */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm flex items-center gap-2">
                          <Sparkles className="w-4 h-4 text-purple-500" />
                          Blur
                        </Label>
                        <span className="text-xs text-gray-500">{currentAdjustments.blur}px</span>
                      </div>
                      <Slider
                        value={[currentAdjustments.blur]}
                        onValueChange={([value]) => updateAdjustment('blur', value)}
                        onValueCommit={() => saveToHistory()}
                        min={0}
                        max={20}
                        step={0.5}
                        className="w-full"
                      />
                    </div>
                  </TabsContent>

                  {/* Filters Tab */}
                  <TabsContent value="filters" className="space-y-4 mt-4">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Palette className="w-4 h-4 text-purple-500" />
                      Quick Filters
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyFilter('vintage')}
                        className="h-8 text-xs"
                      >
                        Vintage
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyFilter('bright')}
                        className="h-8 text-xs"
                      >
                        Bright
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyFilter('dramatic')}
                        className="h-8 text-xs"
                      >
                        Dramatic
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyFilter('soft')}
                        className="h-8 text-xs"
                      >
                        Soft
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyFilter('bw')}
                        className="h-8 text-xs col-span-2"
                      >
                        Black & White
                      </Button>
                    </div>
                  </TabsContent>

                  {/* Export Tab */}
                  <TabsContent value="export" className="space-y-4 mt-4">
                    {/* Format Selection */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Format</Label>
                      <div className="grid grid-cols-3 gap-1">
                        {(['jpeg', 'png', 'webp'] as const).map((format) => (
                          <Button
                            key={format}
                            variant={exportFormat === format ? "default" : "outline"}
                            size="sm"
                            onClick={() => setExportFormat(format)}
                            className="h-8 text-xs"
                          >
                            {format.toUpperCase()}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Quality */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm">Quality</Label>
                        <span className="text-xs text-gray-500">{exportQuality}%</span>
                      </div>
                      <Slider
                        value={[exportQuality]}
                        onValueChange={([value]) => setExportQuality(value)}
                        min={10}
                        max={100}
                        step={5}
                        className="w-full"
                      />
                    </div>

                    {/* Export Buttons */}
                    <div className="space-y-2">
                      <Button
                        onClick={() => currentImage && exportImage(currentImage)}
                        disabled={processing}
                        className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                      >
                        {processing ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Download className="w-4 h-4 mr-2" />
                        )}
                        Export Current
                      </Button>

                      {images.length > 1 && (
                        <Button
                          onClick={exportAllImages}
                          disabled={processing}
                          variant="outline"
                          className="w-full"
                        >
                          {processing ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <Download className="w-4 h-4 mr-2" />
                          )}
                          Export All ({images.length})
                        </Button>
                      )}
                    </div>

                    {processing && (
                      <div className="space-y-2">
                        <Progress value={progress} className="w-full" />
                        <p className="text-xs text-center text-gray-500">
                          Processing... {Math.round(progress)}%
                        </p>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

    </div>
  );
}
