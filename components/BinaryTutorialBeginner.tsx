'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import {
  Lightbulb,
  CheckCircle,
  XCircle,
  ArrowRight,
  Binary,
  Calculator,
  Zap
} from 'lucide-react';
import { binaryToDecimal, decimalToBinary, isValidBinary } from '@/lib/binaryUtils';

interface Exercise {
  id: number;
  question: string;
  answer: string;
  type: 'binary-to-decimal' | 'decimal-to-binary';
  completed: boolean;
}

export function BinaryTutorialBeginner() {
  const [currentSection, setCurrentSection] = useState(0);
  const [exercises, setExercises] = useState<Exercise[]>([
    { id: 1, question: '1010', answer: '10', type: 'binary-to-decimal', completed: false },
    { id: 2, question: '1111', answer: '15', type: 'binary-to-decimal', completed: false },
    { id: 3, question: '5', answer: '101', type: 'decimal-to-binary', completed: false },
    { id: 4, question: '8', answer: '1000', type: 'decimal-to-binary', completed: false },
  ]);
  const [userAnswers, setUserAnswers] = useState<Record<number, string>>({});
  const [selectedBit, setSelectedBit] = useState<number | null>(null);

  const sections = [
    'What is Binary?',
    'Number System Comparison',
    'Interactive Bit Explorer',
    'Practice Exercises'
  ];

  const numberSystems = [
    { name: 'Binary', base: 2, digits: '0, 1', example: '1010₂ = 10₁₀ = A₁₆' },
    { name: 'Decimal', base: 10, digits: '0-9', example: '42₁₀ = 101010₂ = 2A₁₆' },
    { name: 'Octal', base: 8, digits: '0-7', example: '52₈ = 42₁₀ = 2A₁₆' },
    { name: 'Hexadecimal', base: 16, digits: '0-9, A-F', example: '2A₁₆ = 42₁₀ = 101010₂' },
    { name: 'Pentary', base: 5, digits: '0-4', example: '132₅ = 42₁₀ = 2A₁₆' },
  ];

  const checkAnswer = (exerciseId: number, userAnswer: string) => {
    const exercise = exercises.find(e => e.id === exerciseId);
    if (!exercise) return;

    const isCorrect = userAnswer.trim() === exercise.answer;

    if (isCorrect) {
      toast.success('Correct! Well done!');
      setExercises(prev => prev.map(e =>
        e.id === exerciseId ? { ...e, completed: true } : e
      ));
    } else {
      toast.error(`Incorrect. The correct answer is ${exercise.answer}`);
    }
  };

  const getProgress = () => {
    const completed = exercises.filter(e => e.completed).length;
    return (completed / exercises.length) * 100;
  };

  const BitExplorer = () => {
    const [binaryValue, setBinaryValue] = useState('00001010');
    const bits = binaryValue.split('');

    const toggleBit = (index: number) => {
      const newBits = [...bits];
      newBits[index] = newBits[index] === '0' ? '1' : '0';
      setBinaryValue(newBits.join(''));
    };

    const getDecimalValue = () => {
      try {
        return binaryToDecimal(binaryValue);
      } catch {
        return 0;
      }
    };

    return (
      <div className="space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Interactive Bit Explorer</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Click on any bit to toggle it and see how it affects the decimal value!
          </p>
        </div>

        <div className="flex justify-center space-x-2 mb-4">
          {bits.map((bit, index) => (
            <div key={index} className="text-center">
              <div className="text-xs text-gray-500 mb-1">
                2^{bits.length - 1 - index}
              </div>
              <Button
                variant={bit === '1' ? 'default' : 'outline'}
                size="lg"
                className="w-12 h-12 text-lg font-mono"
                onClick={() => toggleBit(index)}
              >
                {bit}
              </Button>
              <div className="text-xs text-gray-500 mt-1">
                {Math.pow(2, bits.length - 1 - index)}
              </div>
            </div>
          ))}
        </div>

        <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-sm text-gray-600 dark:text-gray-400">Binary Value</div>
          <div className="text-2xl font-mono font-bold">{binaryValue}</div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-2">Decimal Value</div>
          <div className="text-3xl font-bold text-blue-600">{getDecimalValue()}</div>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
          Calculation: {bits.map((bit, index) => {
            const power = bits.length - 1 - index;
            const value = Math.pow(2, power);
            return bit === '1' ? `${value}` : null;
          }).filter(Boolean).join(' + ') || '0'} = {getDecimalValue()}
        </div>
      </div>
    );
  };

  const renderSection = () => {
    switch (currentSection) {
      case 0:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Binary className="h-16 w-16 mx-auto mb-4 text-blue-600" />
              <h2 className="text-2xl font-bold mb-4">What is Binary?</h2>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lightbulb className="h-5 w-5" />
                    The Basics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p>Binary is a base-2 number system that uses only two digits: <strong>0</strong> and <strong>1</strong>.</p>
                  <p>Each position represents a power of 2, starting from 2⁰ on the right.</p>
                  <p>It's the fundamental language of computers and digital systems.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calculator className="h-5 w-5" />
                    Why Binary?
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p>Computers use binary because digital circuits can easily represent two states:</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li><strong>0</strong> = Off/Low voltage</li>
                    <li><strong>1</strong> = On/High voltage</li>
                  </ul>
                  <p>This makes binary perfect for digital electronics!</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Binary Place Values</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-center">
                    <thead>
                      <tr className="border-b">
                        <th className="p-2">Position</th>
                        <th className="p-2">Power of 2</th>
                        <th className="p-2">Decimal Value</th>
                      </tr>
                    </thead>
                    <tbody>
                      {[7, 6, 5, 4, 3, 2, 1, 0].map(power => (
                        <tr key={power} className="border-b">
                          <td className="p-2 font-mono">2^{power}</td>
                          <td className="p-2">{Math.pow(2, power)}</td>
                          <td className="p-2 font-bold">{Math.pow(2, power)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">Number System Comparison</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Understanding different number systems and how they relate to each other
              </p>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              {numberSystems.map((system) => (
                <Card key={system.name}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      {system.name}
                      <Badge variant="outline">Base {system.base}</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p><strong>Digits:</strong> {system.digits}</p>
                    <p><strong>Example:</strong> <code className="font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">{system.example}</code></p>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Conversion Examples</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h4 className="font-semibold mb-2">Binary to Decimal: 1011₂</h4>
                    <p className="font-mono">1×2³ + 0×2² + 1×2¹ + 1×2⁰ = 8 + 0 + 2 + 1 = 11₁₀</p>
                  </div>
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <h4 className="font-semibold mb-2">Decimal to Binary: 13₁₀</h4>
                    <p className="font-mono">13 ÷ 2 = 6 remainder 1</p>
                    <p className="font-mono">6 ÷ 2 = 3 remainder 0</p>
                    <p className="font-mono">3 ÷ 2 = 1 remainder 1</p>
                    <p className="font-mono">1 ÷ 2 = 0 remainder 1</p>
                    <p className="font-mono font-bold">Result: 1101₂</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 2:
        return <BitExplorer />;

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">Practice Exercises</h2>
              <Progress value={getProgress()} className="w-full max-w-md mx-auto" />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                {exercises.filter(e => e.completed).length} of {exercises.length} completed
              </p>
            </div>

            <div className="grid gap-4">
              {exercises.map((exercise) => (
                <Card key={exercise.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-semibold mb-2">
                          {exercise.type === 'binary-to-decimal'
                            ? `Convert ${exercise.question}₂ to decimal`
                            : `Convert ${exercise.question}₁₀ to binary`
                          }
                        </p>
                        <div className="flex gap-2">
                          <Input
                            placeholder="Your answer"
                            value={userAnswers[exercise.id] || ''}
                            onChange={(e) => setUserAnswers(prev => ({
                              ...prev,
                              [exercise.id]: e.target.value
                            }))}
                            disabled={exercise.completed}
                          />
                          <Button
                            onClick={() => checkAnswer(exercise.id, userAnswers[exercise.id] || '')}
                            disabled={exercise.completed || !userAnswers[exercise.id]}
                          >
                            Check
                          </Button>
                        </div>
                      </div>
                      <div className="ml-4">
                        {exercise.completed ? (
                          <CheckCircle className="h-6 w-6 text-green-600" />
                        ) : (
                          <div className="h-6 w-6 rounded-full border-2 border-gray-300" />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Section Navigation */}
      <div className="flex flex-wrap gap-2 justify-center">
        {sections.map((section, index) => (
          <Button
            key={index}
            variant={currentSection === index ? 'default' : 'outline'}
            onClick={() => setCurrentSection(index)}
            className="text-sm"
          >
            {section}
          </Button>
        ))}
      </div>

      {/* Section Content */}
      <Card>
        <CardContent className="p-6">
          {renderSection()}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentSection(Math.max(0, currentSection - 1))}
          disabled={currentSection === 0}
        >
          Previous
        </Button>
        <Button
          onClick={() => setCurrentSection(Math.min(sections.length - 1, currentSection + 1))}
          disabled={currentSection === sections.length - 1}
        >
          Next
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}
