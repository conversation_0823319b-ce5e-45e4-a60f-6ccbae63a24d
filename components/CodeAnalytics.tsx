'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { FileText, GitCompare, AlertTriangle } from 'lucide-react';
import Editor from '@monaco-editor/react';

interface CodeMetrics {
  lines: number;
  characters: number;
  words: number;
  syntaxErrors: Array<{
    line: number;
    message: string;
  }>;
}

interface CodeAnalyticsProps {
  code: string;
  language: string;
}

export function CodeAnalytics({ code, language }: CodeAnalyticsProps) {
  const [metrics, setMetrics] = useState<CodeMetrics>({
    lines: 0,
    characters: 0,
    words: 0,
    syntaxErrors: [],
  });
  const [showDiff, setShowDiff] = useState(false);
  const [originalCode, setOriginalCode] = useState(code);

  useEffect(() => {
    calculateMetrics(code);
  }, [code]);

  const calculateMetrics = (text: string) => {
    const lines = text.split('\n').length;
    const characters = text.length;
    const words = text.trim().split(/\s+/).length;

    setMetrics({
      lines,
      characters,
      words,
      syntaxErrors: [], // This will be populated by Monaco Editor's markers
    });
  };

  const handleEditorDidMount = (editor: any, monaco: any) => {
    // Set up Monaco Editor for syntax error detection
    monaco.editor.onDidChangeMarkers(([resource]: any) => {
      const markers = monaco.editor.getModelMarkers({ resource });
      const errors = markers.map((marker: any) => ({
        line: marker.startLineNumber,
        message: marker.message,
      }));
      setMetrics(prev => ({ ...prev, syntaxErrors: errors }));
    });
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Lines</span>
            <Badge variant="secondary">{metrics.lines}</Badge>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Characters</span>
            <Badge variant="secondary">{metrics.characters}</Badge>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Words</span>
            <Badge variant="secondary">{metrics.words}</Badge>
          </div>
        </Card>
      </div>

      {metrics.syntaxErrors.length > 0 && (
        <Card className="p-4 border-yellow-500">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
            <h3 className="font-semibold">Syntax Warnings</h3>
          </div>
          <ul className="space-y-2">
            {metrics.syntaxErrors.map((error, index) => (
              <li key={index} className="text-sm text-gray-600 dark:text-gray-400">
                Line {error.line}: {error.message}
              </li>
            ))}
          </ul>
        </Card>
      )}

      {showDiff && (
        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <GitCompare className="h-4 w-4" />
              <h3 className="font-semibold">Code Diff</h3>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDiff(false)}
            >
              Close Diff
            </Button>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm font-medium mb-2">Original</div>
              <Editor
                height="300px"
                language={language}
                value={originalCode}
                theme="vs-dark"
                options={{
                  readOnly: true,
                  minimap: { enabled: false },
                  lineNumbers: 'on',
                  scrollBeyondLastLine: false,
                  wordWrap: 'on',
                }}
              />
            </div>
            <div>
              <div className="text-sm font-medium mb-2">Modified</div>
              <Editor
                height="300px"
                language={language}
                value={code}
                theme="vs-dark"
                options={{
                  readOnly: true,
                  minimap: { enabled: false },
                  lineNumbers: 'on',
                  scrollBeyondLastLine: false,
                  wordWrap: 'on',
                }}
              />
            </div>
          </div>
        </Card>
      )}

      {!showDiff && (
        <Button
          variant="outline"
          className="w-full"
          onClick={() => {
            setOriginalCode(code);
            setShowDiff(true);
          }}
        >
          <GitCompare className="h-4 w-4 mr-2" />
          Compare Changes
        </Button>
      )}
    </div>
  );
}
