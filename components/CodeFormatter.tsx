'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Copy, FileJson, FileCode, Code, Database, FileText, Moon, Sun, Braces } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useSearchParams, useRouter } from 'next/navigation';
import Editor from '@monaco-editor/react';
import { CodeAnalytics } from '@/components/CodeAnalytics';

// Import formatting libraries
import * as prettier from 'prettier';
import parserBabel from 'prettier/parser-babel';
import parserHtml from 'prettier/parser-html';
import parserCss from 'prettier/parser-postcss';
import parserTypeScript from 'prettier/parser-typescript';
import { format as formatSql } from 'sql-formatter';
import { format as formatXml } from 'xml-formatter';

// Helper function to map format types to Monaco Editor languages
const getMonacoLanguage = (formatType: string): string => {
  const languageMap: { [key: string]: string } = {
    javascript: 'javascript',
    json: 'json',
    css: 'css',
    html: 'html',
    python: 'python',
    sql: 'sql',
    xml: 'xml',
  };
  return languageMap[formatType] || 'plaintext';
};

export function CodeFormatter() {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [formatType, setFormatType] = useState('javascript');
  const [theme, setTheme] = useState<'vs-dark' | 'vs-light'>('vs-dark');
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Supported languages for code formatting
  const supportedLanguages = ['javascript', 'json', 'css', 'html', 'python', 'sql', 'xml'];

  useEffect(() => {
    const type = searchParams.get('type');
    if (type && supportedLanguages.includes(type)) {
      setFormatType(type);
    }
  }, [searchParams]);

  // JavaScript/TypeScript formatting
  const formatJavaScript = async (text: string, minify: boolean = false) => {
    try {
      return await prettier.format(text, {
        parser: 'babel',
        plugins: [parserBabel],
        semi: true,
        singleQuote: true,
        tabWidth: 2,
        trailingComma: 'es5',
        printWidth: minify ? 1000 : 80,
      });
    } catch (error) {
      throw new Error('Invalid JavaScript code. Please check your syntax.');
    }
  };

  // JSON formatting
  const formatJSON = (text: string, minify: boolean = false) => {
    try {
      const parsed = JSON.parse(text);
      return minify ? JSON.stringify(parsed) : JSON.stringify(parsed, null, 2);
    } catch (error) {
      throw new Error('Invalid JSON. Please check your input and try again.');
    }
  };

  // CSS formatting
  const formatCSS = async (text: string, minify: boolean = false) => {
    try {
      return await prettier.format(text, {
        parser: 'css',
        plugins: [parserCss],
        tabWidth: 2,
        printWidth: minify ? 1000 : 80,
      });
    } catch (error) {
      throw new Error('Invalid CSS code. Please check your syntax.');
    }
  };

  // HTML formatting
  const formatHTML = async (text: string, minify: boolean = false) => {
    try {
      return await prettier.format(text, {
        parser: 'html',
        plugins: [parserHtml],
        tabWidth: 2,
        printWidth: minify ? 1000 : 80,
        htmlWhitespaceSensitivity: 'css',
      });
    } catch (error) {
      throw new Error('Invalid HTML code. Please check your syntax.');
    }
  };

  // Python formatting (basic indentation-based)
  const formatPython = (text: string, minify: boolean = false) => {
    try {
      if (minify) {
        // Basic minification: remove extra whitespace and comments
        return text
          .split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.startsWith('#'))
          .join('\n');
      } else {
        // Basic formatting: fix indentation
        const lines = text.split('\n');
        let indentLevel = 0;
        const formatted = lines.map(line => {
          const trimmed = line.trim();
          if (!trimmed) return '';

          // Decrease indent for certain keywords
          if (trimmed.match(/^(except|elif|else|finally):/)) {
            indentLevel = Math.max(0, indentLevel - 1);
          }

          const formatted = '    '.repeat(indentLevel) + trimmed;

          // Increase indent after certain keywords
          if (trimmed.endsWith(':') &&
              trimmed.match(/^(if|for|while|def|class|try|except|elif|else|finally|with)/)) {
            indentLevel++;
          }

          return formatted;
        });

        return formatted.join('\n');
      }
    } catch (error) {
      throw new Error('Error formatting Python code.');
    }
  };

  // SQL formatting
  const formatSQLCode = (text: string, minify: boolean = false) => {
    try {
      return formatSql(text, {
        language: 'sql',
        tabWidth: minify ? 0 : 2,
        keywordCase: 'upper',
        functionCase: 'upper',
      });
    } catch (error) {
      throw new Error('Invalid SQL code. Please check your syntax.');
    }
  };

  // XML formatting
  const formatXMLCode = (text: string, minify: boolean = false) => {
    try {
      return formatXml(text, {
        indentation: minify ? '' : '  ',
        collapseContent: minify,
        lineSeparator: minify ? '' : '\n',
      });
    } catch (error) {
      throw new Error('Invalid XML code. Please check your syntax.');
    }
  };

  const handleFormat = async (action: string) => {
    if (!input.trim()) {
      toast.error("Please enter some code to format.");
      return;
    }

    try {
      let result = '';
      const isMinify = action === 'minify';

      switch(formatType) {
        case 'javascript':
          result = await formatJavaScript(input, isMinify);
          break;
        case 'json':
          result = formatJSON(input, isMinify);
          break;
        case 'css':
          result = await formatCSS(input, isMinify);
          break;
        case 'html':
          result = await formatHTML(input, isMinify);
          break;
        case 'python':
          result = formatPython(input, isMinify);
          break;
        case 'sql':
          result = formatSQLCode(input, isMinify);
          break;
        case 'xml':
          result = formatXMLCode(input, isMinify);
          break;
        default:
          throw new Error('Unsupported language');
      }

      setOutput(result);
      toast.success(`${formatType.toUpperCase()} code ${isMinify ? 'minified' : 'formatted'} successfully!`);
    } catch (error: any) {
      toast.error(error.message || "An error occurred while formatting your code.");
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(output);
      toast.success("Output copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy to clipboard.");
    }
  };

  const handleTabChange = (value: string) => {
    setFormatType(value);
    setInput('');
    setOutput('');
    router.push(`/code-formatter?type=${value}`);
  };

  return (
    <div className="w-full space-y-4">
      <div className="flex justify-between items-center">
        <Tabs defaultValue={formatType} className="w-full" onValueChange={handleTabChange}>
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="javascript" className="flex items-center gap-1 text-xs">
              <Code className="h-3 w-3" />
              JS
            </TabsTrigger>
            <TabsTrigger value="json" className="flex items-center gap-1 text-xs">
              <FileJson className="h-3 w-3" />
              JSON
            </TabsTrigger>
            <TabsTrigger value="css" className="flex items-center gap-1 text-xs">
              <FileCode className="h-3 w-3" />
              CSS
            </TabsTrigger>
            <TabsTrigger value="html" className="flex items-center gap-1 text-xs">
              <FileText className="h-3 w-3" />
              HTML
            </TabsTrigger>
            <TabsTrigger value="python" className="flex items-center gap-1 text-xs">
              <Code className="h-3 w-3" />
              Python
            </TabsTrigger>
            <TabsTrigger value="sql" className="flex items-center gap-1 text-xs">
              <Database className="h-3 w-3" />
              SQL
            </TabsTrigger>
            <TabsTrigger value="xml" className="flex items-center gap-1 text-xs">
              <Braces className="h-3 w-3" />
              XML
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setTheme(theme === 'vs-dark' ? 'vs-light' : 'vs-dark')}
          className="ml-2"
        >
          {theme === 'vs-dark' ? (
            <Sun className="h-4 w-4" />
          ) : (
            <Moon className="h-4 w-4" />
          )}
        </Button>
      </div>

      <div className="mt-4">
        {/* Main editor grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left side - Input */}
          <div className="space-y-4">
            <div className="grid gap-2 mt-[16px]">
              <Label>{formatType.toUpperCase()} Input</Label>
              <div className="relative h-[500px] border rounded-md overflow-hidden">
                <Editor
                  height="100%"
                  language={getMonacoLanguage(formatType)}
                  value={input}
                  onChange={(value) => setInput(value || '')}
                  theme={theme}
                  options={{
                    minimap: { enabled: false },
                    fontSize: 14,
                    lineNumbers: 'on',
                    scrollBeyondLastLine: false,
                    automaticLayout: true,
                    scrollbar: {
                      vertical: 'visible',
                      horizontal: 'visible',
                      verticalScrollbarSize: 14,
                      horizontalScrollbarSize: 14,
                      alwaysConsumeMouseWheel: false
                    },
                    wordWrap: 'on',
                    wrappingIndent: 'indent',
                    mouseWheelZoom: true,
                    quickSuggestions: true,
                    formatOnPaste: true,
                    formatOnType: true,
                    bracketPairColorization: {
                      enabled: true
                    }
                  }}
                />
              </div>
            </div>

            <div className="flex space-x-2">
              <Button onClick={() => handleFormat('format')} className="flex-1">
                <Code className="mr-2 h-4 w-4" /> Format
              </Button>
              <Button onClick={() => handleFormat('minify')} className="flex-1" variant="outline">
                <FileCode className="mr-2 h-4 w-4" /> Minify
              </Button>
            </div>
          </div>

          {/* Right side - Output */}
          <div className="space-y-4">
            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <Label>Output</Label>
                <Button variant="outline" size="sm" onClick={copyToClipboard}>
                  <Copy className="mr-2 h-4 w-4" />
                  Copy
                </Button>
              </div>
              <div className="relative h-[500px] border rounded-md overflow-hidden">
                <Editor
                  height="100%"
                  language={getMonacoLanguage(formatType)}
                  value={output}
                  theme={theme}
                  options={{
                    readOnly: true,
                    minimap: { enabled: false },
                    fontSize: 14,
                    lineNumbers: 'on',
                    scrollBeyondLastLine: false,
                    automaticLayout: true,
                    scrollbar: {
                      vertical: 'visible',
                      horizontal: 'visible',
                      verticalScrollbarSize: 14,
                      horizontalScrollbarSize: 14,
                      alwaysConsumeMouseWheel: false
                    },
                    wordWrap: 'on',
                    wrappingIndent: 'indent',
                    mouseWheelZoom: true,
                    quickSuggestions: true,
                    formatOnPaste: true,
                    formatOnType: true,
                    bracketPairColorization: {
                      enabled: true
                    }
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Analytics section below the editors */}
        <div className="mt-6">
          <CodeAnalytics
            code={input}
            language={getMonacoLanguage(formatType)}
          />
        </div>
      </div>
    </div>
  );
}
