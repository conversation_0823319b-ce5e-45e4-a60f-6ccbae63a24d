'use client';

import React, { useState } from 'react';
import { ThumbsUp, ThumbsDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { submitFeedback } from '@/lib/feedback';
import { toast } from 'sonner';

interface FeedbackButtonsProps {
  toolName: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

export function FeedbackButtons({
  toolName,
  className = '',
  size = 'sm',
  showLabel = true
}: FeedbackButtonsProps) {
  const [selectedFeedback, setSelectedFeedback] = useState<'positive' | 'negative' | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFeedback = async (feedbackType: 'positive' | 'negative') => {
    if (isSubmitting || selectedFeedback) return;

    setIsSubmitting(true);
    try {
      await submitFeedback(toolName, feedbackType);
      setSelectedFeedback(feedbackType);

      toast.success(
        feedbackType === 'positive'
          ? 'Thanks for the positive feedback! 👍'
          : 'Thanks for the feedback! We\'ll work on improvements. 👎'
      );
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const buttonSize = size === 'sm' ? 'h-8 w-8' : size === 'md' ? 'h-10 w-10' : 'h-12 w-12';
  const iconSize = size === 'sm' ? 'h-4 w-4' : size === 'md' ? 'h-5 w-5' : 'h-6 w-6';

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showLabel && (
        <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
          Was this helpful?
        </span>
      )}

      <div className="flex items-center gap-3">
        <Button
          variant="outline"
          size="sm"
          className={`${buttonSize} p-0 transition-all duration-300 transform hover:scale-105 shadow-sm ${
            selectedFeedback === 'positive'
              ? 'bg-gradient-to-r from-green-500 to-emerald-500 border-green-400 text-white shadow-lg shadow-green-200 dark:shadow-green-900/30 scale-105'
              : 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-600 hover:from-green-100 hover:to-emerald-100 hover:border-green-300 hover:text-green-700 hover:shadow-md dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-700/50 dark:text-green-400 dark:hover:from-green-800/30 dark:hover:to-emerald-800/30'
          } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => handleFeedback('positive')}
          disabled={isSubmitting || selectedFeedback !== null}
          title="This was helpful"
        >
          <ThumbsUp className={`${iconSize} ${
            selectedFeedback === 'positive' ? 'fill-current drop-shadow-sm' : ''
          }`} />
        </Button>

        <Button
          variant="outline"
          size="sm"
          className={`${buttonSize} p-0 transition-all duration-300 transform hover:scale-105 shadow-sm ${
            selectedFeedback === 'negative'
              ? 'bg-gradient-to-r from-red-500 to-rose-500 border-red-400 text-white shadow-lg shadow-red-200 dark:shadow-red-900/30 scale-105'
              : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-600 hover:from-red-100 hover:to-rose-100 hover:border-red-300 hover:text-red-700 hover:shadow-md dark:from-red-900/20 dark:to-rose-900/20 dark:border-red-700/50 dark:text-red-400 dark:hover:from-red-800/30 dark:hover:to-rose-800/30'
          } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => handleFeedback('negative')}
          disabled={isSubmitting || selectedFeedback !== null}
          title="This was not helpful"
        >
          <ThumbsDown className={`${iconSize} ${
            selectedFeedback === 'negative' ? 'fill-current drop-shadow-sm' : ''
          }`} />
        </Button>
      </div>

      {selectedFeedback && (
        <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
          Thank you!
        </span>
      )}
    </div>
  );
}
