'use client';

import { Recipe, Ingredient } from '@/types/recipe';
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Users, ChefHat } from 'lucide-react';
import { useState, useEffect } from 'react';
import Image from 'next/image';

interface RecipeCardProps {
  recipe: Recipe;
}

export function RecipeCard({ recipe }: RecipeCardProps) {
  const [imgSrc, setImgSrc] = useState<string | null>(recipe.imageUrl || null);

  const renderIngredient = (ingredient: string | Ingredient) => {
    if (typeof ingredient === 'string') {
      return ingredient;
    }
    return `${ingredient.amount} ${ingredient.unit} ${ingredient.name}${ingredient.notes ? ` (${ingredient.notes})` : ''}`;
  };

  return (
    <Card className="overflow-hidden">
      {imgSrc && (
        <div className="relative h-48 w-full">
          <Image
            src={imgSrc}
            alt={recipe.title}
            fill
            className="object-cover"
            onError={() => {
              setImgSrc(null);
            }}
          />
        </div>
      )}
      
      <div className="p-6 space-y-4">
        <div>
          <h3 className="text-2xl font-semibold">{recipe.title}</h3>
          <p className="text-muted-foreground">{recipe.description}</p>
        </div>

        <div className="flex gap-4">
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span>{recipe.cookingTime} mins</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            <span>{recipe.servings} servings</span>
          </div>
          <div className="flex items-center gap-1">
            <ChefHat className="h-4 w-4" />
            <span className="capitalize">{recipe.difficulty}</span>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Ingredients</h4>
          <ul className="list-disc list-inside space-y-1">
            {recipe.ingredients.map((ingredient, index) => (
              <li key={index} className="text-gray-600">
                {renderIngredient(ingredient)}
              </li>
            ))}
          </ul>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Instructions</h4>
          <ol className="list-decimal list-inside space-y-2">
            {recipe.instructions.steps.map((step: string, index: number) => (
              <li key={index} className="text-gray-600">{step}</li>
            ))}
          </ol>
        </div>

        {recipe.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {recipe.tags.map((tag, index) => (
              <Badge key={index} variant="secondary">{tag}</Badge>
            ))}
          </div>
        )}
      </div>
    </Card>
  );
}