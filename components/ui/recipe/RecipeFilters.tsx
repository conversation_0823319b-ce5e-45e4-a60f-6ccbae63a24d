'use client';

import { RecipeFilters as RecipeFiltersType } from '@/types/recipe';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

interface RecipeFiltersProps {
  filters: RecipeFiltersType;
  onFiltersChange: (filters: RecipeFiltersType) => void;
}

const cuisines = [
  'Any',
  'Italian',
  'Chinese',
  'Indian',
  'Mexican',
  'Japanese',
  'Thai',
  'Mediterranean',
  'American',
  'French'
];

const dietaryOptions = [
  'Vegetarian',
  'Vegan',
  'Gluten-free',
  'Dairy-free',
  'Low-carb',
  'Keto',
  'Paleo'
];

export function RecipeFilters({ filters, onFiltersChange }: RecipeFiltersProps) {
  const updateFilters = (key: keyof RecipeFiltersType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Cuisine</Label>
        <Select
          value={filters.cuisine || 'Any'}
          onValueChange={(value) => updateFilters('cuisine', value === 'Any' ? undefined : value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select cuisine" />
          </SelectTrigger>
          <SelectContent>
            {cuisines.map((cuisine) => (
              <SelectItem key={cuisine} value={cuisine}>
                {cuisine}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Maximum Cooking Time (minutes)</Label>
        <Slider
          value={[filters.maxCookingTime || 60]}
          onValueChange={([value]) => updateFilters('maxCookingTime', value)}
          min={15}
          max={180}
          step={15}
          className="w-full"
        />
        <div className="text-sm text-muted-foreground">
          {filters.maxCookingTime || 60} minutes
        </div>
      </div>

      <div className="space-y-2">
        <Label>Dietary Restrictions</Label>
        <div className="flex flex-wrap gap-2">
          {dietaryOptions.map((option) => {
            const isSelected = filters.dietary?.includes(option);
            return (
              <Badge
                key={option}
                variant={isSelected ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => {
                  const dietary = filters.dietary || [];
                  updateFilters(
                    'dietary',
                    isSelected
                      ? dietary.filter((d) => d !== option)
                      : [...dietary, option]
                  );
                }}
              >
                {option}
              </Badge>
            );
          })}
        </div>
      </div>
    </div>
  );
}