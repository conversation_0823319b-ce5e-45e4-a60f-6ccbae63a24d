'use client';

import { useState, Dispatch, SetStateAction } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X } from 'lucide-react';

interface IngredientInputProps {
  ingredients: string[];
  setIngredients: Dispatch<SetStateAction<string[]>>;
}

export function IngredientInput({ ingredients, setIngredients }: IngredientInputProps) {
  const [currentIngredient, setCurrentIngredient] = useState('');

  const addIngredient = () => {
    if (currentIngredient.trim()) {
      setIngredients([...ingredients, currentIngredient.trim()]);
      setCurrentIngredient('');
    }
  };

  const removeIngredient = (index: number) => {
    setIngredients(ingredients.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <Input
          value={currentIngredient}
          onChange={(e) => setCurrentIngredient(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && addIngredient()}
          placeholder="Enter an ingredient"
          className="flex-1"
        />
        <Button onClick={addIngredient}>Add</Button>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {ingredients.map((ingredient, index) => (
          <div
            key={index}
            className="flex items-center gap-1 bg-secondary px-3 py-1 rounded-full"
          >
            <span>{ingredient}</span>
            <button
              onClick={() => removeIngredient(index)}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}