import React, { useState } from 'react';

const Converter = () => {
  const [text, setText] = useState("");
  const [result, setResult] = useState("");
  const [analysis, setAnalysis] = useState<{
    characters: number;
    words: number;
    sentences: number;
    paragraphs: number;
  } | null>(null);

  const transformText = (style: string) => {
    switch (style) {
      case "italic":
        return text.split('').map(char => {
          const italicChar = String.fromCharCode(char.charCodeAt(0) + 0x1D434 - 0x41);
          return italicChar;
        }).join('');
      case "mini":
        return text.toLowerCase().split('').map(char => {
          if (char >= 'a' && char <= 'z') {
            return String.fromCharCode(char.charCodeAt(0) + 0x1D5EE - 0x61);
          }
          return char;
        }).join('');
      case "bubble":
        return text.toLowerCase().split('').map(char => {
          if (char >= 'a' && char <= 'z') {
            return String.fromCharCode(char.charCodeAt(0) + 0x24D0 - 0x61);
          }
          return char;
        }).join('');
      case "strikethrough":
        return text.split('').map(char => char + '\u0336').join('');
      case "underline":
        return text.split('').map(char => char + '\u0332').join('');
      default:
        return text;
    }
  };

  const handleConvert = (style: string) => {
    const convertedText = transformText(style);
    setResult(convertedText);
  };

  const handleAnalyze = () => {
    const analysis = {
      characters: text.length,
      words: text.trim().split(/\s+/).filter(word => word.length > 0).length,
      sentences: text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length,
      paragraphs: text.split(/\n\s*\n/).filter(para => para.trim().length > 0).length
    };
    setAnalysis(analysis);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-center mb-8">Free Text Converter</h1>
      
      <div className="max-w-3xl mx-auto">
        <textarea
          className="w-full h-48 p-4 border rounded-lg mb-4"
          placeholder="Enter your text here..."
          value={text}
          onChange={(e) => setText(e.target.value)}
        />
        
        <div className="flex gap-4 mb-6">
          <button
            onClick={() => handleConvert('italic')}
            className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600"
          >
            Italic
          </button>
          <button
            onClick={() => handleConvert('mini')}
            className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600"
          >
            Mini
          </button>
          <button
            onClick={() => handleConvert('bubble')}
            className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600"
          >
            Bubble
          </button>
          <button
            onClick={() => handleConvert('strikethrough')}
            className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600"
          >
            Strikethrough
          </button>
          <button
            onClick={() => handleConvert('underline')}
            className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600"
          >
            Underline
          </button>
        </div>

        {result && (
          <div className="bg-gray-100 p-4 rounded-lg mb-6">
            <h2 className="font-bold mb-2">Converted Text:</h2>
            <p>{result}</p>
          </div>
        )}

        {analysis && (
          <div className="bg-gray-100 p-4 rounded-lg">
            <h2 className="font-bold mb-2">Text Analysis:</h2>
            <ul className="space-y-2">
              <li>Characters: {analysis.characters}</li>
              <li>Words: {analysis.words}</li>
              <li>Sentences: {analysis.sentences}</li>
              <li>Paragraphs: {analysis.paragraphs}</li>
            </ul>
          </div>
        )}
      </div>

      <footer className="text-center mt-12 text-gray-600">
        <p>&copy; {new Date().getFullYear()} Free Text Converter by Pixel & Code</p>
      </footer>
    </div>
  );
};

export default Converter;