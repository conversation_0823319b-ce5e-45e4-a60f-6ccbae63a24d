"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker, DayContent } from "react-day-picker"
import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export type Holiday = {
  name: string
  date: Date
}

export type CalendarProps = React.ComponentProps<typeof DayPicker> & {
  showHolidays?: boolean
  holidays?: Holiday[]
}

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  showHolidays = true,
  holidays = [],
  ...props
}: CalendarProps) {
  const isHoliday = React.useCallback((date: Date) => {
    return holidays.some(holiday => 
      holiday.date.getDate() === date.getDate() &&
      holiday.date.getMonth() === date.getMonth()
    )
  }, [holidays])

  const getHolidayName = React.useCallback((date: Date) => {
    const holiday = holidays.find(holiday => 
      holiday.date.getDate() === date.getDate() &&
      holiday.date.getMonth() === date.getMonth()
    )
    return holiday?.name
  }, [holidays])

  const modifiers = React.useMemo(() => ({
    holiday: (date: Date) => showHolidays && isHoliday(date)
  }), [showHolidays, isHoliday])

  const modifiersStyles = {
    holiday: { 
      color: '#ef4444',
      fontWeight: 'bold'
    }
  }

  return (
    <DayPicker
      className={cn("p-3", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "text-sm font-medium",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          buttonVariants({ variant: "outline" }),
          "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex",
        head_cell:
          "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
        row: "flex w-full mt-2",
        cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "h-9 w-9 p-0 font-normal aria-selected:opacity-100"
        ),
        day_range_end: "day-range-end",
        day_selected:
          "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
        day_today: "bg-accent text-accent-foreground",
        day_outside:
          "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
        day_disabled: "text-muted-foreground opacity-50",
        day_range_middle:
          "aria-selected:bg-accent aria-selected:text-accent-foreground",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: ({ ...props }) => <ChevronLeft className="h-4 w-4" />,
        IconRight: ({ ...props }) => <ChevronRight className="h-4 w-4" />,
        DayContent: ({ date, ...contentProps }) => {
          const isToday = date.toDateString() === new Date().toDateString()
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div 
                    className={cn(
                      "w-full h-full flex items-center justify-center",
                      isToday && "bg-accent text-accent-foreground"
                    )}
                  >
                    <time dateTime={date.toISOString()}>
                      {date.getDate()}
                    </time>
                  </div>
                </TooltipTrigger>
                {isHoliday(date) && (
                  <TooltipContent>
                    <p className="text-sm">{getHolidayName(date)}</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          )
        }
      }}
      showOutsideDays={showOutsideDays}
      modifiers={modifiers}
      modifiersStyles={modifiersStyles}
      {...props}
    />
  )
}

Calendar.displayName = "Calendar"

export { Calendar }
