'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { QRCodeSVG } from 'qrcode.react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>hart, <PERSON>, <PERSON>, Pie, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell } from 'recharts';
import { format } from 'date-fns';
import { QrCode, Calendar, Scan, Eye, Palette, FileText, MapPin, Map, Wifi, Lock, ChartBar, Smartphone, Link2, Clock, History, Monitor, Paintbrush } from 'lucide-react';

interface ScanData {
  date: string;
  scans: number;
  device?: string;
  location?: string;
}

interface QRCodeScanStats {
  scans: ScanData[];
  deviceStats: {
    mobile: number;
    desktop: number;
  };
  hourlyStats: {
    [key: string]: number;
  };
  lastDeviceType: string;
  lastUserAgent: string;
  lastScanTime: string | null;
  totalScans?: number;
}

interface QRCode {
  name: string;
  id: string;
  url: string;
  content: string;
  title: string;
  type: string;
  scanCount?: number;
  description?: string;
  backgroundColor?: string;
  foregroundColor?: string;
  frameColor?: string;
  location?: string;
  isPrivate: boolean;
  createdAt: Date;
  size?: string;
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  labelFont?: string;
}

interface QRCodeDetailsDialogProps {
  qrCode: QRCode;
  scanData: QRCodeScanStats;
  onClose: () => void;
  loading?: boolean;
}

interface CustomizedLabelProps {
  percent: number;
  x: number;
  y: number;
  fill: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const renderCustomizedLabel = ({ percent, x, y, fill }: CustomizedLabelProps) => {
  return (
    <text x={x} y={y} fill={fill} textAnchor={x > 50 ? 'start' : 'end'} dominantBaseline="central">
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

export function QRCodeDetailsDialog({ qrCode, scanData, onClose, loading = false }: QRCodeDetailsDialogProps) {
  // Process device data from scans
  const deviceChartData = scanData?.deviceStats ? [
    { name: 'Mobile', value: scanData.deviceStats.mobile || 0 },
    { name: 'Desktop', value: scanData.deviceStats.desktop || 0 }
  ] : [];

  // Process hourly scan data with proper type handling
  const hourlyChartData = scanData?.hourlyStats ? 
    Object.entries(scanData.hourlyStats).map(([hour, count]) => ({
      hour: parseInt(hour),
      scans: typeof count === 'number' ? count : 0
    })).sort((a, b) => a.hour - b.hour) : [];

  // Format the last scan time with proper type checking
  const formatLastScan = () => {
    if (!scanData?.lastScanTime) return 'Never';
    try {
      const date = new Date(scanData.lastScanTime);
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      return format(date, 'PPp');
    } catch (error) {
      console.error('Error formatting last scan time:', error);
      return 'Invalid Date';
    }
  };

  // Process device data from scans
  const deviceData = (scanData?.scans || []).reduce((acc: { [key: string]: number }, scan) => {
    const device = scan.device || 'Unknown';
    acc[device] = (acc[device] || 0) + scan.scans;
    return acc;
  }, {});

  const deviceChartDatap = Object.entries(deviceData).map(([name, value]) => ({
    name,
    value
  }));

  // Process location data from scans
  const locationData = (scanData?.scans || []).reduce((acc: { [key: string]: number }, scan) => {
    const location = scan.location || 'Unknown';
    acc[location] = (acc[location] || 0) + scan.scans;
    return acc;
  }, {});

  const locationChartData = Object.entries(locationData).map(([name, value]) => ({
    name,
    value
  }));

  // Safely parse QR code content
  const parseContent = (content: string | undefined) => {
    if (!content) return null;
    try {
      return JSON.parse(content);
    } catch {
      return null;
    }
  };

  const renderDeviceStats = () => {
    if (!scanData?.deviceStats) return null;
    
    const data = [
      { name: 'Mobile', value: scanData.deviceStats.mobile },
      { name: 'Desktop', value: scanData.deviceStats.desktop }
    ];

    return (
      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">Device Distribution</h3>
        <ResponsiveContainer width="100%" height={200}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomizedLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    );
  };

  const renderHourlyStats = () => {
    if (!scanData?.hourlyStats) return null;

    const data = Object.entries(scanData.hourlyStats).map(([hour, count]) => ({
      hour: `${hour}:00`,
      scans: count
    }));

    return (
      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">Hourly Scan Activity</h3>
        <ResponsiveContainer width="100%" height={200}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="hour" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="scans" fill="#8884d8" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  };

  const renderLastScanInfo = () => {
    if (!scanData?.lastScanTime) return null;

    return (
      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">Last Scan Details</h3>
        <div className="space-y-2">
          <p>Time: {formatLastScan()}</p>
          <p>Device: {scanData.lastDeviceType || 'Unknown'}</p>
          <p>User Agent: {scanData.lastUserAgent || 'Not available'}</p>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={true} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-purple-500">
            {qrCode.title || qrCode.name || 'Untitled QR Code'}
          </DialogTitle>
          
          {/* Basic Info Bar */}
          <div className="mt-6 grid grid-cols-3 gap-6">
            <div className="p-4 rounded-xl bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 dark:border-gray-700">
              <div className="flex flex-col items-center">
                <QrCode className="w-6 h-6 text-blue-500 mb-2" />
                <p className="text-sm text-gray-500 dark:text-gray-400">Content Type</p>
                <p className="font-semibold mt-1 text-gray-900 dark:text-gray-100">{qrCode.type || 'URL'}</p>
              </div>
            </div>
            <div className="p-4 rounded-xl bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 dark:border-gray-700">
              <div className="flex flex-col items-center">
                <Calendar className="w-6 h-6 text-purple-500 mb-2" />
                <p className="text-sm text-gray-500 dark:text-gray-400">Created</p>
                <p className="font-semibold mt-1 text-gray-900 dark:text-gray-100">
                  {qrCode.createdAt ? format(qrCode.createdAt, 'PPp') : 'Unknown'}
                </p>
              </div>
            </div>
            <div className="p-4 rounded-xl bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 dark:border-gray-700">
              <div className="flex flex-col items-center">
                <Scan className="w-6 h-6 text-green-500 mb-2" />
                <p className="text-sm text-gray-500 dark:text-gray-400">Total Scans</p>
                <p className="font-semibold mt-1 text-gray-900 dark:text-gray-100">
                  {loading ? (
                    <div className="animate-pulse w-8 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  ) : (
                    qrCode.scanCount || scanData?.totalScans || 0
                  )}
                </p>
              </div>
            </div>
          </div>
        </DialogHeader>

        {/* Main Content */}
        {loading ? (
          <div className="flex items-center justify-center h-[400px]">
            <div className="flex flex-col items-center space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              <p className="text-gray-500 dark:text-gray-400">Loading scan data...</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            {/* QR Code */}
            <div className="mt-8">
              <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100 flex items-center">
                <QrCode className="w-5 h-5 mr-2 text-green-500" />
                QR Code
              </h3>
              <div className="space-y-6">
                {/* QR Code Preview */}
                <div className="flex flex-col items-center space-y-4">
                  <div 
                    className="p-6 bg-white rounded-lg shadow-sm"
                    style={{ 
                      backgroundColor: qrCode.backgroundColor || '#FFFFFF',
                      border: qrCode.frameColor ? `2px solid ${qrCode.frameColor}` : 'none'
                    }}
                  >
                    <QRCodeSVG
                      value={qrCode.content || qrCode.url || ''}
                      size={parseInt(qrCode.size as string) || 256}
                      level={qrCode.errorCorrectionLevel || 'M'}
                      fgColor={qrCode.foregroundColor || '#000000'}
                      bgColor={qrCode.backgroundColor || '#FFFFFF'}
                    />
                  </div>
                  
                  {/* Content Details */}
                  <div className="w-full grid grid-cols-2 gap-4 mt-4">
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Content Type</p>
                      <p className="font-medium text-gray-900 dark:text-gray-100 capitalize">
                        {qrCode.type || 'URL'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Content</p>
                      <p className="font-medium text-gray-900 dark:text-gray-100 break-all">
                        {qrCode.content || qrCode.url || 'No content available'}
                      </p>
                    </div>
                    {qrCode.title && (
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Title</p>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{qrCode.title}</p>
                      </div>
                    )}
                    {qrCode.description && (
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Description</p>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{qrCode.description}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Style Properties */}
            <div className="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold mb-6 text-gray-900 dark:text-gray-100 flex items-center">
                <Paintbrush className="w-5 h-5 mr-2 text-purple-500" />
                Style Properties
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Size</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">{qrCode.size || '256px'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Error Level</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">{qrCode.errorCorrectionLevel || 'M'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Foreground Color</p>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded-full border border-gray-200" 
                      style={{ backgroundColor: qrCode.foregroundColor || '#000000' }}
                    />
                    <p className="font-medium text-gray-900 dark:text-gray-100">
                      {qrCode.foregroundColor || '#000000'}
                    </p>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Background Color</p>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded-full border border-gray-200" 
                      style={{ backgroundColor: qrCode.backgroundColor || '#FFFFFF' }}
                    />
                    <p className="font-medium text-gray-900 dark:text-gray-100">
                      {qrCode.backgroundColor || '#FFFFFF'}
                    </p>
                  </div>
                </div>
                {qrCode.frameColor && (
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Frame Color</p>
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-4 h-4 rounded-full border border-gray-200" 
                        style={{ backgroundColor: qrCode.frameColor }}
                      />
                      <p className="font-medium text-gray-900 dark:text-gray-100">{qrCode.frameColor}</p>
                    </div>
                  </div>
                )}
                {qrCode.labelFont && (
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Label Font</p>
                    <p className="font-medium text-gray-900 dark:text-gray-100">{qrCode.labelFont}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Scan Activity Chart */}
            <div className="md:col-span-2 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold mb-6 text-gray-900 dark:text-gray-100 flex items-center">
                <ChartBar className="w-5 h-5 mr-2 text-blue-500" />
                Scan Activity
              </h3>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={scanData.scans}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="scans" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Device Distribution */}
            <div className="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold mb-6 text-gray-900 dark:text-gray-100 flex items-center">
                <Smartphone className="w-5 h-5 mr-2 text-purple-500" />
                Device Distribution
              </h3>
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-900/50">
                  <div className="flex items-center">
                    <Smartphone className="w-4 h-4 mr-2 text-blue-500" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">Mobile</p>
                  </div>
                  <p className="font-medium text-gray-900 dark:text-gray-100 mt-1">
                    {scanData.deviceStats.mobile} scans
                    ({scanData.deviceStats ? 
                      Math.round((scanData.deviceStats.mobile / (scanData.deviceStats.mobile + scanData.deviceStats.desktop)) * 100) : 0}%)
                  </p>
                </div>
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-900/50">
                  <div className="flex items-center">
                    <Monitor className="w-4 h-4 mr-2 text-green-500" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">Desktop</p>
                  </div>
                  <p className="font-medium text-gray-900 dark:text-gray-100 mt-1">
                    {scanData.deviceStats.desktop} scans
                    ({scanData.deviceStats ? 
                      Math.round((scanData.deviceStats.desktop / (scanData.deviceStats.mobile + scanData.deviceStats.desktop)) * 100) : 0}%)
                  </p>
                </div>
              </div>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={deviceChartData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    fill="#8884d8"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {deviceChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={index === 0 ? '#4F46E5' : '#10B981'} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Hourly Distribution */}
            <div className="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold mb-6 text-gray-900 dark:text-gray-100 flex items-center">
                <Clock className="w-5 h-5 mr-2 text-green-500" />
                Hourly Distribution
              </h3>
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={hourlyChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="hour" 
                    tickFormatter={(hour) => `${hour}:00`}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(hour) => `${hour}:00`}
                    formatter={(value) => [`${value} scans`, 'Scans']}
                  />
                  <Legend />
                  <Bar 
                    name="Scans per Hour" 
                    dataKey="scans" 
                    fill="#82ca9d" 
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Last Scan Details */}
            <div className="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold mb-6 text-gray-900 dark:text-gray-100 flex items-center">
                <History className="w-5 h-5 mr-2 text-blue-500" />
                Last Scan Details
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500">Device Type</div>
                  <div className="font-medium flex items-center mt-1">
                    {scanData.lastDeviceType === 'mobile' ? 
                      <Smartphone className="w-4 h-4 mr-2" /> : 
                      <Monitor className="w-4 h-4 mr-2" />
                    }
                    {scanData.lastDeviceType || 'Unknown'}
                  </div>
                </div>
                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500">Last Scanned</div>
                  <div className="font-medium mt-1">{formatLastScan()}</div>
                </div>
                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg col-span-2">
                  <div className="text-sm text-gray-500">User Agent</div>
                  <div className="font-medium mt-1 truncate">
                    {scanData.lastUserAgent || 'Unknown'}
                  </div>
                </div>
              </div>
            </div>

            {/* Geographic Distribution */}
            <div className="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold mb-6 text-gray-900 dark:text-gray-100 flex items-center">
                <MapPin className="w-5 h-5 mr-2 text-green-500" />
                Geographic Distribution
              </h3>
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={locationChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {renderDeviceStats()}
            {renderHourlyStats()}
            {renderLastScanInfo()}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}