'use client';

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Copy, Download, Wand2, ArrowRight, Sparkles } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";

export function ReplaceSpaces() {
  const [text, setText] = useState("");
  const [replacement, setReplacement] = useState("_");
  const [result, setResult] = useState("");

  // Update result whenever text or replacement changes
  useEffect(() => {
    if (text === "") {
      setResult("");
    } else {
      setResult(text.replace(/\s/g, replacement));
    }
  }, [text, replacement]);

  const handleTextChange = (value: string) => {
    setText(value);
  };

  const handleReplacementChange = (value: string) => {
    setReplacement(value);
  };

  const countStats = (text: string) => {
    const trimmedText = text.trim();
    return {
      characters: text.length,
      words: trimmedText === "" ? 0 : trimmedText.split(/\s+/).length,
      sentences: trimmedText === "" ? 0 : trimmedText.split(/[.!?]+/).filter(Boolean).length,
      paragraphs: trimmedText === "" ? 0 : trimmedText.split(/\n\s*\n/).filter(Boolean).length,
      lines: trimmedText === "" ? 0 : trimmedText.split(/\n/).length,
    };
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(result);
      toast.success("Text copied to clipboard successfully");
    } catch (err) {
      toast.error("Failed to copy text to clipboard");
    }
  };

  const stats = countStats(text);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Main Content */}
        <div className="grid gap-8 md:grid-cols-2">
          {/* Input Section */}
          <Card className="p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="input" className="text-lg font-semibold">Input Text</Label>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="replacement" className="text-sm">Replace with:</Label>
                  <Input
                    id="replacement"
                    value={replacement}
                    onChange={(e) => handleReplacementChange(e.target.value)}
                    className="w-20 text-center"
                    maxLength={5}
                  />
                </div>
              </div>
              <Textarea
                id="input"
                placeholder="Enter your text here..."
                className="min-h-[200px] resize-none"
                value={text}
                onChange={(e) => handleTextChange(e.target.value)}
              />
            </div>
          </Card>

          {/* Output Section */}
          <Card className="p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-lg font-semibold">Result</Label>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={copyToClipboard}>
                    <Copy className="h-4 w-4 mr-1" />
                    Copy
                  </Button>
                </div>
              </div>
              <div className="relative">
                <Textarea
                  readOnly
                  value={result}
                  className="min-h-[200px] resize-none bg-slate-50 dark:bg-gray-800"
                />
                {result && <Sparkles className="absolute top-2 right-2 h-4 w-4 text-blue-500 animate-pulse" />}
              </div>
            </div>
          </Card>
        </div>

        {/* Stats Section */}
        <Card className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {Object.entries(stats).map(([key, value]) => (
              <div key={key} className="text-center">
                <Badge variant="secondary" className="mb-1 capitalize">
                  {key}
                </Badge>
                <p className="text-2xl font-bold text-[#1C6997]">{value}</p>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}