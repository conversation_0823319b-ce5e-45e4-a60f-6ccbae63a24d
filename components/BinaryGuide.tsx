import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Binary, Calculator, Plus, Minus, Divide, X, RotateCw, Binary as BinaryIcon } from "lucide-react";

export default function BinaryGuide() {
  const sections = [
    {
      title: "Binary Number System",
      icon: BinaryIcon,
      description: "Understanding the basics of binary numbers",
      content: [
        "Binary is a base-2 number system using only 0s and 1s",
        "Each position represents a power of 2 (1, 2, 4, 8, 16, etc.)",
        "Used as the foundation of digital computing",
        "Example: 1010₂ = 8 + 0 + 2 + 0 = 10₁₀"
      ]
    },
    {
      title: "Binary Operations",
      icon: Calculator,
      description: "Basic arithmetic operations in binary",
      content: [
        "Addition (+): Similar to decimal addition but carries over at 2",
        "Subtraction (-): Uses borrowing system with base-2",
        "Multiplication (×): Repeated addition in binary",
        "Division (÷): Repeated subtraction in binary"
      ]
    },
    {
      title: "Binary Conversion",
      icon: RotateCw,
      description: "Converting between number systems",
      content: [
        "Decimal to Binary: Divide by 2 and track remainders",
        "Binary to Decimal: Multiply each digit by its position value",
        "Hexadecimal conversion: Group binary digits in sets of 4",
        "Octal conversion: Group binary digits in sets of 3"
      ]
    }
  ];

  const examples = [
    {
      operation: "Addition",
      icon: Plus,
      example: "1101₂ + 1010₂ = 10111₂",
      explanation: "13₁₀ + 10₁₀ = 23₁₀"
    },
    {
      operation: "Subtraction",
      icon: Minus,
      example: "1100₂ - 0101₂ = 0111₂",
      explanation: "12₁₀ - 5₁₀ = 7₁₀"
    },
    {
      operation: "Multiplication",
      icon: X,
      example: "101₂ × 11₂ = 1111₂",
      explanation: "5₁₀ × 3₁₀ = 15₁₀"
    },
    {
      operation: "Division",
      icon: Divide,
      example: "1100₂ ÷ 11₂ = 100₂",
      explanation: "12₁₀ ÷ 3₁₀ = 4₁₀"
    }
  ];

  return (
    <div className="py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Binary Calculator Guide
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mt-4 text-lg">
            Master binary numbers and operations with our comprehensive guide
          </p>
        </div>

        {/* Main Sections */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {sections.map((section) => {
            const Icon = section.icon;
            return (
              <Card key={section.title} className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className="flex items-center space-x-2">
                    <Icon className="h-6 w-6 text-blue-600" />
                    <CardTitle>{section.title}</CardTitle>
                  </div>
                  <CardDescription>{section.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {section.content.map((item, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-blue-600 font-bold">•</span>
                        <span className="text-gray-600 dark:text-gray-300">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Examples Section */}
        <div className="bg-slate-50 dark:bg-gray-800 rounded-lg p-8">
          <h3 className="text-2xl font-bold text-center mb-8">Example Operations</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {examples.map((example) => {
              const Icon = example.icon;
              return (
                <Card key={example.operation} className="hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <Icon className="h-5 w-5 text-blue-600" />
                      <CardTitle className="text-lg">{example.operation}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="font-mono text-sm">{example.example}</div>
                      <div className="text-gray-600 dark:text-gray-300 text-sm">
                        {example.explanation}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}