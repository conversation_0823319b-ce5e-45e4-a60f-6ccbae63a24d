'use client'

import { useState } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { Copy, RefreshCw } from 'lucide-react'

export function UUIDGenerator() {
  const [uuid, setUuid] = useState<string>('')
  const [quantity, setQuantity] = useState<number>(1)
  const [copied, setCopied] = useState(false)

  const generateUUID = () => {
    if (quantity <= 0 || quantity > 100) {
      toast.error('Please enter a number between 1 and 100')
      return
    }

    const uuids = Array(quantity)
      .fill(null)
      .map(() => uuidv4())
      .join('\n')
    setUuid(uuids)
    setCopied(false)
  }

  const copyToClipboard = () => {
    if (!uuid) return
    navigator.clipboard.writeText(uuid)
    toast.success('UUID has been copied to your clipboard')
    setCopied(true)
  }

  return (
    <div className="container mx-auto p-4">
      <Card className="p-6 space-y-6">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">UUID Generator</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Generate secure, random UUIDs (Universally Unique Identifiers) in version 4 format.
          </p>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="quantity" className="text-sm font-medium">
              Number of UUIDs (1-100)
            </Label>
            <div className="flex gap-2">
              <Input
                id="quantity"
                type="number"
                min="1"
                max="100"
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                className="w-32"
              />
              <Button onClick={generateUUID} className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Generate
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="result" className="text-sm font-medium">
              Generated UUID(s)
            </Label>
            <div className="flex gap-2">
              <textarea
                id="result"
                value={uuid}
                readOnly
                className="w-full h-32 p-2 font-mono text-sm bg-gray-50 dark:bg-gray-900 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Generated UUIDs will appear here..."
              />
            </div>
            <Button
              variant="outline"
              onClick={copyToClipboard}
              disabled={!uuid}
              className="flex items-center gap-2"
            >
              <Copy className="h-4 w-4" />
              {copied ? 'Copied!' : 'Copy to Clipboard'}
            </Button>
          </div>
        </div>

        <div className="text-sm text-gray-500 dark:text-gray-400">
          <p className="font-medium mb-2">Tips:</p>
          <ul className="list-disc list-inside space-y-1">
            <li>UUIDs are guaranteed to be unique across space and time</li>
            <li>Version 4 UUIDs are generated using random or pseudo-random numbers</li>
            <li>Commonly used in databases, distributed systems, and web applications</li>
          </ul>
        </div>
      </Card>
    </div>
  )
}
