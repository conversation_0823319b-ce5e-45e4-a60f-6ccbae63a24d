'use client';

import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Copy, Download, Code, Eye, RotateCcw, Maximize2, FileText, ArrowRight, Sun, Moon, Link, Unlink } from 'lucide-react';
import { toast } from 'sonner';

import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import dynamic from 'next/dynamic';

// Dynamically import Monaco Editor to avoid SSR issues
const MonacoEditor = dynamic(() => import('@monaco-editor/react'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full bg-gray-50 dark:bg-gray-900">
      <div className="text-gray-500 dark:text-gray-400">Loading editor...</div>
    </div>
  ),
});

export function MarkdownConverter() {
  const [markdownText, setMarkdownText] = useState(`# Welcome to Markdown Converter

This is **bold** text and this is *italic* text.

## Features
- Convert Markdown to HTML
- Extract plain text
- Copy and download results

[Visit our website](https://example.com)`);
  const [plainText, setPlainText] = useState('');
  const [htmlOutput, setHtmlOutput] = useState('');
  const [activeTab, setActiveTab] = useState('live-editor');
  const [editorWidth, setEditorWidth] = useState(50); // Percentage width for editor
  const [isResizing, setIsResizing] = useState(false);
  const [editorTheme, setEditorTheme] = useState('vs-dark');
  const [isScrollSynced, setIsScrollSynced] = useState(false);
  const editorRef = useRef<any>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  const isScrollingRef = useRef(false);

  // Handle mouse resize functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return;

    const container = document.querySelector('.resizable-container');
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const newWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100;

    // Constrain between 20% and 80%
    const constrainedWidth = Math.min(Math.max(newWidth, 20), 80);
    setEditorWidth(constrainedWidth);
  };

  const handleMouseUp = () => {
    setIsResizing(false);
  };

  // Add event listeners for resize
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // Store scroll event disposables
  const editorScrollDisposable = useRef<any>(null);
  const previewScrollHandler = useRef<((e: Event) => void) | null>(null);

  // Handle Monaco Editor mount to fix trackpad scrolling
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;

    // Simple trackpad scrolling fix for macOS
    const domNode = editor.getDomNode();
    if (domNode) {
      // Don't override Monaco's built-in scrolling - just ensure it's not blocked
      // Monaco Editor handles trackpad scrolling well by default with the right options
    }
  };

  // Set up or remove scroll sync based on isScrollSynced state
  useEffect(() => {
    // Wait a bit for refs to be properly set
    const timer = setTimeout(() => {
      if (!editorRef.current || !previewRef.current) return;

      // Clean up existing listeners
      if (editorScrollDisposable.current) {
        editorScrollDisposable.current.dispose();
        editorScrollDisposable.current = null;
      }

      if (previewScrollHandler.current && previewRef.current) {
        previewRef.current.removeEventListener('scroll', previewScrollHandler.current);
        previewScrollHandler.current = null;
      }

      // Only set up scroll sync if enabled
      if (isScrollSynced) {
        console.log('Setting up scroll sync...');

        // Editor scroll handler
        editorScrollDisposable.current = editorRef.current.onDidScrollChange((e: any) => {
          if (previewRef.current && !isScrollingRef.current) {
            isScrollingRef.current = true;

            const scrollTop = e.scrollTop;
            const scrollHeight = editorRef.current.getScrollHeight();
            const visibleHeight = editorRef.current.getLayoutInfo().height;

            // Calculate scroll percentage
            const maxScroll = scrollHeight - visibleHeight;
            if (maxScroll > 0) {
              const scrollPercentage = scrollTop / maxScroll;

              // Apply to preview
              const previewElement = previewRef.current;
              const previewMaxScroll = previewElement.scrollHeight - previewElement.clientHeight;
              if (previewMaxScroll > 0) {
                previewElement.scrollTop = scrollPercentage * previewMaxScroll;
              }
            }

            // Reset flag after a short delay
            setTimeout(() => {
              isScrollingRef.current = false;
            }, 100);
          }
        });

        // Preview scroll handler
        previewScrollHandler.current = (e: Event) => {
          if (!editorRef.current || isScrollingRef.current) {
            return;
          }

          isScrollingRef.current = true;

          const target = e.target as HTMLDivElement;
          const scrollTop = target.scrollTop;
          const maxScroll = target.scrollHeight - target.clientHeight;

          if (maxScroll > 0) {
            // Calculate scroll percentage
            const scrollPercentage = scrollTop / maxScroll;

            // Apply to editor
            const editor = editorRef.current;
            const editorMaxScroll = editor.getScrollHeight() - editor.getLayoutInfo().height;
            if (editorMaxScroll > 0) {
              editor.setScrollTop(scrollPercentage * editorMaxScroll);
            }
          }

          // Reset flag after a short delay
          setTimeout(() => {
            isScrollingRef.current = false;
          }, 100);
        };

        // Attach preview scroll listener
        if (previewRef.current) {
          previewRef.current.addEventListener('scroll', previewScrollHandler.current, { passive: true });
          console.log('Scroll sync enabled');
        }
      } else {
        console.log('Scroll sync disabled');
      }
    }, 100);

    // Cleanup on unmount or when sync is disabled
    return () => {
      clearTimeout(timer);
      if (editorScrollDisposable.current) {
        editorScrollDisposable.current.dispose();
        editorScrollDisposable.current = null;
      }
      if (previewScrollHandler.current && previewRef.current) {
        previewRef.current.removeEventListener('scroll', previewScrollHandler.current);
        previewScrollHandler.current = null;
      }
    };
  }, [isScrollSynced]);

  // Enhanced Markdown to HTML converter
  const markdownToHtml = (markdown: string): string => {
    let html = markdown
      // Headers with proper spacing
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mt-4 mb-2 text-gray-800 dark:text-gray-200">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mt-6 mb-3 text-gray-900 dark:text-gray-100">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-4 text-gray-900 dark:text-gray-100">$1</h1>')
      // Bold and italic
      .replace(/\*\*(.*?)\*\*/gim, '<strong class="font-semibold text-gray-900 dark:text-gray-100">$1</strong>')
      .replace(/__(.*?)__/gim, '<strong class="font-semibold text-gray-900 dark:text-gray-100">$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em class="italic text-gray-800 dark:text-gray-200">$1</em>')
      .replace(/_(.*?)_/gim, '<em class="italic text-gray-800 dark:text-gray-200">$1</em>')
      // Code blocks with styling
      .replace(/```([\s\S]*?)```/gim, '<pre class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto my-4"><code class="text-sm font-mono text-gray-800 dark:text-gray-200">$1</code></pre>')
      // Inline code
      .replace(/`(.*?)`/gim, '<code class="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono text-pink-600 dark:text-pink-400">$1</code>')
      // Links with styling
      .replace(/\[([^\]]+)\]\(([^\)]+)\)/gim, '<a href="$2" class="text-blue-600 dark:text-blue-400 hover:underline font-medium">$1</a>')
      // Lists
      .replace(/^- (.*$)/gim, '<li class="ml-4 mb-1 text-gray-700 dark:text-gray-300">• $1</li>')
      // Paragraphs
      .replace(/\n\n/gim, '</p><p class="mb-4 text-gray-700 dark:text-gray-300">')
      // Line breaks
      .replace(/\n/gim, '<br>');

    // Wrap in paragraph tags
    html = '<div class="prose prose-sm max-w-none dark:prose-invert"><p class="mb-4 text-gray-700 dark:text-gray-300">' + html + '</p></div>';

    return html;
  };

  // Convert HTML/Markdown to plain text
  const toPlainText = (text: string): string => {
    return text
      .replace(/<[^>]*>/g, '')
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/__(.*?)__/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/_(.*?)_/g, '$1')
      .replace(/`(.*?)`/g, '$1')
      .replace(/```[\s\S]*?```/g, '')
      .replace(/#{1,6}\s/g, '')
      .replace(/\[([^\]]+)\]\([^\)]+\)/g, '$1')
      .replace(/^- /gm, '• ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim();
  };





  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${type} copied to clipboard!`);
  };

  // Copy formatted preview content as plain text
  const copyPreviewAsText = async () => {
    try {
      const previewElement = document.querySelector('.preview-content');
      if (previewElement) {
        const textContent = previewElement.textContent || (previewElement as HTMLElement).innerText || '';
        await navigator.clipboard.writeText(textContent);
        toast.success('Preview content copied as text!');
      } else {
        toast.error('No preview content to copy');
      }
    } catch (err) {
      toast.error('Failed to copy preview content');
    }
  };

  const downloadFile = (content: string, filename: string, type: string) => {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
    toast.success(`File downloaded: ${filename}`);
  };

  // Convert plain text to basic markdown
  const plainToMarkdown = (text: string): string => {
    return text
      .split('\n\n')
      .map(paragraph => {
        const trimmed = paragraph.trim();
        if (trimmed.length < 50 && !trimmed.includes('.') && !trimmed.includes(',')) {
          return `# ${trimmed}`;
        }
        if (trimmed.startsWith('•')) {
          return trimmed.replace(/^• /gm, '- ');
        }
        return trimmed;
      })
      .join('\n\n');
  };

  const handleMarkdownToHtml = () => {
    if (!markdownText.trim()) {
      toast.error('Please enter some markdown text');
      return;
    }
    const html = markdownToHtml(markdownText);
    setHtmlOutput(html);
    const plain = toPlainText(markdownText);
    setPlainText(plain);
    toast.success('Conversion completed!');
  };

  const handlePlainToMarkdown = () => {
    if (!plainText.trim()) {
      toast.error('Please enter some plain text');
      return;
    }
    const markdown = plainToMarkdown(plainText);
    setMarkdownText(markdown);
    toast.success('Text converted to Markdown!');
  };

  // Initialize with converted markdown on component mount
  useEffect(() => {
    const html = markdownToHtml(markdownText);
    setHtmlOutput(html);
    const plain = toPlainText(markdownText);
    setPlainText(plain);
  }, []);

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">


      {/* Tabs for different modes */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="flex items-center justify-between mx-4 mt-1">
          <TabsList className="grid grid-cols-3 h-10 flex-1 mr-4">
            <TabsTrigger value="live-editor" className="flex items-center gap-2 text-sm font-medium">
              <Eye className="w-4 h-4" />
              Live Editor
            </TabsTrigger>
            <TabsTrigger value="md-to-html" className="flex items-center gap-2 text-sm font-medium">
              <Code className="w-4 h-4" />
              Markdown to HTML/Text
            </TabsTrigger>
            <TabsTrigger value="text-to-md" className="flex items-center gap-2 text-sm font-medium">
              <FileText className="w-4 h-4" />
              Text to Markdown
            </TabsTrigger>
          </TabsList>

          {/* Quick resize buttons and theme toggle */}
          <div className="flex items-center gap-1">
            {/* Theme toggle for editor */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditorTheme(editorTheme === 'vs-dark' ? 'light' : 'vs-dark')}
              className="h-8 px-2 text-xs"
              title={editorTheme === 'vs-dark' ? 'Switch to Light Theme' : 'Switch to Dark Theme'}
            >
              {editorTheme === 'vs-dark' ? <Sun className="w-3 h-3" /> : <Moon className="w-3 h-3" />}
            </Button>

            {/* Quick resize buttons for live editor */}
            {activeTab === 'live-editor' && (
              <>
                <div className="w-px h-4 bg-gray-300 dark:bg-gray-600 mx-1"></div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsScrollSynced(!isScrollSynced)}
                  className={`h-8 px-2 text-xs ${isScrollSynced ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : ''}`}
                  title={isScrollSynced ? 'Disable scroll sync' : 'Enable scroll sync'}
                >
                  {isScrollSynced ? <Link className="w-3 h-3" /> : <Unlink className="w-3 h-3" />}
                </Button>
                <div className="w-px h-4 bg-gray-300 dark:bg-gray-600 mx-1"></div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setEditorWidth(25)}
                  className="h-8 px-2 text-xs"
                  title="Focus on Preview"
                >
                  <Eye className="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setEditorWidth(50)}
                  className="h-8 px-2 text-xs"
                  title="Balanced View"
                >
                  <div className="flex gap-0.5">
                    <div className="w-1 h-3 bg-current"></div>
                    <div className="w-1 h-3 bg-current"></div>
                  </div>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setEditorWidth(75)}
                  className="h-8 px-2 text-xs"
                  title="Focus on Editor"
                >
                  <Code className="w-3 h-3" />
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Live Editor Tab */}
        <TabsContent value="live-editor" className="flex-1 flex overflow-hidden mt-0">
          {/* Split layout container */}
          <div className="flex-1 flex overflow-hidden resizable-container">
            {/* Left panel - Editor */}
            <div
              className="flex flex-col border-r border-gray-200 dark:border-gray-700"
              style={{ width: `${editorWidth}%` }}
            >
              {/* Editor header */}
              <div className="flex items-center justify-between px-4 py-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2">
                  <Code className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Editor</span>
                </div>
                <div className="flex items-center gap-1">
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <RotateCcw className="w-3 h-3" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Download className="w-3 h-3" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Maximize2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              {/* Editor content */}
              <div className="flex-1">
                <MonacoEditor
                  height="100%"
                  defaultLanguage="markdown"
                  value={markdownText}
                  onChange={(value) => {
                    const newValue = value || '';
                    setMarkdownText(newValue);
                    // Auto-convert on change for live preview
                    const html = markdownToHtml(newValue);
                    setHtmlOutput(html);
                    const plain = toPlainText(newValue);
                    setPlainText(plain);
                  }}
                  onMount={handleEditorDidMount}
                  options={{
                    minimap: { enabled: false },
                    fontSize: 14,
                    lineNumbers: 'on',
                    wordWrap: 'on',
                    automaticLayout: true,
                    scrollBeyondLastLine: false,
                    padding: { top: 16, bottom: 16 },
                    lineHeight: 24,
                    fontFamily: 'JetBrains Mono, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                    tabSize: 2,
                    insertSpaces: true,
                    renderLineHighlight: 'line',
                    cursorBlinking: 'smooth',
                    smoothScrolling: true,
                    contextmenu: true,
                    selectOnLineNumbers: true,
                    roundedSelection: false,
                    readOnly: false,
                    cursorStyle: 'line',
                    mouseWheelZoom: false,
                    scrollbar: {
                      vertical: 'visible',
                      horizontal: 'visible',
                      useShadows: false,
                      verticalHasArrows: true,
                      horizontalHasArrows: true,
                      verticalScrollbarSize: 14,
                      horizontalScrollbarSize: 14,
                      handleMouseWheel: true,
                      alwaysConsumeMouseWheel: false, // Keep false for proper scrolling
                    },
                    overviewRulerLanes: 0,
                    hideCursorInOverviewRuler: true,
                    quickSuggestions: {
                      other: true,
                      comments: true,
                      strings: true
                    },
                    parameterHints: {
                      enabled: true
                    },
                    autoIndent: 'full',
                    formatOnType: true,
                    formatOnPaste: true,
                    // Enhanced settings for macOS trackpad scrolling
                    mouseWheelScrollSensitivity: 1,
                    fastScrollSensitivity: 5,
                    // Additional scrolling improvements
                    scrollPredominantAxis: false,
                    revealHorizontalRightPadding: 5,
                  }}
                  theme={editorTheme}
                />
              </div>
              {/* Editor footer */}
              <div className="px-4 py-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>{markdownText.split(' ').filter(word => word.length > 0).length} words | {markdownText.length} characters | {markdownText.split('\n').length} lines</span>
                  <div className="flex items-center gap-2">
                    {isScrollSynced && (
                      <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                        <Link className="w-3 h-3" />
                        <span>Synced</span>
                      </div>
                    )}
                    <span>Editor</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Resize Handle */}
            <div
              className="w-1 bg-gray-300 dark:bg-gray-600 hover:bg-blue-500 dark:hover:bg-blue-400 cursor-col-resize transition-colors duration-200 flex-shrink-0"
              onMouseDown={handleMouseDown}
              title="Drag to resize"
            >
              <div className="w-full h-full flex items-center justify-center">
                <div className="w-0.5 h-8 bg-gray-400 dark:bg-gray-500 rounded-full"></div>
              </div>
            </div>

            {/* Right panel - Preview */}
            <div
              className="flex flex-col"
              style={{ width: `${100 - editorWidth}%` }}
            >
              {/* Preview header */}
              <div className="flex items-center justify-between px-4 py-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2">
                  <Eye className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Preview</span>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={copyPreviewAsText}
                    disabled={!htmlOutput}
                    title="Copy formatted text"
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => copyToClipboard(htmlOutput, 'HTML source')}
                    disabled={!htmlOutput}
                    title="Copy HTML source"
                  >
                    <Code className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => downloadFile(htmlOutput, 'converted.html', 'text/html')}
                    disabled={!htmlOutput}
                    title="Download HTML"
                  >
                    <Download className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    title="Maximize preview"
                  >
                    <Maximize2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              {/* Preview content */}
              <div
                className="flex-1 p-4 overflow-auto bg-white dark:bg-gray-900"
                ref={previewRef}
              >
                {htmlOutput ? (
                  <div
                    className="prose prose-sm max-w-none dark:prose-invert preview-content"
                    dangerouslySetInnerHTML={{ __html: htmlOutput }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                    <div className="text-center">
                      <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">Live Preview</p>
                      <p className="text-sm">Start typing in the editor to see your Markdown rendered here</p>
                    </div>
                  </div>
                )}
              </div>
              {/* Preview footer */}
              <div className="px-4 py-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>{htmlOutput ? `${htmlOutput.split(' ').filter(word => word.length > 0).length} words | ${htmlOutput.replace(/<[^>]*>/g, '').length} characters | ${htmlOutput.split('\n').length} lines` : 'No content'}</span>
                  <div className="flex items-center gap-2">
                    {isScrollSynced && (
                      <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                        <Link className="w-3 h-3" />
                        <span>Synced</span>
                      </div>
                    )}
                    <span>Preview</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Markdown to HTML/Text Tab */}
        <TabsContent value="md-to-html" className="flex-1 p-3 space-y-3">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 h-full">
            {/* Input Section */}
            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Code className="w-4 h-4 text-blue-600" />
                  Markdown Input
                </CardTitle>
                <CardDescription className="text-sm">
                  Enter your markdown text here and see the magic happen
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3 flex-1 flex flex-col">
                <div className="flex-1 min-h-[500px] border-2 rounded-md overflow-hidden">
                  <MonacoEditor
                    height="100%"
                    defaultLanguage="markdown"
                    value={markdownText}
                    onChange={(value) => setMarkdownText(value || '')}
                    onMount={handleEditorDidMount}
                    options={{
                      minimap: { enabled: false },
                      fontSize: 14,
                      lineNumbers: 'on',
                      wordWrap: 'on',
                      automaticLayout: true,
                      scrollBeyondLastLine: false,
                      padding: { top: 16, bottom: 16 },
                      lineHeight: 22,
                      fontFamily: 'JetBrains Mono, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                      tabSize: 2,
                      insertSpaces: true,
                      renderLineHighlight: 'line',
                      cursorBlinking: 'smooth',
                      smoothScrolling: true,
                      contextmenu: true,
                      selectOnLineNumbers: true,
                      roundedSelection: false,
                      readOnly: false,
                      cursorStyle: 'line',
                      mouseWheelZoom: false,
                      scrollbar: {
                        vertical: 'visible',
                        horizontal: 'visible',
                        useShadows: false,
                        verticalHasArrows: true,
                        horizontalHasArrows: true,
                        verticalScrollbarSize: 14,
                        horizontalScrollbarSize: 14,
                        handleMouseWheel: true,
                        alwaysConsumeMouseWheel: false, // Keep false for proper scrolling
                      },
                      overviewRulerLanes: 0,
                      hideCursorInOverviewRuler: true,
                      // Enhanced settings for macOS trackpad scrolling
                      mouseWheelScrollSensitivity: 1,
                      fastScrollSensitivity: 5,
                      scrollPredominantAxis: false,
                      revealHorizontalRightPadding: 5,
                    }}
                    theme={editorTheme}
                  />
                </div>
                <Button
                  onClick={handleMarkdownToHtml}
                  className="w-full h-10 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium transition-all duration-200 transform hover:scale-[1.02]"
                >
                  <ArrowRight className="w-4 h-4 mr-2" />
                  Convert to HTML & Plain Text
                </Button>
              </CardContent>
            </Card>

            {/* Output Section */}
            <div className="space-y-3 flex flex-col">
              {/* HTML Output */}
              <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex-1 flex flex-col">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2 text-lg">
                      <Code className="w-4 h-4 text-green-600" />
                      HTML Output
                    </span>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(htmlOutput, 'HTML')}
                        disabled={!htmlOutput}
                        className="h-7 px-2"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadFile(htmlOutput, 'converted.html', 'text/html')}
                        disabled={!htmlOutput}
                        className="h-7 px-2"
                      >
                        <Download className="w-3 h-3" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col">
                  <div className="flex-1 min-h-[240px] border-2 rounded-md overflow-hidden">
                    <MonacoEditor
                      height="100%"
                      defaultLanguage="html"
                      value={htmlOutput}
                      onMount={handleEditorDidMount}
                      options={{
                        minimap: { enabled: false },
                        fontSize: 13,
                        lineNumbers: 'on',
                        wordWrap: 'on',
                        automaticLayout: true,
                        scrollBeyondLastLine: false,
                        padding: { top: 12, bottom: 12 },
                        lineHeight: 20,
                        fontFamily: 'JetBrains Mono, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                        tabSize: 2,
                        insertSpaces: true,
                        renderLineHighlight: 'none',
                        cursorBlinking: 'solid',
                        contextmenu: false,
                        selectOnLineNumbers: true,
                        readOnly: true,
                        domReadOnly: true,
                        mouseWheelZoom: false,
                        scrollbar: {
                          vertical: 'visible',
                          horizontal: 'visible',
                          useShadows: false,
                          verticalHasArrows: true,
                          horizontalHasArrows: true,
                          verticalScrollbarSize: 14,
                          horizontalScrollbarSize: 14,
                          handleMouseWheel: true,
                          alwaysConsumeMouseWheel: false, // Keep false for proper scrolling
                        },
                        overviewRulerLanes: 0,
                        hideCursorInOverviewRuler: true,
                        // Enhanced settings for macOS trackpad scrolling
                        mouseWheelScrollSensitivity: 1,
                        fastScrollSensitivity: 5,
                        scrollPredominantAxis: false,
                        revealHorizontalRightPadding: 5,
                      }}
                      theme={editorTheme}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Plain Text Output */}
              <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex-1 flex flex-col">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2 text-lg">
                      <FileText className="w-4 h-4 text-purple-600" />
                      Plain Text Output
                    </span>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(plainText, 'Plain text')}
                        disabled={!plainText}
                        className="h-7 px-2"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadFile(plainText, 'converted.txt', 'text/plain')}
                        disabled={!plainText}
                        className="h-7 px-2"
                      >
                        <Download className="w-3 h-3" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col">
                  <Textarea
                    value={plainText}
                    readOnly
                    className="flex-1 min-h-[240px] text-sm resize-none border-2"
                    placeholder="Plain text output will appear here after conversion..."
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Text to Markdown Tab */}
        <TabsContent value="text-to-md" className="flex-1 p-3 space-y-3">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 h-full">
            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <FileText className="w-4 h-4 text-green-600" />
                  Plain Text Input
                </CardTitle>
                <CardDescription className="text-sm">
                  Paste your plain text and we'll format it as Markdown
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3 flex-1 flex flex-col">
                <div className="flex-1 min-h-[500px] border-2 rounded-md overflow-hidden">
                  <MonacoEditor
                    height="100%"
                    defaultLanguage="plaintext"
                    value={plainText}
                    onChange={(value) => setPlainText(value || '')}
                    onMount={handleEditorDidMount}
                    options={{
                      minimap: { enabled: false },
                      fontSize: 14,
                      lineNumbers: 'on',
                      wordWrap: 'on',
                      automaticLayout: true,
                      scrollBeyondLastLine: false,
                      padding: { top: 16, bottom: 16 },
                      lineHeight: 22,
                      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                      tabSize: 2,
                      insertSpaces: true,
                      renderLineHighlight: 'line',
                      cursorBlinking: 'smooth',
                      smoothScrolling: true,
                      contextmenu: true,
                      selectOnLineNumbers: true,
                      roundedSelection: false,
                      readOnly: false,
                      cursorStyle: 'line',
                      mouseWheelZoom: false,
                      scrollbar: {
                        vertical: 'visible',
                        horizontal: 'visible',
                        useShadows: false,
                        verticalHasArrows: true,
                        horizontalHasArrows: true,
                        verticalScrollbarSize: 14,
                        horizontalScrollbarSize: 14,
                        handleMouseWheel: true,
                        alwaysConsumeMouseWheel: false, // Keep false for proper scrolling
                      },
                      overviewRulerLanes: 0,
                      hideCursorInOverviewRuler: true,
                      // Enhanced settings for macOS trackpad scrolling
                      mouseWheelScrollSensitivity: 1,
                      fastScrollSensitivity: 5,
                      scrollPredominantAxis: false,
                      revealHorizontalRightPadding: 5,
                    }}
                    theme={editorTheme}
                  />
                </div>
                <Button
                  onClick={handlePlainToMarkdown}
                  className="w-full h-10 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-medium transition-all duration-200 transform hover:scale-[1.02]"
                >
                  <ArrowRight className="w-4 h-4 mr-2" />
                  Convert to Markdown
                </Button>
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2 text-lg">
                    <Code className="w-4 h-4 text-blue-600" />
                    Markdown Output
                  </span>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(markdownText, 'Markdown')}
                      disabled={!markdownText}
                      className="h-7 px-2"
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadFile(markdownText, 'converted.md', 'text/markdown')}
                      disabled={!markdownText}
                      className="h-7 px-2"
                    >
                      <Download className="w-3 h-3" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                <div className="flex-1 min-h-[500px] border-2 rounded-md overflow-hidden">
                  <MonacoEditor
                    height="100%"
                    defaultLanguage="markdown"
                    value={markdownText}
                    onMount={handleEditorDidMount}
                    options={{
                      minimap: { enabled: false },
                      fontSize: 14,
                      lineNumbers: 'on',
                      wordWrap: 'on',
                      automaticLayout: true,
                      scrollBeyondLastLine: false,
                      padding: { top: 16, bottom: 16 },
                      lineHeight: 22,
                      fontFamily: 'JetBrains Mono, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                      tabSize: 2,
                      insertSpaces: true,
                      renderLineHighlight: 'none',
                      cursorBlinking: 'solid',
                      contextmenu: false,
                      selectOnLineNumbers: true,
                      readOnly: true,
                      domReadOnly: true,
                      mouseWheelZoom: false,
                      scrollbar: {
                        vertical: 'visible',
                        horizontal: 'visible',
                        useShadows: false,
                        verticalHasArrows: true,
                        horizontalHasArrows: true,
                        verticalScrollbarSize: 14,
                        horizontalScrollbarSize: 14,
                        handleMouseWheel: true,
                        alwaysConsumeMouseWheel: false, // Keep false for proper scrolling
                      },
                      overviewRulerLanes: 0,
                      hideCursorInOverviewRuler: true,
                      // Enhanced settings for macOS trackpad scrolling
                      mouseWheelScrollSensitivity: 1,
                      fastScrollSensitivity: 5,
                      scrollPredominantAxis: false,
                      revealHorizontalRightPadding: 5,
                    }}
                    theme={editorTheme}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
