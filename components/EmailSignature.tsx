'use client';

import { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card } from '@/components/ui/card';
import { Upload, RefreshCw, Copy, ImageIcon, Code, Mail as MailIcon, Phone as PhoneIcon, Globe as GlobeIcon, Linkedin as LinkedinIcon, Twitter as TwitterIcon, ArrowUpDown as ArrowUpDownIcon } from 'lucide-react';
import { toast } from 'sonner';
import html2canvas from 'html2canvas';
import Editor from '@monaco-editor/react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { HexColorPicker } from "react-colorful";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";

interface DesignSettings {
  // Colors
  nameColor: string;
  companyColor: string;
  accentColor: string;
  backgroundColor: string;
  iconColor: string;
  borderColor: string;
  shadowColor: string;
  
  // Typography
  fontFamily: string;
  fontSize: number;
  nameFontWeight: number;
  titleFontWeight: number;
  companyFontWeight: number;
  
  // Layout & Spacing
  layout: 'horizontal' | 'vertical';
  padding: number;
  itemSpacing: number;
  
  // Profile Image
  imageSize: number;
  imagePosition: number;
  showImageBorder: boolean;
  imageBorderWidth: number;
  imageBorderRadius: number;
  imageBorderColor: string;
  
  // Border & Shadow
  showBorder: boolean;
  borderWidth: number;
  borderRadius: number;
  shadowEffect: boolean;
  
  // Social Icons
  socialIconSize: number;
  
  // Template
  template: string;
  backgroundStyle: 'solid' | 'gradient' | 'pattern';
  gradientColors: string[];
  gradientDirection: string;
  pattern: string;
}

interface Template {
  id: string;
  name: string;
  preview: string;
  settings: Partial<DesignSettings>;
}

const GRADIENT_DIRECTIONS = [
  { value: 'to right', label: 'Horizontal' },
  { value: 'to bottom', label: 'Vertical' },
  { value: '45deg', label: 'Diagonal' },
  { value: '-45deg', label: 'Reverse Diagonal' },
];

const GRADIENT_PRESETS = [
  {
    name: 'Ocean Blue',
    colors: ['#2563eb', '#4f46e5'],
    direction: 'to right'
  },
  {
    name: 'Sunset',
    colors: ['#f97316', '#db2777'],
    direction: '45deg'
  },
  {
    name: 'Forest',
    colors: ['#059669', '#16a34a'],
    direction: 'to bottom'
  },
  {
    name: 'Purple Rain',
    colors: ['#7c3aed', '#db2777'],
    direction: '-45deg'
  },
  {
    name: 'Golden Hour',
    colors: ['#f59e0b', '#dc2626'],
    direction: 'to right'
  },
  {
    name: 'Northern Lights',
    colors: ['#06b6d4', '#3b82f6'],
    direction: '45deg'
  },
  {
    name: 'Emerald',
    colors: ['#10b981', '#14b8a6'],
    direction: 'to bottom'
  },
  {
    name: 'Midnight',
    colors: ['#312e81', '#4f46e5'],
    direction: '-45deg'
  }
];

const PATTERNS = [
  { value: 'dots', label: 'Dots', css: 'radial-gradient(circle at center, #000 2px, transparent 2px) 0 0 / 10px 10px' },
  { value: 'lines', label: 'Lines', css: 'repeating-linear-gradient(90deg, #000 0, #000 1px, transparent 1px, transparent 10px)' },
  { value: 'grid', label: 'Grid', css: 'linear-gradient(to right, #000 1px, transparent 1px) 0 0 / 10px 10px, linear-gradient(to bottom, #000 1px, transparent 1px) 0 0 / 10px 10px' },
];

const TEMPLATES: Template[] = [
  {
    id: 'modern-gradient',
    name: 'Modern Gradient',
    preview: '/templates/modern-gradient.png',
    settings: {
      template: 'modern-gradient',
      backgroundStyle: 'gradient',
      gradientColors: ['#2563eb', '#4f46e5'],
      gradientDirection: 'to right',
      nameColor: '#ffffff',
      companyColor: '#e5e7eb',
      accentColor: '#93c5fd',
      iconColor: '#93c5fd',
      layout: 'horizontal',
      fontSize: 16,
      fontFamily: 'Inter',
      itemSpacing: 12,
      socialIconSize: 20,
      showBorder: false,
      shadowEffect: true,
      shadowColor: 'rgba(0, 0, 0, 0.2)',
    }
  },
  {
    id: 'minimal-pattern',
    name: 'Minimal Pattern',
    preview: '/templates/minimal-pattern.png',
    settings: {
      template: 'minimal-pattern',
      backgroundStyle: 'pattern',
      pattern: 'dots',
      backgroundColor: '#ffffff',
      nameColor: '#1f2937',
      companyColor: '#4b5563',
      accentColor: '#2563eb',
      iconColor: '#2563eb',
      layout: 'vertical',
      fontSize: 14,
      fontFamily: 'system-ui',
      itemSpacing: 8,
      socialIconSize: 18,
      showBorder: true,
      borderColor: '#e5e7eb',
      borderWidth: 1,
      borderRadius: 8,
    }
  },
  {
    id: 'professional',
    name: 'Professional',
    preview: '/templates/professional.png',
    settings: {
      template: 'professional',
      backgroundStyle: 'solid',
      backgroundColor: '#1e3a8a',
      nameColor: '#ffffff',
      companyColor: '#93c5fd',
      accentColor: '#60a5fa',
      iconColor: '#60a5fa',
      layout: 'horizontal',
      fontSize: 15,
      fontFamily: 'Inter',
      itemSpacing: 10,
      socialIconSize: 16,
      showBorder: false,
      shadowEffect: true,
      shadowColor: 'rgba(0, 0, 0, 0.3)',
    }
  },
  {
    id: 'minimal-clean',
    name: 'Minimal Clean',
    preview: '/templates/minimal-clean.png',
    settings: {
      template: 'minimal-clean',
      backgroundStyle: 'solid',
      backgroundColor: '#ffffff',
      nameColor: '#111827',
      companyColor: '#374151',
      accentColor: '#3b82f6',
      iconColor: '#6b7280',
      layout: 'horizontal',
      fontSize: 14,
      fontFamily: 'Arial',
      itemSpacing: 8,
      socialIconSize: 16,
      showBorder: true,
      borderColor: '#f3f4f6',
      borderWidth: 1,
      borderRadius: 4,
    }
  },
  {
    id: 'bold-gradient',
    name: 'Bold Gradient',
    preview: '/templates/bold-gradient.png',
    settings: {
      template: 'bold-gradient',
      backgroundStyle: 'gradient',
      gradientColors: ['#7c3aed', '#db2777'],
      gradientDirection: '45deg',
      nameColor: '#ffffff',
      companyColor: '#fce7f3',
      accentColor: '#f0abfc',
      iconColor: '#f0abfc',
      layout: 'horizontal',
      fontSize: 16,
      fontFamily: 'Inter',
      nameFontWeight: 700,
      titleFontWeight: 600,
      itemSpacing: 12,
      socialIconSize: 20,
      showBorder: false,
      shadowEffect: true,
      shadowColor: 'rgba(0, 0, 0, 0.25)',
    }
  },
  {
    id: 'elegant-pattern',
    name: 'Elegant Pattern',
    preview: '/templates/elegant-pattern.png',
    settings: {
      template: 'elegant-pattern',
      backgroundStyle: 'pattern',
      pattern: 'grid',
      backgroundColor: '#f8fafc',
      nameColor: '#0f172a',
      companyColor: '#334155',
      accentColor: '#0ea5e9',
      iconColor: '#64748b',
      layout: 'vertical',
      fontSize: 15,
      fontFamily: 'Times New Roman',
      itemSpacing: 10,
      socialIconSize: 18,
      showBorder: true,
      borderColor: '#cbd5e1',
      borderWidth: 1,
      borderRadius: 6,
    }
  }
];

const defaultDesignSettings: DesignSettings = {
  // Colors
  nameColor: '#000000',
  companyColor: '#444444',
  accentColor: '#0066cc',
  backgroundColor: '#ffffff',
  iconColor: '#666666',
  borderColor: '#e5e7eb',
  shadowColor: 'rgba(0, 0, 0, 0.1)',

  // Typography
  fontFamily: 'Inter',
  fontSize: 14,
  nameFontWeight: 600,
  titleFontWeight: 500,
  companyFontWeight: 400,

  // Layout & Spacing
  layout: 'horizontal',
  padding: 16,
  itemSpacing: 8,

  // Profile Image
  imageSize: 100,
  imagePosition: 0,
  showImageBorder: true,
  imageBorderWidth: 1,
  imageBorderRadius: 8,
  imageBorderColor: '#e5e7eb',

  // Border & Shadow
  showBorder: true,
  borderWidth: 1,
  borderRadius: 8,
  shadowEffect: false,

  // Template
  template: '',
  backgroundStyle: 'solid',
  gradientColors: ['#ffffff', '#f3f4f6'],
  gradientDirection: 'to right',
  pattern: 'dots',
  socialIconSize: 0
};

export function EmailSignature() {
  const [formData, setFormData] = useState({
    fullName: '',
    position: '',
    company: '',
    email: '',
    phone: '',
    website: '',
    linkedinUrl: '',
    twitterUrl: '',
    profileImage: '',
  });

  const [isEditingHTML, setIsEditingHTML] = useState(false);
  const [customHTML, setCustomHTML] = useState('');
  const [editorTheme, setEditorTheme] = useState<'vs-dark' | 'vs-light'>('vs-dark');
  const [designSettings, setDesignSettings] = useState<DesignSettings>(defaultDesignSettings);
  const [exportSettings, setExportSettings] = useState({
    format: 'png',
    quality: 1.0,
    scale: 2,
  });
  const signatureRef = useRef<HTMLDivElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleDesignChange = (
    setting: keyof DesignSettings, 
    value: string | number | boolean | string[]
  ) => {
    setDesignSettings(prev => ({ ...prev, [setting]: value }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setFormData(prev => ({ ...prev, profileImage: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  const generateDefaultHTML = () => {
    const getBackgroundStyle = () => {
      switch (designSettings.backgroundStyle) {
        case 'solid':
          return `background-color: ${designSettings.backgroundColor};`;
        case 'gradient':
          return `background-image: linear-gradient(${designSettings.gradientDirection}, ${designSettings.gradientColors[0]}, ${designSettings.gradientColors[1]});`;
        case 'pattern': {
          const pattern = PATTERNS.find(p => p.value === designSettings.pattern);
          if (pattern) {
            const css = pattern.css.replace(/#000/g, designSettings.backgroundColor);
            return `background-image: ${css};`;
          }
          return '';
        }
        default:
          return '';
      }
    };

    return `
      <div style="
        font-family: ${designSettings.fontFamily};
        font-size: ${designSettings.fontSize}px;
        line-height: ${designSettings.fontSize * 1.5}px;
        padding: ${designSettings.padding}px;
        ${getBackgroundStyle()}
        border: ${designSettings.showBorder ? `${designSettings.borderWidth}px solid ${designSettings.borderColor}` : 'none'};
        border-radius: ${designSettings.borderRadius}px;
        ${designSettings.shadowEffect ? `box-shadow: 0 2px 8px ${designSettings.shadowColor}` : ''}
      ">
        <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 550px;">
          <tr>
            <td style="vertical-align: top; width: 100px;">
              ${formData.profileImage ? `<img src="${formData.profileImage}" alt="Profile" style="width: ${designSettings.imageSize}px; height: ${designSettings.imageSize}px; border-radius: ${designSettings.imageBorderRadius}px; object-fit: cover; border: ${designSettings.showImageBorder ? `${designSettings.imageBorderWidth}px solid ${designSettings.imageBorderColor}` : 'none'};">` : ''}
            </td>
            <td style="vertical-align: top; padding-left: 15px;">
              <div style="margin-bottom: 5px;">
                <span style="font-size: ${designSettings.fontSize + 4}px; font-weight: ${designSettings.nameFontWeight}; color: ${designSettings.nameColor};">${formData.fullName}</span>
              </div>
              <div style="color: ${designSettings.accentColor}; font-weight: ${designSettings.titleFontWeight}; margin-bottom: 3px;">${formData.position}</div>
              <div style="color: ${designSettings.companyColor}; font-weight: ${designSettings.companyFontWeight}; margin-bottom: 8px;">${formData.company}</div>
              <div style="margin-top: 12px;">
                ${formData.email ? `<div style="margin-bottom: 4px;"><a href="mailto:${formData.email}" style="color: ${designSettings.accentColor}; text-decoration: none;">📧 ${formData.email}</a></div>` : ''}
                ${formData.phone ? `<div style="margin-bottom: 4px;"><a href="tel:${formData.phone}" style="color: ${designSettings.accentColor}; text-decoration: none;">📱 ${formData.phone}</a></div>` : ''}
                ${formData.website ? `<div style="margin-bottom: 4px;"><a href="${formData.website}" style="color: ${designSettings.accentColor}; text-decoration: none;">🌐 ${formData.website}</a></div>` : ''}
              </div>
              ${(formData.linkedinUrl || formData.twitterUrl) ? `
                <div style="margin-top: 10px; display: flex; gap: 10px;">
                  ${formData.linkedinUrl ? `<a href="${formData.linkedinUrl}" style="color: ${designSettings.accentColor}; text-decoration: none;">LinkedIn</a>` : ''}
                  ${formData.twitterUrl ? `<a href="${formData.twitterUrl}" style="color: ${designSettings.accentColor}; text-decoration: none;">Twitter</a>` : ''}
                </div>
              ` : ''}
            </td>
          </tr>
        </table>
      </div>
    `;
  };

  const toggleHTMLEdit = () => {
    setIsEditingHTML(!isEditingHTML);
    if (!isEditingHTML) {
      setCustomHTML(generateDefaultHTML());
    }
  };

  const copyToClipboard = async () => {
    const html = isEditingHTML ? customHTML : generateDefaultHTML();
    await navigator.clipboard.writeText(html);
    toast.success('HTML copied to clipboard!');
  };

  const handleSaveAsImage = async () => {
    if (signatureRef.current) {
      try {
        const canvas = await html2canvas(signatureRef.current, {
          backgroundColor: null,
          scale: exportSettings.scale,
          useCORS: true,
          allowTaint: true,
          onclone: (clonedDoc) => {
            const element = clonedDoc.querySelector("[data-signature]") as HTMLElement;
            if (element) {
              element.style.width = '100%';
              element.style.height = '100%';
            }
          }
        });
        
        let mimeType = 'image/png';
        let extension = 'png';
        let quality = exportSettings.quality;

        switch (exportSettings.format) {
          case 'jpeg':
            mimeType = 'image/jpeg';
            extension = 'jpg';
            break;
          case 'webp':
            mimeType = 'image/webp';
            extension = 'webp';
            break;
          default:
            mimeType = 'image/png';
            extension = 'png';
        }
        
        const link = document.createElement('a');
        link.download = `email-signature.${extension}`;
        link.href = canvas.toDataURL(mimeType, quality);
        link.click();
      } catch (error) {
        console.error('Error saving image:', error);
      }
    }
  };

  const downloadAsHTML = () => {
    const html = isEditingHTML ? customHTML : generateDefaultHTML();
    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.download = 'email-signature.html';
    link.href = url;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Input Section */}
      <div className="space-y-6">
        <Card className="p-6 border-t-4 border-t-blue-500 shadow-lg">
          <div className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">
                {isEditingHTML ? 'Code Editor' : 'Signature Details'}
              </h3>
              <div className="flex items-center space-x-2">
                <Label htmlFor="html-edit" className="text-sm">Edit Code</Label>
                <Switch
                  id="html-edit"
                  checked={isEditingHTML}
                  onCheckedChange={toggleHTMLEdit}
                />
              </div>
            </div>

            {isEditingHTML ? (
              <div className="space-y-4">
                <Tabs defaultValue="html">
                  <TabsList>
                    <TabsTrigger value="html">HTML</TabsTrigger>
                    <TabsTrigger value="css">CSS</TabsTrigger>
                  </TabsList>
                  <TabsContent value="html" className="mt-4">
                    <div className="h-[600px] border rounded-lg overflow-hidden relative">
                      <Editor
                        height="100%"
                        defaultLanguage="html"
                        value={customHTML}
                        onChange={(value) => setCustomHTML(value || '')}
                        theme={editorTheme}
                        options={{
                          minimap: { enabled: false },
                          fontSize: 14,
                          wordWrap: 'on',
                          formatOnPaste: true,
                          formatOnType: true,
                        }}
                      />
                      <Button 
                        size="sm"
                        variant="ghost"
                        className="absolute top-2 right-2 bg-background/90 backdrop-blur-sm"
                        onClick={() => {
                          navigator.clipboard.writeText(customHTML);
                          toast.success('HTML code copied to clipboard');
                        }}
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        Copy
                      </Button>
                    </div>
                  </TabsContent>
                  <TabsContent value="css" className="mt-4">
                    <div className="h-[600px] border rounded-lg overflow-hidden relative">
                      <Editor
                        height="100%"
                        defaultLanguage="css"
                        value={customHTML.match(/<style[^>]*>([\s\S]*?)<\/style>/)?.[1] || ''}
                        onChange={(value) => {
                          const newHTML = customHTML.replace(
                            /<style>.*?<\/style>/,
                            `<style>${value}</style>`
                          );
                          setCustomHTML(newHTML);
                        }}
                        theme={editorTheme}
                        options={{
                          minimap: { enabled: false },
                          fontSize: 14,
                          wordWrap: 'on',
                          formatOnPaste: true,
                          formatOnType: true,
                        }}
                      />
                      <Button 
                        size="sm"
                        variant="ghost"
                        className="absolute top-2 right-2 bg-background/90 backdrop-blur-sm"
                        onClick={() => {
                          const cssCode = customHTML.match(/<style[^>]*>([\s\S]*?)<\/style>/)?.[1] || '';
                          navigator.clipboard.writeText(cssCode);
                          toast.success('CSS code copied to clipboard');
                        }}
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        Copy
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
                <Button 
                  variant="outline" 
                  onClick={() => setCustomHTML(generateDefaultHTML())}
                  className="w-full"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reset to Default
                </Button>
              </div>
            ) : (
              <>
                {/* Personal Details Form */}
                <div className="space-y-6">
                  {/* Profile Image Upload Section */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="profileImage" className="text-base font-medium">Profile Image</Label>
                      {formData.profileImage && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setFormData(prev => ({ ...prev, profileImage: '' }))}
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="relative w-24 h-24 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center bg-gray-50">
                        {formData.profileImage ? (
                          <img
                            src={formData.profileImage}
                            alt="Profile"
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <div className="text-center">
                            <Upload className="w-6 h-6 mx-auto text-gray-400" />
                            <span className="mt-1 text-xs text-gray-500">Upload</span>
                          </div>
                        )}
                        <Input
                          id="profileImage"
                          name="profileImage"
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                      </div>
                      <div className="flex-1 text-sm text-gray-500">
                        <p>Upload a professional photo</p>
                        <p>Recommended size: 400x400px</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="fullName">Full Name</Label>
                      <Input
                        id="fullName"
                        name="fullName"
                        value={formData.fullName}
                        onChange={handleInputChange}
                        placeholder="John Doe"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="position">Position</Label>
                      <Input
                        id="position"
                        name="position"
                        value={formData.position}
                        onChange={handleInputChange}
                        placeholder="Software Engineer"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="company">Company</Label>
                      <Input
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        placeholder="Acme Inc."
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="+****************"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        name="website"
                        type="url"
                        value={formData.website}
                        onChange={handleInputChange}
                        placeholder="https://example.com"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
                      <Input
                        id="linkedinUrl"
                        name="linkedinUrl"
                        type="url"
                        value={formData.linkedinUrl}
                        onChange={handleInputChange}
                        placeholder="https://linkedin.com/in/johndoe"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="twitterUrl">Twitter URL</Label>
                      <Input
                        id="twitterUrl"
                        name="twitterUrl"
                        type="url"
                        value={formData.twitterUrl}
                        onChange={handleInputChange}
                        placeholder="https://twitter.com/johndoe"
                      />
                    </div>
                  </div>
                </div>

                {/* Design Controls */}
                <div className="space-y-4 mt-6">
                  <h2 className="text-lg font-semibold">Design Settings</h2>
                  <Accordion type="single" collapsible className="w-full space-y-2">
                    {/* Templates Section */}
                    <AccordionItem value="templates">
                      <AccordionTrigger>Templates</AccordionTrigger>
                      <AccordionContent>
                        <div className="grid grid-cols-2 gap-4">
                          {TEMPLATES.map((template) => (
                            <button
                              key={template.id}
                              className={cn(
                                "relative aspect-video w-full overflow-hidden rounded-lg border-2 transition-all hover:border-primary",
                                {
                                  "border-primary": designSettings.template === template.id,
                                  "border-muted": designSettings.template !== template.id
                                }
                              )}
                              onClick={() => {
                                // If clicking the same template, unselect it
                                if (designSettings.template === template.id) {
                                  handleDesignChange('template', '');
                                  // Reset to default settings
                                  Object.keys(defaultDesignSettings).forEach((key) => {
                                    handleDesignChange(key as keyof DesignSettings, defaultDesignSettings[key as keyof DesignSettings]);
                                  });
                                } else {
                                  handleDesignChange('template', template.id);
                                  Object.entries(template.settings).forEach(([key, value]) => {
                                    if (value !== undefined) {
                                      handleDesignChange(key as keyof DesignSettings, value as any);
                                    }
                                  });
                                }
                              }}
                            >
                              <img src={template.preview} alt={template.name} className="w-full h-full object-cover" />
                              <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-2 text-sm">
                                {template.name}
                              </div>
                            </button>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Colors Section */}
                    <AccordionItem value="colors">
                      <AccordionTrigger>Colors</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="grid gap-4">
                            {/* Name Color */}
                            <div className="flex items-center justify-between gap-2">
                              <Label>Name Color</Label>
                              <Popover>
                                <PopoverTrigger>
                                  <div 
                                    className="w-8 h-8 rounded border shadow-sm cursor-pointer"
                                    style={{ backgroundColor: designSettings.nameColor }}
                                  />
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <div className="p-2 space-y-3">
                                    <HexColorPicker
                                      color={designSettings.nameColor}
                                      onChange={(newColor: string) => handleDesignChange('nameColor', newColor)}
                                      style={{ width: '200px', height: '150px' }}
                                    />
                                    <div className="space-y-2">
                                      <div className="flex items-center gap-2">
                                        <Label className="w-12">Hex</Label>
                                        <Input 
                                          value={designSettings.nameColor}
                                          onChange={(e) => {
                                            const value = e.target.value;
                                            if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                                              handleDesignChange('nameColor', value);
                                            }
                                          }}
                                          className="font-mono"
                                        />
                                      </div>
                                      <div className="grid grid-cols-3 gap-2">
                                        <div className="space-y-1">
                                          <Label className="text-xs">R</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.nameColor.slice(1, 3), 16)}
                                            onChange={(e) => {
                                              const r = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `#${r}${designSettings.nameColor.slice(3)}`;
                                              handleDesignChange('nameColor', newColor);
                                            }}
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs">G</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.nameColor.slice(3, 5), 16)}
                                            onChange={(e) => {
                                              const g = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `#${designSettings.nameColor.slice(1, 3)}${g}${designSettings.nameColor.slice(5)}`;
                                              handleDesignChange('nameColor', newColor);
                                            }}
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs">B</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.nameColor.slice(5, 7), 16)}
                                            onChange={(e) => {
                                              const b = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `${designSettings.nameColor.slice(0, 5)}${b}`;
                                              handleDesignChange('nameColor', newColor);
                                            }}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </PopoverContent>
                              </Popover>
                            </div>

                            {/* Company Color */}
                            <div className="flex items-center justify-between gap-2">
                              <Label>Company Color</Label>
                              <Popover>
                                <PopoverTrigger>
                                  <div 
                                    className="w-8 h-8 rounded border shadow-sm cursor-pointer"
                                    style={{ backgroundColor: designSettings.companyColor }}
                                  />
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <div className="p-2 space-y-3">
                                    <HexColorPicker
                                      color={designSettings.companyColor}
                                      onChange={(newColor: string) => handleDesignChange('companyColor', newColor)}
                                      style={{ width: '200px', height: '150px' }}
                                    />
                                    <div className="space-y-2">
                                      <div className="flex items-center gap-2">
                                        <Label className="w-12">Hex</Label>
                                        <Input 
                                          value={designSettings.companyColor}
                                          onChange={(e) => {
                                            const value = e.target.value;
                                            if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                                              handleDesignChange('companyColor', value);
                                            }
                                          }}
                                          className="font-mono"
                                        />
                                      </div>
                                      <div className="grid grid-cols-3 gap-2">
                                        <div className="space-y-1">
                                          <Label className="text-xs">R</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.companyColor.slice(1, 3), 16)}
                                            onChange={(e) => {
                                              const r = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `#${r}${designSettings.companyColor.slice(3)}`;
                                              handleDesignChange('companyColor', newColor);
                                            }}
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs">G</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.companyColor.slice(3, 5), 16)}
                                            onChange={(e) => {
                                              const g = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `#${designSettings.companyColor.slice(1, 3)}${g}${designSettings.companyColor.slice(5)}`;
                                              handleDesignChange('companyColor', newColor);
                                            }}
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs">B</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.companyColor.slice(5, 7), 16)}
                                            onChange={(e) => {
                                              const b = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `${designSettings.companyColor.slice(0, 5)}${b}`;
                                              handleDesignChange('companyColor', newColor);
                                            }}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </PopoverContent>
                              </Popover>
                            </div>

                            {/* Accent Color */}
                            <div className="flex items-center justify-between gap-2">
                              <Label>Accent Color</Label>
                              <Popover>
                                <PopoverTrigger>
                                  <div 
                                    className="w-8 h-8 rounded border shadow-sm cursor-pointer"
                                    style={{ backgroundColor: designSettings.accentColor }}
                                  />
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <div className="p-2 space-y-3">
                                    <HexColorPicker
                                      color={designSettings.accentColor}
                                      onChange={(newColor: string) => handleDesignChange('accentColor', newColor)}
                                      style={{ width: '200px', height: '150px' }}
                                    />
                                    <div className="space-y-2">
                                      <div className="flex items-center gap-2">
                                        <Label className="w-12">Hex</Label>
                                        <Input 
                                          value={designSettings.accentColor}
                                          onChange={(e) => {
                                            const value = e.target.value;
                                            if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                                              handleDesignChange('accentColor', value);
                                            }
                                          }}
                                          className="font-mono"
                                        />
                                      </div>
                                      <div className="grid grid-cols-3 gap-2">
                                        <div className="space-y-1">
                                          <Label className="text-xs">R</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.accentColor.slice(1, 3), 16)}
                                            onChange={(e) => {
                                              const r = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `#${r}${designSettings.accentColor.slice(3)}`;
                                              handleDesignChange('accentColor', newColor);
                                            }}
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs">G</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.accentColor.slice(3, 5), 16)}
                                            onChange={(e) => {
                                              const g = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `#${designSettings.accentColor.slice(1, 3)}${g}${designSettings.accentColor.slice(5)}`;
                                              handleDesignChange('accentColor', newColor);
                                            }}
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs">B</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.accentColor.slice(5, 7), 16)}
                                            onChange={(e) => {
                                              const b = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `${designSettings.accentColor.slice(0, 5)}${b}`;
                                              handleDesignChange('accentColor', newColor);
                                            }}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </PopoverContent>
                              </Popover>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Typography Section */}
                    <AccordionItem value="typography">
                      <AccordionTrigger>Typography</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label>Font Family</Label>
                            <Select
                              value={designSettings.fontFamily}
                              onValueChange={(value) => handleDesignChange('fontFamily', value)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Inter">Inter</SelectItem>
                                <SelectItem value="Arial">Arial</SelectItem>
                                <SelectItem value="Helvetica">Helvetica</SelectItem>
                                <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          
                          <div className="space-y-2">
                            <Label>Font Size</Label>
                            <Slider
                              value={[designSettings.fontSize]}
                              onValueChange={([value]) => handleDesignChange('fontSize', value)}
                              min={12}
                              max={20}
                              step={1}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Name Font Weight</Label>
                            <Select
                              value={designSettings.nameFontWeight.toString()}
                              onValueChange={(value) => handleDesignChange('nameFontWeight', parseInt(value))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="400">Regular</SelectItem>
                                <SelectItem value="500">Medium</SelectItem>
                                <SelectItem value="600">Semi Bold</SelectItem>
                                <SelectItem value="700">Bold</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Title Font Weight</Label>
                            <Select
                              value={designSettings.titleFontWeight.toString()}
                              onValueChange={(value) => handleDesignChange('titleFontWeight', parseInt(value))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="400">Regular</SelectItem>
                                <SelectItem value="500">Medium</SelectItem>
                                <SelectItem value="600">Semi Bold</SelectItem>
                                <SelectItem value="700">Bold</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Company Font Weight</Label>
                            <Select
                              value={designSettings.companyFontWeight.toString()}
                              onValueChange={(value) => handleDesignChange('companyFontWeight', parseInt(value))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="400">Regular</SelectItem>
                                <SelectItem value="500">Medium</SelectItem>
                                <SelectItem value="600">Semi Bold</SelectItem>
                                <SelectItem value="700">Bold</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Background Section */}
                    <AccordionItem value="background">
                      <AccordionTrigger>Background</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label>Style</Label>
                            <Select
                              value={designSettings.backgroundStyle}
                              onValueChange={(value) => handleDesignChange('backgroundStyle', value)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="solid">Solid Color</SelectItem>
                                <SelectItem value="gradient">Gradient</SelectItem>
                                <SelectItem value="pattern">Pattern</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {designSettings.backgroundStyle === 'solid' && (
                            <div className="flex items-center justify-between gap-2">
                              <Label>Background Color</Label>
                              <Popover>
                                <PopoverTrigger>
                                  <div 
                                    className="w-8 h-8 rounded border shadow-sm cursor-pointer"
                                    style={{ backgroundColor: designSettings.backgroundColor }}
                                  />
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <div className="p-2 space-y-3">
                                    <HexColorPicker
                                      color={designSettings.backgroundColor}
                                      onChange={(color) => handleDesignChange('backgroundColor', color)}
                                      style={{ width: '200px', height: '150px' }}
                                    />
                                    <div className="space-y-2">
                                      <div className="flex items-center gap-2">
                                        <Label className="w-12">Hex</Label>
                                        <Input 
                                          value={designSettings.backgroundColor}
                                          onChange={(e) => {
                                            const value = e.target.value;
                                            if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                                              handleDesignChange('backgroundColor', value);
                                            }
                                          }}
                                          className="font-mono"
                                        />
                                      </div>
                                      <div className="grid grid-cols-3 gap-2">
                                        <div className="space-y-1">
                                          <Label className="text-xs">R</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.backgroundColor.slice(1, 3), 16)}
                                            onChange={(e) => {
                                              const r = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `#${r}${designSettings.backgroundColor.slice(3)}`;
                                              handleDesignChange('backgroundColor', newColor);
                                            }}
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs">G</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.backgroundColor.slice(3, 5), 16)}
                                            onChange={(e) => {
                                              const g = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `#${designSettings.backgroundColor.slice(1, 3)}${g}${designSettings.backgroundColor.slice(5)}`;
                                              handleDesignChange('backgroundColor', newColor);
                                            }}
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs">B</Label>
                                          <Input 
                                            type="number"
                                            min={0}
                                            max={255}
                                            value={parseInt(designSettings.backgroundColor.slice(5, 7), 16)}
                                            onChange={(e) => {
                                              const b = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                              const newColor = `${designSettings.backgroundColor.slice(0, 5)}${b}`;
                                              handleDesignChange('backgroundColor', newColor);
                                            }}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </PopoverContent>
                              </Popover>
                            </div>
                          )}

                          {designSettings.backgroundStyle === 'gradient' && (
                            <>
                              <div className="space-y-2">
                                <Label>Gradient Presets</Label>
                                <div className="grid grid-cols-2 gap-2">
                                  {GRADIENT_PRESETS.map((preset, index) => (
                                    <button
                                      key={index}
                                      className={cn(
                                        "h-12 rounded-md border-2 transition-all hover:scale-105",
                                        designSettings.gradientColors[0] === preset.colors[0] &&
                                        designSettings.gradientColors[1] === preset.colors[1] &&
                                        designSettings.gradientDirection === preset.direction
                                          ? "border-primary"
                                          : "border-muted"
                                      )}
                                      style={{
                                        background: `linear-gradient(${preset.direction}, ${preset.colors[0]}, ${preset.colors[1]})`
                                      }}
                                      onClick={() => {
                                        handleDesignChange('gradientColors', preset.colors);
                                        handleDesignChange('gradientDirection', preset.direction);
                                      }}
                                      title={preset.name}
                                    />
                                  ))}
                                </div>
                              </div>

                              <div className="space-y-2">
                                <Label>Custom Colors</Label>
                                <div className="flex items-center gap-2">
                                  <Popover>
                                    <PopoverTrigger>
                                      <div 
                                        className="w-10 h-10 rounded border shadow-sm cursor-pointer"
                                        style={{ backgroundColor: designSettings.gradientColors[0] }}
                                      />
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                      <div className="p-2">
                                        <HexColorPicker
                                          color={designSettings.gradientColors[0]}
                                          onChange={(color) => {
                                            handleDesignChange('gradientColors', [
                                              color,
                                              designSettings.gradientColors[1]
                                            ]);
                                          }}
                                          style={{ width: '200px', height: '150px' }}
                                        />
                                      </div>
                                    </PopoverContent>
                                  </Popover>

                                  <ArrowUpDownIcon className="w-4 h-4 text-muted-foreground" />

                                  <Popover>
                                    <PopoverTrigger>
                                      <div 
                                        className="w-10 h-10 rounded border shadow-sm cursor-pointer"
                                        style={{ backgroundColor: designSettings.gradientColors[1] }}
                                      />
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                      <div className="p-2">
                                        <HexColorPicker
                                          color={designSettings.gradientColors[1]}
                                          onChange={(color) => {
                                            handleDesignChange('gradientColors', [
                                              designSettings.gradientColors[0],
                                              color
                                            ]);
                                          }}
                                          style={{ width: '200px', height: '150px' }}
                                        />
                                      </div>
                                    </PopoverContent>
                                  </Popover>
                                </div>
                              </div>

                              <div className="space-y-2">
                                <Label>Direction</Label>
                                <Select
                                  value={designSettings.gradientDirection}
                                  onValueChange={(value) => handleDesignChange('gradientDirection', value)}
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {GRADIENT_DIRECTIONS.map((direction) => (
                                      <SelectItem key={direction.value} value={direction.value}>
                                        {direction.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            </>
                          )}

                          {designSettings.backgroundStyle === 'pattern' && (
                            <>
                              <div className="space-y-2">
                                <Label>Pattern Style</Label>
                                <Select
                                  value={designSettings.pattern}
                                  onValueChange={(value) => handleDesignChange('pattern', value)}
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {PATTERNS.map((pattern) => (
                                      <SelectItem key={pattern.value} value={pattern.value}>
                                        {pattern.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="space-y-2">
                                <Label>Pattern Color</Label>
                                <Popover>
                                  <PopoverTrigger>
                                    <div 
                                      className="w-8 h-8 rounded border shadow-sm cursor-pointer"
                                      style={{ backgroundColor: designSettings.backgroundColor }}
                                    />
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0">
                                    <div className="p-2 space-y-3">
                                      <HexColorPicker
                                        color={designSettings.backgroundColor}
                                        onChange={(color) => handleDesignChange('backgroundColor', color)}
                                        style={{ width: '200px', height: '150px' }}
                                      />
                                      <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                          <Label className="w-12">Hex</Label>
                                          <Input 
                                            value={designSettings.backgroundColor}
                                            onChange={(e) => {
                                              const value = e.target.value;
                                              if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                                                handleDesignChange('backgroundColor', value);
                                              }
                                            }}
                                            className="font-mono"
                                          />
                                        </div>
                                        <div className="grid grid-cols-3 gap-2">
                                          <div className="space-y-1">
                                            <Label className="text-xs">R</Label>
                                            <Input 
                                              type="number"
                                              min={0}
                                              max={255}
                                              value={parseInt(designSettings.backgroundColor.slice(1, 3), 16)}
                                              onChange={(e) => {
                                                const r = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                const newColor = `#${r}${designSettings.backgroundColor.slice(3)}`;
                                                handleDesignChange('backgroundColor', newColor);
                                              }}
                                            />
                                          </div>
                                          <div className="space-y-1">
                                            <Label className="text-xs">G</Label>
                                            <Input 
                                              type="number"
                                              min={0}
                                              max={255}
                                              value={parseInt(designSettings.backgroundColor.slice(3, 5), 16)}
                                              onChange={(e) => {
                                                const g = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                const newColor = `#${designSettings.backgroundColor.slice(1, 3)}${g}${designSettings.backgroundColor.slice(5)}`;
                                                handleDesignChange('backgroundColor', newColor);
                                              }}
                                            />
                                          </div>
                                          <div className="space-y-1">
                                            <Label className="text-xs">B</Label>
                                            <Input 
                                              type="number"
                                              min={0}
                                              max={255}
                                              value={parseInt(designSettings.backgroundColor.slice(5, 7), 16)}
                                              onChange={(e) => {
                                                const b = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                const newColor = `${designSettings.backgroundColor.slice(0, 5)}${b}`;
                                                handleDesignChange('backgroundColor', newColor);
                                              }}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </PopoverContent>
                                </Popover>
                              </div>
                            </>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Layout & Spacing Section */}
                    <AccordionItem value="layout">
                      <AccordionTrigger>Layout & Spacing</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label>Layout</Label>
                            <Select
                              value={designSettings.layout}
                              onValueChange={(value) => handleDesignChange('layout', value)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="horizontal">Horizontal</SelectItem>
                                <SelectItem value="vertical">Vertical</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Padding ({designSettings.padding}px)</Label>
                            <Slider
                              value={[designSettings.padding]}
                              onValueChange={([value]) => handleDesignChange('padding', value)}
                              min={8}
                              max={32}
                              step={4}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Item Spacing ({designSettings.itemSpacing}px)</Label>
                            <Slider
                              value={[designSettings.itemSpacing]}
                              onValueChange={([value]) => handleDesignChange('itemSpacing', value)}
                              min={4}
                              max={24}
                              step={2}
                            />
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Profile Image Section */}
                    <AccordionItem value="profile">
                      <AccordionTrigger>Profile Image</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label>Image Size ({designSettings.imageSize}px)</Label>
                            <Slider
                              value={[designSettings.imageSize]}
                              onValueChange={([value]) => handleDesignChange('imageSize', value)}
                              min={60}
                              max={120}
                              step={10}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="flex items-center gap-2">
                              Image Position <ArrowUpDownIcon className="h-4 w-4" />
                            </Label>
                            <Slider
                              value={[designSettings.imagePosition]}
                              onValueChange={([value]) => handleDesignChange('imagePosition', value)}
                              min={-20}
                              max={20}
                              step={1}
                            />
                            <div className="text-sm text-muted-foreground">
                              Move the image up or down ({designSettings.imagePosition}px)
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Border & Shadow Section */}
                    <AccordionItem value="border">
                      <AccordionTrigger>Border & Shadow</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          {/* Card Border */}
                          <div className="space-y-4">
                            <Label className="text-sm font-medium">Card Border</Label>
                            <div className="flex items-center justify-between">
                              <Label>Show Border</Label>
                              <Switch
                                checked={designSettings.showBorder}
                                onCheckedChange={(checked) => handleDesignChange('showBorder', checked)}
                              />
                            </div>

                            {designSettings.showBorder && (
                              <>
                                <div className="space-y-2">
                                  <Label>Border Width ({designSettings.borderWidth}px)</Label>
                                  <Slider
                                    value={[designSettings.borderWidth]}
                                    onValueChange={([value]) => handleDesignChange('borderWidth', value)}
                                    min={1}
                                    max={5}
                                    step={1}
                                  />
                                </div>

                                <div className="space-y-2">
                                  <Label>Border Color</Label>
                                  <Popover>
                                    <PopoverTrigger>
                                      <div 
                                        className="w-8 h-8 rounded border shadow-sm cursor-pointer"
                                        style={{ backgroundColor: designSettings.borderColor }}
                                      />
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                      <div className="p-2 space-y-3">
                                        <HexColorPicker
                                          color={designSettings.borderColor}
                                          onChange={(color) => handleDesignChange('borderColor', color)}
                                          style={{ width: '200px', height: '150px' }}
                                        />
                                        <div className="space-y-2">
                                          <div className="flex items-center gap-2">
                                            <Label className="w-12">Hex</Label>
                                            <Input 
                                              value={designSettings.borderColor}
                                              onChange={(e) => {
                                                const value = e.target.value;
                                                if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                                                  handleDesignChange('borderColor', value);
                                                }
                                              }}
                                              className="font-mono"
                                            />
                                          </div>
                                          <div className="grid grid-cols-3 gap-2">
                                            <div className="space-y-1">
                                              <Label className="text-xs">R</Label>
                                              <Input 
                                                type="number"
                                                min={0}
                                                max={255}
                                                value={parseInt(designSettings.borderColor.slice(1, 3), 16)}
                                                onChange={(e) => {
                                                  const r = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                  const newColor = `#${r}${designSettings.borderColor.slice(3)}`;
                                                  handleDesignChange('borderColor', newColor);
                                                }}
                                              />
                                            </div>
                                            <div className="space-y-1">
                                              <Label className="text-xs">G</Label>
                                              <Input 
                                                type="number"
                                                min={0}
                                                max={255}
                                                value={parseInt(designSettings.borderColor.slice(3, 5), 16)}
                                                onChange={(e) => {
                                                  const g = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                  const newColor = `#${designSettings.borderColor.slice(1, 3)}${g}${designSettings.borderColor.slice(5)}`;
                                                  handleDesignChange('borderColor', newColor);
                                                }}
                                              />
                                            </div>
                                            <div className="space-y-1">
                                              <Label className="text-xs">B</Label>
                                              <Input 
                                                type="number"
                                                min={0}
                                                max={255}
                                                value={parseInt(designSettings.borderColor.slice(5, 7), 16)}
                                                onChange={(e) => {
                                                  const b = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                  const newColor = `${designSettings.borderColor.slice(0, 5)}${b}`;
                                                  handleDesignChange('borderColor', newColor);
                                                }}
                                              />
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </PopoverContent>
                                  </Popover>
                                </div>
                              </>
                            )}
                          </div>

                          {/* Image Border */}
                          <div className="space-y-4 pt-4 border-t">
                            <Label className="text-sm font-medium">Image Border</Label>
                            <div className="flex items-center justify-between">
                              <Label>Show Image Border</Label>
                              <Switch
                                checked={designSettings.showImageBorder}
                                onCheckedChange={(checked) => handleDesignChange('showImageBorder', checked)}
                              />
                            </div>

                            {designSettings.showImageBorder && (
                              <>
                                <div className="space-y-2">
                                  <Label>Image Border Width ({designSettings.imageBorderWidth}px)</Label>
                                  <Slider
                                    value={[designSettings.imageBorderWidth]}
                                    onValueChange={([value]) => handleDesignChange('imageBorderWidth', value)}
                                    min={1}
                                    max={5}
                                    step={1}
                                  />
                                </div>

                                <div className="space-y-2">
                                  <Label>Image Border Color</Label>
                                  <Popover>
                                    <PopoverTrigger>
                                      <div 
                                        className="w-8 h-8 rounded border shadow-sm cursor-pointer"
                                        style={{ backgroundColor: designSettings.imageBorderColor }}
                                      />
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                      <div className="p-2 space-y-3">
                                        <HexColorPicker
                                          color={designSettings.imageBorderColor}
                                          onChange={(color) => handleDesignChange('imageBorderColor', color)}
                                          style={{ width: '200px', height: '150px' }}
                                        />
                                        <div className="space-y-2">
                                          <div className="flex items-center gap-2">
                                            <Label className="w-12">Hex</Label>
                                            <Input 
                                              value={designSettings.imageBorderColor}
                                              onChange={(e) => {
                                                const value = e.target.value;
                                                if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                                                  handleDesignChange('imageBorderColor', value);
                                                }
                                              }}
                                              className="font-mono"
                                            />
                                          </div>
                                          <div className="grid grid-cols-3 gap-2">
                                            <div className="space-y-1">
                                              <Label className="text-xs">R</Label>
                                              <Input 
                                                type="number"
                                                min={0}
                                                max={255}
                                                value={parseInt(designSettings.imageBorderColor.slice(1, 3), 16)}
                                                onChange={(e) => {
                                                  const r = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                  const newColor = `#${r}${designSettings.imageBorderColor.slice(3)}`;
                                                  handleDesignChange('imageBorderColor', newColor);
                                                }}
                                              />
                                            </div>
                                            <div className="space-y-1">
                                              <Label className="text-xs">G</Label>
                                              <Input 
                                                type="number"
                                                min={0}
                                                max={255}
                                                value={parseInt(designSettings.imageBorderColor.slice(3, 5), 16)}
                                                onChange={(e) => {
                                                  const g = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                  const newColor = `#${designSettings.imageBorderColor.slice(1, 3)}${g}${designSettings.imageBorderColor.slice(5)}`;
                                                  handleDesignChange('imageBorderColor', newColor);
                                                }}
                                              />
                                            </div>
                                            <div className="space-y-1">
                                              <Label className="text-xs">B</Label>
                                              <Input 
                                                type="number"
                                                min={0}
                                                max={255}
                                                value={parseInt(designSettings.imageBorderColor.slice(5, 7), 16)}
                                                onChange={(e) => {
                                                  const b = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                  const newColor = `${designSettings.imageBorderColor.slice(0, 5)}${b}`;
                                                  handleDesignChange('imageBorderColor', newColor);
                                                }}
                                              />
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </PopoverContent>
                                  </Popover>
                                </div>

                                <div className="space-y-2">
                                  <Label>Image Border Radius ({designSettings.imageBorderRadius}px)</Label>
                                  <Slider
                                    value={[designSettings.imageBorderRadius]}
                                    onValueChange={([value]) => handleDesignChange('imageBorderRadius', value)}
                                    min={0}
                                    max={50}
                                    step={1}
                                  />
                                </div>
                              </>
                            )}
                          </div>

                          {/* Shadow */}
                          <div className="space-y-4 pt-4 border-t">
                            <Label className="text-sm font-medium">Shadow</Label>
                            <div className="flex items-center justify-between">
                              <Label>Shadow Effect</Label>
                              <Switch
                                checked={designSettings.shadowEffect}
                                onCheckedChange={(checked) => handleDesignChange('shadowEffect', checked)}
                              />
                            </div>

                            {designSettings.shadowEffect && (
                              <div className="space-y-2">
                                <Label>Shadow Color</Label>
                                <Popover>
                                  <PopoverTrigger>
                                    <div 
                                      className="w-8 h-8 rounded border shadow-sm cursor-pointer"
                                      style={{ backgroundColor: designSettings.shadowColor }}
                                    />
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0">
                                    <div className="p-2 space-y-3">
                                      <HexColorPicker
                                        color={designSettings.shadowColor}
                                        onChange={(color) => handleDesignChange('shadowColor', color)}
                                        style={{ width: '200px', height: '150px' }}
                                      />
                                      <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                          <Label className="w-12">Hex</Label>
                                          <Input 
                                            value={designSettings.shadowColor}
                                            onChange={(e) => {
                                              const value = e.target.value;
                                              if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                                                handleDesignChange('shadowColor', value);
                                              }
                                            }}
                                            className="font-mono"
                                          />
                                        </div>
                                        <div className="grid grid-cols-3 gap-2">
                                          <div className="space-y-1">
                                            <Label className="text-xs">R</Label>
                                            <Input 
                                              type="number"
                                              min={0}
                                              max={255}
                                              value={parseInt(designSettings.shadowColor.slice(1, 3), 16)}
                                              onChange={(e) => {
                                                const r = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                const newColor = `#${r}${designSettings.shadowColor.slice(3)}`;
                                                handleDesignChange('shadowColor', newColor);
                                              }}
                                            />
                                          </div>
                                          <div className="space-y-1">
                                            <Label className="text-xs">G</Label>
                                            <Input 
                                              type="number"
                                              min={0}
                                              max={255}
                                              value={parseInt(designSettings.shadowColor.slice(3, 5), 16)}
                                              onChange={(e) => {
                                                const g = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                const newColor = `#${designSettings.shadowColor.slice(1, 3)}${g}${designSettings.shadowColor.slice(5)}`;
                                                handleDesignChange('shadowColor', newColor);
                                              }}
                                            />
                                          </div>
                                          <div className="space-y-1">
                                            <Label className="text-xs">B</Label>
                                            <Input 
                                              type="number"
                                              min={0}
                                              max={255}
                                              value={parseInt(designSettings.shadowColor.slice(5, 7), 16)}
                                              onChange={(e) => {
                                                const b = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                                const newColor = `${designSettings.shadowColor.slice(0, 5)}${b}`;
                                                handleDesignChange('shadowColor', newColor);
                                              }}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </PopoverContent>
                                </Popover>
                              </div>
                            )}
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Social Icons Section */}
                    <AccordionItem value="socialIcons">
                      <AccordionTrigger>Social Icons</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-6">
                          <div className="space-y-2">
                            <Label>Icon Size ({designSettings.socialIconSize}px)</Label>
                            <Slider
                              value={[designSettings.socialIconSize]}
                              onValueChange={([value]) => handleDesignChange('socialIconSize', value)}
                              min={14}
                              max={24}
                              step={1}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="flex items-center justify-between">
                              Icon Color
                              <div 
                                className="w-8 h-8 rounded border shadow-sm"
                                style={{ backgroundColor: designSettings.iconColor }}
                              />
                            </Label>
                            <Popover>
                              <PopoverTrigger>
                                <div 
                                  className="w-8 h-8 rounded border shadow-sm cursor-pointer"
                                  style={{ backgroundColor: designSettings.iconColor }}
                                />
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0">
                                <div className="p-2 space-y-3">
                                  <HexColorPicker
                                    color={designSettings.iconColor}
                                    onChange={(newColor: string) => handleDesignChange('iconColor', newColor)}
                                    style={{ width: '200px', height: '150px' }}
                                  />
                                  <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                      <Label className="w-12">Hex</Label>
                                      <Input 
                                        value={designSettings.iconColor}
                                        onChange={(e) => {
                                          const value = e.target.value;
                                          if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                                            handleDesignChange('iconColor', value);
                                          }
                                        }}
                                        className="font-mono"
                                      />
                                    </div>
                                    <div className="grid grid-cols-3 gap-2">
                                      <div className="space-y-1">
                                        <Label className="text-xs">R</Label>
                                        <Input 
                                          type="number"
                                          min={0}
                                          max={255}
                                          value={parseInt(designSettings.iconColor.slice(1, 3), 16)}
                                          onChange={(e) => {
                                            const r = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                            const newColor = `#${r}${designSettings.iconColor.slice(3)}`;
                                            handleDesignChange('iconColor', newColor);
                                          }}
                                        />
                                      </div>
                                      <div className="space-y-1">
                                        <Label className="text-xs">G</Label>
                                        <Input 
                                          type="number"
                                          min={0}
                                          max={255}
                                          value={parseInt(designSettings.iconColor.slice(3, 5), 16)}
                                          onChange={(e) => {
                                            const g = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                            const newColor = `#${designSettings.iconColor.slice(1, 3)}${g}${designSettings.iconColor.slice(5)}`;
                                            handleDesignChange('iconColor', newColor);
                                          }}
                                        />
                                      </div>
                                      <div className="space-y-1">
                                        <Label className="text-xs">B</Label>
                                        <Input 
                                          type="number"
                                          min={0}
                                          max={255}
                                          value={parseInt(designSettings.iconColor.slice(5, 7), 16)}
                                          onChange={(e) => {
                                            const b = Math.max(0, Math.min(255, Number(e.target.value))).toString(16).padStart(2, '0');
                                            const newColor = `${designSettings.iconColor.slice(0, 5)}${b}`;
                                            handleDesignChange('iconColor', newColor);
                                          }}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              </>
            )}
          </div>
        </Card>

        {/* How to Use Guide */}
        <Card className="p-6 border-t-6 border-t-blue-500">
          <h2 className="text-2xl font-bold mb-6">How to Use</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-blue-600 mb-3 flex items-center gap-2">
                  <span className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-sm">1</span>
                  Fill in Your Details
                </h3>
                <p className="text-gray-600 ml-8">
                  Enter your personal and professional information in the form fields. Add your profile image to make your signature more personalized.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-blue-600 mb-3 flex items-center gap-2">
                  <span className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-sm">2</span>
                  Customize Design
                </h3>
                <p className="text-gray-600 ml-8">
                  Use the design controls to customize colors, fonts, spacing, and borders. All changes are reflected in real-time in the preview.
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-blue-600 mb-3 flex items-center gap-2">
                  <span className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-sm">3</span>
                  Advanced Customization
                </h3>
                <p className="text-gray-600 ml-8">
                  Toggle the "Edit Code" switch to directly modify HTML and CSS. Perfect for users who want complete control over their signature's code.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-blue-600 mb-3 flex items-center gap-2">
                  <span className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-sm">4</span>
                  Export Options
                </h3>
                <ul className="text-gray-600 ml-8 space-y-2">
                  <li className="flex items-center gap-2">
                    <Copy className="w-4 h-4" /> Copy HTML to clipboard
                  </li>
                  <li className="flex items-center gap-2">
                    <ImageIcon className="w-4 h-4" /> Export as PNG, JPEG, or WebP
                  </li>
                  <li className="flex items-center gap-2">
                    <Code className="w-4 h-4" /> Download as HTML file
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-100">
            <div className="flex items-start gap-3">
              <div className="mt-1">🔒</div>
              <div>
                <h4 className="font-semibold text-blue-700 mb-1">Privacy Notice</h4>
                <p className="text-sm text-blue-600">
                  All data processing happens locally in your browser. We don't store or transmit any of your personal information. Your data stays private and secure.
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Preview Section */}
      <div className="space-y-6 sticky top-4" style={{ position: 'sticky', top: '1rem', height: 'fit-content' }}>
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold">Preview</h2>
            <div className="flex items-center gap-2">
              <Switch
                checked={isEditingHTML}
                onCheckedChange={toggleHTMLEdit}
              />
              <Label>Edit Code</Label>
            </div>
          </div>
          <div className="rounded-lg overflow-hidden" style={{ 
            backgroundColor: 'transparent',
            boxShadow: 'none',
            border: 'none'
          }}>
            <div
              ref={signatureRef}
              className="p-6"
              style={{
                backgroundColor: designSettings.backgroundStyle === 'solid' 
                  ? designSettings.backgroundColor 
                  : designSettings.backgroundStyle === 'pattern' 
                  ? 'transparent' 
                  : 'transparent',
                backgroundImage: designSettings.backgroundStyle === 'gradient'
                  ? `linear-gradient(${designSettings.gradientDirection}, ${designSettings.gradientColors[0]}, ${designSettings.gradientColors[1]})`
                  : designSettings.backgroundStyle === 'pattern'
                  ? PATTERNS.find(p => p.value === designSettings.pattern)?.css
                  : 'none',
                borderRadius: `${designSettings.borderRadius}px`,
                border: designSettings.showBorder ? `${designSettings.borderWidth}px solid ${designSettings.borderColor}` : 'none',
                boxShadow: designSettings.shadowEffect ? '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' : 'none'
              }}
            >
              <div style={{
                maxWidth: '100%',
                width: '100%',
                display: 'flex',
                flexDirection: designSettings.layout === 'vertical' ? 'column' : 'row',
                gap: `${designSettings.itemSpacing}px`,
                alignItems: designSettings.layout === 'vertical' ? 'center' : 'flex-start'
              }}>
                {/* Profile Image */}
                {formData.profileImage && (
                  <div style={{
                    flexShrink: 0,
                    width: `${designSettings.imageSize}px`,
                    height: `${designSettings.imageSize}px`,
                    marginBottom: designSettings.layout === 'vertical' ? `${designSettings.itemSpacing}px` : '0',
                    transform: `translateY(${designSettings.imagePosition}px)`
                  }}>
                    <img
                      src={formData.profileImage}
                      alt="Profile"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        borderRadius: `${designSettings.imageBorderRadius}px`,
                        border: designSettings.showImageBorder ? `${designSettings.imageBorderWidth}px solid ${designSettings.imageBorderColor}` : 'none'
                      }}
                    />
                  </div>
                )}

                {/* Content */}
                <div style={{
                  flex: 1,
                  textAlign: designSettings.layout === 'vertical' ? 'center' : 'left',
                  fontFamily: designSettings.fontFamily
                }}>
                  <div style={{ marginBottom: `${designSettings.itemSpacing}px` }}>
                    <div style={{ 
                      color: designSettings.nameColor,
                      fontSize: `${designSettings.fontSize + 4}px`,
                      fontWeight: designSettings.nameFontWeight,
                      marginBottom: `${designSettings.itemSpacing/2}px`
                    }}>
                      {formData.fullName || 'Your Name'}
                    </div>
                    <div style={{ 
                      color: designSettings.accentColor,
                      fontSize: `${designSettings.fontSize}px`,
                      fontWeight: designSettings.titleFontWeight,
                      marginBottom: `${designSettings.itemSpacing/2}px`
                    }}>
                      {formData.position || 'Position'}
                    </div>
                    <div style={{ 
                      color: designSettings.companyColor,
                      fontSize: `${designSettings.fontSize}px`,
                      fontWeight: designSettings.companyFontWeight
                    }}>
                      {formData.company || 'Company'}
                    </div>
                  </div>

                  <div style={{ 
                    display: 'flex',
                    flexDirection: 'column',
                    gap: `${designSettings.itemSpacing/2}px`,
                    fontSize: `${designSettings.fontSize - 1}px`,
                    color: '#666'
                  }}>
                    {formData.email && (
                      <a 
                        href={`mailto:${formData.email}`} 
                        style={{ 
                          color: designSettings.accentColor, 
                          textDecoration: 'none',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px'
                        }}
                      >
                        <MailIcon size={16} />
                        {formData.email}
                      </a>
                    )}
                    {formData.phone && (
                      <a 
                        href={`tel:${formData.phone}`} 
                        style={{ 
                          color: designSettings.accentColor, 
                          textDecoration: 'none',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px'
                        }}
                      >
                        <PhoneIcon size={16} />
                        {formData.phone}
                      </a>
                    )}
                    {formData.website && (
                      <a 
                        href={formData.website} 
                        style={{ 
                          color: designSettings.accentColor, 
                          textDecoration: 'none',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px'
                        }} 
                        target="_blank" 
                        rel="noopener noreferrer"
                      >
                        <GlobeIcon size={16} />
                        {formData.website}
                      </a>
                    )}
                  </div>

                  {/* Social Links */}
                  {(formData.linkedinUrl || formData.twitterUrl) && (
                    <div style={{ 
                      marginTop: `${designSettings.itemSpacing}px`,
                      display: 'flex',
                      gap: `${designSettings.itemSpacing}px`,
                      justifyContent: designSettings.layout === 'vertical' ? 'center' : 'flex-start'
                    }}>
                      {formData.linkedinUrl && (
                        <a 
                          href={formData.linkedinUrl} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          style={{ 
                            color: designSettings.iconColor,
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            textDecoration: 'none'
                          }}
                        >
                          <LinkedinIcon size={designSettings.socialIconSize} style={{ color: designSettings.iconColor }} />
                          LinkedIn
                        </a>
                      )}
                      {formData.twitterUrl && (
                        <a 
                          href={formData.twitterUrl} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          style={{ 
                            color: designSettings.iconColor,
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            textDecoration: 'none'
                          }}
                        >
                          <TwitterIcon size={designSettings.socialIconSize} style={{ color: designSettings.iconColor }} />
                          Twitter
                        </a>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button onClick={copyToClipboard}>
              <Copy className="mr-2 h-4 w-4" />
              Copy HTML
            </Button>
            <Button onClick={downloadAsHTML}>
              <Code className="mr-2 h-4 w-4" />
              Download HTML
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button>
                  <ImageIcon className="mr-2 h-4 w-4" />
                  Export Image
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Format</Label>
                    <Select
                      value={exportSettings.format}
                      onValueChange={(value) => setExportSettings(prev => ({ ...prev, format: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="png">PNG (Lossless)</SelectItem>
                        <SelectItem value="jpeg">JPEG (Smaller size)</SelectItem>
                        <SelectItem value="webp">WebP (Modern format)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label>Quality</Label>
                      <span className="text-sm text-muted-foreground">
                        {Math.round(exportSettings.quality * 100)}%
                      </span>
                    </div>
                    <Slider
                      value={[exportSettings.quality * 100]}
                      onValueChange={([value]) => 
                        setExportSettings(prev => ({ ...prev, quality: value / 100 }))
                      }
                      min={10}
                      max={100}
                      step={1}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label>Scale</Label>
                      <span className="text-sm text-muted-foreground">
                        {exportSettings.scale}x
                      </span>
                    </div>
                    <Slider
                      value={[exportSettings.scale]}
                      onValueChange={([value]) => 
                        setExportSettings(prev => ({ ...prev, scale: value }))
                      }
                      min={1}
                      max={4}
                      step={0.5}
                    />
                  </div>

                  <Button className="w-full" onClick={handleSaveAsImage}>
                    Export
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </Card>
      </div>
    </div>
  );
}
