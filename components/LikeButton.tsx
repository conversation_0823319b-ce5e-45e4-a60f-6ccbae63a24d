'use client';

import { useState, useEffect } from 'react';
import { db } from '@/lib/firebase';
import { collection, addDoc, serverTimestamp, query, onSnapshot, orderBy, limit, FirestoreError, type Firestore, doc } from 'firebase/firestore';
import { Button } from '@/components/ui/button';
import { Heart } from 'lucide-react';
import { toast } from "sonner";
import { useAuth } from '@/contexts/AuthContext';

const MAX_LIKES_PER_SESSION = 5;

export function LikeButton() {
  const { user } = useAuth();
  const [likeCount, setLikeCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionLikes, setSessionLikes] = useState(0);

  useEffect(() => {
    // Check if window is defined (client-side)
    if (typeof window !== 'undefined') {
      // Get session likes from sessionStorage
      const storedLikes = sessionStorage.getItem('sessionLikes');
      if (storedLikes) {
        setSessionLikes(parseInt(storedLikes));
      }
    }

    if (!db) return;

    // Subscribe to likes collection
    const q = query(
      collection(db as Firestore, 'archived_likes'),
      orderBy('timestamp', 'desc'),
      limit(1000)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      setLikeCount(snapshot.size);
    }, (error: FirestoreError) => {
      console.error("Error fetching likes:", error);
      // Only show toast if it's not a permission error (which is expected when not authenticated)
      if (error.code !== 'permission-denied') {
        toast("Could not fetch like count", {
          description: error.message,
        });
      }
      // Set a default count if we can't fetch
      setLikeCount(0);
    });

    return () => unsubscribe();
  }, []);

  const handleLike = async () => {
    if (!db) {
      toast("Unable to connect to the database", {
        description: "Please try again later."
      });
      return;
    }

    if (!user) {
      toast("You must be logged in to like.", {
        description: "Please sign in and try again."
      });
      return;
    }

    if (sessionLikes >= MAX_LIKES_PER_SESSION) {
      toast(`You can only like ${MAX_LIKES_PER_SESSION} times per session. Thanks for your support! 🙏`, {
        description: "Try again in a new session!"
      });
      return;
    }

    setIsLoading(true);
    try {
      await addDoc(collection(db as Firestore, `users/${user.uid}/likes`), {
        timestamp: serverTimestamp(),
        path: window.location.pathname,
      });

      const newSessionLikes = sessionLikes + 1;
      setSessionLikes(newSessionLikes);
      sessionStorage.setItem('sessionLikes', newSessionLikes.toString());

      toast("Thanks for your support! 🙏", {
        description: `You have ${MAX_LIKES_PER_SESSION - newSessionLikes} likes remaining this session.`
      });
    } catch (error: unknown) {
      console.error("Error adding like:", error);
      if (error instanceof FirestoreError) {
        toast("Failed to add like", {
          description: error.message,
        });
      } else {
        toast("Failed to add like", {
          description: "Please try again."
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const remainingLikes = MAX_LIKES_PER_SESSION - sessionLikes;
  const isDisabled = isLoading || remainingLikes <= 0;

  return (
    <div className="flex flex-col items-center gap-2">
      <Button
        variant="outline"
        size="lg"
        onClick={handleLike}
        disabled={isDisabled}
        className={`gap-2 transition-all bg-rose-50 hover:bg-rose-100 dark:bg-rose-900/10 dark:hover:bg-rose-900/20 ${
          isDisabled ? 'bg-gray-50 dark:bg-gray-900/20' : 'border-rose-200 hover:border-rose-500'
        }`}
      >
        <Heart className={`h-5 w-5 transition-colors ${
          isDisabled ? 'text-gray-400' : remainingLikes < MAX_LIKES_PER_SESSION ? 'fill-rose-500 text-rose-500' : 'text-rose-500 hover:fill-rose-500'
        }`} />
        <span className={isDisabled ? 'text-gray-500' : 'text-rose-600 dark:text-rose-400'}>
          {likeCount} {likeCount === 1 ? 'Like' : 'Likes'}
          {remainingLikes > 0 && (
            <span className="text-xs ml-2 text-gray-500">
              ({remainingLikes} left)
            </span>
          )}
        </span>
      </Button>
    </div>
  );
}
