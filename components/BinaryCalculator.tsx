'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { cn } from '@/lib/utils'; // Assuming cn is available for potential future use
import { Eraser } from 'lucide-react'; // Import Eraser icon

/* ──────────────────────────────────────────────────────────
   Helpers
   ────────────────────────────────────────────────────────── */

type Field = 'bin' | 'dec' | 'pent' | 'oct' | 'hex';

const baseMap: Record<Field, number> = {
  bin: 2,
  dec: 10,
  pent: 5,
  oct: 8,
  hex: 16,
};

const validators: Record<Field, (v: string) => boolean> = {
  bin:  (v) => /^[01]*$/.test(v),
  dec:  (v) => /^\d*$/.test(v),
  pent: (v) => /^[0-4]*$/.test(v),
  oct:  (v) => /^[0-7]*$/.test(v),
  hex:  (v) => /^[0-9a-f]*$/i.test(v),
};

const toDec   = (value: string, base: number) => value ? parseInt(value, base) : NaN;
const fromDec = (n: number, base: number)   => isNaN(n) ? '' : n.toString(base).toUpperCase(); // Handle NaN case in fromDec

/* ──────────────────────────────────────────────────────────
   Component
   ────────────────────────────────────────────────────────── */

export function BinaryCalculator() {
  const [inputs,  setInputs]  = useState<Record<Field, string>>({
    bin: '', dec: '', pent: '', oct: '', hex: '',
  });
  const [decimal, setDecimal] = useState<number | null>(null);

  /* ── Generic onChange ─────────────────────────────── */
  const handleChange =
    (field: Field) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const raw = e.target.value.trim();

      if (!validators[field](raw)) {
        // Only show error if input is not empty (allows backspacing)
        if (raw) {
            toast.error(`Invalid ${field === 'pent' ? 'Base-5 (0-4)' : field === 'oct' ? 'Octal (0-7)' : field === 'bin' ? 'Binary (0-1)' : field === 'hex' ? 'Hex (0-9, A-F)' : 'Decimal (0-9)'} number`);
        }
        // Keep the invalid input temporarily visible for correction
        setInputs((prev) => ({ ...prev, [field]: raw }));
        // If partially invalid, don't clear other fields yet
        return;
      }

      // If input becomes empty, clear everything
      if (!raw) {
        setDecimal(null);
        setInputs({ bin: '', dec: '', pent: '', oct: '', hex: '' });
        return;
      }

      const dec = toDec(raw, baseMap[field]);

      // Handle potential NaN from parseInt (e.g., very large numbers might exceed limits)
      if (Number.isNaN(dec)) {
        setDecimal(null); // Clear decimal state
        // Update only the current field with raw value, clear others as conversion failed
        setInputs({
            bin: field === 'bin' ? raw : '',
            dec: field === 'dec' ? raw : '',
            pent: field === 'pent' ? raw : '',
            oct: field === 'oct' ? raw : '',
            hex: field === 'hex' ? raw : '',
            [field]: raw // Ensure current field keeps raw input
         });
        toast.error('Unable to convert number (might be too large)');
        return;
      }

      setDecimal(dec);
      setInputs({
        bin:  fromDec(dec, 2),
        dec:  fromDec(dec, 10),
        pent: fromDec(dec, 5),
        oct:  fromDec(dec, 8),
        hex:  fromDec(dec, 16),
      });
    };

  /* ── Clipboard helper ─────────────────────────────── */
  const copyToClipboard = async (text: string, label: string) => {
    if (!text) return; // Don't copy empty strings
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${label} value copied to clipboard`);
    } catch {
      toast.error('Failed to copy to clipboard');
    }
  };

  /* ── Clear All Handler ────────────────────────────── */
   const handleClear = () => {
     setInputs({ bin: '', dec: '', pent: '', oct: '', hex: '' });
     setDecimal(null);
   };

  /* ── Render ───────────────────────────────────────── */
  return (
    <div className="space-y-8 relative">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden -z-10">
        <div className="absolute top-10 right-10 w-32 h-32 bg-blue-400/10 rounded-full animate-pulse"></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 bg-purple-400/10 rounded-full animate-pulse delay-700"></div>
        <div className="absolute top-1/3 left-1/4 w-16 h-16 bg-pink-400/10 rounded-full animate-ping delay-300 duration-1000"></div>
        <div className="absolute bottom-1/3 right-1/4 w-16 h-16 bg-yellow-400/10 rounded-full animate-ping delay-500 duration-1000"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      {/* Main Binary and Decimal Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Binary */}
        <div className="relative overflow-hidden group transform transition-all duration-300 hover:scale-[1.02]">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl opacity-90"></div>
          <div className="absolute inset-0.5 bg-white dark:bg-gray-900 rounded-2xl"></div>
          <div className="relative p-5">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 text-sm font-bold mr-2 shadow-inner">
                01
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Binary (Base‑2)
              </h3>
            </div>
            <div className="relative">
              <Input
                id="binary"
                value={inputs.bin}
                onChange={handleChange('bin')}
                placeholder="Enter binary number"
                className={cn(
                  "font-mono text-base transition-all duration-300 border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-900/20 focus-visible:ring-blue-400",
                  inputs.bin !== '' && "border-blue-400 dark:border-blue-600 ring-1 ring-blue-400 dark:ring-blue-600"
                )}
                spellCheck="false"
              />
              <div className="absolute right-2 top-1/2 -translate-y-1/2">
                <Button
                  variant={inputs.bin !== '' ? "default" : "ghost"}
                  size="sm"
                  onClick={() => copyToClipboard(inputs.bin, 'Binary')}
                  disabled={!inputs.bin}
                  className={cn(
                    "h-7 rounded-full transition-all",
                    inputs.bin !== '' ?
                      "bg-blue-600 hover:bg-blue-700 text-white" :
                      "text-blue-600 hover:text-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/30"
                  )}
                >
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Decimal */}
        <div className="relative overflow-hidden group transform transition-all duration-300 hover:scale-[1.02]">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl opacity-90"></div>
          <div className="absolute inset-0.5 bg-white dark:bg-gray-900 rounded-2xl"></div>
          <div className="relative p-5">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-purple-600 dark:text-purple-400 text-sm font-bold mr-2 shadow-inner">
                10
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Decimal (Base‑10)
              </h3>
            </div>
            <div className="relative">
              <Input
                id="decimal"
                value={inputs.dec}
                onChange={handleChange('dec')}
                placeholder="Enter decimal number"
                className={cn(
                  "font-mono text-base transition-all duration-300 border-purple-200 dark:border-purple-800 bg-purple-50/50 dark:bg-purple-900/20 focus-visible:ring-purple-400",
                  inputs.dec !== '' && "border-purple-400 dark:border-purple-600 ring-1 ring-purple-400 dark:ring-purple-600"
                )}
                spellCheck="false"
              />
              <div className="absolute right-2 top-1/2 -translate-y-1/2">
                <Button
                  variant={inputs.dec !== '' ? "default" : "ghost"}
                  size="sm"
                  onClick={() => copyToClipboard(inputs.dec, 'Decimal')}
                  disabled={!inputs.dec}
                  className={cn(
                    "h-7 rounded-full transition-all",
                    inputs.dec !== '' ?
                      "bg-purple-600 hover:bg-purple-700 text-white" :
                      "text-purple-600 hover:text-purple-700 hover:bg-purple-100 dark:hover:bg-purple-900/30"
                  )}
                >
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Secondary Number Systems */}
      <div className="relative overflow-hidden rounded-2xl">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 opacity-80"></div>
        <div className="relative p-6">
          <h3 className="text-lg font-bold mb-5 inline-flex items-center bg-gradient-to-r from-gray-700 to-gray-900 dark:from-gray-300 dark:to-gray-100 bg-clip-text text-transparent">
            <svg className="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            Additional Number Systems
          </h3>
          <div className="grid gap-4 md:grid-cols-3">
            {/* Pentary */}
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
              <div className="flex items-center mb-2">
                <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center text-green-600 dark:text-green-400 text-xs font-bold mr-2">
                  5
                </div>
                <h4 className="font-medium text-green-700 dark:text-green-400">Pentary (Base‑5)</h4>
              </div>
              <div className="relative">
                <Input
                  id="pentary"
                  value={inputs.pent}
                  onChange={handleChange('pent')}
                  placeholder="Enter base‑5 number"
                  className={cn(
                    "font-mono text-sm transition-all duration-300 border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/20 focus-visible:ring-green-400",
                    inputs.pent !== '' && "border-green-400 dark:border-green-600 ring-1 ring-green-400 dark:ring-green-600"
                  )}
                  spellCheck="false"
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2">
                  <Button
                    variant={inputs.pent !== '' ? "default" : "ghost"}
                    size="sm"
                    onClick={() => copyToClipboard(inputs.pent, 'Pentary')}
                    disabled={!inputs.pent}
                    className={cn(
                      "h-6 w-6 p-0 rounded-full transition-all",
                      inputs.pent !== '' ?
                        "bg-green-600 hover:bg-green-700 text-white" :
                        "text-green-600 hover:text-green-700 hover:bg-green-100 dark:hover:bg-green-900/30"
                    )}
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                    </svg>
                  </Button>
                </div>
              </div>
            </div>

            {/* Octal */}
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
              <div className="flex items-center mb-2">
                <div className="w-6 h-6 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-400 text-xs font-bold mr-2">
                  8
                </div>
                <h4 className="font-medium text-yellow-700 dark:text-yellow-400">Octal (Base‑8)</h4>
              </div>
              <div className="relative">
                <Input
                  id="octal"
                  value={inputs.oct}
                  onChange={handleChange('oct')}
                  placeholder="Enter octal number"
                  className={cn(
                    "font-mono text-sm transition-all duration-300 border-yellow-200 dark:border-yellow-800 bg-yellow-50/50 dark:bg-yellow-900/20 focus-visible:ring-yellow-400",
                    inputs.oct !== '' && "border-yellow-400 dark:border-yellow-600 ring-1 ring-yellow-400 dark:ring-yellow-600"
                  )}
                  spellCheck="false"
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2">
                  <Button
                    variant={inputs.oct !== '' ? "default" : "ghost"}
                    size="sm"
                    onClick={() => copyToClipboard(inputs.oct, 'Octal')}
                    disabled={!inputs.oct}
                    className={cn(
                      "h-6 w-6 p-0 rounded-full transition-all",
                      inputs.oct !== '' ?
                        "bg-yellow-600 hover:bg-yellow-700 text-white" :
                        "text-yellow-600 hover:text-yellow-700 hover:bg-yellow-100 dark:hover:bg-yellow-900/30"
                    )}
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                    </svg>
                  </Button>
                </div>
              </div>
            </div>

            {/* Hexadecimal */}
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
              <div className="flex items-center mb-2">
                <div className="w-6 h-6 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center text-red-600 dark:text-red-400 text-xs font-bold mr-2">
                  16
                </div>
                <h4 className="font-medium text-red-700 dark:text-red-400">Hexadecimal (Base‑16)</h4>
              </div>
              <div className="relative">
                <Input
                  id="hexadecimal"
                  value={inputs.hex}
                  onChange={handleChange('hex')}
                  placeholder="Enter hexadecimal number"
                  className={cn(
                    "font-mono text-sm transition-all duration-300 border-red-200 dark:border-red-800 bg-red-50/50 dark:bg-red-900/20 focus-visible:ring-red-400",
                    inputs.hex !== '' && "border-red-400 dark:border-red-600 ring-1 ring-red-400 dark:ring-red-600"
                  )}
                  spellCheck="false"
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2">
                  <Button
                    variant={inputs.hex !== '' ? "default" : "ghost"}
                    size="sm"
                    onClick={() => copyToClipboard(inputs.hex, 'Hexadecimal')}
                    disabled={!inputs.hex}
                    className={cn(
                      "h-6 w-6 p-0 rounded-full transition-all",
                      inputs.hex !== '' ?
                        "bg-red-600 hover:bg-red-700 text-white" :
                        "text-red-600 hover:text-red-700 hover:bg-red-100 dark:hover:bg-red-900/30"
                    )}
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                    </svg>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Clear Button Section */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          onClick={handleClear}
          disabled={decimal === null && Object.values(inputs).every(v => !v)}
          className="rounded-full px-6 py-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 dark:border-gray-700 dark:hover:border-gray-600 dark:hover:bg-gray-800/70 transition-all duration-200 shadow-sm hover:shadow group"
        >
          <Eraser className="mr-2 h-4 w-4 group-hover:animate-bounce" /> Clear All
        </Button>
      </div>

      {/* Tips Section */}
      <div className="relative overflow-hidden rounded-2xl">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 opacity-80"></div>
        <div className="relative p-6">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center shadow-inner">
              <svg className="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Quick Reference
            </h3>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 shadow-md">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Primary Number Systems</h4>
              <ul className="space-y-3">
                <li className="flex items-center gap-2 group">
                  <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 text-xs font-bold group-hover:scale-110 transition-transform">
                    01
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Binary numbers use only 0 and 1</span>
                </li>
                <li className="flex items-center gap-2 group">
                  <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-purple-600 dark:text-purple-400 text-xs font-bold group-hover:scale-110 transition-transform">
                    10
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Decimal numbers use 0‑9</span>
                </li>
              </ul>
            </div>

            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 shadow-md">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Additional Number Systems</h4>
              <ul className="space-y-3">
                <li className="flex items-center gap-2 group">
                  <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center text-green-600 dark:text-green-400 text-xs font-bold group-hover:scale-110 transition-transform">
                    5
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Pentary numbers use 0‑4</span>
                </li>
                <li className="flex items-center gap-2 group">
                  <div className="w-6 h-6 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-400 text-xs font-bold group-hover:scale-110 transition-transform">
                    8
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Octal numbers use 0‑7</span>
                </li>
                <li className="flex items-center gap-2 group">
                  <div className="w-6 h-6 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center text-red-600 dark:text-red-400 text-xs font-bold group-hover:scale-110 transition-transform">
                    16
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Hexadecimal numbers use 0‑9 and A‑F</span>
                </li>
              </ul>
            </div>
          </div>

          {decimal !== null && (
            <div className="mt-5 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl p-4 shadow-md border border-blue-100/50 dark:border-blue-900/30 transform transition-all duration-300 hover:shadow-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="h-5 w-5 mr-2 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Current decimal value:</span>
                </div>
                <span className="font-mono font-bold text-blue-600 dark:text-blue-400 text-lg">{decimal.toLocaleString()}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/* ──────────────────────────────────────────────────────────
   Small sub‑component for an input + copy button
   ────────────────────────────────────────────────────────── */

interface FieldBlockProps {
  id: string;
  label: string;
  value: string;
  placeholder: string;
  onChange: React.ChangeEventHandler<HTMLInputElement>;
  onCopy: () => void;
  // isError?: boolean; // Removed for simplicity now, can be added back later
}

function FieldBlock({
  id,
  label,
  icon,
  value,
  placeholder,
  onChange,
  onCopy,
  activeField = false,
  compact = false,
}: FieldBlockProps & {
  icon?: React.ReactNode;
  activeField?: boolean;
  compact?: boolean;
}) {
  return (
    <div className={cn("space-y-2", compact && "space-y-1")}>
      <div className="flex items-center gap-2">
        {icon}
        <Label
          htmlFor={id}
          className={cn(
            compact ? "text-sm" : "text-base font-medium",
            activeField && "text-blue-600 dark:text-blue-400"
          )}
        >
          {label}
        </Label>
      </div>
      <div className="flex gap-2">
        <Input
          id={id}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className={cn(
            "font-mono transition-all duration-300",
            activeField ? "border-blue-300 dark:border-blue-700 ring-1 ring-blue-300 dark:ring-blue-700" : "",
            compact ? "text-sm" : ""
          )}
          spellCheck="false" // Prevent spellcheck on mono inputs
        />
        <Button
          variant={activeField ? "default" : "outline"}
          onClick={onCopy}
          disabled={!value} // Disable copy if field is empty
          aria-label={`Copy ${label} value`} // Accessibility
          className={cn(
            "transition-all duration-300",
            activeField ?
              "bg-blue-500 hover:bg-blue-600 text-white" :
              "hover:bg-blue-50 dark:hover:bg-blue-900/30",
            compact ? "text-xs px-2" : ""
          )}
        >
          {compact ? "Copy" : (
            <>
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
              </svg>
              Copy
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
