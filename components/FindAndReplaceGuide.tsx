'use client';

import {
  <PERSON>,
  Replace,
  FileText,
  Settings2,
  Download,
  Copy,
  MessageSquare,
  Keyboard,
  Info,
  CheckCircle2,
  MousePointer<PERSON>lick,
  Regex,
  Align<PERSON>eft,
  Quote
} from "lucide-react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from "@/components/ui/tooltip";

const FindAndReplaceGuide = () => {
  return (
    <TooltipProvider>
      <section className="py-12 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              How to Use Find and Replace
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              Master our powerful text search and replace features
            </p>
            
            {/* Info Dialog Button */}
            <Dialog>
              <DialogTrigger asChild>
                <button className="mt-4 inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 transition-colors shadow-sm">
                  <Info className="h-4 w-4 text-blue-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-300">Click for detailed instructions</span>
                </button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Detailed Usage Guide</DialogTitle>
                  <DialogDescription>
                    Learn how to effectively use the Find and Replace tool
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-blue-600 dark:text-blue-400 flex items-center gap-2">
                      <AlignLeft className="h-4 w-4" />
                      Text Input
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Works with any length of text, from single words to entire paragraphs. Perfect for documents, code, or any text content.
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-semibold text-purple-600 dark:text-purple-400 flex items-center gap-2">
                      <Quote className="h-4 w-4" />
                      Search Capabilities
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      • Search for words, phrases, or complete sentences<br />
                      • Example: "The quick brown fox" will find that exact phrase<br />
                      • Works with special characters and punctuation<br />
                      • Toggle case sensitivity for precise matching
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-semibold text-pink-600 dark:text-pink-400 flex items-center gap-2">
                      <Settings2 className="h-4 w-4" />
                      Advanced Tips
                    </h3>
                    <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                      <li>• Use regex for pattern matching</li>
                      <li>• Replace multiple occurrences at once</li>
                      <li>• Preview changes before applying</li>
                      <li>• Track number of matches found</li>
                    </ul>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Basic Usage */}
            <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-4">
                <MousePointerClick className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-blue-600 dark:text-blue-400 font-semibold text-lg mb-2">Basic Usage</div>
              <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
                <li className="flex items-start gap-2">
                  <FileText className="h-4 w-4 mt-1 text-blue-500" />
                  <span>
                    <strong>Enter Text:</strong> Paste your content - works with words, phrases, or paragraphs
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Search className="h-4 w-4 mt-1 text-blue-500" />
                  <span>
                    <strong>Find:</strong> Enter any text or sentence to search
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Replace className="h-4 w-4 mt-1 text-blue-500" />
                  <span>
                    <strong>Replace:</strong> Type the replacement text
                  </span>
                </li>
              </ul>
            </div>

            {/* Advanced Features */}
            <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-4">
                <Settings2 className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="text-purple-600 dark:text-purple-400 font-semibold text-lg mb-2">Advanced Features</div>
              <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
                <li className="flex items-start gap-2">
                  <Dialog>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <DialogTrigger asChild>
                          <button className="mt-1 p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                            <CheckCircle2 className="h-4 w-4 text-purple-500" />
                          </button>
                        </DialogTrigger>
                      </TooltipTrigger>
                      <TooltipContent side="right" className="max-w-[200px] p-2">
                        <p>Click for detailed examples of case sensitivity</p>
                      </TooltipContent>
                    </Tooltip>
                    <DialogContent className="sm:max-w-[425px]">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2 text-purple-600">
                          <CheckCircle2 className="h-5 w-5" />
                          Case Sensitivity Guide
                        </DialogTitle>
                        <DialogDescription>
                          Understanding how case-sensitive search works
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 mt-4">
                        <div className="space-y-2">
                          <h4 className="font-medium text-purple-600">When Case Sensitive is ON:</h4>
                          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md space-y-2 text-sm">
                            <p>✓ "Hello" matches "Hello"</p>
                            <p>✗ "Hello" doesn't match "hello"</p>
                            <p>✗ "HELLO" doesn't match "Hello"</p>
                            <p>✓ "The Quick Brown Fox" only matches exact case</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <h4 className="font-medium text-purple-600">When Case Sensitive is OFF:</h4>
                          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md space-y-2 text-sm">
                            <p>✓ "hello" matches "Hello", "HELLO", "hello"</p>
                            <p>✓ "The Quick Brown Fox" matches regardless of case</p>
                            <p>✓ "Python" matches "PYTHON", "python", "Python"</p>
                          </div>
                        </div>
                        <div className="mt-4 text-sm text-gray-600 dark:text-gray-300">
                          <p><strong>Tip:</strong> Use case sensitivity when you need exact matching, turn it off for more flexible searches.</p>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                  <span>
                    <strong>Case Sensitivity:</strong> Match exact letter casing when needed
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Regex className="h-4 w-4 mt-1 text-purple-500" />
                  <span>
                    <strong>Pattern Matching:</strong> Use regex for advanced search
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Quote className="h-4 w-4 mt-1 text-purple-500" />
                  <span>
                    <strong>Full Sentences:</strong> Find and replace complete phrases
                  </span>
                </li>
              </ul>
            </div>

            {/* Output Options */}
            <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <div className="w-12 h-12 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-4">
                <Download className="w-6 h-6 text-pink-600 dark:text-pink-400" />
              </div>
              <div className="text-pink-600 dark:text-pink-400 font-semibold text-lg mb-2">Output Options</div>
              <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
                <li className="flex items-start gap-2">
                  <Copy className="h-4 w-4 mt-1 text-pink-500" />
                  <span>
                    <strong>Copy:</strong> Save results to clipboard
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Download className="h-4 w-4 mt-1 text-pink-500" />
                  <span>
                    <strong>Download:</strong> Export as text file
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <MessageSquare className="h-4 w-4 mt-1 text-pink-500" />
                  <span>
                    <strong>Preview:</strong> See changes in real-time
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </TooltipProvider>
  );
};

export default FindAndReplaceGuide;