'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { db, auth } from '@/lib/firebase';
import { collection, query, where, orderBy, getDocs, deleteDoc, doc, onSnapshot, limit, Timestamp } from 'firebase/firestore';
import { User } from 'firebase/auth';
import { toast } from 'sonner';
import { QRCodeSVG } from 'qrcode.react';
import { jsPDF } from 'jspdf';
import { QRCodeDetailsDialog } from './QRCodeDetailsDialog';
import { format, subDays } from 'date-fns';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer, 
  PieChart, 
  Pie 
} from 'recharts';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { MoreHorizontal, BarChart2, Download, Trash, QrCode, Scan, Calendar, Clock } from 'lucide-react';
import { AlertTriangle } from 'lucide-react';
import Link from 'next/link';

interface ScanData {
  date: string;
  scans: number;
  device?: string;
  location?: string;
}

interface QRCodeScanStats {
  scans: ScanData[];
  deviceStats: {
    mobile: number;
    desktop: number;
  };
  hourlyStats: {
    [key: string]: number;
  };
  lastDeviceType: string;
  lastUserAgent: string;
  lastScanTime: string | null;
  totalScans: number;
}

interface QRCodeData {
  name: string;
  id: string;
  userId: string;
  url: string;
  content: string;
  title: string;
  type: string;
  description?: string;
  backgroundColor?: string;
  foregroundColor?: string;
  frameColor?: string;
  location?: string;
  isPrivate: boolean;
  createdAt: any; // Firestore Timestamp
  lastScanned: Date | null;
  size?: string;
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  labelFont?: string;
  scanCount?: number;
}

export function QRCodeAnalytics() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [qrCodes, setQrCodes] = useState<QRCodeData[]>([]);
  const [totalGenerated, setTotalGenerated] = useState(0);
  const [loading, setLoading] = useState(true);
  const [qrToDelete, setQrToDelete] = useState<QRCodeData | null>(null);
  const [scanData, setScanData] = useState<Record<string, QRCodeScanStats>>({});
  const [selectedQrCode, setSelectedQrCode] = useState<QRCodeData | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [authChecked, setAuthChecked] = useState(false);
  const [scanDataLoading, setScanDataLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      console.log('Auth state changed:', firebaseUser?.uid);
      setAuthChecked(true);
      
      if (!firebaseUser) {
        if (authChecked) {
          router.replace('/auth/signin');
        }
        return;
      }

      // Get the ID token and set it in the Authorization header
      const token = await firebaseUser.getIdToken();
      if (token) {
        const headers = new Headers();
        headers.append('Authorization', `Bearer ${token}`);
        
        // Make a request to verify auth
        try {
          const response = await fetch('/qr-code/analytics', {
            headers
          });
          
          if (!response.ok) {
            throw new Error('Authentication failed');
          }
        } catch (error) {
          console.error('Auth verification failed:', error);
          router.replace('/auth/signin');
          return;
        }
      }
      
      setUser(firebaseUser);
      // Get QR codes from nested collection
      const qrCodesRef = collection(db, 'users', firebaseUser.uid, 'qrcodes');
      console.log('Fetching QR codes for user:', firebaseUser.uid);
      const q = query(qrCodesRef, orderBy('createdAt', 'desc'));
      
      const unsubscribeQrCodes = onSnapshot(q, (snapshot) => {
        console.log('🔵 QR codes snapshot received:', {
          size: snapshot.size,
          path: q.toString()
        });
        const qrCodesData: QRCodeData[] = [];
        snapshot.forEach((doc) => {
          console.log('📱 Processing QR code:', {
            id: doc.id,
            data: doc.data(),
            path: doc.ref.path
          });
          const data = doc.data();
          try {
            // Convert Firestore timestamp to Date
            const createdAt = data.createdAt?.toDate() || new Date();
            const lastScanned = data.lastScanned?.toDate() || null;

            qrCodesData.push({
              id: doc.id,
              userId: firebaseUser.uid,
              type: data.type || 'url',
              scanCount: data.scanCount || 0,
              url: data.url || data.content || '', // Use content as fallback
              content: data.content || data.url || '', // Use url as fallback
              title: data.title || data.name || '', // Use name as fallback
              description: data.description || '',
              backgroundColor: data.backgroundColor || '#FFFFFF',
              foregroundColor: data.foregroundColor || '#000000',
              frameColor: data.frameColor,
              location: data.location || '',
              isPrivate: data.isPrivate || false,
              createdAt: createdAt,
              lastScanned: lastScanned,
              size: data.size || '256px',
              errorCorrectionLevel: data.errorCorrectionLevel || 'M',
              labelFont: data.labelFont,
            } as QRCodeData);
          } catch (error) {
            console.error('Error processing QR code data:', error, {
              docId: doc.id,
              data: data
            });
          }
        });
        setQrCodes(qrCodesData);
        setLoading(false);
      });

      // Get scan data for each QR code
      const unsubscribeScanData = onSnapshot(qrCodesRef, (snapshot) => {
        console.log('🔄 Starting scan data fetch');
        setScanDataLoading(true);
        
        const scanDataPromises = qrCodes.map(async (doc) => {
          if (!user) {
            console.error('User is not authenticated');
            return {
              [doc.id]: {
                scans: [],
                deviceStats: { mobile: 0, desktop: 0 },
                hourlyStats: {},
                lastDeviceType: '',
                lastUserAgent: '',
                lastScanTime: null,
                totalScans: 0
              }
            };
          }

          try {
            const scanRef = collection(db, 'users', user.uid, 'qrcodes', doc.id, 'scans');
            const scanSnapshot = await getDocs(scanRef);
            
            const scans: ScanData[] = [];
            const deviceStats = { mobile: 0, desktop: 0 };
            const hourlyStats: { [key: string]: number } = {};
            let lastDeviceType = '';
            let lastUserAgent = '';
            let lastScanTime: Date | null = null;
            let totalScans = 0;

            scanSnapshot.forEach((scan) => {
              const data = scan.data();
              const timestamp = data.timestamp?.toDate();
              const scanDate = timestamp instanceof Date ? timestamp : new Date();
              const formattedDate = format(scanDate, 'yyyy-MM-dd');
              const hour = format(scanDate, 'HH:00');
              
              // Update scan counts
              const existingScan = scans.find(s => s.date === formattedDate);
              if (existingScan) {
                existingScan.scans++;
              } else {
                scans.push({
                  date: formattedDate,
                  scans: 1,
                  device: data.deviceType || undefined,
                  location: data.location || undefined
                });
              }

              // Update device stats
              if (data.deviceType === 'mobile') deviceStats.mobile++;
              if (data.deviceType === 'desktop') deviceStats.desktop++;

              // Update hourly stats
              hourlyStats[hour] = (hourlyStats[hour] || 0) + 1;

              // Update last scan info
              if (!lastScanTime || scanDate > lastScanTime) {
                lastScanTime = scanDate;
                lastDeviceType = data.deviceType || '';
                lastUserAgent = data.userAgent || '';
              }

              totalScans++;
            });

            const stats: QRCodeScanStats = {
              scans: scans.sort((a, b) => a.date.localeCompare(b.date)),
              deviceStats,
              hourlyStats,
              lastDeviceType,
              lastUserAgent,
              lastScanTime: (lastScanTime as unknown as Date)?.toISOString() || null,
              totalScans
            };

            return {
              [doc.id]: stats
            };
          } catch (error) {
            console.error(`Error processing QR code ${doc.id}:`, error);
            return {
              [doc.id]: {
                scans: [],
                deviceStats: { mobile: 0, desktop: 0 },
                hourlyStats: {},
                lastDeviceType: '',
                lastUserAgent: '',
                lastScanTime: null,
                totalScans: 0
              }
            };
          }
        });

        Promise.all(scanDataPromises).then((results) => {
          const combinedData = results.reduce((acc, curr) => ({ ...acc, ...curr }), {});
          console.log('🎯 Setting scan data:', combinedData);
          setScanData(combinedData);
          setScanDataLoading(false);
        }).catch(error => {
          console.error('❌ Error processing scan data:', error);
          setScanData({});
          setScanDataLoading(false);
        });
      });

      return () => {
        unsubscribeQrCodes();
        unsubscribeScanData();
      };
    });

    return () => {
      console.log('Cleaning up auth subscription');
      unsubscribe();
    };
  }, [router]);

  // Show loading state while checking auth
  if (!authChecked || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  const handleDelete = async (qrCode: QRCodeData) => {
    setQrToDelete(qrCode);
  };

  const confirmDelete = async () => {
    if (!qrToDelete || !user) return;

    try {
      await deleteDoc(doc(db, 'users', user.uid, 'qrcodes', qrToDelete.id));
      setQrCodes(qrCodes.filter(qr => qr.id !== qrToDelete.id));
      setTotalGenerated(prev => prev - 1);
      toast.success('QR code deleted successfully');
    } catch (error) {
      console.error('Error deleting QR code:', error);
      toast.error('Failed to delete QR code');
    } finally {
      setQrToDelete(null);
    }
  };

  const handleDownload = async (qrCode: QRCodeData) => {
    const qrCodeElement = document.getElementById(`qr-${qrCode.id}`);
    if (!qrCodeElement) {
      toast.error('QR code element not found');
      return;
    }

    try {
      // Get the SVG element and its dimensions
      const svg = qrCodeElement as unknown as SVGElement;
      if (!svg) {
        toast.error('SVG element not found');
        return;
      }

      // Create a temporary container
      const container = document.createElement('div');
      container.innerHTML = svg.outerHTML;
      const tempSvg = container.firstChild as SVGElement;
      
      // Set explicit dimensions
      tempSvg.setAttribute('width', '1024');
      tempSvg.setAttribute('height', '1024');

      // Convert SVG to string with proper dimensions
      const svgString = new XMLSerializer().serializeToString(tempSvg);
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(svgBlob);

      // Create image for conversion
      const img = new Image();
      img.onload = () => {
        // Create canvas with proper dimensions
        const canvas = document.createElement('canvas');
        canvas.width = 1024;
        canvas.height = 1024;
        const ctx = canvas.getContext('2d');
        
        if (ctx) {
          // Fill white background
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          
          // Draw QR code
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          // Convert to PNG and trigger download
          const pngUrl = canvas.toDataURL('image/png');
          const a = document.createElement('a');
          a.href = pngUrl;
          a.download = `qr-code-${qrCode.type}-${Date.now()}.png`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          
          // Cleanup
          URL.revokeObjectURL(url);
          toast.success('QR code downloaded successfully!');
        }
      };

      img.onerror = () => {
        toast.error('Failed to generate QR code image');
        URL.revokeObjectURL(url);
      };

      img.src = url;
    } catch (error) {
      console.error('Error downloading QR code:', error);
      toast.error('Failed to download QR code');
    }
  };

  const handleViewAnalytics = (qrCode: QRCodeData) => {
    console.log('Viewing analytics for QR code:', qrCode.id);
    console.log('Raw scan data from Firebase:', scanData[qrCode.id]);
    console.log('Device stats:', scanData[qrCode.id]?.deviceStats);
    console.log('Hourly stats:', scanData[qrCode.id]?.hourlyStats);
    console.log('Last scan info:', {
      deviceType: scanData[qrCode.id]?.lastDeviceType,
      userAgent: scanData[qrCode.id]?.lastUserAgent,
      lastScanned: scanData[qrCode.id]?.lastScanTime
    });
    setSelectedQrCode(qrCode);
    setOpenDialog(true);
  };

  const truncateText = (text: string | undefined | null, length: number) => {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
  };

  if (!user) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Beta Notice */}
      <div className="p-2 bg-purple-800 border border-blue-200/40 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-white" />
            <div className="flex items-center gap-4">
              <p className="text-white">
                QR Code Analytics is currently in beta. You might encounter some issues with the download functionality.
              </p>
              <Link 
                href="/contact?type=issue" 
                className="px-3 py-1 bg-blue-900 hover:bg-blue-500/40 rounded-md transition text-white text-sm whitespace-nowrap"
              >
                Report an Issue
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Header with Generate Button */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">QR Code Analytics</h1>
        <button
          onClick={() => router.push('/qr-code')}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Generate New QR Code
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <Card className="p-6 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-blue-800">Total QR Codes</h3>
              <p className="text-2xl font-bold text-blue-900">{qrCodes.length}</p>
            </div>
            <div className="p-3 bg-blue-200 rounded-full">
              <QrCode className="w-6 h-6 text-blue-700" />
            </div>
          </div>
        </Card>
        <Card className="p-6 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-purple-800">Total Scans</h3>
              <p className="text-2xl font-bold text-purple-900">
                {qrCodes.reduce((total, qr) => total + (qr.scanCount || 0), 0)}
              </p>
            </div>
            <div className="p-3 bg-purple-200 rounded-full">
              <Scan className="w-6 h-6 text-purple-700" />
            </div>
          </div>
        </Card>
        <Card className="p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-green-800">Average Scans</h3>
              <p className="text-2xl font-bold text-green-900">
                {qrCodes.length
                  ? Math.round(
                      qrCodes.reduce((total, qr) => total + (qr.scanCount || 0), 0) /
                        qrCodes.length
                    )
                  : 0}
              </p>
            </div>
            <div className="p-3 bg-green-200 rounded-full">
              <BarChart2 className="w-6 h-6 text-green-700" />
            </div>
          </div>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card className="p-6 border-t-4 border-t-blue-500 shadow-lg">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">Scan Activity</h3>
          {loading ? (
            <div className="flex items-center justify-center h-[300px]">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : Object.values(scanData).flat().length === 0 ? (
            <div className="flex flex-col items-center justify-center h-[300px] text-gray-500">
              <LineChart className="w-12 h-12 mb-2 text-gray-400" />
              <p>No scan data available yet</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={Object.values(scanData).flat().map((data) => data.scans)}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis dataKey="date" stroke="#6b7280" />
                <YAxis stroke="#6b7280" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#fff',
                    border: '1px solid #e5e7eb',
                    borderRadius: '6px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                  }}
                />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="scans" 
                  stroke="#3b82f6" 
                  strokeWidth={3}
                  name="Number of Scans"
                  dot={{ stroke: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#2563eb', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </Card>
        
        <Card className="p-6 border-t-4 border-t-purple-500 shadow-lg">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">QR Code Types</h3>
          {loading ? (
            <div className="flex items-center justify-center h-[300px]">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>
          ) : qrCodes.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-[300px] text-gray-500">
              <PieChart className="w-12 h-12 mb-2 text-gray-400" />
              <p>No QR codes created yet</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={Object.entries(
                    qrCodes.reduce((acc, qr) => {
                      acc[qr.type] = (acc[qr.type] || 0) + 1;
                      return acc;
                    }, {} as Record<string, number>)
                  ).map(([name, value], index) => ({ 
                    name, 
                    value,
                    fill: [
                      '#3b82f6', // blue
                      '#8b5cf6', // purple
                      '#10b981', // green
                      '#f59e0b', // yellow
                      '#ef4444'  // red
                    ][index % 5]
                  }))}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #e5e7eb',
                    borderRadius: '6px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                  }}
                />
                <Legend verticalAlign="bottom" height={36} />
              </PieChart>
            </ResponsiveContainer>
          )}
        </Card>
      </div>

      {/* QR Codes List */}
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800">Your QR Codes</h2>
          <p className="text-sm text-gray-500">{qrCodes.length} codes generated</p>
        </div>
        <div className="space-y-4">
          {qrCodes.map((qrCode) => (
            <Card key={qrCode.id} className="p-6 hover:shadow-lg transition-shadow duration-200 border-l-4 border-l-blue-500">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="bg-white p-1 rounded-lg shadow-sm">
                    <QRCodeSVG
                      id={`qr-${qrCode.id}`}
                      value={qrCode.url || ''}
                      size={60}
                      level="M"
                      fgColor={qrCode.foregroundColor || "#000000"}
                      bgColor={qrCode.backgroundColor || "#FFFFFF"}
                    />
                  </div>
                  <div className="space-y-2">
                    <div>
                      <h3 className="font-semibold text-gray-800">{qrCode.title || qrCode.name || 'Untitled QR Code'}</h3>
                      <p className="text-sm text-gray-500">{truncateText(qrCode.description, 50)}</p>
                    </div>
                    <div className="text-sm space-y-1.5">
                      <p className="text-gray-600 flex items-center">
                        <Calendar className="w-4 h-4 mr-1.5" />
                        Created: {format(qrCode.createdAt, 'PPP')}
                      </p>
                      <p className="text-gray-600 flex items-center">
                        <Scan className="w-4 h-4 mr-1.5" />
                        Scans: {qrCode.scanCount || 0}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewAnalytics(qrCode)}
                    className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                  >
                    <BarChart2 className="mr-2 h-4 w-4" />
                    View Analytics
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownload(qrCode)}
                    className="text-green-600 hover:text-green-800 hover:bg-green-50"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(qrCode)}
                    className="text-red-600 hover:text-red-800 hover:bg-red-50"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!qrToDelete} onOpenChange={(open) => !open && setQrToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete QR Code</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this QR code? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-500 hover:bg-red-600"
              onClick={confirmDelete}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      {openDialog && selectedQrCode && (
        <QRCodeDetailsDialog
          qrCode={selectedQrCode}
          scanData={scanData[selectedQrCode.id] || {
            scans: [],
            deviceStats: { mobile: 0, desktop: 0 },
            hourlyStats: {},
            lastDeviceType: '',
            lastUserAgent: '',
            lastScanTime: null,
            totalScans: 0
          }}
          onClose={() => setOpenDialog(false)}
          loading={scanDataLoading}
        />
      )}
    </div>
  );
}