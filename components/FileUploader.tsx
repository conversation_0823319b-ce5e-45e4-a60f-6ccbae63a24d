'use client';

import { useState, useRef } from 'react';
import { Upload, FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface FileUploaderProps {
  onFilesSelected: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  maxSizeInMB?: number;
  className?: string;
  buttonText?: string;
  dragActiveText?: string;
  dragInactiveText?: string;
  icon?: React.ReactNode;
}

export function FileUploader({
  onFilesSelected,
  accept = '.pdf',
  multiple = false,
  maxSizeInMB = 50,
  className = '',
  buttonText = 'Select File',
  dragActiveText = 'Drop your file here',
  dragInactiveText = 'Drag & Drop File Here',
  icon = <FileUp className="h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />
}: FileUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;
    
    processFiles(Array.from(selectedFiles));
    
    // Reset the input value so the same file can be selected again
    e.target.value = '';
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files.length > 0) {
      processFiles(Array.from(e.dataTransfer.files));
    }
  };

  const processFiles = (files: File[]) => {
    // Filter out files that don't match the accepted file types
    const acceptedExtensions = accept.split(',').map(ext => ext.trim().toLowerCase());
    
    const validFiles = files.filter(file => {
      // Check file type
      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;
      const isValidType = acceptedExtensions.includes('.*') || 
                          acceptedExtensions.includes(fileExtension) || 
                          acceptedExtensions.some(ext => file.type.match(ext.replace('.', '')));
      
      // Check file size
      const isValidSize = file.size <= maxSizeInMB * 1024 * 1024;
      
      if (!isValidType) {
        toast.error('Invalid file type', {
          description: `Only ${accept} files are allowed.`
        });
      }
      
      if (!isValidSize) {
        toast.error('File too large', {
          description: `Maximum file size is ${maxSizeInMB}MB.`
        });
      }
      
      return isValidType && isValidSize;
    });
    
    if (validFiles.length > 0) {
      onFilesSelected(multiple ? validFiles : [validFiles[0]]);
    }
  };

  return (
    <div
      className={`border-2 border-dashed rounded-lg p-8 text-center ${
        isDragging
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
          : 'border-gray-300 dark:border-gray-700'
      } ${className}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept={accept}
        multiple={multiple}
        className="hidden"
      />

      {icon}

      <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
        {isDragging ? dragActiveText : dragInactiveText}
      </h3>

      <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
        Or click the button below to select {multiple ? 'files' : 'a file'}
      </p>

      <Button
        onClick={() => fileInputRef.current?.click()}
        className="bg-blue-500 hover:bg-blue-600 text-white"
      >
        <Upload className="mr-2 h-4 w-4" />
        {buttonText}
      </Button>
    </div>
  );
} 