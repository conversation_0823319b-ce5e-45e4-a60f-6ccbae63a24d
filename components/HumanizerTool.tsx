'use client';

import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Progress } from "@/components/ui/progress";
import { Wand2 } from 'lucide-react';

const BASE_COOLDOWN_TIME = 15000; // 15 seconds base cooldown
const EXTRA_COOLDOWN_TIME = 5000; // 5 seconds extra per threshold
const USAGE_THRESHOLD = 5; // Number of uses before increasing cooldown
const WORD_LIMIT = 1000; // Increased from 100 to 1000 words

interface HumanizerResponse {
  humanizedText: string;
}

async function humanizeText(text: string, level: string): Promise<HumanizerResponse> {
  const response = await fetch('/api/humanize', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ text, level }),
  });

  if (!response.ok) {
    throw new Error('Failed to humanize text');
  }

  return response.json();
}

export function HumanizerTool() {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [level, setLevel] = useState('medium');
  const [cooldownRemaining, setCooldownRemaining] = useState(0);
  const [wordCount, setWordCount] = useState(0);
  const [outputWordCount, setOutputWordCount] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout>();
  const [lastRequestTime, setLastRequestTime] = useState(0);
  const [usageCount, setUsageCount] = useState(0);

  const getCurrentCooldownTime = () => {
    const extraTime = Math.floor(Math.max(0, usageCount - USAGE_THRESHOLD) * EXTRA_COOLDOWN_TIME);
    return BASE_COOLDOWN_TIME + extraTime;
  };

  useEffect(() => {
    const words = input.trim().split(/\s+/);
    setWordCount(input.trim() ? words.length : 0);
  }, [input]);

  useEffect(() => {
    const words = output.trim().split(/\s+/);
    setOutputWordCount(output.trim() ? words.length : 0);
  }, [output]);

  useEffect(() => {
    if (cooldownRemaining > 0) {
      intervalRef.current = setInterval(() => {
        const remaining = Math.max(0, getCurrentCooldownTime() - (Date.now() - lastRequestTime));
        setCooldownRemaining(remaining);
        if (remaining === 0) {
          clearInterval(intervalRef.current);
        }
      }, 100);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [lastRequestTime, cooldownRemaining, usageCount]);

  const handleSubmit = async () => {
    if (!input.trim()) {
      toast.error('Please enter some text to humanize');
      return;
    }

    if (wordCount > WORD_LIMIT) {
      toast.error(`Text exceeds ${WORD_LIMIT} words limit`);
      return;
    }

    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    const currentCooldownTime = getCurrentCooldownTime();

    if (timeSinceLastRequest < currentCooldownTime) {
      const remainingTime = Math.ceil((currentCooldownTime - timeSinceLastRequest) / 1000);
      toast.error(`Please wait ${remainingTime} seconds before making another request`);
      setCooldownRemaining(currentCooldownTime - timeSinceLastRequest);
      return;
    }

    try {
      setIsLoading(true);
      const result = await humanizeText(input, level);
      setOutput(result.humanizedText);
      setLastRequestTime(now);
      setUsageCount(prev => prev + 1);
      setCooldownRemaining(currentCooldownTime);

      if (usageCount === USAGE_THRESHOLD - 1) {
        toast.warning('Next use will increase cooldown time by 5 seconds');
      } else if (usageCount >= USAGE_THRESHOLD) {
        const extraTime = Math.floor((usageCount - USAGE_THRESHOLD + 1) * 5);
        toast.warning(`Cooldown time increased to ${15 + extraTime} seconds due to frequent use`);
      }
    } catch (error) {
      toast.error('Failed to humanize text. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  const getCooldownDisplay = () => {
    if (cooldownRemaining > 0) {
      const seconds = Math.ceil(cooldownRemaining / 1000);
      const baseTime = BASE_COOLDOWN_TIME / 1000;
      const extraTime = (getCurrentCooldownTime() - BASE_COOLDOWN_TIME) / 1000;
      const progress = ((getCurrentCooldownTime() - cooldownRemaining) / getCurrentCooldownTime()) * 100;
      
      return (
        <div className="w-full flex flex-col items-center gap-2">
          <div className="text-sm text-gray-500">
            Wait {seconds}s ({baseTime}+{extraTime}s)
          </div>
          <Progress 
            value={progress} 
            className="w-full h-2 bg-gray-200 dark:bg-gray-700 overflow-hidden rounded-full"
            style={{
              '--progress-background': 'linear-gradient(to right, #9333ea, #3b82f6)',
            } as any}
          />
        </div>
      );
    }
    return 'Humanize';
  };

  return (
    <div className="space-y-8">
      {/* Input Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <label htmlFor="input" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Your Text
          </label>
          <div className="flex items-center gap-4">
            <span className={`text-sm ${wordCount > WORD_LIMIT ? 'text-red-500' : 'text-gray-500'}`}>
              {wordCount}/{WORD_LIMIT} words
            </span>
            <span className="text-sm text-gray-500">
              Uses: {usageCount}
            </span>
          </div>
        </div>
        <div className="relative">
          <Textarea
            id="input"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Enter your text here (max 1000 words)"
            className="min-h-[150px] transition-all duration-200 focus:shadow-lg resize-y"
          />
          <div className="absolute bottom-2 right-2 flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCopy(input)}
              className="text-gray-500 hover:text-gray-700"
            >
              Copy
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setInput('')}
              className="text-gray-500 hover:text-gray-700"
            >
              Clear
            </Button>
          </div>
        </div>
      </div>

      {/* Level Selection and Submit Button */}
      <div className="flex flex-col sm:flex-row gap-4 items-center">
        <div className="w-full sm:w-64">
          <Select value={level} onValueChange={setLevel}>
            <SelectTrigger>
              <SelectValue placeholder="Select Level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="basic">Basic</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="advanced">Advanced</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button
          onClick={handleSubmit}
          disabled={isLoading || cooldownRemaining > 0}
          className={`w-full sm:w-auto min-h-[60px] relative bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 active:from-purple-700 active:to-blue-700 text-white transition-all duration-300 shadow-lg hover:shadow-xl ${cooldownRemaining > 0 ? 'animate-pulse' : ''}`}
        >
          <div className="flex items-center gap-2 justify-center">
            <Wand2 className="w-5 h-5" />
            {isLoading ? (
              <span className="animate-pulse">Processing...</span>
            ) : (
              getCooldownDisplay()
            )}
          </div>
        </Button>
      </div>

      {/* Output Section */}
      {output && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Humanized Text
            </label>
            <span className="text-sm text-gray-500">
              {outputWordCount} words
            </span>
          </div>
          <div className="relative">
            <Textarea
              value={output}
              readOnly
              className="min-h-[150px] bg-gray-50 dark:bg-gray-900"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCopy(output)}
              className="absolute bottom-2 right-2 text-gray-500 hover:text-gray-700"
            >
              Copy
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}