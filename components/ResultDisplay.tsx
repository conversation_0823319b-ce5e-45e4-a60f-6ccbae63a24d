'use client';

export interface DetailedBreakdown {
  years: number;
  months: number;
  weeks: number;
  days: number;
}

export interface ResultDisplayProps {
  detailedBreakdown: DetailedBreakdown;
}

export default function ResultDisplay({ detailedBreakdown }: ResultDisplayProps) {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
      <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg text-center">
        <p className="text-2xl font-bold">{detailedBreakdown.days}</p>
        <p className="text-sm text-gray-600 dark:text-gray-400">Total Days</p>
      </div>
      <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg text-center">
        <p className="text-2xl font-bold">{detailedBreakdown.weeks}</p>
        <p className="text-sm text-gray-600 dark:text-gray-400">As Weeks</p>
      </div>
      <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg text-center">
        <p className="text-2xl font-bold">{detailedBreakdown.months}</p>
        <p className="text-sm text-gray-600 dark:text-gray-400">As Months</p>
      </div>
      <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg text-center">
        <p className="text-2xl font-bold">{detailedBreakdown.years}</p>
        <p className="text-sm text-gray-600 dark:text-gray-400">As Years</p>
      </div>
    </div>
  );
}