rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    // Likes collection
    match /likes/{docId} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // Feedback collection - anonymous feedback for tools
    match /feedback/{feedbackId} {
      allow read: if false; // No public read access to feedback
      allow create: if true; // Anyone can submit feedback
      allow update, delete: if false; // No updates or deletes allowed
    }

    // Archived likes collection - for displaying total like count
    match /archived_likes/{docId} {
      allow read: if true;
      allow write: if false; // Only allow reads, no writes from client
    }

    // Global QR codes collection
    match /qr_codes/{qrCodeId} {
      // Anyone can read QR codes
      allow read: if true;

      // Allow creation if user is authenticated and sets themselves as owner
      allow create: if isAuthenticated() &&
                   request.resource.data.userId == request.auth.uid;

      // Only owners can update or delete QR codes
      allow update, delete: if isAuthenticated() &&
                          resource.data.userId == request.auth.uid;

      // Allow public access to scan data
      match /scans/{scanId} {
        allow read: if true;
        allow write: if true;
      }
    }

    // Users collection and nested structure
    match /users/{userId} {
      // Allow listing users for QR code scanning
      allow list: if true;
      allow read, write: if isOwner(userId);

      // User likes collection
      match /likes/{likeId} {
        allow read, write: if isAuthenticated() && isOwner(userId);
      }

      // QR codes subcollection
      match /qrcodes/{qrCodeId} {
        // Allow reading QR codes for scanning
        allow read: if true;
        allow create: if isAuthenticated() && request.auth.uid == userId;
        allow update, delete: if isAuthenticated() && request.auth.uid == userId;

        match /scans/{scanId} {
          allow read: if true;
          allow write: if true;
        }
      }

      // Recipes subcollection
      match /recipes/{document=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      // Image-generator subcollection
      match /image-generator/{document=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      // Credits subcollection
      match /credits/{creditType} {
        allow read: if request.auth != null && request.auth.uid == userId;
        allow create: if request.auth != null
                     && request.auth.uid == userId
                     && request.resource.data.remaining == 20
                     && request.resource.data.total == 20
                     && request.resource.data.type == creditType;
        allow update: if request.auth != null
                     && request.auth.uid == userId
                     && request.resource.data.remaining == resource.data.remaining - 1
                     && request.resource.data.remaining >= 0;

        // Credit transactions
        match /transactions/{transactionId} {
          allow read: if request.auth != null && request.auth.uid == userId;
          allow create: if request.auth != null
                       && request.auth.uid == userId
                       && request.resource.data.remaining >= 0;
        }
      }
    }

    // Global credits collection
    match /credits/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if false;  // Only allow writes through functions
    }
  }
}
