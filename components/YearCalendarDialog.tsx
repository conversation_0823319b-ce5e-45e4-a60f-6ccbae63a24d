import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { Calendar } from "@/components/ui/calendar"
import { CalendarDays, ChevronLeft, ChevronRight } from "lucide-react"
import { getAustralianHolidays } from "@/lib/aus-holidays"

const AUSTRALIAN_STATES = {
  NSW: "New South Wales",
  VIC: "Victoria",
  QLD: "Queensland",
  WA: "Western Australia",
  SA: "South Australia",
  TAS: "Tasmania",
  NT: "Northern Territory",
  ACT: "Australian Capital Territory",
} as const

export function YearCalendarDialog() {
  const [year, setYear] = React.useState(new Date().getFullYear())
  const [selectedState, setSelectedState] = React.useState<keyof typeof AUSTRALIAN_STATES>("NSW")
  const months = Array.from({ length: 12 }, (_, i) => new Date(year, i))

  const stateHolidays = React.useMemo(() => {
    const allHolidays = getAustralianHolidays(year)
    return allHolidays.filter(holiday => 
      !holiday.state || holiday.state.includes(selectedState)
    )
  }, [selectedState, year])

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="lg" className="gap-2">
          <CalendarDays className="h-5 w-5" />
          View Full Calendar
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[95vw] w-[1200px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4">
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-2xl font-bold">
                {year} Calendar
              </DialogTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setYear(year - 1)}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="default"
                  onClick={() => setYear(new Date().getFullYear())}
                >
                  Current Year
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setYear(year + 1)}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <ToggleGroup 
                type="single" 
                value={selectedState} 
                onValueChange={(value: keyof typeof AUSTRALIAN_STATES) => value && setSelectedState(value)}
                className="flex flex-wrap gap-2"
              >
                {Object.entries(AUSTRALIAN_STATES).map(([code, name]) => (
                  <ToggleGroupItem 
                    key={code} 
                    value={code} 
                    aria-label={name}
                    className="px-3 py-2 text-sm hover:bg-gray-100 data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
                  >
                    {code}
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            Showing public holidays for {AUSTRALIAN_STATES[selectedState]}. Hover over dates to see holiday information.
          </p>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
          {months.map((month) => (
            <div
              key={month.toString()}
              className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg p-2"
            >
              <Calendar
                mode="default"
                defaultMonth={month}
                showOutsideDays={false}
                showHolidays={true}
                holidays={stateHolidays}
                className="w-full"
              />
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  )
}