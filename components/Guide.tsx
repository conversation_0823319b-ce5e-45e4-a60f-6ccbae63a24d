import {
  CaseS<PERSON><PERSON>,
  <PERSON>,
  Settings2,
  <PERSON>r<PERSON><PERSON>,
  HelpCircle
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "./ui/button";

export function Guide() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon">
          <HelpCircle className="h-6 w-6" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[825px]">
        <DialogHeader>
          <DialogTitle>How to Use Text Converter</DialogTitle>
        </DialogHeader>
        <div className="space-y-8 py-4">
          <p className="text-gray-600 dark:text-gray-300 text-center">Master the art of text transformation with our comprehensive features</p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-4">
                <CaseSensitive className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-blue-600 dark:text-blue-400">Text Options</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
                <li>Sentence case formatting</li>
                <li>Title Case conversion</li>
                <li>UPPERCASE transformation</li>
                <li>lowercase options</li>
                <li>AlTeRnAtInG case styles</li>
              </ul>
            </div>

            <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-4">
                <Brain className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-purple-600 dark:text-purple-400">Smart Features</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
                <li>AI-powered analysis</li>
                <li>Sentiment detection</li>
                <li>Word frequency counting</li>
                <li>Smart title case handling</li>
                <li>Multi-language support</li>
              </ul>
            </div>

            <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <div className="w-12 h-12 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-4">
                <QrCode className="w-6 h-6 text-pink-600 dark:text-pink-400" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-pink-600 dark:text-pink-400">QR Generator</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
                <li>Create custom QR codes</li>
                <li>Choose colors and size</li>
                <li>Download in PNG/SVG</li>
                <li>Track usage analytics</li>
                <li>Save and manage codes</li>
              </ul>
            </div>

            <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <div className="w-12 h-12 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-4">
                <Settings2 className="w-6 h-6 text-pink-600 dark:text-pink-400" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-pink-600 dark:text-pink-400">Tools & Export</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
                <li>Copy to clipboard</li>
                <li>Download as text file</li>
                <li>Character counting</li>
                <li>Word statistics</li>
                <li>Clear history option</li>
              </ul>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
