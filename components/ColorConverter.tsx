'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import {
  Copy, RefreshCw, Palette, Plus, Trash, Save, Heart, Info, Droplet, Zap,
  Contrast, Sparkles, Grid, Star, Undo, Redo, Keyboard, Eye, EyeOff,
  ChevronDown, ChevronUp, Maximize2, Minimize2, Download, Upload, X,
  FileCode, FileText, <PERSON>race<PERSON>, Code2, Layers
} from 'lucide-react'
import { HexColorPicker, HexColorInput } from 'react-colorful'
import {
  ColorValues,
  hexToRgb,
  rgbToHex,
  rgbToHsl,
  hslToRgb,
  getColorName,
  getComplementaryColor,
  getAnalogousColors,
  getTriadicColors,
  getContrastRatio,
  gradientPresets
} from '@/lib/colorUtils'
import { materialColors, materialColorCategories, getMaterialColorsByCategory, findMaterialColor } from '@/lib/materialColors'

interface GradientStop {
  color: ColorValues;
  position: number; // 0-100
}

export function ColorConverter() {
  // Color converter state
  const [color, setColor] = useState<ColorValues>({
    hex: '#6366f1',
    rgb: { r: 99, g: 102, b: 241 },
    hsl: { h: 239, s: 84, l: 67 },
  })
  const [colorWithAlpha, setColorWithAlpha] = useState<ColorValues>({
    hex: '#6366f1',
    rgb: { r: 99, g: 102, b: 241, a: 1 },
    hsl: { h: 239, s: 84, l: 67, a: 1 },
  })
  const [copied, setCopied] = useState<string | null>(null)
  const [hexInput, setHexInput] = useState('#6366f1')
  const [showAlpha, setShowAlpha] = useState(false)
  const [colorName, setColorName] = useState('')
  const [savedColors, setSavedColors] = useState<ColorValues[]>([])
  const [recentColors, setRecentColors] = useState<ColorValues[]>([])

  // Enhanced UI state
  const [colorHistory, setColorHistory] = useState<ColorValues[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false)
  const [paletteView, setPaletteView] = useState<'grid' | 'list'>('grid')
  const [expandedSections, setExpandedSections] = useState({
    colorPicker: true,
    savedColors: true,
    recentColors: true,
    harmonies: true,
    contrast: true,
    materialColors: false,
    colorBlindness: false
  })
  const [isMobile, setIsMobile] = useState(false)

  // Phase 2 features state
  const [selectedMaterialCategory, setSelectedMaterialCategory] = useState('Blue')
  const [colorBlindnessType, setColorBlindnessType] = useState<'protanopia' | 'deuteranopia' | 'tritanopia'>('protanopia')
  const [showColorBlindnessSimulation, setShowColorBlindnessSimulation] = useState(false)
  const [contrastBackgrounds, setContrastBackgrounds] = useState(['#ffffff', '#000000', '#f5f5f5', '#333333'])
  const [selectedContrastBg, setSelectedContrastBg] = useState('#ffffff')

  // Phase 3 features state - Advanced Export Options
  const [showExportOptions, setShowExportOptions] = useState(false)
  const [exportFormat, setExportFormat] = useState<'css' | 'scss' | 'json' | 'tokens'>('css')

  // Gradient generator state
  const [gradientStops, setGradientStops] = useState<GradientStop[]>([
    {
      color: {
        hex: '#ff0000',
        rgb: { r: 255, g: 0, b: 0 },
        hsl: { h: 0, s: 100, l: 50 },
      },
      position: 0
    },
    {
      color: {
        hex: '#0000ff',
        rgb: { r: 0, g: 0, b: 255 },
        hsl: { h: 240, s: 100, l: 50 },
      },
      position: 100
    }
  ])
  const [gradientType, setGradientType] = useState<'linear' | 'radial'>('linear')
  const [gradientAngle, setGradientAngle] = useState(90)
  const [radialShape, setRadialShape] = useState<'circle' | 'ellipse'>('circle')
  const [radialSize, setRadialSize] = useState<'closest-side' | 'closest-corner' | 'farthest-side' | 'farthest-corner'>('farthest-corner')
  const [radialPosition, setRadialPosition] = useState({ x: 50, y: 50 }) // center position in percentage
  const [activeStopIndex, setActiveStopIndex] = useState(0)

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Load saved colors from localStorage on component mount
  useEffect(() => {
    const savedColorsFromStorage = localStorage.getItem('savedColors')
    if (savedColorsFromStorage) {
      try {
        setSavedColors(JSON.parse(savedColorsFromStorage))
      } catch (e) {
        console.error('Error loading saved colors:', e)
      }
    }

    const recentColorsFromStorage = localStorage.getItem('recentColors')
    if (recentColorsFromStorage) {
      try {
        setRecentColors(JSON.parse(recentColorsFromStorage))
      } catch (e) {
        console.error('Error loading recent colors:', e)
      }
    }

    const historyFromStorage = localStorage.getItem('colorHistory')
    if (historyFromStorage) {
      try {
        setColorHistory(JSON.parse(historyFromStorage))
      } catch (e) {
        console.error('Error loading color history:', e)
      }
    }
  }, [])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when not typing in an input
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'c':
            e.preventDefault()
            copyToClipboard(color.hex, 'HEX')
            break
          case 'r':
            e.preventDefault()
            randomColor()
            break
          case 's':
            e.preventDefault()
            saveColor()
            break
          case 'z':
            e.preventDefault()
            if (e.shiftKey) {
              redoColor()
            } else {
              undoColor()
            }
            break
          case '?':
            e.preventDefault()
            setShowKeyboardShortcuts(!showKeyboardShortcuts)
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [color.hex, showKeyboardShortcuts])

  // Add color to history
  const addToHistory = useCallback((newColor: ColorValues) => {
    setColorHistory(prev => {
      const newHistory = [newColor, ...prev.filter(c => c.hex !== newColor.hex)].slice(0, 20)
      localStorage.setItem('colorHistory', JSON.stringify(newHistory))
      return newHistory
    })
    setHistoryIndex(0)
  }, [])

  // Undo/Redo functions
  const undoColor = useCallback(() => {
    if (historyIndex < colorHistory.length - 1) {
      const newIndex = historyIndex + 1
      const prevColor = colorHistory[newIndex]
      setColor(prevColor)
      setHexInput(prevColor.hex)
      setHistoryIndex(newIndex)
      toast.success('Undid color change')
    }
  }, [colorHistory, historyIndex])

  const redoColor = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1
      const nextColor = colorHistory[newIndex]
      setColor(nextColor)
      setHexInput(nextColor.hex)
      setHistoryIndex(newIndex)
      toast.success('Redid color change')
    }
  }, [colorHistory, historyIndex])

  // Toggle section expansion
  const toggleSection = useCallback((section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }, [])

  // Update color name when color changes
  useEffect(() => {
    setColorName(getColorName(color.hex))

    // Add to recent colors
    const newRecentColors = [color, ...recentColors.filter(c => c.hex !== color.hex)].slice(0, 12)
    setRecentColors(newRecentColors)
    localStorage.setItem('recentColors', JSON.stringify(newRecentColors))
  }, [color])

  // Handle hex input change
  const handleHexInput = (value: string) => {
    setHexInput(value)
    if (/^#[0-9A-F]{6}$/i.test(value)) {
      handleHexChange(value)
    }
  }

  // Handle hex color change
  const handleHexChange = (hex: string) => {
    const rgb = hexToRgb(hex)
    if (rgb) {
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
      const newColor = { hex, rgb, hsl }

      // Add current color to history before changing
      if (color.hex !== hex) {
        addToHistory(color)
      }

      setColor(newColor)

      // Also update the color with alpha
      setColorWithAlpha({
        hex,
        rgb: { ...rgb, a: colorWithAlpha.rgb.a },
        hsl: { ...hsl, a: colorWithAlpha.hsl.a }
      })
    }
  }

  // Handle RGB change
  const handleRgbChange = (component: 'r' | 'g' | 'b', value: string) => {
    const numValue = parseInt(value)
    if (isNaN(numValue)) return

    // Clamp value between 0 and 255
    const clampedValue = Math.max(0, Math.min(255, numValue))
    const newRgb = { ...color.rgb, [component]: clampedValue }
    const hex = rgbToHex(newRgb.r, newRgb.g, newRgb.b)
    const hsl = rgbToHsl(newRgb.r, newRgb.g, newRgb.b)

    // Add current color to history before changing
    if (color.hex !== hex) {
      addToHistory(color)
    }

    const newColor = { hex, rgb: newRgb, hsl }
    setColor(newColor)
    setHexInput(hex)

    // Also update the color with alpha
    setColorWithAlpha({
      hex,
      rgb: { ...newRgb, a: colorWithAlpha.rgb.a },
      hsl: { ...hsl, a: colorWithAlpha.hsl.a }
    })
  }

  // Handle HSL change
  const handleHslChange = (component: 'h' | 's' | 'l', value: number) => {
    const newHsl = { ...color.hsl, [component]: value }
    const rgb = hslToRgb(newHsl.h, newHsl.s, newHsl.l)
    const hex = rgbToHex(rgb.r, rgb.g, rgb.b)

    // Add current color to history before changing
    if (color.hex !== hex) {
      addToHistory(color)
    }

    const newColor = { hex, rgb, hsl: newHsl }
    setColor(newColor)
    setHexInput(hex)

    // Also update the color with alpha
    setColorWithAlpha({
      hex,
      rgb: { ...rgb, a: colorWithAlpha.rgb.a },
      hsl: { ...newHsl, a: colorWithAlpha.hsl.a }
    })
  }

  // Handle alpha change
  const handleAlphaChange = (value: number) => {
    const alpha = value / 100
    const newRgb = { ...colorWithAlpha.rgb, a: alpha }
    const newHsl = { ...colorWithAlpha.hsl, a: alpha }
    const hex = rgbToHex(newRgb.r, newRgb.g, newRgb.b, alpha)

    setColorWithAlpha({
      hex,
      rgb: newRgb,
      hsl: newHsl
    })
  }

  // Copy to clipboard
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    setCopied(label)
    toast.success(`${label} copied to clipboard`)
    setTimeout(() => setCopied(null), 2000)
  }

  // Phase 3: Advanced Export Functions
  const generateCSSVariables = () => {
    const colorName = getColorName(color.hex).toLowerCase().replace(/\s+/g, '-')
    return `:root {
  --color-primary: ${color.hex};
  --color-primary-rgb: ${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b};
  --color-primary-hsl: ${color.hsl.h}, ${color.hsl.s}%, ${color.hsl.l}%;
  --color-${colorName}: ${color.hex};
  --color-${colorName}-rgb: ${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b};
  --color-${colorName}-hsl: ${color.hsl.h}, ${color.hsl.s}%, ${color.hsl.l}%;
}`
  }

  const generateSCSSVariables = () => {
    const colorName = getColorName(color.hex).toLowerCase().replace(/\s+/g, '-')
    return `// SCSS Color Variables
$color-primary: ${color.hex};
$color-primary-rgb: ${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b};
$color-primary-hsl: ${color.hsl.h}, ${color.hsl.s}%, ${color.hsl.l}%;
$color-${colorName}: ${color.hex};
$color-${colorName}-rgb: ${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b};
$color-${colorName}-hsl: ${color.hsl.h}, ${color.hsl.s}%, ${color.hsl.l}%;

// SCSS Color Map
$colors: (
  "primary": ${color.hex},
  "${colorName}": ${color.hex}
);`
  }

  const generateDesignTokens = () => {
    const colorName = getColorName(color.hex).toLowerCase().replace(/\s+/g, '-')
    return JSON.stringify({
      color: {
        primary: {
          value: color.hex,
          type: "color",
          description: "Primary brand color"
        },
        [colorName]: {
          value: color.hex,
          type: "color",
          description: `${getColorName(color.hex)} color`,
          rgb: {
            r: color.rgb.r,
            g: color.rgb.g,
            b: color.rgb.b
          },
          hsl: {
            h: color.hsl.h,
            s: color.hsl.s,
            l: color.hsl.l
          }
        }
      }
    }, null, 2)
  }

  const generateJSONExport = () => {
    const harmonies = {
      complementary: getComplementaryColor(color),
      analogous: getAnalogousColors(color),
      triadic: getTriadicColors(color)
    }

    return JSON.stringify({
      color: {
        hex: color.hex,
        rgb: color.rgb,
        hsl: color.hsl,
        name: getColorName(color.hex)
      },
      harmonies,
      accessibility: {
        contrastRatios: contrastBackgrounds.map(bg => ({
          background: bg,
          ratio: getContrastRatio(color.hex, bg),
          wcag: getWCAGCompliance(color.hex, bg)
        }))
      },
      materialDesign: findMaterialColor(color.hex),
      timestamp: new Date().toISOString()
    }, null, 2)
  }

  const exportColor = (format: string) => {
    let content = ''
    let filename = ''
    let mimeType = 'text/plain'

    switch (format) {
      case 'css':
        content = generateCSSVariables()
        filename = 'color-variables.css'
        mimeType = 'text/css'
        break
      case 'scss':
        content = generateSCSSVariables()
        filename = 'color-variables.scss'
        mimeType = 'text/scss'
        break
      case 'tokens':
        content = generateDesignTokens()
        filename = 'design-tokens.json'
        mimeType = 'application/json'
        break
      case 'json':
        content = generateJSONExport()
        filename = 'color-export.json'
        mimeType = 'application/json'
        break
      default:
        return
    }

    // Create and download file
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success(`${format.toUpperCase()} file downloaded successfully`)
  }

  // Generate random color
  const randomColor = useCallback(() => {
    // Add current color to history before changing
    addToHistory(color)

    const r = Math.floor(Math.random() * 256)
    const g = Math.floor(Math.random() * 256)
    const b = Math.floor(Math.random() * 256)
    const hex = rgbToHex(r, g, b)
    const hsl = rgbToHsl(r, g, b)
    const newColor = { hex, rgb: { r, g, b }, hsl }
    setColor(newColor)
    setHexInput(hex)

    // Also update the color with alpha
    setColorWithAlpha({
      hex,
      rgb: { r, g, b, a: colorWithAlpha.rgb.a },
      hsl: { ...hsl, a: colorWithAlpha.hsl.a }
    })

    toast.success('Generated random color')
  }, [color, addToHistory, colorWithAlpha.rgb.a, colorWithAlpha.hsl.a])

  // Save current color
  const saveColor = useCallback(() => {
    if (!savedColors.some(c => c.hex === color.hex)) {
      const newSavedColors = [...savedColors, { ...color, name: colorName }]
      setSavedColors(newSavedColors)
      localStorage.setItem('savedColors', JSON.stringify(newSavedColors))
      toast.success('Color saved to palette')
    } else {
      toast.info('Color already in palette')
    }
  }, [savedColors, color, colorName])

  // Remove saved color
  const removeSavedColor = useCallback((hexToRemove: string) => {
    const newSavedColors = savedColors.filter(c => c.hex !== hexToRemove)
    setSavedColors(newSavedColors)
    localStorage.setItem('savedColors', JSON.stringify(newSavedColors))
    toast.success('Color removed from palette')
  }, [savedColors])

  // Save all harmony colors
  const saveAllHarmonies = useCallback(() => {
    const complementary = getComplementaryColor(color.hex)
    const analogous = getAnalogousColors(color.hex)
    const harmonyColors = [color, complementary, ...analogous]

    const newColors = harmonyColors.filter(c => !savedColors.some(saved => saved.hex === c.hex))
    if (newColors.length > 0) {
      const newSavedColors = [...savedColors, ...newColors]
      setSavedColors(newSavedColors)
      localStorage.setItem('savedColors', JSON.stringify(newSavedColors))
      toast.success(`Saved ${newColors.length} harmony colors`)
    } else {
      toast.info('All harmony colors already saved')
    }
  }, [color, savedColors])

  // Load color from palette
  const loadColor = useCallback((colorToLoad: ColorValues) => {
    addToHistory(color)
    setColor(colorToLoad)
    setHexInput(colorToLoad.hex)
    setColorWithAlpha({
      hex: colorToLoad.hex,
      rgb: { ...colorToLoad.rgb, a: colorWithAlpha.rgb.a },
      hsl: { ...colorToLoad.hsl, a: colorWithAlpha.hsl.a }
    })
  }, [color, addToHistory, colorWithAlpha.rgb.a, colorWithAlpha.hsl.a])

  // Enhanced contrast checking function
  const getWCAGCompliance = useCallback((foreground: string, background: string) => {
    const ratio = getContrastRatio(foreground, background)
    return {
      ratio: ratio,
      aa: ratio >= 4.5,
      aaLarge: ratio >= 3,
      aaa: ratio >= 7,
      aaaLarge: ratio >= 4.5,
      level: ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : ratio >= 3 ? 'AA Large' : 'Fail'
    }
  }, [])

  // Color blindness simulation
  const simulateColorBlindness = useCallback((hex: string, type: 'protanopia' | 'deuteranopia' | 'tritanopia') => {
    const rgb = hexToRgb(hex)
    if (!rgb) return hex

    let { r, g, b } = rgb

    // Convert to linear RGB
    const toLinear = (c: number) => {
      c = c / 255
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    }

    const fromLinear = (c: number) => {
      c = c <= 0.0031308 ? c * 12.92 : 1.055 * Math.pow(c, 1 / 2.4) - 0.055
      return Math.round(Math.max(0, Math.min(1, c)) * 255)
    }

    const lr = toLinear(r)
    const lg = toLinear(g)
    const lb = toLinear(b)

    let newR, newG, newB

    switch (type) {
      case 'protanopia': // Red-blind
        newR = 0.567 * lr + 0.433 * lg
        newG = 0.558 * lr + 0.442 * lg
        newB = 0.242 * lg + 0.758 * lb
        break
      case 'deuteranopia': // Green-blind
        newR = 0.625 * lr + 0.375 * lg
        newG = 0.7 * lr + 0.3 * lg
        newB = 0.3 * lg + 0.7 * lb
        break
      case 'tritanopia': // Blue-blind
        newR = 0.95 * lr + 0.05 * lg
        newG = 0.433 * lg + 0.567 * lb
        newB = 0.475 * lg + 0.525 * lb
        break
      default:
        return hex
    }

    const finalR = fromLinear(newR)
    const finalG = fromLinear(newG)
    const finalB = fromLinear(newB)

    return rgbToHex(finalR, finalG, finalB)
  }, [])

  // Generate random gradient
  const randomGradient = () => {
    // Generate random stops (2-4 stops)
    const numStops = Math.floor(Math.random() * 3) + 2
    const newStops: GradientStop[] = []

    for (let i = 0; i < numStops; i++) {
      const r = Math.floor(Math.random() * 256)
      const g = Math.floor(Math.random() * 256)
      const b = Math.floor(Math.random() * 256)
      const hex = rgbToHex(r, g, b)
      const hsl = rgbToHsl(r, g, b)

      newStops.push({
        color: { hex, rgb: { r, g, b }, hsl },
        position: i === 0 ? 0 : i === numStops - 1 ? 100 : Math.floor(Math.random() * 80) + 10
      })
    }

    // Sort stops by position
    newStops.sort((a, b) => a.position - b.position)

    setGradientStops(newStops)
    setActiveStopIndex(0)

    if (gradientType === 'linear') {
      setGradientAngle(Math.floor(Math.random() * 360))
    } else {
      setRadialPosition({
        x: Math.floor(Math.random() * 100),
        y: Math.floor(Math.random() * 100)
      })
      setRadialShape(Math.random() > 0.5 ? 'circle' : 'ellipse')
      const sizes = ['closest-side', 'closest-corner', 'farthest-side', 'farthest-corner']
      setRadialSize(sizes[Math.floor(Math.random() * sizes.length)] as typeof radialSize)
    }
  }

  return (
    <div className="space-y-8">
      <Card className="p-6 bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-gray-800/50 dark:to-gray-900/50 backdrop-blur-sm border-2 border-blue-100 dark:border-blue-900">
        <div className="flex items-center justify-between mb-6">
          <div className="flex-1">
            <h2 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Color Converter
            </h2>
          </div>
          <div className="flex-1 flex justify-center">
            {/* Empty space for better balance */}
          </div>
          <div className="flex-1 flex justify-end gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={saveColor}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
              title="Save to palette"
            >
              <Heart className="h-4 w-4 mr-2" />
              Save
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={randomColor}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Random
            </Button>
            <Popover open={showExportOptions} onOpenChange={setShowExportOptions}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                  title="Export color data"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-64 p-4" align="end">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Export Format</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant={exportFormat === 'css' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setExportFormat('css')}
                        className="justify-start"
                      >
                        <FileCode className="h-3 w-3 mr-2" />
                        CSS
                      </Button>
                      <Button
                        variant={exportFormat === 'scss' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setExportFormat('scss')}
                        className="justify-start"
                      >
                        <Braces className="h-3 w-3 mr-2" />
                        SCSS
                      </Button>
                      <Button
                        variant={exportFormat === 'tokens' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setExportFormat('tokens')}
                        className="justify-start"
                      >
                        <Layers className="h-3 w-3 mr-2" />
                        Tokens
                      </Button>
                      <Button
                        variant={exportFormat === 'json' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setExportFormat('json')}
                        className="justify-start"
                      >
                        <FileText className="h-3 w-3 mr-2" />
                        JSON
                      </Button>
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <Button
                      onClick={() => {
                        exportColor(exportFormat)
                        setShowExportOptions(false)
                      }}
                      className="w-full"
                      size="sm"
                    >
                      <Download className="h-3 w-3 mr-2" />
                      Download {exportFormat.toUpperCase()}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        let content = ''
                        switch (exportFormat) {
                          case 'css': content = generateCSSVariables(); break
                          case 'scss': content = generateSCSSVariables(); break
                          case 'tokens': content = generateDesignTokens(); break
                          case 'json': content = generateJSONExport(); break
                        }
                        copyToClipboard(content, `${exportFormat.toUpperCase()} export`)
                        setShowExportOptions(false)
                      }}
                      className="w-full"
                      size="sm"
                    >
                      <Copy className="h-3 w-3 mr-2" />
                      Copy to Clipboard
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
          <div className="space-y-6">
            {/* Color Preview and Picker */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Enhanced Color Preview */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleSection('colorPicker')}
                    className="p-0 h-auto font-medium"
                  >
                    <div className="flex items-center gap-2">
                      {expandedSections.colorPicker ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                      Color Preview
                    </div>
                  </Button>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowAlpha(!showAlpha)}
                      className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                      title={showAlpha ? "Hide alpha channel" : "Show alpha channel"}
                    >
                      {showAlpha ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      {!isMobile && <span className="ml-2">{showAlpha ? 'Hide' : 'Show'} Alpha</span>}
                    </Button>
                  </div>
                </div>

                {expandedSections.colorPicker && (
                  <div className="space-y-4">
                    <div className="relative group">
                      <div
                        className="w-full h-48 sm:h-56 rounded-xl border-2 border-gray-200 dark:border-gray-700 shadow-inner transition-all duration-300 cursor-pointer hover:shadow-lg"
                        style={{ backgroundColor: color.hex }}
                        onClick={() => copyToClipboard(color.hex, 'HEX')}
                        title="Click to copy HEX value"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 rounded-xl transition-colors duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg px-3 py-2">
                          <div className="flex items-center gap-2 text-sm font-medium">
                            <Copy className="h-4 w-4" />
                            Click to copy
                          </div>
                        </div>
                      </div>
                      <div className="absolute top-3 left-3 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg px-3 py-2">
                        <div className="text-sm font-mono font-medium">{color.hex.toUpperCase()}</div>
                      </div>
                    </div>

                    {/* Color Info */}
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 text-sm">
                      <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-3">
                        <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">RGB</div>
                        <div className="font-mono">{color.rgb.r}, {color.rgb.g}, {color.rgb.b}</div>
                      </div>
                      <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-3">
                        <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">HSL</div>
                        <div className="font-mono">{color.hsl.h}°, {color.hsl.s}%, {color.hsl.l}%</div>
                      </div>
                      <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-3 col-span-2 sm:col-span-1">
                        <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">Name</div>
                        <div className="font-medium truncate">{colorName || 'Unknown'}</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Enhanced Color Picker */}
              <div className="space-y-4">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Palette className="h-4 w-4 text-purple-500" />
                  Interactive Picker
                </Label>
                <div className="space-y-4">
                  <div className="relative">
                    <HexColorPicker
                      color={color.hex}
                      onChange={(hex) => {
                        handleHexChange(hex);
                        setHexInput(hex);
                      }}
                      className="w-full !h-48 sm:!h-56"
                    />
                  </div>

                  {showAlpha && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Transparency</Label>
                        <span className="text-sm font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                          {Math.round((colorWithAlpha.rgb.a || 1) * 100)}%
                        </span>
                      </div>
                      <Slider
                        value={[colorWithAlpha.rgb.a ? colorWithAlpha.rgb.a * 100 : 100]}
                        min={0}
                        max={100}
                        step={1}
                        onValueChange={(value) => handleAlphaChange(value[0])}
                        className="w-full"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Saved Colors */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleSection('savedColors')}
                  className="p-0 h-auto font-medium"
                >
                  <div className="flex items-center gap-2">
                    {expandedSections.savedColors ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    <Heart className="h-4 w-4 text-red-500" />
                    Saved Colors ({savedColors.length})
                  </div>
                </Button>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPaletteView(paletteView === 'grid' ? 'list' : 'grid')}
                    className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  {savedColors.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSavedColors([])
                        localStorage.removeItem('savedColors')
                        toast.success('Cleared saved colors')
                      }}
                      className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-red-600 hover:text-red-700"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {expandedSections.savedColors && (
                <div className="space-y-3">
                  {savedColors.length === 0 ? (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <Heart className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No saved colors yet</p>
                      <p className="text-xs">Save colors to build your palette</p>
                    </div>
                  ) : (
                    <div className={`grid gap-2 ${paletteView === 'grid' ? 'grid-cols-6 sm:grid-cols-8 md:grid-cols-10' : 'grid-cols-1'}`}>
                      {savedColors.map((savedColor, index) => (
                        <div
                          key={index}
                          className={`group relative ${paletteView === 'grid' ? 'aspect-square' : 'h-12 flex items-center'}`}
                        >
                          <div
                            className={`${paletteView === 'grid' ? 'w-full h-full' : 'w-12 h-full flex-shrink-0'} rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-lg ${color.hex === savedColor.hex ? 'ring-2 ring-purple-500 ring-offset-2' : ''}`}
                            style={{ backgroundColor: savedColor.hex }}
                            onClick={() => loadColor(savedColor)}
                            title={`${savedColor.hex} - Click to use`}
                          />
                          {paletteView === 'list' && (
                            <div className="flex-1 ml-3">
                              <div className="font-mono text-sm">{savedColor.hex.toUpperCase()}</div>
                              <div className="text-xs text-gray-500">{getColorName(savedColor.hex)}</div>
                            </div>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              removeSavedColor(savedColor.hex)
                            }}
                            className={`absolute ${paletteView === 'grid' ? '-top-2 -right-2' : 'right-0'} opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white w-6 h-6 p-0 rounded-full`}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Enhanced Recent Colors */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleSection('recentColors')}
                  className="p-0 h-auto font-medium"
                >
                  <div className="flex items-center gap-2">
                    {expandedSections.recentColors ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    <Zap className="h-4 w-4 text-yellow-500" />
                    Recent Colors ({recentColors.length})
                  </div>
                </Button>
                {recentColors.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setRecentColors([])
                      localStorage.removeItem('recentColors')
                      toast.success('Cleared recent colors')
                    }}
                    className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-red-600 hover:text-red-700"
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {expandedSections.recentColors && (
                <div className="space-y-3">
                  {recentColors.length === 0 ? (
                    <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                      <p className="text-sm">No recent colors</p>
                    </div>
                  ) : (
                    <div className="flex flex-wrap gap-2">
                      {recentColors.map((recentColor, index) => (
                        <div
                          key={index}
                          className="group relative"
                        >
                          <div
                            className={`w-10 h-10 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer transition-all duration-200 hover:scale-110 hover:shadow-lg ${color.hex === recentColor.hex ? 'ring-2 ring-purple-500 ring-offset-1' : ''}`}
                            style={{ backgroundColor: recentColor.hex }}
                            onClick={() => loadColor(recentColor)}
                            title={`${recentColor.hex} - Click to use`}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Material Design Colors */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleSection('materialColors')}
                  className="p-0 h-auto font-medium"
                >
                  <div className="flex items-center gap-2">
                    {expandedSections.materialColors ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    <Sparkles className="h-4 w-4 text-green-500" />
                    Material Design Colors
                  </div>
                </Button>
                <Badge variant="outline" className="text-xs">Google</Badge>
              </div>

              {expandedSections.materialColors && (
                <div className="space-y-4 p-4 border rounded-lg bg-white/50 dark:bg-gray-800/50">
                  {/* Category Selector */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Color Category</Label>
                    <div className="flex flex-wrap gap-2">
                      {materialColorCategories.map((category) => (
                        <Button
                          key={category}
                          variant={selectedMaterialCategory === category ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedMaterialCategory(category)}
                          className="text-xs"
                        >
                          {category}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Material Colors Grid */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">{selectedMaterialCategory} Palette</Label>
                    <div className="grid grid-cols-5 sm:grid-cols-10 gap-2">
                      {getMaterialColorsByCategory(selectedMaterialCategory).map((materialColor, index) => (
                        <div
                          key={index}
                          className="group relative"
                        >
                          <div
                            className={`aspect-square rounded-lg border-2 cursor-pointer transition-all hover:scale-110 hover:shadow-lg ${
                              color.hex.toLowerCase() === materialColor.hex.toLowerCase() ? 'border-purple-500 ring-2 ring-purple-500 ring-offset-1' : 'border-gray-300'
                            }`}
                            style={{ backgroundColor: materialColor.hex }}
                            onClick={() => {
                              addToHistory(color)
                              handleHexChange(materialColor.hex)
                              setHexInput(materialColor.hex)
                            }}
                            title={`${materialColor.name} - ${materialColor.hex}`}
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors duration-200 flex items-center justify-center">
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded px-2 py-1 text-xs font-medium">
                              {materialColor.name.split(' ')[1]}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Material Color Info */}
                  {findMaterialColor(color.hex) && (
                    <div className="p-3 bg-green-50/50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-sm font-medium text-green-800 dark:text-green-200">
                        Material Design Color: {findMaterialColor(color.hex)?.name}
                      </div>
                      <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                        This color follows Google's Material Design guidelines
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Color Blindness Simulator */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleSection('colorBlindness')}
                  className="p-0 h-auto font-medium"
                >
                  <div className="flex items-center gap-2">
                    {expandedSections.colorBlindness ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    <Eye className="h-4 w-4 text-orange-500" />
                    Color Blindness Simulator
                  </div>
                </Button>
                <Badge variant="outline" className="text-xs">Accessibility</Badge>
              </div>

              {expandedSections.colorBlindness && (
                <div className="space-y-4 p-4 border rounded-lg bg-white/50 dark:bg-gray-800/50">
                  {/* Color Blindness Type Selector */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Simulation Type</Label>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                      {[
                        { type: 'protanopia' as const, name: 'Protanopia', desc: 'Red-blind (~1% of men)' },
                        { type: 'deuteranopia' as const, name: 'Deuteranopia', desc: 'Green-blind (~1% of men)' },
                        { type: 'tritanopia' as const, name: 'Tritanopia', desc: 'Blue-blind (~0.003%)' }
                      ].map(({ type, name, desc }) => (
                        <Button
                          key={type}
                          variant={colorBlindnessType === type ? "default" : "outline"}
                          size="sm"
                          onClick={() => setColorBlindnessType(type)}
                          className="flex flex-col items-start p-3 h-auto"
                        >
                          <div className="font-medium text-sm">{name}</div>
                          <div className="text-xs opacity-70">{desc}</div>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Color Blindness Simulation */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">How this color appears</Label>
                    <div className="grid grid-cols-2 gap-4">
                      {/* Original Color */}
                      <div className="space-y-2">
                        <div className="text-xs font-medium text-center">Normal Vision</div>
                        <div
                          className="h-20 rounded-lg border-2 border-gray-300 flex items-center justify-center"
                          style={{ backgroundColor: color.hex }}
                        >
                          <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded px-2 py-1 text-xs font-mono">
                            {color.hex.toUpperCase()}
                          </div>
                        </div>
                      </div>

                      {/* Simulated Color */}
                      <div className="space-y-2">
                        <div className="text-xs font-medium text-center">
                          {colorBlindnessType === 'protanopia' ? 'Protanopia' :
                           colorBlindnessType === 'deuteranopia' ? 'Deuteranopia' : 'Tritanopia'}
                        </div>
                        <div
                          className="h-20 rounded-lg border-2 border-gray-300 flex items-center justify-center"
                          style={{ backgroundColor: simulateColorBlindness(color.hex, colorBlindnessType) }}
                        >
                          <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded px-2 py-1 text-xs font-mono">
                            {simulateColorBlindness(color.hex, colorBlindnessType).toUpperCase()}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Accessibility Note */}
                    <div className="text-xs text-gray-600 dark:text-gray-400 p-3 bg-orange-50/50 dark:bg-orange-900/20 rounded-lg">
                      <div className="font-medium mb-1">Accessibility Tip:</div>
                      <div>Consider how your color choices appear to users with color vision deficiencies. Use patterns, textures, or labels alongside color to convey information.</div>
                    </div>
                  </div>
                </div>
              )}
            </div>

          <Tabs defaultValue="hex" className="w-full">
            <TabsList className="grid grid-cols-3 w-full mb-4">
              <TabsTrigger value="hex" className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full border" style={{ backgroundColor: color.hex }}></span>
                HEX
              </TabsTrigger>
              <TabsTrigger value="rgb" className="flex items-center gap-2">
                <span className="flex">
                  <span className="w-1 h-3 bg-red-500 rounded-l"></span>
                  <span className="w-1 h-3 bg-green-500"></span>
                  <span className="w-1 h-3 bg-blue-500 rounded-r"></span>
                </span>
                RGB
              </TabsTrigger>
              <TabsTrigger value="hsl" className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-gradient-to-r from-red-500 via-green-500 to-blue-500"></span>
                HSL
              </TabsTrigger>
            </TabsList>

            <TabsContent value="hex" className="space-y-4 mt-0">
              <div className="space-y-2">
                <Label htmlFor="hex" className="text-sm font-medium flex items-center justify-between">
                  HEX Color
                  <span className="text-xs text-gray-500">(e.g., #FF0000)</span>
                </Label>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Input
                      id="hex"
                      type="text"
                      value={hexInput}
                      onChange={(e) => handleHexInput(e.target.value)}
                      className="font-mono pl-7"
                      placeholder="#000000"
                    />
                    <div
                      className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 rounded-full border"
                      style={{ backgroundColor: color.hex }}
                    />
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => copyToClipboard(color.hex, 'HEX')}
                    className="flex items-center gap-2"
                  >
                    <Copy className="h-4 w-4" />
                    {copied === 'HEX' ? 'Copied!' : 'Copy'}
                  </Button>
                </div>

                {showAlpha && (
                  <div className="flex gap-2 mt-4">
                    <div className="relative flex-1">
                      <Input
                        type="text"
                        value={colorWithAlpha.hex}
                        readOnly
                        className="font-mono pl-7"
                      />
                      <div
                        className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 rounded-full border"
                        style={{ backgroundColor: colorWithAlpha.hex }}
                      />
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => copyToClipboard(colorWithAlpha.hex, 'HEX with Alpha')}
                      className="flex items-center gap-2"
                    >
                      <Copy className="h-4 w-4" />
                      {copied === 'HEX with Alpha' ? 'Copied!' : 'Copy'}
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="rgb" className="space-y-4 mt-0">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label className="text-sm font-medium">Red</Label>
                    <span className="text-sm font-mono">{color.rgb.r}</span>
                  </div>
                  <div className="flex gap-2 items-center">
                    <Slider
                      value={[color.rgb.r]}
                      min={0}
                      max={255}
                      step={1}
                      onValueChange={(value) => handleRgbChange('r', value[0].toString())}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={color.rgb.r}
                      onChange={(e) => handleRgbChange('r', e.target.value)}
                      className="w-16 font-mono"
                      min="0"
                      max="255"
                    />
                  </div>
                  <div className="h-1 bg-red-500 mt-1 rounded-full" />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label className="text-sm font-medium">Green</Label>
                    <span className="text-sm font-mono">{color.rgb.g}</span>
                  </div>
                  <div className="flex gap-2 items-center">
                    <Slider
                      value={[color.rgb.g]}
                      min={0}
                      max={255}
                      step={1}
                      onValueChange={(value) => handleRgbChange('g', value[0].toString())}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={color.rgb.g}
                      onChange={(e) => handleRgbChange('g', e.target.value)}
                      className="w-16 font-mono"
                      min="0"
                      max="255"
                    />
                  </div>
                  <div className="h-1 bg-green-500 mt-1 rounded-full" />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label className="text-sm font-medium">Blue</Label>
                    <span className="text-sm font-mono">{color.rgb.b}</span>
                  </div>
                  <div className="flex gap-2 items-center">
                    <Slider
                      value={[color.rgb.b]}
                      min={0}
                      max={255}
                      step={1}
                      onValueChange={(value) => handleRgbChange('b', value[0].toString())}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={color.rgb.b}
                      onChange={(e) => handleRgbChange('b', e.target.value)}
                      className="w-16 font-mono"
                      min="0"
                      max="255"
                    />
                  </div>
                  <div className="h-1 bg-blue-500 mt-1 rounded-full" />
                </div>

                <Button
                  variant="outline"
                  onClick={() => copyToClipboard(
                    showAlpha
                      ? `rgba(${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b}, ${colorWithAlpha.rgb.a})`
                      : `rgb(${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b})`,
                    'RGB'
                  )}
                  className="w-full flex items-center gap-2 mt-2"
                >
                  <Copy className="h-4 w-4" />
                  {copied === 'RGB' ? 'Copied!' : `Copy ${showAlpha ? 'RGBA' : 'RGB'}`}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="hsl" className="space-y-4 mt-0">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label className="text-sm font-medium">Hue</Label>
                    <span className="text-sm font-mono">{color.hsl.h}°</span>
                  </div>
                  <div className="flex gap-2 items-center">
                    <Slider
                      value={[color.hsl.h]}
                      min={0}
                      max={360}
                      step={1}
                      onValueChange={(value) => handleHslChange('h', value[0])}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={color.hsl.h}
                      onChange={(e) => handleHslChange('h', parseInt(e.target.value) || 0)}
                      className="w-16 font-mono"
                      min="0"
                      max="360"
                    />
                  </div>
                  <div className="h-1 bg-gradient-to-r from-red-500 via-yellow-500 to-red-500 mt-1 rounded-full" />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label className="text-sm font-medium">Saturation</Label>
                    <span className="text-sm font-mono">{color.hsl.s}%</span>
                  </div>
                  <div className="flex gap-2 items-center">
                    <Slider
                      value={[color.hsl.s]}
                      min={0}
                      max={100}
                      step={1}
                      onValueChange={(value) => handleHslChange('s', value[0])}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={color.hsl.s}
                      onChange={(e) => handleHslChange('s', parseInt(e.target.value) || 0)}
                      className="w-16 font-mono"
                      min="0"
                      max="100"
                    />
                  </div>
                  <div
                    className="h-1 mt-1 rounded-full"
                    style={{
                      background: `linear-gradient(to right,
                        hsl(${color.hsl.h}, 0%, ${color.hsl.l}%),
                        hsl(${color.hsl.h}, 100%, ${color.hsl.l}%))`
                    }}
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label className="text-sm font-medium">Lightness</Label>
                    <span className="text-sm font-mono">{color.hsl.l}%</span>
                  </div>
                  <div className="flex gap-2 items-center">
                    <Slider
                      value={[color.hsl.l]}
                      min={0}
                      max={100}
                      step={1}
                      onValueChange={(value) => handleHslChange('l', value[0])}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={color.hsl.l}
                      onChange={(e) => handleHslChange('l', parseInt(e.target.value) || 0)}
                      className="w-16 font-mono"
                      min="0"
                      max="100"
                    />
                  </div>
                  <div
                    className="h-1 mt-1 rounded-full"
                    style={{
                      background: `linear-gradient(to right,
                        hsl(${color.hsl.h}, ${color.hsl.s}%, 0%),
                        hsl(${color.hsl.h}, ${color.hsl.s}%, 50%),
                        hsl(${color.hsl.h}, ${color.hsl.s}%, 100%))`
                    }}
                  />
                </div>

                <Button
                  variant="outline"
                  onClick={() => copyToClipboard(
                    showAlpha
                      ? `hsla(${color.hsl.h}, ${color.hsl.s}%, ${color.hsl.l}%, ${colorWithAlpha.hsl.a})`
                      : `hsl(${color.hsl.h}, ${color.hsl.s}%, ${color.hsl.l}%)`,
                    'HSL'
                  )}
                  className="w-full flex items-center gap-2 mt-2"
                >
                  <Copy className="h-4 w-4" />
                  {copied === 'HSL' ? 'Copied!' : `Copy ${showAlpha ? 'HSLA' : 'HSL'}`}
                </Button>
              </div>
            </TabsContent>
          </Tabs>

          {/* Phase 3: Advanced Export Preview */}
          <div className="mt-6 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium flex items-center gap-2">
                <Code2 className="h-4 w-4 text-purple-500" />
                Export Preview
              </h3>
              <Badge variant="outline" className="text-xs">Phase 3</Badge>
            </div>

            <Tabs value={exportFormat} onValueChange={(value) => setExportFormat(value as typeof exportFormat)} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="css" className="text-xs">
                  <FileCode className="h-3 w-3 mr-1" />
                  CSS
                </TabsTrigger>
                <TabsTrigger value="scss" className="text-xs">
                  <Braces className="h-3 w-3 mr-1" />
                  SCSS
                </TabsTrigger>
                <TabsTrigger value="tokens" className="text-xs">
                  <Layers className="h-3 w-3 mr-1" />
                  Tokens
                </TabsTrigger>
                <TabsTrigger value="json" className="text-xs">
                  <FileText className="h-3 w-3 mr-1" />
                  JSON
                </TabsTrigger>
              </TabsList>

              <TabsContent value="css" className="mt-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs font-medium">CSS Variables</Label>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(generateCSSVariables(), 'CSS Variables')}
                        className="h-7 text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => exportColor('css')}
                        className="h-7 text-xs"
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                  <div className="relative">
                    <pre className="text-xs bg-gray-50 dark:bg-gray-800 p-3 rounded border font-mono overflow-x-auto max-h-32">
                      {generateCSSVariables()}
                    </pre>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="scss" className="mt-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs font-medium">SCSS Variables & Map</Label>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(generateSCSSVariables(), 'SCSS Variables')}
                        className="h-7 text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => exportColor('scss')}
                        className="h-7 text-xs"
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                  <div className="relative">
                    <pre className="text-xs bg-gray-50 dark:bg-gray-800 p-3 rounded border font-mono overflow-x-auto max-h-32">
                      {generateSCSSVariables()}
                    </pre>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="tokens" className="mt-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs font-medium">Design Tokens (JSON)</Label>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(generateDesignTokens(), 'Design Tokens')}
                        className="h-7 text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => exportColor('tokens')}
                        className="h-7 text-xs"
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                  <div className="relative">
                    <pre className="text-xs bg-gray-50 dark:bg-gray-800 p-3 rounded border font-mono overflow-x-auto max-h-32">
                      {generateDesignTokens()}
                    </pre>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="json" className="mt-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs font-medium">Complete Color Data</Label>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(generateJSONExport(), 'JSON Export')}
                        className="h-7 text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => exportColor('json')}
                        className="h-7 text-xs"
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                  <div className="relative">
                    <pre className="text-xs bg-gray-50 dark:bg-gray-800 p-3 rounded border font-mono overflow-x-auto max-h-32">
                      {generateJSONExport()}
                    </pre>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Color Harmonies and Palettes */}
          <div className="mt-6 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Color Harmonies</h3>
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-gray-500 hover:text-gray-700"
                onClick={() => {
                  const complementary = getComplementaryColor(color);
                  const analogous = getAnalogousColors(color);
                  const triadic = getTriadicColors(color);

                  // Save all harmonies to palette
                  const newColors = [
                    complementary,
                    ...analogous,
                    ...triadic
                  ].filter(c => !savedColors.some(sc => sc.hex === c.hex));

                  if (newColors.length > 0) {
                    const updatedPalette = [...savedColors, ...newColors];
                    setSavedColors(updatedPalette);
                    localStorage.setItem('savedColors', JSON.stringify(updatedPalette));
                    toast.success('Color harmonies added to palette');
                  } else {
                    toast.info('Harmonies already in palette');
                  }
                }}
              >
                <Sparkles className="h-3 w-3 mr-1" />
                Save All
              </Button>
            </div>

            <div className="grid grid-cols-4 gap-2">
              <div className="space-y-1">
                <div
                  className="w-full aspect-square rounded-md border cursor-pointer hover:scale-105 transition-transform"
                  style={{ backgroundColor: color.hex }}
                  onClick={() => handleHexChange(color.hex)}
                  title="Current"
                />
                <p className="text-xs text-center">Current</p>
              </div>

              {/* Complementary */}
              {(() => {
                const complementary = getComplementaryColor(color);
                return (
                  <div className="space-y-1">
                    <div
                      className="w-full aspect-square rounded-md border cursor-pointer hover:scale-105 transition-transform"
                      style={{ backgroundColor: complementary.hex }}
                      onClick={() => handleHexChange(complementary.hex)}
                      title="Complementary"
                    />
                    <p className="text-xs text-center">Complement</p>
                  </div>
                );
              })()}

              {/* Analogous */}
              {getAnalogousColors(color).map((analogous, index) => (
                <div key={`analogous-${index}`} className="space-y-1">
                  <div
                    className="w-full aspect-square rounded-md border cursor-pointer hover:scale-105 transition-transform"
                    style={{ backgroundColor: analogous.hex }}
                    onClick={() => handleHexChange(analogous.hex)}
                    title={`Analogous ${index + 1}`}
                  />
                  <p className="text-xs text-center">Analogous {index + 1}</p>
                </div>
              ))}
            </div>

            {/* Enhanced Contrast Checker */}
            <div className="mt-4">
              <div className="flex items-center justify-between mb-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleSection('contrast')}
                  className="p-0 h-auto font-medium"
                >
                  <div className="flex items-center gap-2">
                    {expandedSections.contrast ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    <Contrast className="h-4 w-4 text-blue-500" />
                    Enhanced Contrast Checker
                  </div>
                </Button>
                <Badge variant="outline" className="text-xs">WCAG 2.1</Badge>
              </div>

              {expandedSections.contrast && (
                <div className="space-y-4 p-4 border rounded-lg bg-white/50 dark:bg-gray-800/50">
                  {/* Background Color Selector */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Test Backgrounds</Label>
                    <div className="flex flex-wrap gap-2">
                      {contrastBackgrounds.map((bg, index) => (
                        <button
                          key={index}
                          onClick={() => setSelectedContrastBg(bg)}
                          className={`w-8 h-8 rounded border-2 transition-all ${
                            selectedContrastBg === bg ? 'border-purple-500 scale-110' : 'border-gray-300'
                          }`}
                          style={{ backgroundColor: bg }}
                          title={`Background: ${bg}`}
                        />
                      ))}
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" size="sm" className="w-8 h-8 p-0">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-64">
                          <div className="space-y-2">
                            <Label>Custom Background</Label>
                            <HexColorInput
                              color={selectedContrastBg}
                              onChange={(newBg) => {
                                setSelectedContrastBg(newBg)
                                if (!contrastBackgrounds.includes(newBg)) {
                                  setContrastBackgrounds([...contrastBackgrounds, newBg])
                                }
                              }}
                              className="w-full p-2 border rounded"
                            />
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {/* Contrast Results */}
                  <div className="space-y-3">
                    {contrastBackgrounds.map((bg, index) => {
                      const compliance = getWCAGCompliance(color.hex, bg)
                      return (
                        <div key={index} className="flex items-center gap-3 p-3 border rounded-lg bg-white/30 dark:bg-gray-700/30">
                          <div
                            className="w-16 h-12 rounded border flex items-center justify-center text-xs font-medium"
                            style={{ backgroundColor: bg, color: color.hex }}
                          >
                            Aa
                          </div>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-mono">{bg.toUpperCase()}</span>
                              <span className="text-sm font-bold">{compliance.ratio.toFixed(2)}:1</span>
                            </div>
                            <div className="flex gap-2 text-xs">
                              <Badge variant={compliance.aa ? "default" : "destructive"} className="text-xs">
                                AA {compliance.aa ? '✓' : '✗'}
                              </Badge>
                              <Badge variant={compliance.aaLarge ? "default" : "destructive"} className="text-xs">
                                AA Large {compliance.aaLarge ? '✓' : '✗'}
                              </Badge>
                              <Badge variant={compliance.aaa ? "default" : "destructive"} className="text-xs">
                                AAA {compliance.aaa ? '✓' : '✗'}
                              </Badge>
                              <Badge variant={compliance.aaaLarge ? "default" : "destructive"} className="text-xs">
                                AAA Large {compliance.aaaLarge ? '✓' : '✗'}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>

                  {/* WCAG Guidelines */}
                  <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1 p-3 bg-blue-50/50 dark:bg-blue-900/20 rounded-lg">
                    <div className="font-medium mb-2">WCAG 2.1 Guidelines:</div>
                    <div>• <strong>AA Normal:</strong> 4.5:1 minimum (standard text)</div>
                    <div>• <strong>AA Large:</strong> 3:1 minimum (18pt+ or 14pt+ bold)</div>
                    <div>• <strong>AAA Normal:</strong> 7:1 minimum (enhanced)</div>
                    <div>• <strong>AAA Large:</strong> 4.5:1 minimum (enhanced large text)</div>
                  </div>
                </div>
              )}
            </div>

            {/* Color Palettes */}
            <div className="mt-4 space-y-4">
              {/* Saved Colors */}
              {savedColors.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium flex items-center">
                      <Heart className="h-4 w-4 mr-1" />
                      Saved Colors
                    </h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs text-gray-500 hover:text-gray-700"
                      onClick={() => {
                        setSavedColors([]);
                        localStorage.removeItem('savedColors');
                        toast.success('Palette cleared');
                      }}
                    >
                      Clear All
                    </Button>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {savedColors.map((savedColor, index) => (
                      <div key={`saved-${index}`} className="relative group">
                        <div
                          className="w-8 h-8 rounded-md border cursor-pointer hover:scale-110 transition-transform"
                          style={{ backgroundColor: savedColor.hex }}
                          onClick={() => handleHexChange(savedColor.hex)}
                          title={getColorName(savedColor.hex) || savedColor.hex}
                        />
                        <button
                          className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-red-500 text-white opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeSavedColor(savedColor.hex);
                          }}
                        >
                          <Trash className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recent Colors */}
              {recentColors.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium flex items-center mb-2">
                    <Palette className="h-4 w-4 mr-1" />
                    Recent Colors
                  </h3>

                  <div className="flex flex-wrap gap-2">
                    {recentColors.map((recentColor, index) => (
                      <div
                        key={`recent-${index}`}
                        className="w-8 h-8 rounded-md border cursor-pointer hover:scale-110 transition-transform"
                        style={{ backgroundColor: recentColor.hex }}
                        onClick={() => handleHexChange(recentColor.hex)}
                        title={getColorName(recentColor.hex) || recentColor.hex}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-gradient-to-br from-pink-50/50 to-orange-50/50 dark:from-gray-800/50 dark:to-gray-900/50 backdrop-blur-sm border-2 border-pink-100 dark:border-pink-900">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold bg-gradient-to-r from-pink-600 to-orange-600 bg-clip-text text-transparent">
            Gradient Generator
          </h2>
          <div className="flex gap-2">
            <div className="flex items-center gap-2">
              <input
                type="radio"
                id="linear"
                checked={gradientType === 'linear'}
                onChange={() => setGradientType('linear')}
                className="text-pink-600"
              />
              <Label htmlFor="linear" className="text-sm">Linear</Label>
            </div>
            <div className="flex items-center gap-2">
              <input
                type="radio"
                id="radial"
                checked={gradientType === 'radial'}
                onChange={() => setGradientType('radial')}
                className="text-pink-600"
              />
              <Label htmlFor="radial" className="text-sm">Radial</Label>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={randomGradient}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Random
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          {/* Color Stops */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Color Stops</Label>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (gradientStops.length < 5) {
                      // Add a new stop in the middle
                      const newPosition = Math.round((gradientStops[0].position + gradientStops[gradientStops.length - 1].position) / 2);
                      const newColor = {
                        hex: '#8844ee',
                        rgb: { r: 136, g: 68, b: 238 },
                        hsl: { h: 260, s: 82, l: 60 }
                      };

                      const newStops = [...gradientStops, { color: newColor, position: newPosition }];
                      newStops.sort((a, b) => a.position - b.position);

                      setGradientStops(newStops);
                      setActiveStopIndex(newStops.findIndex(stop => stop.position === newPosition));
                    } else {
                      toast.info('Maximum 5 color stops allowed');
                    }
                  }}
                  disabled={gradientStops.length >= 5}
                  className="h-7"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Stop
                </Button>
                {gradientStops.length > 2 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (gradientStops.length > 2) {
                        const newStops = gradientStops.filter((_, i) => i !== activeStopIndex);
                        setGradientStops(newStops);
                        setActiveStopIndex(Math.min(activeStopIndex, newStops.length - 1));
                      }
                    }}
                    className="h-7"
                  >
                    <Trash className="h-3 w-3 mr-1" />
                    Remove
                  </Button>
                )}
              </div>
            </div>

            {/* Color Stop Slider */}
            <div
              className="relative h-12 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden"
              ref={(el) => {
                if (el) {
                  // Add click handler to add stops at clicked position
                  el.onclick = (e) => {
                    if (e.target === el && gradientStops.length < 5) {
                      const rect = el.getBoundingClientRect();
                      const clickX = e.clientX - rect.left;
                      const position = Math.round((clickX / rect.width) * 100);

                      // Interpolate color between nearest stops
                      let leftStop = gradientStops[0];
                      let rightStop = gradientStops[gradientStops.length - 1];

                      // Find the two stops that surround the clicked position
                      for (let i = 0; i < gradientStops.length - 1; i++) {
                        if (gradientStops[i].position <= position && gradientStops[i + 1].position >= position) {
                          leftStop = gradientStops[i];
                          rightStop = gradientStops[i + 1];
                          break;
                        }
                      }

                      // Interpolate color
                      const ratio = (position - leftStop.position) / (rightStop.position - leftStop.position);
                      const r = Math.round(leftStop.color.rgb.r + ratio * (rightStop.color.rgb.r - leftStop.color.rgb.r));
                      const g = Math.round(leftStop.color.rgb.g + ratio * (rightStop.color.rgb.g - leftStop.color.rgb.g));
                      const b = Math.round(leftStop.color.rgb.b + ratio * (rightStop.color.rgb.b - leftStop.color.rgb.b));

                      const hex = rgbToHex(r, g, b);
                      const hsl = rgbToHsl(r, g, b);

                      const newStop = {
                        color: { hex, rgb: { r, g, b }, hsl },
                        position
                      };

                      const newStops = [...gradientStops, newStop];
                      newStops.sort((a, b) => a.position - b.position);

                      setGradientStops(newStops);
                      setActiveStopIndex(newStops.findIndex(stop => stop.position === position));
                    }
                  };
                }
              }}
            >
              <div
                className="absolute inset-0"
                style={{
                  background: gradientType === 'linear'
                    ? `linear-gradient(${gradientAngle}deg, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')})`
                    : `radial-gradient(${radialShape} ${radialSize} at ${radialPosition.x}% ${radialPosition.y}%, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')})`
                }}
              />

              {/* Stop Markers */}
              {gradientStops.map((stop, index) => (
                <div
                  key={index}
                  className={`absolute bottom-0 -translate-x-1/2 cursor-pointer transition-all ${index === activeStopIndex ? 'z-10' : 'z-0'}`}
                  style={{
                    left: `${stop.position}%`,
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setActiveStopIndex(index);
                  }}
                  onMouseDown={(e) => {
                    e.stopPropagation();

                    // Set active stop
                    setActiveStopIndex(index);

                    // Get slider element
                    const slider = e.currentTarget.parentElement;
                    if (!slider) return;

                    const rect = slider.getBoundingClientRect();
                    const handleMouseMove = (moveEvent: MouseEvent) => {
                      // Calculate new position
                      const x = moveEvent.clientX - rect.left;
                      const newPosition = Math.max(0, Math.min(100, Math.round((x / rect.width) * 100)));

                      // Update stop position
                      const newStops = [...gradientStops];
                      newStops[index] = {
                        ...newStops[index],
                        position: newPosition
                      };

                      // Sort stops by position
                      newStops.sort((a, b) => a.position - b.position);

                      // Update state
                      setGradientStops(newStops);
                      setActiveStopIndex(newStops.findIndex(s =>
                        s.color.hex === gradientStops[index].color.hex &&
                        s.position === newPosition
                      ));
                    };

                    const handleMouseUp = () => {
                      document.removeEventListener('mousemove', handleMouseMove);
                      document.removeEventListener('mouseup', handleMouseUp);
                    };

                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);
                  }}
                >
                  <div
                    className={`w-5 h-5 rounded-full border-2 ${index === activeStopIndex ? 'border-white scale-110' : 'border-gray-300'}`}
                    style={{
                      backgroundColor: stop.color.hex,
                      transform: `translateY(${index === activeStopIndex ? '-10px' : '-5px'})`,
                    }}
                  />
                </div>
              ))}
            </div>

            {/* Active Color Stop */}
            <div className="p-4 border rounded-lg bg-white/50 dark:bg-gray-800/50">
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-medium">
                  Stop {activeStopIndex + 1} Color
                </Label>
                <div className="flex items-center gap-2">
                  <Label className="text-xs">Position:</Label>
                  <Input
                    type="number"
                    value={gradientStops[activeStopIndex].position}
                    onChange={(e) => {
                      const newPosition = Math.max(0, Math.min(100, parseInt(e.target.value) || 0));
                      const newStops = [...gradientStops];
                      newStops[activeStopIndex] = {
                        ...newStops[activeStopIndex],
                        position: newPosition
                      };
                      newStops.sort((a, b) => a.position - b.position);
                      setGradientStops(newStops);
                      setActiveStopIndex(newStops.findIndex(stop => stop.position === newPosition));
                    }}
                    className="w-16 h-7 text-xs"
                    min="0"
                    max="100"
                  />
                  <span className="text-xs">%</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Input
                        type="text"
                        value={gradientStops[activeStopIndex].color.hex}
                        onChange={(e) => {
                          const hex = e.target.value;
                          if (/^#[0-9A-F]{6}$/i.test(hex)) {
                            const rgb = hexToRgb(hex);
                            if (rgb) {
                              const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
                              const newStops = [...gradientStops];
                              newStops[activeStopIndex] = {
                                ...newStops[activeStopIndex],
                                color: { hex, rgb, hsl }
                              };
                              setGradientStops(newStops);
                            }
                          }
                        }}
                        className="font-mono pl-7"
                      />
                      <div
                        className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 rounded-full border"
                        style={{ backgroundColor: gradientStops[activeStopIndex].color.hex }}
                      />
                    </div>
                    <input
                      type="color"
                      value={gradientStops[activeStopIndex].color.hex}
                      onChange={(e) => {
                        const hex = e.target.value;
                        const rgb = hexToRgb(hex);
                        if (rgb) {
                          const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
                          const newStops = [...gradientStops];
                          newStops[activeStopIndex] = {
                            ...newStops[activeStopIndex],
                            color: { hex, rgb, hsl }
                          };
                          setGradientStops(newStops);
                        }
                      }}
                      className="w-10 h-10 p-1 rounded border"
                    />
                  </div>

                  {/* RGB Controls */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs">Red</Label>
                      <span className="text-xs font-mono">{gradientStops[activeStopIndex].color.rgb.r}</span>
                    </div>
                    <Slider
                      value={[gradientStops[activeStopIndex].color.rgb.r]}
                      min={0}
                      max={255}
                      step={1}
                      onValueChange={(value) => {
                        const r = value[0];
                        const { g, b } = gradientStops[activeStopIndex].color.rgb;
                        const hex = rgbToHex(r, g, b);
                        const hsl = rgbToHsl(r, g, b);

                        const newStops = [...gradientStops];
                        newStops[activeStopIndex] = {
                          ...newStops[activeStopIndex],
                          color: { hex, rgb: { r, g, b }, hsl }
                        };
                        setGradientStops(newStops);
                      }}
                      className="h-2"
                    />
                    <div className="h-1 bg-gradient-to-r from-black to-red-500 rounded-full" />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs">Green</Label>
                      <span className="text-xs font-mono">{gradientStops[activeStopIndex].color.rgb.g}</span>
                    </div>
                    <Slider
                      value={[gradientStops[activeStopIndex].color.rgb.g]}
                      min={0}
                      max={255}
                      step={1}
                      onValueChange={(value) => {
                        const g = value[0];
                        const { r, b } = gradientStops[activeStopIndex].color.rgb;
                        const hex = rgbToHex(r, g, b);
                        const hsl = rgbToHsl(r, g, b);

                        const newStops = [...gradientStops];
                        newStops[activeStopIndex] = {
                          ...newStops[activeStopIndex],
                          color: { hex, rgb: { r, g, b }, hsl }
                        };
                        setGradientStops(newStops);
                      }}
                      className="h-2"
                    />
                    <div className="h-1 bg-gradient-to-r from-black to-green-500 rounded-full" />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs">Blue</Label>
                      <span className="text-xs font-mono">{gradientStops[activeStopIndex].color.rgb.b}</span>
                    </div>
                    <Slider
                      value={[gradientStops[activeStopIndex].color.rgb.b]}
                      min={0}
                      max={255}
                      step={1}
                      onValueChange={(value) => {
                        const b = value[0];
                        const { r, g } = gradientStops[activeStopIndex].color.rgb;
                        const hex = rgbToHex(r, g, b);
                        const hsl = rgbToHsl(r, g, b);

                        const newStops = [...gradientStops];
                        newStops[activeStopIndex] = {
                          ...newStops[activeStopIndex],
                          color: { hex, rgb: { r, g, b }, hsl }
                        };
                        setGradientStops(newStops);
                      }}
                      className="h-2"
                    />
                    <div className="h-1 bg-gradient-to-r from-black to-blue-500 rounded-full" />
                  </div>
                </div>

                {/* Color Wheel */}
                <div className="flex flex-col items-center justify-center">
                  <HexColorPicker
                    color={gradientStops[activeStopIndex].color.hex}
                    onChange={(hex) => {
                      const rgb = hexToRgb(hex);
                      if (rgb) {
                        const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
                        const newStops = [...gradientStops];
                        newStops[activeStopIndex] = {
                          ...newStops[activeStopIndex],
                          color: { hex, rgb, hsl }
                        };
                        setGradientStops(newStops);
                      }
                    }}
                    className="w-full max-w-[180px]"
                  />

                  {/* Color Name */}
                  {getColorName(gradientStops[activeStopIndex].color.hex) && (
                    <div className="mt-2 text-xs text-center text-gray-500 italic">
                      {getColorName(gradientStops[activeStopIndex].color.hex)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Gradient Presets */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Gradient Presets</Label>
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-gray-500 hover:text-gray-700"
                onClick={randomGradient}
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Random
              </Button>
            </div>

            <div className="grid grid-cols-3 sm:grid-cols-6 gap-2">
              {gradientPresets.map((preset, index) => (
                <div
                  key={index}
                  className="cursor-pointer group"
                  onClick={() => {
                    // Apply preset
                    setGradientType(preset.type as 'linear' | 'radial');

                    // Convert preset stops to gradient stops
                    const newStops = preset.stops.map(stop => {
                      const hex = stop.color;
                      const rgb = hexToRgb(hex) || { r: 0, g: 0, b: 0 };
                      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
                      return {
                        color: { hex, rgb, hsl },
                        position: stop.position
                      };
                    });

                    setGradientStops(newStops);
                    setActiveStopIndex(0);

                    if (preset.type === 'linear' && preset.angle !== undefined) {
                      setGradientAngle(preset.angle);
                    } else if (preset.type === 'radial') {
                      if (preset.shape) setRadialShape(preset.shape as 'circle' | 'ellipse');
                      if (preset.size) setRadialSize(preset.size as 'closest-side' | 'closest-corner' | 'farthest-side' | 'farthest-corner');
                      if (preset.position) setRadialPosition(preset.position);
                    }
                  }}
                >
                  <div className="space-y-1">
                    <div
                      className="w-full h-12 rounded-md border overflow-hidden group-hover:scale-105 transition-transform"
                      style={{
                        background: preset.type === 'linear'
                          ? `linear-gradient(${preset.angle || 90}deg, ${preset.stops.map(s => `${s.color} ${s.position}%`).join(', ')})`
                          : `radial-gradient(${preset.shape || 'circle'} ${preset.size || 'farthest-corner'} at ${preset.position?.x || 50}% ${preset.position?.y || 50}%, ${preset.stops.map(s => `${s.color} ${s.position}%`).join(', ')})`
                      }}
                    />
                    <p className="text-xs text-center truncate">{preset.name}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Gradient Type Controls */}
          <div className="space-y-4">
            {gradientType === 'linear' && (
              <div className="p-4 border rounded-lg bg-white/50 dark:bg-gray-800/50">
                <Label className="text-sm font-medium mb-3 block">Linear Gradient Settings</Label>
                <div className="flex flex-wrap items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Label className="text-sm font-medium">Angle</Label>
                    <div className="relative w-20">
                      <Input
                        type="number"
                        value={gradientAngle}
                        onChange={(e) => setGradientAngle(Math.max(0, Math.min(360, parseInt(e.target.value) || 0)))}
                        min="0"
                        max="360"
                        className="pr-6"
                      />
                      <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-gray-500">°</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {[0, 45, 90, 135, 180, 225, 270, 315].map((angle) => (
                      <Button
                        key={angle}
                        variant="outline"
                        size="sm"
                        onClick={() => setGradientAngle(angle)}
                        className={`w-10 h-8 ${gradientAngle === angle ? 'border-pink-500 bg-pink-50 dark:bg-pink-900/20' : ''}`}
                      >
                        {angle}°
                      </Button>
                    ))}
                  </div>

                  <div className="relative w-20 h-20 flex-shrink-0">
                    <div
                      className="absolute inset-0 rounded-full border-2 border-gray-200 dark:border-gray-700"
                      style={{
                        background: `conic-gradient(from -90deg, ${gradientStops.map(stop => stop.color.hex).join(', ')})`
                      }}
                    />
                    <div
                      className="absolute w-0.5 h-6 bg-white dark:bg-gray-800 origin-bottom left-1/2 -translate-x-1/2"
                      style={{
                        transform: `rotate(${gradientAngle}deg) translateY(-50%)`
                      }}
                    >
                      <div className="w-2 h-2 rounded-full bg-pink-500 -translate-x-1/3" />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {gradientType === 'radial' && (
              <div className="p-4 border rounded-lg bg-white/50 dark:bg-gray-800/50">
                <Label className="text-sm font-medium mb-3 block">Radial Gradient Settings</Label>
                <div className="flex flex-wrap items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Label className="text-sm font-medium">Shape</Label>
                    <select
                      className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors"
                      value={radialShape}
                      onChange={(e) => setRadialShape(e.target.value as 'circle' | 'ellipse')}
                    >
                      <option value="circle">Circle</option>
                      <option value="ellipse">Ellipse</option>
                    </select>
                  </div>
                  <div className="flex items-center gap-2">
                    <Label className="text-sm font-medium">Size</Label>
                    <select
                      className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors"
                      value={radialSize}
                      onChange={(e) => setRadialSize(e.target.value as 'closest-side' | 'closest-corner' | 'farthest-side' | 'farthest-corner')}
                    >
                      <option value="closest-side">Closest Side</option>
                      <option value="closest-corner">Closest Corner</option>
                      <option value="farthest-side">Farthest Side</option>
                      <option value="farthest-corner">Farthest Corner</option>
                    </select>
                  </div>

                  <div className="relative w-20 h-20 flex-shrink-0">
                    <div
                      className="absolute inset-0 rounded-full border-2 border-gray-200 dark:border-gray-700 overflow-hidden"
                      style={{
                        background: `radial-gradient(${radialShape} ${radialSize} at ${radialPosition.x}% ${radialPosition.y}%, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')})`
                      }}
                      onClick={(e) => {
                        const rect = e.currentTarget.getBoundingClientRect()
                        const x = ((e.clientX - rect.left) / rect.width) * 100
                        const y = ((e.clientY - rect.top) / rect.height) * 100
                        setRadialPosition({
                          x: Math.round(Math.max(0, Math.min(100, x))),
                          y: Math.round(Math.max(0, Math.min(100, y)))
                        })
                      }}
                    >
                      <div
                        className="absolute w-2 h-2 rounded-full bg-pink-500 border-2 border-white dark:border-gray-800 -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                        style={{
                          left: `${radialPosition.x}%`,
                          top: `${radialPosition.y}%`
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Preview */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Preview</Label>
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-gray-500 hover:text-gray-700"
                onClick={() => {
                  // Toggle between light and dark background
                  const preview = document.getElementById('gradient-preview');
                  if (preview) {
                    preview.classList.toggle('bg-gray-900');
                    preview.classList.toggle('bg-white');
                  }
                }}
              >
                Toggle Background
              </Button>
            </div>
            <div
              id="gradient-preview"
              className="w-full h-40 rounded-lg border shadow-inner bg-white transition-colors duration-300"
              style={{
                background: gradientType === 'linear'
                  ? `linear-gradient(${gradientAngle}deg, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')})`
                  : `radial-gradient(${radialShape} ${radialSize} at ${radialPosition.x}% ${radialPosition.y}%, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')})`
              }}
            />
          </div>

          {/* Accessibility Check */}
          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium flex items-center">
                <Contrast className="h-4 w-4 mr-1" />
                Gradient Accessibility
              </Label>
              <span className="text-xs text-gray-500">For text readability</span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Light Text on Gradient */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-4 h-4 rounded-full bg-white border" />
                  <span className="text-xs">White Text on Gradient</span>
                </div>
                <div
                  className="p-4 rounded border text-center font-medium text-white"
                  style={{
                    background: gradientType === 'linear'
                      ? `linear-gradient(${gradientAngle}deg, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')})`
                      : `radial-gradient(${radialShape} ${radialSize} at ${radialPosition.x}% ${radialPosition.y}%, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')})`
                  }}
                >
                  Sample Text on Gradient
                </div>
                <div className="mt-2 text-xs text-center">
                  {(() => {
                    // Calculate average luminance of gradient colors
                    const avgLuminance = gradientStops.reduce((sum, stop) => {
                      const ratio = getContrastRatio(stop.color.hex, '#FFFFFF');
                      return sum + ratio;
                    }, 0) / gradientStops.length;

                    const level = avgLuminance >= 4.5 ? 'AA' : avgLuminance >= 3 ? 'AA Large' : 'Fail';
                    return (
                      <span className={avgLuminance >= 4.5 ? 'text-green-600' : avgLuminance >= 3 ? 'text-yellow-600' : 'text-red-600'}>
                        ~{avgLuminance.toFixed(2)}:1 ({level})
                      </span>
                    );
                  })()}
                </div>
              </div>

              {/* Dark Text on Gradient */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-4 h-4 rounded-full bg-black border" />
                  <span className="text-xs">Black Text on Gradient</span>
                </div>
                <div
                  className="p-4 rounded border text-center font-medium text-black"
                  style={{
                    background: gradientType === 'linear'
                      ? `linear-gradient(${gradientAngle}deg, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')})`
                      : `radial-gradient(${radialShape} ${radialSize} at ${radialPosition.x}% ${radialPosition.y}%, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')})`
                  }}
                >
                  Sample Text on Gradient
                </div>
                <div className="mt-2 text-xs text-center">
                  {(() => {
                    // Calculate average luminance of gradient colors
                    const avgLuminance = gradientStops.reduce((sum, stop) => {
                      const ratio = getContrastRatio(stop.color.hex, '#000000');
                      return sum + ratio;
                    }, 0) / gradientStops.length;

                    const level = avgLuminance >= 4.5 ? 'AA' : avgLuminance >= 3 ? 'AA Large' : 'Fail';
                    return (
                      <span className={avgLuminance >= 4.5 ? 'text-green-600' : avgLuminance >= 3 ? 'text-yellow-600' : 'text-red-600'}>
                        ~{avgLuminance.toFixed(2)}:1 ({level})
                      </span>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>

          {/* CSS Code */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">CSS Code</Label>

            {/* Standard CSS */}
            <div className="relative">
              <Input
                type="text"
                value={
                  gradientType === 'linear'
                    ? `background: linear-gradient(${gradientAngle}deg, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')});`
                    : `background: radial-gradient(${radialShape} ${radialSize} at ${radialPosition.x}% ${radialPosition.y}%, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')});`
                }
                readOnly
                className="font-mono pr-24"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(
                  gradientType === 'linear'
                    ? `background: linear-gradient(${gradientAngle}deg, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')});`
                    : `background: radial-gradient(${radialShape} ${radialSize} at ${radialPosition.x}% ${radialPosition.y}%, ${gradientStops.map(stop => `${stop.color.hex} ${stop.position}%`).join(', ')});`,
                  'gradient'
                )}
                className="absolute right-1 top-1 h-7"
              >
                <Copy className="h-3 w-3 mr-1" />
                {copied === 'gradient' ? 'Copied!' : 'Copy'}
              </Button>
            </div>

            {/* Tailwind CSS */}
            <div className="relative">
              <Label className="text-xs text-gray-500 mb-1 block">Tailwind CSS (approximate)</Label>
              <Input
                type="text"
                value={
                  gradientType === 'linear'
                    ? `bg-gradient-to-r from-[${gradientStops[0].color.hex}] to-[${gradientStops[gradientStops.length - 1].color.hex}]${gradientStops.length > 2 ? ` via-[${gradientStops[Math.floor(gradientStops.length / 2)].color.hex}]` : ''}`
                    : `bg-radial from-[${gradientStops[0].color.hex}] to-[${gradientStops[gradientStops.length - 1].color.hex}]`
                }
                readOnly
                className="font-mono pr-24"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(
                  gradientType === 'linear'
                    ? `bg-gradient-to-r from-[${gradientStops[0].color.hex}] to-[${gradientStops[gradientStops.length - 1].color.hex}]${gradientStops.length > 2 ? ` via-[${gradientStops[Math.floor(gradientStops.length / 2)].color.hex}]` : ''}`
                    : `bg-radial from-[${gradientStops[0].color.hex}] to-[${gradientStops[gradientStops.length - 1].color.hex}]`,
                  'tailwind'
                )}
                className="absolute right-1 top-1 h-7"
              >
                <Copy className="h-3 w-3 mr-1" />
                {copied === 'tailwind' ? 'Copied!' : 'Copy'}
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
