'use client';

import React, { useState, use<PERSON>allback, useRef } from 'react'; // Added useRef
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import {
  Loader2,
  Download,
  Sparkles,
  Palette,
  Wand2,
  Image as ImageIcon,
  Shuffle,
  Ratio as RatioIcon,
  X,
  Trash2,
  RefreshCw,
  CreditCard,
  User,
  LogOut,
  Eye,
  Copy,
  Heart,
  Share2,
  Maximize2,
  Upload,
  Edit3,
  <PERSON><PERSON>,
  Expand,
  <PERSON><PERSON><PERSON>,
  Setting<PERSON>
} from 'lucide-react';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useImageGenerator } from '@/hooks/useImageGenerator';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { AuthModal } from '@/components/auth/AuthModal';

const styles = [
  { value: 'photographic', label: 'Photographic', description: 'Ultra-realistic photography style' },
  { value: 'digital-art', label: 'Digital Art', description: 'Modern digital artwork style' },
  { value: 'cinematic', label: 'Cinematic', description: 'Movie-like dramatic style' },
  { value: 'anime', label: 'Anime', description: 'Japanese anime style' },
  { value: 'fantasy-art', label: 'Fantasy Art', description: 'Magical and fantastical style' },
  { value: 'line-art', label: 'Line Art', description: 'Clean line drawing style' },
  { value: 'comic-book', label: 'Comic Book', description: 'Comic book illustration style' },
  { value: 'abstract', label: 'Abstract', description: 'Abstract artistic style' }
];

const aspectRatios = [
  { value: '1:1', label: 'Square (1:1)', description: 'Perfect for social media posts' },
  { value: '16:9', label: 'Landscape (16:9)', description: 'Great for wallpapers and presentations' },
  { value: '9:16', label: 'Portrait (9:16)', description: 'Ideal for mobile and stories' },
  { value: '4:3', label: 'Classic (4:3)', description: 'Traditional photography ratio' },
  { value: '3:4', label: 'Portrait (3:4)', description: 'Portrait photography ratio' },
  { value: '21:9', label: 'Ultra-wide (21:9)', description: 'Cinematic ultra-wide format' },
  { value: '2:3', label: 'Tall Portrait (2:3)', description: 'Magazine cover style' },
  { value: '3:2', label: 'Wide Landscape (3:2)', description: 'DSLR camera standard' }
];

const editTypes = [
  { value: 'kontext-pro', label: 'Kontext Pro', description: 'Smart image editing with context' },
  { value: 'kontext-max', label: 'Kontext Max', description: 'Maximum quality context editing' },
  { value: 'fill', label: 'Fill/Inpaint', description: 'Fill or modify specific areas' },
  { value: 'expand', label: 'Expand Canvas', description: 'Extend image beyond original borders' }
];

const qualitySettings = [
  { value: 'standard', label: 'Standard', description: 'Good quality, faster generation' },
  { value: 'ultra', label: 'Ultra (Recommended)', description: 'Maximum quality, premium results' }
];

const examplePrompts = [
  "a serene mountain lake at sunset with snow-capped peaks reflecting in crystal clear water",
  "futuristic cyberpunk city street with neon signs and flying cars in the rain",
  "magical forest clearing with glowing mushrooms and ethereal fairy lights",
  "professional portrait of a confident businesswoman in modern office setting",
  "cozy coffee shop interior with warm lighting and people reading books",
  "abstract geometric composition with vibrant colors and dynamic shapes"
];

const MAX_PROMPT_LENGTH = 500;

export default function ImageGenerator() {
  const [safetyTolerance, setSafetyTolerance] = useState([2]);
  // const [isLoading, setIsLoading] = useState(false); // Removed local isLoading

  const {
    prompt, setPrompt,
    style, setStyle,
    aspectRatio, setAspectRatio,
    generatedImage,
    setGeneratedImage,
    isLoading, // Ensure isLoading is destructured from the hook
    user,
    savedImages,
    credits,
    isInitializing,
    generateImage,
    deleteImage,
    handleSignOut,
    loadMoreImages,
    loadPrevImages,
    hasMore,
    currentPage,
    processEditedImage, // Added this
  } = useImageGenerator();

  const fileInputRef = useRef<HTMLInputElement>(null); // Added ref for file input
  const [selectedImage, setSelectedImage] = useState<any>(null);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [activeTab, setActiveTab] = useState('generate');
  const [editingImage, setEditingImage] = useState<File | null>(null);
  const [editPrompt, setEditPrompt] = useState('');
  const [editType, setEditType] = useState('kontext-pro');
  const [editStrength, setEditStrength] = useState([0.6]);
  const [qualityMode, setQualityMode] = useState('ultra');
  const [promptUpsampling, setPromptUpsampling] = useState(true);

  const getRandomPrompt = () => {
    const randomIndex = Math.floor(Math.random() * examplePrompts.length);
    setPrompt(examplePrompts[randomIndex]);
  };

    // Use the hook's generateImage function which handles credits and saving properly
    const handleGenerateClick = async () => {
      if (!user) {
        setShowAuthModal(true);
        return;
      }
      await generateImage();
    };

  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast.error('Image file size must be less than 10MB');
        return;
      }
      setEditingImage(file);
      toast.success('Image uploaded successfully!');
    }
  }, []);

  const handleEditImage = async () => {
    if (!user) {
      setShowAuthModal(true);
      return;
    }

    if (!editingImage || !editPrompt.trim()) {
      toast.error('Please upload an image and enter an edit prompt');
      return;
    }

    if (credits === null || credits <= 0) {
      toast.error('You have no credits left.');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('inputImage', editingImage);
      formData.append('prompt', editPrompt);
      formData.append('editType', editType);
      formData.append('strength', editStrength[0].toString());
      formData.append('aspectRatio', aspectRatio);
      formData.append('safetyTolerance', safetyTolerance[0].toString());

      const response = await fetch('/api/edit-image', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to edit image');
      }

      const data = await response.json();
      setGeneratedImage(data.imageUrl);
      // Call the hook function to handle post-edit logic
      if (user?.uid && data.imageUrl) {
        processEditedImage({
          userId: user.uid,
          editedImageBackblazeUrl: data.imageUrl, // Assuming this is the persistent URL
          originalImageFileName: editingImage?.name, // editingImage is File | null
          prompt: editPrompt,
          editType: editType,
          editStrength: editStrength[0],
          aspectRatio: aspectRatio, // This is the general aspectRatio, might need specific from 'data'
          width: data.width || 1024, // Assuming data.width, fallback to a default
          height: data.height || 1024, // Assuming data.height, fallback to a default
        });
      } else {
        console.warn("Skipping processEditedImage call: missing user, edited image URL, or other critical data.");
        toast.warning("Image edited, but post-processing skipped (missing data).");
      }
      toast.success('Image edited successfully!');
    } catch (error: any) {
      console.error('Image editing error:', error);
      toast.error(error.message || 'Failed to edit image');
    }
  };

    const downloadImage = async (imageUrl: string, filename: string) => {
    try {
      console.log('Starting download for:', imageUrl);

      // Use our proxy API to handle CORS issues
      const proxyUrl = `/api/download-image?url=${encodeURIComponent(imageUrl)}&filename=${encodeURIComponent(filename)}`;

      const link = document.createElement('a');
      link.href = proxyUrl;
      link.download = filename;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Image download started!');

    } catch (error) {
      console.error('Download error:', error);
      // Fallback: Open image in new tab
      try {
        const link = document.createElement('a');
        link.href = imageUrl;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success('Image opened in new tab. Right-click to save!');
      } catch (fallbackError) {
        toast.error('Failed to download image. Try right-clicking the image to save.');
      }
    }
  };

  const copyPrompt = (promptText: string) => {
    navigator.clipboard.writeText(promptText);
    toast.success('Prompt copied to clipboard!');
  };

  const clearEditingImage = () => {
    setEditingImage(null);
    setEditPrompt('');
  };

  if (isInitializing) {
    return (
      <div className="w-full max-w-7xl mx-auto p-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-muted-foreground">Initializing...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 space-y-8">
      {/* Header with user info */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <ImageIcon className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">AI Image Generator</h2>
            <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
              Ultra Model
            </Badge>
          </div>
          {user && (
            <Badge variant="secondary" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              {credits} credits
            </Badge>
          )}
        </div>

        {user && (
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <User className="h-4 w-4" />
              {user.email}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Sign out button clicked');
                handleSignOut();
              }}
              type="button"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        )}
      </div>

      {/* Main content with tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generate" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Generate Image
          </TabsTrigger>
          <TabsTrigger value="edit" className="flex items-center gap-2">
            <Edit3 className="h-4 w-4" />
            Edit Image
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-8">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            {/* Input Panel */}
            <Card className="border-2 border-primary/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wand2 className="h-5 w-5" />
                  Create Your Image
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Quality Mode */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Quality Mode
                  </Label>
                  <Select value={qualityMode} onValueChange={setQualityMode}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose quality mode" />
                    </SelectTrigger>
                    <SelectContent>
                      {qualitySettings.map((quality) => (
                        <SelectItem key={quality.value} value={quality.value}>
                          <div className="flex flex-col">
                            <span>{quality.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {quality.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Prompt Input */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Describe your image</Label>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={getRandomPrompt}
                        className="text-xs"
                      >
                        <Shuffle className="h-3 w-3 mr-1" />
                        Random
                      </Button>
                      <span className="text-xs text-muted-foreground">
                        {prompt.length}/{MAX_PROMPT_LENGTH}
                      </span>
                    </div>
                  </div>
                  <Textarea
                    placeholder="E.g., A serene mountain lake at sunset with snow-capped peaks reflecting in the crystal clear water..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    maxLength={MAX_PROMPT_LENGTH}
                    className="min-h-[120px] resize-none"
                  />
                  {examplePrompts.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {examplePrompts.slice(0, 3).map((example, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => setPrompt(example)}
                          className="text-xs h-auto py-1 px-2"
                        >
                          {example.slice(0, 40)}...
                        </Button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Style Selection */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Palette className="h-4 w-4" />
                    Art Style
                  </Label>
                  <Select value={style} onValueChange={setStyle}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a style" />
                    </SelectTrigger>
                    <SelectContent>
                      {styles.map((styleOption) => (
                        <SelectItem key={styleOption.value} value={styleOption.value}>
                          <div className="flex flex-col">
                            <span>{styleOption.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {styleOption.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Aspect Ratio */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <RatioIcon className="h-4 w-4" />
                    Aspect Ratio
                  </Label>
                  <Select value={aspectRatio} onValueChange={setAspectRatio}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose aspect ratio" />
                    </SelectTrigger>
                    <SelectContent>
                      {aspectRatios.map((ratio) => (
                        <SelectItem key={ratio.value} value={ratio.value}>
                          <div className="flex flex-col">
                            <span>{ratio.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {ratio.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Advanced Settings */}
                <div className="space-y-4 border rounded-lg p-4 bg-muted/30">
                  <Label className="text-sm font-medium">Advanced Settings</Label>

                  {/* Safety Tolerance */}
                  <div className="space-y-2">
                    <Label className="text-xs">Safety Tolerance: {safetyTolerance[0]}</Label>
                    <Slider
                      value={safetyTolerance}
                      onValueChange={setSafetyTolerance}
                      max={6}
                      min={0}
                      step={1}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      Higher values allow more creative freedom
                    </p>
                  </div>
                </div>

                {/* Generate Button */}
                <Button
                  onClick={handleGenerateClick}
                  disabled={isLoading || !prompt.trim()}
                  className="w-full h-12 text-lg bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      Generating with Ultra Model...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-5 w-5 mr-2" />
                      Generate Ultra Image
                      {user && credits !== null && (
                        <span className="ml-2 text-sm opacity-75">
                          ({credits} credits)
                        </span>
                      )}
                    </>
                  )}
                </Button>

                {!user && (
                  <p className="text-sm text-muted-foreground text-center">
                    Sign in to generate and save images
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Preview Panel */}
            <Card className="border-2 border-primary/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ImageIcon className="h-5 w-5" />
                  Generated Image
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-square bg-muted rounded-lg flex items-center justify-center relative overflow-hidden">
                  {isLoading ? (
                    <div className="text-center">
                      <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-primary" />
                      <p className="text-muted-foreground">Creating your ultra-quality image...</p>
                      <div className="mt-4 w-64 bg-muted-foreground/20 rounded-full h-2">
                        <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full animate-pulse w-1/3"></div>
                      </div>
                    </div>
                  ) : generatedImage ? (
                    <div className="relative w-full h-full group">
                      <img
                        src={generatedImage}
                        alt="Generated artwork"
                        className="w-full h-full object-contain rounded-lg"
                      />
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => downloadImage(generatedImage, `ultra-generated-${Date.now()}.png`)}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => setSelectedImage({ imageUrl: generatedImage, prompt })}
                        >
                          <Maximize2 className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground">
                      <ImageIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">Your ultra-quality image will appear here</p>
                      <p className="text-sm">Enter a prompt and click generate to create amazing AI art</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="edit" className="space-y-8">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            {/* Edit Input Panel */}
            <Card className="border-2 border-blue-200 dark:border-blue-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Edit3 className="h-5 w-5" />
                  Edit Your Image
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Image Upload */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    Upload Image to Edit
                  </Label>
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                    {editingImage ? (
                      <div className="space-y-4">
                        <div className="flex items-center justify-center">
                          <img
                            src={URL.createObjectURL(editingImage)}
                            alt="Uploaded for editing"
                            className="max-h-32 object-contain rounded"
                          />
                        </div>
                        <div className="flex items-center gap-2 justify-center">
                          <span className="text-sm text-muted-foreground">{editingImage.name}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={clearEditingImage}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground mb-2">
                          Click to upload an image to edit
                        </p>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                          id="image-upload"
                        />
                        <Label
                          htmlFor="image-upload"
                          className="cursor-pointer inline-block p-2 border rounded-md hover:bg-accent" // Basic styling for visibility
                          onClick={(e: React.MouseEvent<HTMLLabelElement>) => {
                            console.log('ImageGenerator: Choose Image LABEL clicked. Event defaultPrevented:', e.isDefaultPrevented());
                            console.log('ImageGenerator: fileInputRef.current before click attempt:', fileInputRef.current);
                            if (fileInputRef.current) {
                              fileInputRef.current.click();
                              console.log('ImageGenerator: Called click() on fileInputRef.current');
                            } else {
                              console.error('ImageGenerator: fileInputRef.current is null, cannot call click()');
                            }
                          }}
                        >
                          Choose Image (Styled Label)
                        </Label>
                      </div>
                    )}
                  </div>
                </div>

                {/* Edit Type */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Layers className="h-4 w-4" />
                    Edit Type
                  </Label>
                  <Select value={editType} onValueChange={setEditType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose edit type" />
                    </SelectTrigger>
                    <SelectContent>
                      {editTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex flex-col">
                            <span>{type.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {type.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Edit Prompt */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Edit Instructions</Label>
                  <Textarea
                    placeholder="Describe what changes you want to make to the image..."
                    value={editPrompt}
                    onChange={(e) => setEditPrompt(e.target.value)}
                    className="min-h-[100px] resize-none"
                  />
                </div>

                {/* Edit Strength */}
                <div className="space-y-2">
                  <Label className="text-sm">Edit Strength: {editStrength[0]}</Label>
                  <Slider
                    value={editStrength}
                    onValueChange={setEditStrength}
                    max={1}
                    min={0.1}
                    step={0.1}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Lower values preserve more of the original image
                  </p>
                </div>

                {/* Edit Button */}
                <Button
                  onClick={handleEditImage}
                  disabled={isLoading || !editingImage || !editPrompt.trim()}
                  className="w-full h-12 text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      Editing Image...
                    </>
                  ) : (
                    <>
                      <Edit3 className="h-5 w-5 mr-2" />
                      Edit Image
                      {user && credits !== null && (
                        <span className="ml-2 text-sm opacity-75">
                          ({credits} credits)
                        </span>
                      )}
                    </>
                  )}
                </Button>

                {!user && (
                  <p className="text-sm text-muted-foreground text-center">
                    Sign in to edit images
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Edit Preview Panel */}
            <Card className="border-2 border-blue-200 dark:border-blue-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ImageIcon className="h-5 w-5" />
                  Edited Result
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-square bg-muted rounded-lg flex items-center justify-center relative overflow-hidden">
                  {isLoading ? (
                    <div className="text-center">
                      <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-primary" />
                      <p className="text-muted-foreground">Editing your image...</p>
                      <div className="mt-4 w-64 bg-muted-foreground/20 rounded-full h-2">
                        <div className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full animate-pulse w-1/3"></div>
                      </div>
                    </div>
                  ) : generatedImage ? (
                    <div className="relative w-full h-full group">
                      <img
                        src={generatedImage}
                        alt="Edited artwork"
                        className="w-full h-full object-contain rounded-lg"
                      />
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => downloadImage(generatedImage, `edited-image-${Date.now()}.png`)}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => setSelectedImage({ imageUrl: generatedImage, prompt: editPrompt })}
                        >
                          <Maximize2 className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground">
                      <Edit3 className="h-16 w-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">Your edited image will appear here</p>
                      <p className="text-sm">Upload an image and enter edit instructions to get started</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Saved Images Section */}
      {user && savedImages.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              Your Generated Images
            </h3>
            <Badge variant="outline">
              {savedImages.length} image{savedImages.length !== 1 ? 's' : ''}
            </Badge>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <AnimatePresence>
              {savedImages.map((image, index) => (
                <motion.div
                  key={image.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ delay: index * 0.1 }}
                  className="group relative"
                >
                  <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-200">
                    <div className="aspect-square relative">
                      <img
                        src={image.imageUrl}
                        alt={image.prompt}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                        <div className="flex gap-2">
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => setSelectedImage(image)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => downloadImage(image.imageUrl, `image-${image.id}.png`)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => copyPrompt(image.prompt)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => deleteImage(image.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                    <CardContent className="p-3">
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {image.prompt}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <Badge variant="secondary" className="text-xs">
                          {image.style}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {image.aspectRatio}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {/* Pagination */}
          {(hasMore || currentPage > 1) && (
            <div className="flex items-center justify-center gap-4">
              <Button
                variant="outline"
                onClick={loadPrevImages}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm text-muted-foreground">
                Page {currentPage}
              </span>
              <Button
                variant="outline"
                onClick={loadMoreImages}
                disabled={!hasMore}
              >
                Next
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Image Preview Modal */}
      <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Image Preview</DialogTitle>
          </DialogHeader>
          {selectedImage && (
            <div className="space-y-4">
              <div className="relative">
                <img
                  src={selectedImage.imageUrl}
                  alt={selectedImage.prompt}
                  className="w-full max-h-[60vh] object-contain rounded-lg"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Prompt:</p>
                <p className="text-sm text-muted-foreground bg-muted p-3 rounded-lg">
                  {selectedImage.prompt}
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={() => downloadImage(selectedImage.imageUrl, `image-${selectedImage.id || Date.now()}.png`)}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button
                  variant="outline"
                  onClick={() => copyPrompt(selectedImage.prompt)}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Prompt
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </div>
  );
}
