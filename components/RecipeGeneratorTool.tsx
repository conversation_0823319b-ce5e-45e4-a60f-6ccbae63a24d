'use client';

import React, { useState, Dispatch, SetStateAction, useEffect } from 'react';
import { RecipeFilters } from '@/types/recipe';
import { IngredientInput } from './ui/recipe/IngredientInput';
import { RecipeFilters as RecipeFiltersComponent } from './ui/recipe/RecipeFilters';
import { Button } from './ui/button';
import { ChefHat, Loader2, Share2, Download, Star, Clock, UtensilsCrossed, Globe2 } from 'lucide-react';
import { Badge } from './ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const commonIngredients = [
  'chicken', 'beef', 'pork', 'onion', 'garlic', 'tomato', 'potato',
  'carrot', 'rice', 'pasta', 'olive oil', 'salt', 'pepper'
];

const languages = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'it', name: 'Italian' },
  { code: 'de', name: 'German' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'ne', name: 'Nepali' },
  { code: 'hi', name: 'Hindi' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'zh', name: 'Chinese' },
  { code: 'ru', name: 'Russian' },
];

const popularRecipes = [
  { name: "Classic Spaghetti Carbonara", time: "30 mins", cuisine: "Italian" },
  { name: "Chicken Stir-Fry", time: "25 mins", cuisine: "Asian" },
  { name: "Vegetable Curry", time: "40 mins", cuisine: "Indian" },
  { name: "Grilled Salmon", time: "20 mins", cuisine: "International" },
  { name: "Beef Tacos", time: "35 mins", cuisine: "Mexican" },
  { name: "Pad Thai", time: "30 mins", cuisine: "Thai" },
  { name: "Greek Salad", time: "15 mins", cuisine: "Mediterranean" },
  { name: "Butter Chicken", time: "45 mins", cuisine: "Indian" }
];

function generateSuggestions(ingredients: string[], cuisine?: string, maxTime?: number): string[] {
  if (ingredients.length === 0) return [];

  const effectiveCuisine = cuisine || 'Any';
  const effectiveMaxTime = maxTime || 180; // Default to 3 hours if not specified

  const commonPairings: { [key: string]: Array<{ name: string; cuisine: string; time: number }> } = {
    'chicken': [
      { name: 'Grilled Chicken with Herbs', cuisine: 'Mediterranean', time: 30 },
      { name: 'Chicken Stir-Fry', cuisine: 'Chinese', time: 25 },
      { name: 'Chicken Curry', cuisine: 'Indian', time: 45 },
      { name: 'Chicken Teriyaki', cuisine: 'Japanese', time: 30 }
    ],
    'beef': [
      { name: 'Classic Beef Stew', cuisine: 'American', time: 60 },
      { name: 'Beef Stir-Fry', cuisine: 'Chinese', time: 25 },
      { name: 'Beef Tacos', cuisine: 'Mexican', time: 30 },
      { name: 'Korean BBQ Beef', cuisine: 'Korean', time: 35 }
    ],
    'rice': [
      { name: 'Fried Rice', cuisine: 'Chinese', time: 20 },
      { name: 'Risotto', cuisine: 'Italian', time: 40 },
      { name: 'Biryani', cuisine: 'Indian', time: 50 },
      { name: 'Paella', cuisine: 'Spanish', time: 45 }
    ],
    'pasta': [
      { name: 'Pasta Carbonara', cuisine: 'Italian', time: 25 },
      { name: 'Pasta Primavera', cuisine: 'Italian', time: 30 },
      { name: 'Pesto Pasta', cuisine: 'Italian', time: 20 },
      { name: 'Spicy Garlic Pasta', cuisine: 'Fusion', time: 25 }
    ]
  };

  const suggestions = new Set<string>();
  
  // Add suggestions based on individual ingredients, cuisine, and time
  ingredients.forEach(ingredient => {
    const lowerIngredient = ingredient.toLowerCase();
    Object.entries(commonPairings).forEach(([key, recipes]) => {
      if (lowerIngredient.includes(key)) {
        recipes.forEach(recipe => {
          if ((effectiveCuisine === 'Any' || recipe.cuisine === effectiveCuisine) && recipe.time <= effectiveMaxTime) {
            suggestions.add(recipe.name);
          }
        });
      }
    });
  });

  // Add combination suggestions
  if (ingredients.length >= 2) {
    const hasRice = ingredients.some(i => i.toLowerCase().includes('rice'));
    const hasVegetables = ingredients.some(i => i.toLowerCase().includes('vegetable'));
    const hasPasta = ingredients.some(i => i.toLowerCase().includes('pasta'));
    const hasTomato = ingredients.some(i => i.toLowerCase().includes('tomato'));

    if (hasRice && hasVegetables && effectiveMaxTime >= 25) {
      suggestions.add('Vegetable Rice Bowl');
    }
    if (hasPasta && hasTomato && (effectiveCuisine === 'Any' || effectiveCuisine === 'Italian') && effectiveMaxTime >= 30) {
      suggestions.add('Classic Pasta with Tomato Sauce');
    }
  }

  return Array.from(suggestions).slice(0, 5);
}

interface Props {
  ingredients: string[];
  setIngredients: Dispatch<SetStateAction<string[]>>;
  servings: number;
  setServings: (servings: number) => void;
  filters: RecipeFilters;
  setFilters: (filters: RecipeFilters) => void;
  generateImage: boolean;
  setGenerateImage: (generate: boolean) => void;
  onSubmit: () => void;
  isLoading: boolean;
}

export default function RecipeGeneratorTool({
  ingredients,
  setIngredients,
  servings,
  setServings,
  filters,
  setFilters,
  generateImage,
  setGenerateImage,
  onSubmit,
  isLoading
}: Props) {
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // Load saved inputs from localStorage on component mount
  useEffect(() => {
    const savedInputs = localStorage.getItem('recipeGeneratorInputs');
    if (savedInputs) {
      const { savedIngredients, savedServings, savedFilters, savedGenerateImage } = JSON.parse(savedInputs);
      setIngredients(savedIngredients || []);
      setServings(savedServings || 2);
      setFilters(savedFilters || {});
      setGenerateImage(savedGenerateImage || false);
    }
  }, []);

  // Save inputs to localStorage whenever they change
  useEffect(() => {
    const inputsToSave = {
      savedIngredients: ingredients,
      savedServings: servings,
      savedFilters: filters,
      savedGenerateImage: generateImage
    };
    localStorage.setItem('recipeGeneratorInputs', JSON.stringify(inputsToSave));
  }, [ingredients, servings, filters, generateImage]);

  const handleIngredientsChange = (value: string[] | ((prev: string[]) => string[])) => {
    setIngredients(value);
    const newIngredients = typeof value === 'function' ? value(ingredients) : value;
    setSuggestions(generateSuggestions(newIngredients, filters.cuisine, filters.maxCookingTime));
  };

  const handleFiltersChange = (newFilters: RecipeFilters) => {
    setFilters(newFilters);
    setSuggestions(generateSuggestions(ingredients, newFilters.cuisine, newFilters.maxCookingTime));
  };

  const handleLanguageChange = (value: string) => {
    setFilters({ ...filters, outputLanguage: value });
  };

  const handleSuggestionClick = (suggestion: string) => {
    // Set ingredients based on the suggestion and trigger recipe generation
    setIngredients([...ingredients]);
    onSubmit();
  };

  return (
    <div className="grid md:grid-cols-7 gap-8">
      {/* Main Form - 5 columns */}
      <div className="md:col-span-5">
        <div className="bg-[#FFE5D9] p-8 rounded-xl shadow-lg space-y-6">
          {/* Ingredients Section */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-[#333333]">What's in your kitchen?</h3>
            <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 border border-[#2E7D32]/20">
              <IngredientInput
                ingredients={ingredients}
                setIngredients={handleIngredientsChange}
              />
              {/* Quick Add Ingredients */}
              <div className="mt-2 flex flex-wrap gap-2">
                {commonIngredients.slice(0, 5).map((ingredient) => (
                  <Badge
                    key={ingredient}
                    variant="outline"
                    className="cursor-pointer hover:bg-[#2E7D32]/10"
                    onClick={() => setIngredients(prev => [...prev, ingredient])}
                  >
                    + {ingredient}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Servings and Cuisine */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#333333]">Servings</label>
              <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 border border-[#2E7D32]/20">
                <input
                  type="number"
                  min="1"
                  max="12"
                  value={servings}
                  onChange={(e) => setServings(Number(e.target.value))}
                  className="w-full px-3 py-2 rounded-lg border border-[#2E7D32]/20 focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32]"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#333333]">Cuisine Type</label>
              <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 border border-[#2E7D32]/20">
                <select 
                  value={filters.cuisine} 
                  onChange={(e) => handleFiltersChange({...filters, cuisine: e.target.value})}
                  className="w-full px-3 py-2 rounded-lg border border-[#2E7D32]/20 focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32]"
                >
                  <option value="Any">Any</option>
                  <option value="Italian">Italian</option>
                  <option value="French">French</option>
                  <option value="Chinese">Chinese</option>
                  <option value="Japanese">Japanese</option>
                  <option value="Korean">Korean</option>
                  <option value="Thai">Thai</option>
                  <option value="Vietnamese">Vietnamese</option>
                  <option value="Indian">Indian</option>
                  <option value="Nepalese">Nepalese</option>
                  <option value="Spanish">Spanish</option>
                  <option value="Greek">Greek</option>
                  <option value="Middle Eastern">Middle Eastern</option>
                  <option value="Turkish">Turkish</option>
                  <option value="Mongolian">Mongolian</option>
                  <option value="Mexican">Mexican</option>
                  <option value="Spanish">Spanish</option>
                  <option value="Greek">Greek</option>
                  <option value="Mediterranean">Mediterranean</option>
                  <option value="Middle Eastern">Middle Eastern</option>
                  <option value="American">American</option>
                  <option value="Brazilian">Brazilian</option>
                  <option value="Caribbean">Caribbean</option>
                  <option value="African">African</option>

                </select>
              </div>
            </div>
          </div>

          {/* Cooking Time and Dietary */}
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#333333]">Maximum Cooking Time (minutes)</label>
              <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 border border-[#2E7D32]/20">
                <input
                  type="number"
                  min="15"
                  max="180"
                  value={filters.maxCookingTime}
                  onChange={(e) => handleFiltersChange({...filters, maxCookingTime: Number(e.target.value)})}
                  className="w-full px-3 py-2 rounded-lg border border-[#2E7D32]/20 focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32]"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#333333]">Dietary Restrictions</label>
              <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 border border-[#2E7D32]/20">
                <div className="grid grid-cols-2 gap-2">
                  {['Vegetarian', 'Vegan', 'Gluten-free', 'Dairy-free', 'Low-carb', 'Keto', 'Paleo'].map((diet) => (
                    <label key={diet} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={filters.dietary?.includes(diet) || false}
                        onChange={(e) => {
                          const newDietary = e.target.checked
                            ? [...(filters.dietary || []), diet]
                            : (filters.dietary || []).filter(d => d !== diet);
                          handleFiltersChange({...filters, dietary: newDietary});
                        }}
                        className="rounded border-[#2E7D32]/20 text-[#2E7D32] focus:ring-[#2E7D32]"
                      />
                      <span className="text-sm text-[#333333]">{diet}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Language */}
          <div className="space-y-2">
            <h3 className="text-xl font-semibold text-[#333333] flex items-center gap-2">
              <Globe2 className="w-5 h-5" />
              Recipe Output Language
            </h3>
            <Select
              value={filters.outputLanguage || 'en'}
              onValueChange={handleLanguageChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select Language" />
              </SelectTrigger>
              <SelectContent>
                {languages.map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    {lang.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Image Generation */}
          <div className="mb-6">
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="checkbox"
                checked={generateImage}
                onChange={(e) => setGenerateImage(e.target.checked)}
                className="h-5 w-5 text-[#2E7D32] rounded border-[#2E7D32]/30 focus:ring-[#2E7D32]"
              />
              <div>
                <span className="text-[#333333] font-medium">Generate Recipe Image</span>
                <p className="text-sm text-[#666666]">Enable AI-powered food photography (generates 1 image)</p>
              </div>
            </label>
          </div>

          {/* Recipe Suggestions */}
          {suggestions.length > 0 && (
            <div className="mb-6">
              <h4 className="text-sm font-medium text-[#333333] mb-3">Suggested Recipes</h4>
              <div className="grid grid-cols-2 gap-2">
                {suggestions.map((suggestion, index) => (
                  <div 
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="bg-white/90 backdrop-blur-sm rounded-lg p-3 border border-[#2E7D32]/20 hover:bg-[#2E7D32]/5 transition-colors cursor-pointer"
                  >
                    <div className="flex items-center gap-2">
                      <ChefHat className="h-4 w-4 text-[#2E7D32]" />
                      <span className="text-[#333333] text-sm">{suggestion}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button
              variant="outline"
              onClick={() => {
                setIngredients([]);
                setFilters({
                  cuisine: 'Any',
                  maxCookingTime: 60,
                  dietary: []
                });
                setServings(2);
                setGenerateImage(false);
              }}
              className="flex-1 py-6"
            >
              Cancel
            </Button>
            <Button
              onClick={onSubmit}
              disabled={isLoading || ingredients.length === 0}
              className="flex-1 bg-[#2E7D32] hover:bg-[#1B5E20] text-white py-6 rounded-lg shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Cooking up your recipe...
                </>
              ) : (
                'Generate Recipe'
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Recipe Suggestions - 2 columns */}
      <div className="md:col-span-2">
        <div className="bg-[#E8D5C4] rounded-xl shadow-lg sticky top-4">
          <div className="p-4 border-b border-[#2E7D32]/10">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-semibold text-[#333333]">Popular Recipes</h4>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" className="text-[#2E7D32]">
                  <Share2 className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" className="text-[#2E7D32]">
                  <Star className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          
          <div className="p-4 space-y-3">
            {popularRecipes.map((recipe, index) => (
              <div 
                key={index}
                onClick={() => handleSuggestionClick(recipe.name)}
                className="bg-white/90 backdrop-blur-sm rounded-lg p-3 border border-[#2E7D32]/20 hover:bg-[#2E7D32]/5 transition-colors cursor-pointer"
              >
                <div className="flex items-start gap-3">
                  <ChefHat className="h-5 w-5 text-[#2E7D32] mt-1" />
                  <div className="flex-1">
                    <h5 className="font-medium text-[#333333] text-sm mb-1">{recipe.name}</h5>
                    <div className="flex items-center gap-3 text-xs text-[#666666]">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {recipe.time}
                      </div>
                      <div className="flex items-center gap-1">
                        <UtensilsCrossed className="h-3 w-3" />
                        {recipe.cuisine}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}