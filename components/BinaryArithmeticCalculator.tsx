'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  Calculator,
  Plus,
  Minus,
  X,
  Divide,
  Equal,
  Trash2,
  Copy,
  History
} from 'lucide-react';
import {
  binaryAdd,
  binarySubtract,
  binaryMultiply,
  binaryDivide,
  getBinaryAdditionSteps,
  isValidBinary,
  formatBinary,
  binaryToDecimal
} from '@/lib/binaryUtils';

type Operation = '+' | '-' | '*' | '/' | null;

interface CalculationStep {
  position: number;
  bit1: string;
  bit2: string;
  carry: string;
  sum: string;
  result: string;
}

export function BinaryArithmeticCalculator() {
  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState<string | null>(null);
  const [operation, setOperation] = useState<Operation>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);
  const [calculationSteps, setCalculationSteps] = useState<CalculationStep[]>([]);
  const [showSteps, setShowSteps] = useState(false);
  const [history, setHistory] = useState<string[]>([]);
  const [memory, setMemory] = useState<string>('0');
  const [showHistory, setShowHistory] = useState(false);

  const inputDigit = (digit: string) => {
    if (waitingForOperand) {
      setDisplay(digit);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? digit : display + digit);
    }
  };

  const inputOperation = (nextOperation: Operation) => {
    const inputValue = display;

    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || '0';
      const newValue = calculate(currentValue, inputValue, operation);

      if (newValue !== null) {
        setDisplay(newValue);
        setPreviousValue(newValue);
      }
    }

    setWaitingForOperand(true);
    setOperation(nextOperation);
  };

  const calculate = (firstOperand: string, secondOperand: string, operation: Operation): string | null => {
    if (!isValidBinary(firstOperand) || !isValidBinary(secondOperand)) {
      toast.error('Invalid binary numbers');
      return null;
    }

    try {
      let result: string;
      let steps: CalculationStep[] = [];

      switch (operation) {
        case '+':
          result = binaryAdd(firstOperand, secondOperand);
          steps = getBinaryAdditionSteps(firstOperand, secondOperand);
          break;
        case '-':
          result = binarySubtract(firstOperand, secondOperand);
          break;
        case '*':
          result = binaryMultiply(firstOperand, secondOperand);
          break;
        case '/':
          result = binaryDivide(firstOperand, secondOperand);
          break;
        default:
          return null;
      }

      setCalculationSteps(steps);

      // Add to history
      const historyEntry = `${firstOperand} ${operation} ${secondOperand} = ${result}`;
      setHistory(prev => [historyEntry, ...prev.slice(0, 9)]); // Keep last 10 calculations

      return result;
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Calculation error');
      return null;
    }
  };

  const performCalculation = () => {
    const inputValue = display;

    if (previousValue !== null && operation) {
      const newValue = calculate(previousValue, inputValue, operation);

      if (newValue !== null) {
        setDisplay(newValue);
        setPreviousValue(null);
        setOperation(null);
        setWaitingForOperand(true);
      }
    }
  };

  const clear = () => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
    setCalculationSteps([]);
    setShowSteps(false);
  };

  const copyToClipboard = async (value: string) => {
    try {
      await navigator.clipboard.writeText(value);
      toast.success('Copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy');
    }
  };

  const getDecimalValue = (binary: string): string => {
    try {
      return binaryToDecimal(binary).toString();
    } catch {
      return 'Invalid';
    }
  };

  // Memory functions
  const memoryAdd = () => {
    try {
      const currentDecimal = binaryToDecimal(display);
      const memoryDecimal = binaryToDecimal(memory);
      const result = (currentDecimal + memoryDecimal).toString(2);
      setMemory(result);
      toast.success('Added to memory');
    } catch {
      toast.error('Invalid binary number');
    }
  };

  const memorySubtract = () => {
    try {
      const currentDecimal = binaryToDecimal(display);
      const memoryDecimal = binaryToDecimal(memory);
      const result = Math.max(0, memoryDecimal - currentDecimal).toString(2);
      setMemory(result);
      toast.success('Subtracted from memory');
    } catch {
      toast.error('Invalid binary number');
    }
  };

  const memoryRecall = () => {
    setDisplay(memory);
    setWaitingForOperand(true);
    toast.success('Memory recalled');
  };

  const memoryClear = () => {
    setMemory('0');
    toast.success('Memory cleared');
  };

  const exportHistory = () => {
    const historyText = history.join('\n');
    const blob = new Blob([historyText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'binary-calculator-history.txt';
    a.click();
    URL.revokeObjectURL(url);
    toast.success('History exported');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Binary Arithmetic Calculator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Display */}
          <div className="space-y-2">
            <Label>Binary Display</Label>
            <div className="relative">
              <Input
                value={display}
                readOnly
                className="text-right font-mono text-lg bg-gray-50 dark:bg-gray-900 pr-12"
              />
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                onClick={() => copyToClipboard(display)}
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-600 dark:text-gray-400">
                Decimal: {getDecimalValue(display)}
              </div>
              <div className="text-gray-600 dark:text-gray-400">
                Hex: {(() => {
                  try {
                    return parseInt(display, 2).toString(16).toUpperCase();
                  } catch {
                    return 'Invalid';
                  }
                })()}
              </div>
            </div>
          </div>

          {/* Calculator Keypad */}
          <div className="grid grid-cols-4 gap-2">
            {/* Memory Row */}
            <Button variant="outline" size="sm" onClick={memoryClear}>MC</Button>
            <Button variant="outline" size="sm" onClick={memoryRecall}>MR</Button>
            <Button variant="outline" size="sm" onClick={memoryAdd}>M+</Button>
            <Button variant="outline" size="sm" onClick={memorySubtract}>M-</Button>

            {/* Row 1 */}
            <Button variant="outline" onClick={clear} className="col-span-2">
              <Trash2 className="h-4 w-4 mr-1" />
              Clear
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowSteps(!showSteps)}
              disabled={calculationSteps.length === 0}
            >
              Steps
            </Button>
            <Button variant="outline" onClick={() => inputOperation('/')}>
              <Divide className="h-4 w-4" />
            </Button>

            {/* Row 2 */}
            <Button variant="outline" onClick={() => inputDigit('1')}>1</Button>
            <Button variant="outline" onClick={() => inputDigit('0')}>0</Button>
            <Button variant="outline" onClick={() => inputDigit('1')}>1</Button>
            <Button variant="outline" onClick={() => inputOperation('*')}>
              <X className="h-4 w-4" />
            </Button>

            {/* Row 3 */}
            <Button variant="outline" onClick={() => inputDigit('0')}>0</Button>
            <Button variant="outline" onClick={() => inputDigit('1')}>1</Button>
            <Button variant="outline" onClick={() => inputDigit('0')}>0</Button>
            <Button variant="outline" onClick={() => inputOperation('-')}>
              <Minus className="h-4 w-4" />
            </Button>

            {/* Row 4 */}
            <Button variant="outline" onClick={() => inputDigit('1')}>1</Button>
            <Button variant="outline" onClick={() => inputDigit('1')}>1</Button>
            <Button variant="outline" onClick={() => inputDigit('1')}>1</Button>
            <Button variant="outline" onClick={() => inputOperation('+')}>
              <Plus className="h-4 w-4" />
            </Button>

            {/* Row 5 */}
            <Button
              variant="default"
              onClick={performCalculation}
              className="col-span-4 bg-blue-600 hover:bg-blue-700"
            >
              <Equal className="h-4 w-4 mr-2" />
              Calculate
            </Button>
          </div>

          {/* Current Operation Display */}
          {operation && previousValue && (
            <div className="text-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
              <span className="font-mono text-sm">
                {previousValue} {operation} {waitingForOperand ? '?' : display}
              </span>
            </div>
          )}

          {/* Memory Display */}
          {memory !== '0' && (
            <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded">
              <span className="text-sm text-green-600 dark:text-green-400">
                Memory: <span className="font-mono">{memory}</span> (Decimal: {getDecimalValue(memory)})
              </span>
            </div>
          )}

          {/* History Toggle */}
          <div className="flex justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHistory(!showHistory)}
              disabled={history.length === 0}
            >
              <History className="h-4 w-4 mr-2" />
              {showHistory ? 'Hide' : 'Show'} History ({history.length})
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Calculation Steps */}
      {showSteps && calculationSteps.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Step-by-Step Addition</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {calculationSteps.map((step, index) => (
                <div key={index} className="flex items-center gap-4 font-mono text-sm">
                  <Badge variant="outline">Pos {step.position}</Badge>
                  <span>{step.bit1} + {step.bit2} + {step.carry} = {step.sum} → {step.result}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* History */}
      {showHistory && history.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Calculation History
              </div>
              <Button variant="outline" size="sm" onClick={exportHistory}>
                Export
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1 max-h-60 overflow-y-auto">
              {history.map((entry, index) => (
                <div key={index} className="font-mono text-sm p-2 bg-gray-50 dark:bg-gray-800 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                  {entry}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
