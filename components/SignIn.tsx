'use client';

import { useState, useEffect } from 'react';
import { auth } from '@/lib/firebase';
import { signInWithEmailAndPassword, GoogleAuthProvider, signInWithPopup } from 'firebase/auth';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import Link from 'next/link';

export function SignIn() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const [redirectPath, setRedirectPath] = useState('/qr-code');

  useEffect(() => {
    // Get redirect path from URL on client side
    const params = new URLSearchParams(window.location.search);
    const redirect = params.get('redirect');
    if (redirect) {
      setRedirectPath(redirect);
    }
  }, []);

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const token = await userCredential.user.getIdToken();

      // Set cookie with proper settings for production
      Cookies.set('firebaseToken', token, {
        path: '/',
        expires: 1, // 1 day
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'lax' : 'strict'
      });

      toast.success('Signed in successfully');

      // Wait a bit for the auth state to update, then redirect
      setTimeout(() => {
        const targetPath = decodeURIComponent(redirectPath);
        console.log('Redirecting to:', targetPath);
        window.location.href = targetPath; // Use window.location for more reliable redirect
      }, 500);

    } catch (error) {
      console.error('Sign-in error:', error);
      toast.error('Failed to sign in');
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      const provider = new GoogleAuthProvider();
      const userCredential = await signInWithPopup(auth, provider);
      const token = await userCredential.user.getIdToken();

      // Set cookie with proper settings for production
      Cookies.set('firebaseToken', token, {
        path: '/',
        expires: 1, // 1 day
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'lax' : 'strict'
      });

      toast.success('Signed in with Google successfully');

      // Wait a bit for the auth state to update, then redirect
      setTimeout(() => {
        const targetPath = decodeURIComponent(redirectPath);
        console.log('Redirecting to:', targetPath);
        window.location.href = targetPath; // Use window.location for more reliable redirect
      }, 500);

    } catch (error) {
      console.error('Google sign-in error:', error);
      toast.error('Failed to sign in with Google');
      setLoading(false);
    }
  };

  return (
    <div className="form-container">
      <form className="form" onSubmit={handleEmailSignIn}>
        <div className="form-group">
          <label htmlFor="email">Email</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            placeholder="Enter your email"
          />
        </div>
        <div className="form-group">
          <label htmlFor="password">Password</label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            placeholder="Enter your password"
          />
          <Link
            href="/auth/reset-password"
            className="block text-sm text-primary hover:underline mt-1"
          >
            Forgot Password?
          </Link>
        </div>
        <button
          className="form-submit-btn"
          type="submit"
          disabled={loading}
        >
          Sign In
        </button>
      </form>

      <div className="form-divider">
        <span>Or</span>
      </div>

      <button
        className="form-submit-btn google-btn"
        type="button"
        onClick={handleGoogleSignIn}
        disabled={loading}
      >
        <svg className="google-icon" viewBox="0 0 24 24">
          <path
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            fill="#4285F4"
          />
          <path
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            fill="#34A853"
          />
          <path
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            fill="#FBBC05"
          />
          <path
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            fill="#EA4335"
          />
        </svg>
        Continue with Google
      </button>

      <div className="text-center text-sm text-gray-500">
        Don't have an account?{' '}
        <a
          href="/auth/signup"
          className="text-[#e81cff] hover:text-[#40c9ff] transition-colors font-medium"
        >
          Sign up
        </a>
      </div>
    </div>
  );
}
