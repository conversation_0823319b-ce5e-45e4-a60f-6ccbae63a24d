'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from 'sonner'
import { Copy, RefreshCw, Calendar, Clock, Globe } from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function UnixTimestampConverter() {
  const [unixTimestamp, setUnixTimestamp] = useState('')
  const [humanDate, setHumanDate] = useState('')
  const [copied, setCopied] = useState<string | null>(null)
  const [timeFormat, setTimeFormat] = useState<'seconds' | 'milliseconds'>('seconds')
  const [timezone, setTimezone] = useState(Intl.DateTimeFormat().resolvedOptions().timeZone)
  const [customFormat, setCustomFormat] = useState('YYYY-MM-DD HH:mm:ss')

  const timezones = Intl.supportedValuesOf('timeZone')

  const convertUnixToHuman = (unix: string) => {
    const timestamp = parseInt(unix)
    if (isNaN(timestamp)) {
      toast.error('Please enter a valid Unix timestamp')
      return
    }

    try {
      // Determine if timestamp is in seconds or milliseconds
      const date = new Date(timeFormat === 'seconds' ? timestamp * 1000 : timestamp)
      if (date.toString() === 'Invalid Date') {
        toast.error('Invalid timestamp')
        return
      }
      setHumanDate(date.toISOString())
    } catch (error) {
      toast.error('Error converting timestamp')
    }
  }

  const convertHumanToUnix = (human: string) => {
    try {
      const date = new Date(human)
      if (date.toString() === 'Invalid Date') {
        toast.error('Invalid date format')
        return
      }
      setUnixTimestamp(timeFormat === 'seconds' 
        ? Math.floor(date.getTime() / 1000).toString()
        : date.getTime().toString()
      )
    } catch (error) {
      toast.error('Error converting date')
    }
  }

  const getCurrentTimestamp = () => {
    const now = new Date()
    setUnixTimestamp(timeFormat === 'seconds' 
      ? Math.floor(now.getTime() / 1000).toString()
      : now.getTime().toString()
    )
    setHumanDate(now.toISOString())
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    setCopied(label)
    toast.success(`${label} copied to clipboard`)
    setTimeout(() => setCopied(null), 2000)
  }

  const formatDate = (date: Date, format: string, tz: string): string => {
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: tz,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
    return formatter.format(date)
  }

  const getCommonTimestamps = () => {
    const now = new Date()
    return [
      { label: 'Start of Today', value: new Date(now.setHours(0, 0, 0, 0)) },
      { label: 'End of Today', value: new Date(now.setHours(23, 59, 59, 999)) },
      { label: 'Start of Week', value: new Date(now.setDate(now.getDate() - now.getDay())) },
      { label: 'End of Week', value: new Date(now.setDate(now.getDate() - now.getDay() + 6)) },
      { label: 'Start of Month', value: new Date(now.setDate(1)) },
      { label: 'End of Month', value: new Date(now.getFullYear(), now.getMonth() + 1, 0) }
    ]
  }

  return (
    <div className="space-y-8">
      <Card className="p-6 bg-gradient-to-br from-green-50/50 to-teal-50/50 dark:from-gray-800/50 dark:to-gray-900/50 backdrop-blur-sm border-2 border-green-100 dark:border-green-900">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
            Unix Timestamp Converter
          </h2>
          <div className="flex gap-2">
            <Select value={timeFormat} onValueChange={(value: 'seconds' | 'milliseconds') => setTimeFormat(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="seconds">Seconds</SelectItem>
                <SelectItem value="milliseconds">Milliseconds</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={getCurrentTimestamp}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Current Time
            </Button>
          </div>
        </div>

        <Tabs defaultValue="convert" className="space-y-6">
          <TabsList>
            <TabsTrigger value="convert">Convert</TabsTrigger>
            <TabsTrigger value="common">Common Timestamps</TabsTrigger>
          </TabsList>

          <TabsContent value="convert" className="space-y-6">
            {/* Unix Timestamp Input */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center justify-between">
                Unix Timestamp
                <span className="text-xs text-gray-500">({timeFormat} since epoch)</span>
              </Label>
              <div className="flex gap-2">
                <Input
                  type="text"
                  value={unixTimestamp}
                  onChange={(e) => {
                    setUnixTimestamp(e.target.value)
                    if (e.target.value) {
                      convertUnixToHuman(e.target.value)
                    }
                  }}
                  placeholder="Enter Unix timestamp"
                  className="font-mono"
                />
                <Button
                  variant="outline"
                  onClick={() => copyToClipboard(unixTimestamp, 'Unix')}
                  className="flex-shrink-0"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  {copied === 'Unix' ? 'Copied!' : 'Copy'}
                </Button>
              </div>
            </div>

            {/* Human Date Input */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center justify-between">
                Human-Readable Date
                <span className="text-xs text-gray-500">(ISO 8601)</span>
              </Label>
              <div className="flex gap-2">
                <Input
                  type="datetime-local"
                  value={humanDate ? humanDate.slice(0, 16) : ''}
                  onChange={(e) => {
                    setHumanDate(e.target.value)
                    if (e.target.value) {
                      convertHumanToUnix(e.target.value)
                    }
                  }}
                  className="font-mono"
                />
                <Button
                  variant="outline"
                  onClick={() => copyToClipboard(humanDate, 'Date')}
                  className="flex-shrink-0"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  {copied === 'Date' ? 'Copied!' : 'Copy'}
                </Button>
              </div>
            </div>

            {/* Timezone Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Timezone</Label>
              <Select value={timezone} onValueChange={setTimezone}>
                <SelectTrigger>
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz} value={tz}>{tz}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </TabsContent>

          <TabsContent value="common">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {getCommonTimestamps().map((timestamp) => (
                <Button
                  key={timestamp.label}
                  variant="outline"
                  className="justify-start"
                  onClick={() => {
                    setHumanDate(timestamp.value.toISOString())
                    convertHumanToUnix(timestamp.value.toISOString())
                  }}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  {timestamp.label}
                </Button>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Additional Time Information */}
        {humanDate && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 mt-6 bg-white/50 dark:bg-gray-800/50 rounded-lg">
            <div>
              <Label className="text-sm font-medium">Local Time</Label>
              <p className="text-sm text-gray-600 dark:text-gray-300 font-mono">
                {formatDate(new Date(humanDate), customFormat, timezone)}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">UTC Time</Label>
              <p className="text-sm text-gray-600 dark:text-gray-300 font-mono">
                {new Date(humanDate).toUTCString()}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Relative Time</Label>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {getRelativeTime(new Date(humanDate))}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Selected Timezone</Label>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {timezone}
              </p>
            </div>
          </div>
        )}
      </Card>
    </div>
  )
}

function getRelativeTime(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const seconds = Math.floor(Math.abs(diff) / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (diff < 0) {
    if (seconds < 60) return `in ${seconds} seconds`
    if (minutes < 60) return `in ${minutes} minutes`
    if (hours < 24) return `in ${hours} hours`
    return `in ${days} days`
  } else {
    if (seconds < 60) return `${seconds} seconds ago`
    if (minutes < 60) return `${minutes} minutes ago`
    if (hours < 24) return `${hours} hours ago`
    return `${days} days ago`
  }
}
