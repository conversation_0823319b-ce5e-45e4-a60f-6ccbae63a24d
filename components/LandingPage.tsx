'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import {
  Type, Replace, Search, FileText, Calendar, QrCode, Database,
  Binary, FileCode, Key, MessageSquareQuote, Wand2, FileDown,
  Heart, UtensilsCrossed, FileSearch, Mail, Video, ImageIcon,
  FilePlus, FileImage, Sparkles, Zap, Shield, Clock, Menu, X,
  ArrowRight, Github, Coffee, ExternalLink, Palette, Shuffle,
  Brain, Infinity, Star, Users
} from 'lucide-react';
// import SplashCursor from './SplashCursor';
import DotGrid from '@/components/DotGrid';

const toolCategories = [
  {
    name: "Text Tools",
    description: "Transform and format your text with powerful tools",
    icon: Type,
    color: "from-blue-500 to-cyan-500",
    tools: [
      {
        name: "Text Converter",
        description: "Transform text case, add effects, and format content",
        icon: Type,
        href: "/text-converter",
        color: "text-blue-500",
        bgColor: "bg-blue-50 dark:bg-blue-900/20",
      },
      {
        name: "Replace Spaces",
        description: "Replace spaces with custom characters or patterns",
        icon: Replace,
        href: "/replace-spaces",
        color: "text-green-500",
        bgColor: "bg-green-50 dark:bg-green-900/20",
      },
      {
        name: "Find & Replace",
        description: "Advanced find and replace with regex support",
        icon: Search,
        href: "/find-replace",
        color: "text-purple-500",
        bgColor: "bg-purple-50 dark:bg-purple-900/20",
      },
      {
        name: "Unicode Finder",
        description: "Find and replace hidden Unicode characters",
        icon: FileSearch,
        href: "/unicode-finder",
        color: "text-indigo-500",
        bgColor: "bg-indigo-50 dark:bg-indigo-900/20",
      },
      {
        name: "Email Signature",
        description: "Create professional email signatures",
        icon: Mail,
        href: "/email-signature",
        color: "text-pink-500",
        bgColor: "bg-pink-50 dark:bg-pink-900/20",
      },
      {
        name: "Markdown Converter",
        description: "Convert between Markdown and HTML",
        icon: FileDown,
        href: "/markdown-converter",
        color: "text-orange-500",
        bgColor: "bg-orange-50 dark:bg-orange-900/20",
      }
    ]
  },
  {
    name: "AI-Powered Tools",
    description: "Smart tools powered by artificial intelligence",
    icon: Sparkles,
    color: "from-purple-500 to-pink-500",
    tools: [
      {
        name: "Paraphrase Tool",
        description: "Rewrite text with AI-powered paraphrasing",
        icon: MessageSquareQuote,
        href: "/paraphrase",
        color: "text-purple-500",
        bgColor: "bg-purple-50 dark:bg-purple-900/20",
      },
      {
        name: "Text Humanizer",
        description: "Make AI text sound more human and natural",
        icon: Wand2,
        href: "/humanizer",
        color: "text-pink-500",
        bgColor: "bg-pink-50 dark:bg-pink-900/20",
      },
      {
        name: "Image Generator",
        description: "Create stunning images with AI",
        icon: ImageIcon,
        href: "/image-generator",
        color: "text-blue-500",
        bgColor: "bg-blue-50 dark:bg-blue-900/20",
      },
      {
        name: "Recipe Generator",
        description: "Generate delicious recipes with AI",
        icon: UtensilsCrossed,
        href: "/recipe-generator",
        color: "text-green-500",
        bgColor: "bg-green-50 dark:bg-green-900/20",
      },
      {
        name: "Pickup Line Generator",
        description: "Generate creative pickup lines",
        icon: Heart,
        href: "/pickup-line-generator",
        color: "text-red-500",
        bgColor: "bg-red-50 dark:bg-red-900/20",
      }
    ]
  },
  {
    name: "Developer Tools",
    description: "Essential tools for developers and programmers",
    icon: FileCode,
    color: "from-green-500 to-emerald-500",
    tools: [
      {
        name: "Binary Calculator",
        description: "Binary arithmetic and bitwise operations",
        icon: Binary,
        href: "/binary-calculator",
        color: "text-green-500",
        bgColor: "bg-green-50 dark:bg-green-900/20",
      },
      {
        name: "Code Formatter",
        description: "Format and beautify your code",
        icon: FileCode,
        href: "/code-formatter",
        color: "text-blue-500",
        bgColor: "bg-blue-50 dark:bg-blue-900/20",
      },
      {
        name: "Password Generator",
        description: "Generate secure passwords",
        icon: Key,
        href: "/password-generator",
        color: "text-red-500",
        bgColor: "bg-red-50 dark:bg-red-900/20",
      },
      {
        name: "UUID Generator",
        description: "Generate unique identifiers",
        icon: Shuffle,
        href: "/uuid-generator",
        color: "text-purple-500",
        bgColor: "bg-purple-50 dark:bg-purple-900/20",
      },
      {
        name: "Color Converter",
        description: "Convert between color formats",
        icon: Palette,
        href: "/color-converter",
        color: "text-pink-500",
        bgColor: "bg-pink-50 dark:bg-pink-900/20",
      },
      {
        name: "Unix Timestamp",
        description: "Convert Unix timestamps to dates",
        icon: Clock,
        href: "/unix-timestamp-converter",
        color: "text-indigo-500",
        bgColor: "bg-indigo-50 dark:bg-indigo-900/20",
      },
      {
        name: "Color Converter",
        description: "Convert between color formats",
        icon: Sparkles,
        href: "/color-converter",
        color: "text-pink-500",
        bgColor: "bg-pink-50 dark:bg-pink-900/20",
      },
      {
        name: "Unix Timestamp",
        description: "Convert Unix timestamps to dates",
        icon: Clock,
        href: "/unix-timestamp-converter",
        color: "text-orange-500",
        bgColor: "bg-orange-50 dark:bg-orange-900/20",
      }
    ]
  }
];

const features = [
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Instant processing with optimized algorithms"
  },
  {
    icon: Shield,
    title: "Privacy First",
    description: "Your data never leaves your browser"
  },
  {
    icon: Brain,
    title: "AI-Powered",
    description: "Advanced AI features for smart processing"
  },
  {
    icon: Infinity,
    title: "No Limits",
    description: "Use all tools without restrictions"
  }
];

const stats = [
  { icon: FileText, number: "15+", label: "PDF Tools" },
  { icon: Database, number: "10+", label: "Utility Tools" },
  { icon: Sparkles, number: "5+", label: "Multimedia" },
  { icon: QrCode, number: "3+", label: "QR Tools" }
];

export default function LandingPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-slate-900 relative">
      {/* Particle Background */}
      <div className="fixed inset-0 z-0">
        <div
          className="w-full h-full opacity-60"
          style={{
            backgroundImage: `
              radial-gradient(circle at 2px 2px, #64748b 2px, transparent 0),
              radial-gradient(circle at 12px 12px, #475569 1px, transparent 0)
            `,
            backgroundSize: '30px 30px, 20px 20px',
            backgroundPosition: '0 0, 10px 10px'
          }}
        />
      </div>

      {/* Splash Cursor Animation */}
      {/* <SplashCursor /> */}
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 z-50 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/Pixel_and_Code Logo.svg"
                alt="Pixel&Code Logo"
                width={32}
                height={32}
                className="rounded-lg"
              />
              <span className="text-xl font-bold text-white">Pixel&Code</span>
            </Link>

            <div className="hidden md:flex items-center space-x-8">
              <Link href="/all-tools" className="text-gray-300 hover:text-blue-400 transition-colors">
                All Tools
              </Link>
              <Link href="/guide" className="text-gray-300 hover:text-blue-400 transition-colors">
                Guide
              </Link>
              <Link href="/contact" className="text-gray-300 hover:text-blue-400 transition-colors">
                Contact
              </Link>
              <Link href="/text-converter">
                <button className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg transition-all duration-300">
                  Get Started
                </button>
              </Link>
            </div>

            <button
              className="md:hidden p-2 text-gray-300 hover:text-blue-400"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="md:hidden bg-slate-900 border-t border-slate-700"
          >
            <div className="px-4 py-4 space-y-4">
              <Link href="/all-tools" className="block text-gray-300 hover:text-blue-400">
                All Tools
              </Link>
              <Link href="/guide" className="block text-gray-300 hover:text-blue-400">
                Guide
              </Link>
              <Link href="/contact" className="block text-gray-300 hover:text-blue-400">
                Contact
              </Link>
              <Link href="/text-converter">
                <button className="w-full px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium">
                  Get Started
                </button>
              </Link>
            </div>
          </motion.div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden z-10">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-grid-slate-700/25 [mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mb-8"
            >
              <div className="inline-flex items-center px-4 py-2 bg-blue-900/30 rounded-full text-blue-200 text-sm font-medium mb-8">
                <Sparkles className="w-4 h-4 mr-2" />
                Free • No Registration • No Limits
              </div>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6"
            >
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                Transform
              </span>
              <br />
              <span className="text-white">
                Everything
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed"
            >
              From text conversion to AI-powered tools, we provide everything you need to
              <span className="text-blue-400 font-semibold"> transform, analyze, and process </span>
              your content instantly.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center mb-16"
            >
              <Link href="/text-converter">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  <span>Start Transforming</span>
                  <ArrowRight className="w-5 h-5" />
                </motion.button>
              </Link>
              <Link href="/all-tools">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 bg-white dark:bg-slate-800 text-gray-800 dark:text-white border-2 border-gray-200 dark:border-slate-600 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Explore All Tools
                </motion.button>
              </Link>
            </motion.div>

            {/* Quick stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 1.2 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">{stat.number}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-slate-800/80 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold text-white mb-4">
              Why Choose Our Tools?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Built for speed, reliability, and ease of use. No downloads, no sign-ups, just instant results.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="text-center p-8 rounded-2xl bg-slate-900/90 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-300">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Tools Categories Section */}
      <section className="py-20 bg-slate-900/60 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold text-white mb-4">
              Explore Our Tool Categories
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              From text transformation to AI-powered tools, we have everything you need to boost your productivity.
            </p>
          </motion.div>

          <div className="space-y-20">
            {toolCategories.map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: categoryIndex * 0.2 }}
                viewport={{ once: true }}
              >
                <div className="text-center mb-12">
                  <div className={`w-20 h-20 mx-auto mb-6 bg-gradient-to-br ${category.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                    <category.icon className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                    {category.name}
                  </h3>
                  <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                    {category.description}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {category.tools.map((tool, toolIndex) => (
                    <motion.div
                      key={toolIndex}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: toolIndex * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ scale: 1.02, y: -5 }}
                      className="group"
                    >
                      <Link href={tool.href}>
                        <div className="p-6 rounded-2xl border border-gray-700 hover:border-blue-600 transition-all duration-300 h-full bg-slate-800/90 shadow-sm hover:shadow-xl group-hover:shadow-blue-500/10">
                          <div className={`w-12 h-12 rounded-xl ${tool.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                            <tool.icon className={`w-6 h-6 ${tool.color}`} />
                          </div>
                          <h4 className="text-xl font-semibold text-white mb-2 group-hover:text-blue-400 transition-colors">
                            {tool.name}
                          </h4>
                          <p className="text-gray-400 leading-relaxed">
                            {tool.description}
                          </p>
                          <div className="mt-4 flex items-center text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity">
                            <span className="text-sm font-medium">Try it now</span>
                            <ArrowRight className="w-4 h-4 ml-1" />
                          </div>
                        </div>
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              Ready to Transform Your Content?
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-8">
              Join thousands of users who trust our tools for their daily content transformation needs.
              Start using our free tools today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/text-converter">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 bg-white text-blue-600 rounded-xl font-semibold text-lg hover:bg-blue-50 transition-all duration-300 shadow-lg flex items-center justify-center space-x-2"
                >
                  <span>Start Now</span>
                  <ArrowRight className="w-5 h-5" />
                </motion.button>
              </Link>
              <Link href="/all-tools">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 bg-transparent border-2 border-white text-white rounded-xl font-semibold text-lg hover:bg-white/10 transition-all duration-300"
                >
                  Browse All Tools
                </motion.button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900/80 text-white py-16 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <Image
                  src="/Pixel_and_Code Logo.svg"
                  alt="Pixel&Code Logo"
                  width={32}
                  height={32}
                  className="rounded-lg"
                />
                <span className="text-xl font-bold">Pixel&Code</span>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Transform and analyze your content with our powerful suite of free online tools.
                Built for developers, creators, and everyone in between.
              </p>
              <div className="flex space-x-4">
                <a href="https://github.com/Sonamkhadka" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                  <Github className="w-6 h-6" />
                </a>
                <a href="https://sonamkhadka.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                  <ExternalLink className="w-6 h-6" />
                </a>
                <a href="/contact" className="text-gray-400 hover:text-white transition-colors">
                  <Mail className="w-6 h-6" />
                </a>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li><Link href="/all-tools" className="text-gray-400 hover:text-white transition-colors">All Tools</Link></li>
                <li><Link href="/text-converter" className="text-gray-400 hover:text-white transition-colors">Text Converter</Link></li>
                <li><Link href="/binary-calculator" className="text-gray-400 hover:text-white transition-colors">Binary Calculator</Link></li>
                <li><Link href="/paraphrase" className="text-gray-400 hover:text-white transition-colors">AI Paraphrase</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Support</h3>
              <ul className="space-y-2">
                <li><Link href="/guide" className="text-gray-400 hover:text-white transition-colors">Guide</Link></li>
                <li><Link href="/contact" className="text-gray-400 hover:text-white transition-colors">Contact</Link></li>
                <li><Link href="/contact?type=issue" className="text-gray-400 hover:text-white transition-colors">Report Issues</Link></li>
                <li><Link href="/contact?type=feature" className="text-gray-400 hover:text-white transition-colors">Feature Requests</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2025 Pixel&Code. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-white text-sm transition-colors">
                <EMAIL>
              </a>
              <a href="https://buymeacoffee.com/sonamkhadka" target="_blank" rel="noopener noreferrer" className="flex items-center space-x-2 text-gray-400 hover:text-white text-sm transition-colors">
                <Coffee className="w-4 h-4" />
                <span>Support Us</span>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
