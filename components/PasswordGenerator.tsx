"use client";

import { useState, useCallback, useEffect } from "react";
import { <PERSON>, Co<PERSON>, Refresh<PERSON><PERSON>, <PERSON>, Eye, <PERSON>Off, Key, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import zxcvbn from 'zxcvbn';
import { Footer } from "@/components/Footer";

// Character sets for password generation
const charSets = {
  uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  lowercase: 'abcdefghijklmnopqrstuvwxyz',
  numbers: '0123456789',
  symbols: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  ambiguous: 'Il1O0'
} as const;

// Pre-generate character arrays for better performance
const preGeneratedChars = {
  uppercase: charSets.uppercase.split(''),
  lowercase: charSets.lowercase.split(''),
  numbers: charSets.numbers.split(''),
  symbols: charSets.symbols.split(''),
  ambiguous: new Set(charSets.ambiguous.split(''))
} as const;

// Common English words for passphrase generation
const commonWords = [
  "apple", "banana", "sunset", "ocean", "mountain", "river", "forest", "star",
  "moon", "sun", "cloud", "rain", "wind", "tree", "flower", "bird", "book",
  // Add more words as needed
];

interface PasswordOptions {
  length: number;
  uppercase: boolean;
  lowercase: boolean;
  numbers: boolean;
  symbols: boolean;
  ambiguous: boolean;
  noRepeating: boolean;
  hint: string;
}

export function PasswordGenerator() {
  const { toast } = useToast();
  const [mode, setMode] = useState<'password' | 'passphrase'>('password');
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [strength, setStrength] = useState(0);
  const [copied, setCopied] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [options, setOptions] = useState<PasswordOptions>({
    length: 16,
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: true,
    ambiguous: false,
    noRepeating: false,
    hint: "",
  });
  const [wordCount, setWordCount] = useState(4);
  const [includeNumber, setIncludeNumber] = useState(false);
  const [separator, setSeparator] = useState("-");
  const [isHighlighted, setIsHighlighted] = useState(false);
  const [isNewPassword, setIsNewPassword] = useState(false);

  // Memoize character pool generation
  const generateCharacterPool = useCallback(() => {
    const chars: string[] = [];
    if (options.uppercase) chars.push(...preGeneratedChars.uppercase);
    if (options.lowercase) chars.push(...preGeneratedChars.lowercase);
    if (options.numbers) chars.push(...preGeneratedChars.numbers);
    if (options.symbols) chars.push(...preGeneratedChars.symbols);
    
    if (!options.ambiguous) {
      return chars.filter(char => !preGeneratedChars.ambiguous.has(char));
    }
    return chars;
  }, [options.uppercase, options.lowercase, options.numbers, options.symbols, options.ambiguous]);

  // Memoize password generation function
  const generatePassword = useCallback(async () => {
    setIsGenerating(true);
    setCopied(false);
    
    try {
      let newPassword = '';
      if (mode === 'password') {
        const chars = generateCharacterPool();
        if (!chars.length) {
          toast.error("Please select at least one character type");
          return;
        }

        let maxAttempts = 50;
        let result: string | null = null;
        
        // Calculate available length for random characters
        const effectiveLength = options.hint ? 
          Math.max(options.length - options.hint.length, 0) : 
          options.length;
        
        while (maxAttempts > 0 && !result) {
          let tempResult = '';
          const cryptoValues = new Uint32Array(effectiveLength);
          crypto.getRandomValues(cryptoValues);

          let validChars = [...chars];
          for (let i = 0; i < effectiveLength; i++) {
            if (validChars.length === 0) break;
            
            const randomIndex = cryptoValues[i] % validChars.length;
            const char = validChars[randomIndex];
            
            if (options.noRepeating) {
              validChars = validChars.filter(c => c !== char);
            }
            
            tempResult += char;
          }

          // Include hint in the password
          if (options.hint) {
            const insertPosition = Math.floor(Math.random() * (tempResult.length + 1));
            tempResult = tempResult.slice(0, insertPosition) + options.hint + tempResult.slice(insertPosition);
          }

          if (tempResult.length === options.length) {
            const hasUppercase = !options.uppercase || /[A-Z]/.test(tempResult);
            const hasLowercase = !options.lowercase || /[a-z]/.test(tempResult);
            const hasNumbers = !options.numbers || /[0-9]/.test(tempResult);
            const hasSymbols = !options.symbols || /[^A-Za-z0-9]/.test(tempResult);

            if (hasUppercase && hasLowercase && hasNumbers && hasSymbols) {
              result = tempResult;
            }
          }

          maxAttempts--;
        }

        if (!result) {
          throw new Error("Could not generate a password meeting all requirements. Try with fewer restrictions.");
        }

        newPassword = result;
      } else {
        const selectedWords = Array.from({ length: wordCount }, () => {
          const index = Math.floor(crypto.getRandomValues(new Uint32Array(1))[0] / (2 ** 32) * commonWords.length);
          return commonWords[index];
        });
        
        const phrase = selectedWords.join(separator);
        const number = includeNumber ? Math.floor(Math.random() * 100) : '';
        newPassword = number ? `${phrase}${number}` : phrase;
        
        // Include hint in passphrase if provided
        if (options.hint) {
          newPassword = `${newPassword}${separator}${options.hint}`;
        }
      }
      
      setPassword(newPassword);
      // Calculate strength after setting the password
      const result = zxcvbn(newPassword);
      setStrength(result.score);
      
      // Add visual feedback
      setIsNewPassword(true);
      setTimeout(() => setIsNewPassword(false), 3000);
      setIsHighlighted(true);
      setTimeout(() => setIsHighlighted(false), 1000);
      
      // Add toast notification for feedback
      toast.success(`New password generated! Strength: ${['Very Weak', 'Weak', 'Fair', 'Strong', 'Very Strong'][result.score]}`);
      
    } catch (error) {
      console.error('Error generating password:', error);
      toast.error("Failed to generate password. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  }, [mode, options, wordCount, includeNumber, separator, generateCharacterPool, toast]);

  // Generate initial password and when options change
  useEffect(() => {
    // Generate initial password
    if (!password) {
      generatePassword();
      return;
    }

    // Create a debounced function for subsequent changes
    const debouncedGenerate = setTimeout(() => {
      generatePassword();
    }, 500);

    // Cleanup timeout
    return () => clearTimeout(debouncedGenerate);
  }, [
    mode,
    options.length,
    options.uppercase,
    options.lowercase,
    options.numbers,
    options.symbols,
    options.ambiguous,
    options.noRepeating,
    wordCount,
    includeNumber,
    separator,
  ]);

  // Debounce strength calculation
  useEffect(() => {
    if (!password) return;

    const timer = setTimeout(() => {
      const result = zxcvbn(password);
      setStrength(result.score);
    }, 300);

    return () => clearTimeout(timer);
  }, [password]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(password);
      setCopied(true);
      toast.success("Password copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy to clipboard");
    }
  };

  return (
    <div className="flex flex-col flex-1">
      <div className="container mx-auto py-6 flex-1">
        {/* Main Content */}
        <div className="w-full max-w-3xl mx-auto">
          <Card className={`p-6 ${isGenerating ? 'opacity-50' : ''}`}>
            <Tabs defaultValue="password" onValueChange={(v) => setMode(v as 'password' | 'passphrase')}>
              <TabsList className="mb-4">
                <TabsTrigger value="password">Password</TabsTrigger>
                <TabsTrigger value="passphrase">Passphrase</TabsTrigger>
              </TabsList>

              <div className="relative mb-6">
                <div className="flex flex-col space-y-4">
                  <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 p-4 rounded-lg relative">
                    <Lock className="text-gray-500" />
                    <input
                      type={showPassword ? "text" : "password"}
                      value={password}
                      readOnly
                      className={`flex-1 bg-transparent border-none focus:outline-none transition-all duration-300 ${
                        isHighlighted ? 'animate-highlight ring-2 ring-blue-500 dark:ring-blue-400 ring-opacity-50' : ''
                      }`}
                    />
                    {isNewPassword && (
                      <CheckCircle 
                        className="w-5 h-5 text-green-500 animate-[bounce_1s_ease-in-out_2] stroke-[3] mx-1" 
                      />
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff /> : <Eye />}
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => generatePassword()}
                      disabled={isGenerating}
                    >
                      <RefreshCw className={isGenerating ? 'animate-spin' : ''} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={copyToClipboard}
                    >
                      {copied ? <Check /> : <Copy />}
                    </Button>
                  </div>

                  {/* Hint Input */}
                  <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <Label htmlFor="hint" className="text-sm text-gray-500">Hint/Include:</Label>
                    <input
                      id="hint"
                      type="text"
                      value={options.hint}
                      onChange={(e) => setOptions({ ...options, hint: e.target.value })}
                      placeholder="Add text to include in password (optional)"
                      className="flex-1 bg-transparent border-none focus:outline-none text-sm"
                    />
                  </div>
                </div>

                <div className="mt-2 h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full transition-all duration-300"
                    style={{
                      width: `${(strength + 1) * 20}%`,
                      backgroundColor: ['#ef4444', '#f97316', '#eab308', '#84cc16', '#22c55e'][strength] || '#6b7280'
                    }}
                  />
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Strength: {['Very Weak', 'Weak', 'Fair', 'Strong', 'Very Strong'][strength]}
                </p>
              </div>

              <TabsContent value="password" className="space-y-4">
                <div>
                  <Label>Length: {options.length}</Label>
                  <Slider
                    value={[options.length]}
                    onValueChange={([value]) => setOptions({ ...options, length: value })}
                    max={50}
                    min={8}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries({
                    uppercase: "Uppercase (A-Z)",
                    lowercase: "Lowercase (a-z)",
                    numbers: "Numbers (0-9)",
                    symbols: "Symbols (!@#$)",
                    ambiguous: "Include Ambiguous Characters",
                    noRepeating: "Avoid Repeating Characters",
                  }).map(([key, label]) => (
                    <div key={key} className="flex items-center justify-between">
                      <Label htmlFor={key}>{label}</Label>
                      <Switch
                        id={key}
                        checked={Boolean(options[key as keyof PasswordOptions])}
                        onCheckedChange={(checked: boolean) =>
                          setOptions({ ...options, [key]: checked })
                        }
                      />
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="passphrase" className="space-y-4">
                <div>
                  <Label>Word Count: {wordCount}</Label>
                  <Slider
                    value={[wordCount]}
                    onValueChange={([value]) => setWordCount(value)}
                    max={8}
                    min={2}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="includeNumber">Include Number</Label>
                  <Switch
                    id="includeNumber"
                    checked={Boolean(includeNumber)}
                    onCheckedChange={(checked: boolean) => setIncludeNumber(checked)}
                  />
                </div>

                <div>
                  <Label>Separator</Label>
                  <div className="flex space-x-2 mt-2">
                    {["-", "_", ".", " "].map((sep) => (
                      <Button
                        key={sep}
                        variant={separator === sep ? "default" : "outline"}
                        onClick={() => setSeparator(sep)}
                        className="w-12"
                      >
                        {sep === " " ? "space" : sep}
                      </Button>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Generate Button */}
            <div className="mt-6">
              <Button 
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                onClick={() => generatePassword()}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <div className="flex items-center space-x-2">
                    <RefreshCw className="animate-spin" />
                    <span>Generating...</span>
                  </div>
                ) : (
                  <span>Generate New Password</span>
                )}
              </Button>
            </div>
          </Card>

          {/* Hint Message */}
          {options.hint && (
            <div className="text-sm text-gray-500 mt-2 px-2">
              <span className="font-medium">💡 Hint:</span> Your password will include "{options.hint}"
            </div>
          )}
        </div>
      </div>
    </div>
  );
}