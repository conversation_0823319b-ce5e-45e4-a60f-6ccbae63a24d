'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Recipe, RecipeGeneratorRequest, RecipeFilters as RecipeFiltersType } from '@/types/recipe';
import { UtensilsCrossed, Clock, Users, ChefHat, Utensils, X, AlertTriangle, XCircle, Download, Image as ImageIcon, Loader2, Save, BookmarkPlus, BookOpen, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import { auth, db } from '@/lib/firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { ConsentDialog } from '@/components/recipe/ConsentDialog';
import SavedRecipes from '@/components/recipe/SavedRecipes';
import { SavedRecipesRef } from '@/components/recipe/SavedRecipes';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { RecipeGeneratorToolClient } from './RecipeGeneratorToolClient';

const foodQuotes = [
  "Where there is good food, there is happiness.",
  "Cooking is love made visible.",
  "Life is what you cook it.",
  "Good food is the foundation of happiness.",
  "The secret ingredient is always love.",
  "Cooking with passion, serving with love.",
  "Food is not just eating, it's an experience.",
  "Great cooking is an art, a gift to share.",
  "I'm on a seafood diet: I see food, I eat it.",
  "You can't make everyone happy. You're not pizza.",
  "Life is short. Eat the cake.",
  "Love at first bite.",
  "Food is my favorite F-word.",
  "If we are what we eat, I'm awfully sweet.",
  "I followed my heart, and it led me to the fridge.",
  "Good food, good mood.",
  "Count the memories, not the calories.",
  "Eat well, live well.",
  "Chocolate is the answer, who cares what the question is.",
  "You had me at 'pizza.'",
];

export function RecipeGeneratorPage() {
  const router = useRouter();
  const [generateImage, setGenerateImage] = useState(false);
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [ingredients, setIngredients] = useState<string[]>([]);
  const [servings, setServings] = useState(2);
  const [filters, setFilters] = useState<RecipeFiltersType>({
    cuisine: 'Any',
    maxCookingTime: 60,
    dietary: []
  });
  const [error, setError] = useState('');
  const [showConsentDialog, setShowConsentDialog] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [user, setUser] = useState<any>(null);
  const recipeRef = useRef<HTMLDivElement>(null);
  const savedRecipesRef = useRef<SavedRecipesRef>(null);

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setUser(user);
    });

    // Restore recipe from localStorage on initial load
    const savedRecipe = localStorage.getItem('currentRecipe');
    if (savedRecipe) {
      setRecipe(JSON.parse(savedRecipe));
    }

    return () => unsubscribe();
  }, []);

  // Save recipe to localStorage whenever it changes
  useEffect(() => {
    if (recipe) {
      localStorage.setItem('currentRecipe', JSON.stringify(recipe));
    }
  }, [recipe]);

  const handleCancel = () => {
    if (isLoading) {
      setIsLoading(false);
      setRecipe(null);
      localStorage.removeItem('currentRecipe');
      toast.success('Recipe generation cancelled');
    }
  };

  const handleError = (error: any) => {
    console.error('Recipe generation error:', error);
    setIsLoading(false);
    setRecipe(null);
    
    const errorMessage = error?.message || 'Failed to generate recipe. Please try again.';
    toast.error(errorMessage);
  };

  const handleSubmit = async () => {
    try {
      setError('');
      setIsLoading(true);
      
      const requestData: RecipeGeneratorRequest = {
        filters,
        generateImage,
        ingredients,
        servings
      };

      if (!ingredients || ingredients.length === 0) {
        throw new Error('empty-ingredients');
      }

      const response = await fetch('/api/recipe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error('api-error');
      }

      const result = await response.json();
      
      if (!result || !result.title || !result.title.trim()) {
        throw new Error('empty-response');
      }

      // Update recipe state with the new recipe data
      setRecipe(prevRecipe => {
        const updatedRecipe = {
          ...result,
          status: {
            recipe: 'completed',
            image: result.status.image || (generateImage ? 'loading' : 'idle')
          },
          // Keep both imageUrl and image fields in sync
          imageUrl: result.imageUrl || result.image || '',
          image: result.image || result.imageUrl || ''
        };
        return updatedRecipe;
      });
    } catch (error: any) {
      let errorMessage;
      switch(error.message) {
        case 'empty-ingredients':
          errorMessage = "Hey chef! We need some ingredients to work with. What's in your pantry? ";
          break;
        case 'api-error':
          errorMessage = "Our kitchen equipment needs a quick check. Let's try again in a moment! ";
          break;
        case 'empty-response':
          errorMessage = "Our recipe book is taking a breather. How about another try? ";
          break;
        default:
          errorMessage = "Our chef AI is doing some taste testing. Let's give it another shot! ";
      }
      handleError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveRecipe = async () => {
    if (!user) {
      router.push(`/auth/signin?redirect=${encodeURIComponent('/recipe-generator')}`);
      return;
    }

    // Check if user is properly authenticated
    const currentUser = auth.currentUser;
    if (!currentUser) {
      toast.error('Please sign in again');
      router.push(`/auth/signin?redirect=${encodeURIComponent('/recipe-generator')}`);
      return;
    }

    if (!recipe) {
      toast.error('No recipe to save');
      return;
    }

    setIsSaving(true);
    try {
      // Debug logs
      console.log('Current User:', {
        uid: currentUser.uid,
        email: currentUser.email,
        isAnonymous: currentUser.isAnonymous,
      });

      let finalImageUrl = recipe.imageUrl || '';
      
      // Only upload the generated image to Backblaze if it exists
      if (recipe.imageUrl) {
        try {
          // Upload image to Backblaze using the existing endpoint
          const response = await fetch('/api/recipe', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
              base64Image: recipe.imageUrl
            }),
          });

          if (!response.ok) {
            console.error('Failed to upload image to Backblaze');
          } else {
            const data = await response.json();
            if (data.url) {
              finalImageUrl = data.url;
            }
          }
        } catch (uploadError) {
          console.error('Error uploading image:', uploadError);
        }
      }

      // Save recipe data to Firebase with the image URL
      const recipePath = `users/${currentUser.uid}/recipes`;
      console.log('Saving recipe to path:', recipePath);
      
      const recipesRef = collection(db, 'users', currentUser.uid, 'recipes');
      await addDoc(recipesRef, {
        ...recipe,
        imageUrl: recipe.imageUrl || recipe.image, // Use imageUrl or fallback to image
        backblazeImageUrl: finalImageUrl, // Store Backblaze URL separately
        createdAt: serverTimestamp(),
      });

      toast.success('Recipe saved successfully!');
      
      // Refresh the saved recipes list
      if (savedRecipesRef.current) {
        await savedRecipesRef.current.refreshRecipes();
      }
    } catch (error) {
      console.error('Error saving recipe:', error);
      // More detailed error logging
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack
        });
      }
      toast.error('Failed to save recipe');
    } finally {
      setIsSaving(false);
    }
  };

  const handleClearRecipe = () => {
    setRecipe(null);
    localStorage.removeItem('currentRecipe');
    toast.success('Recipe cleared');
  };

  const renderIngredient = (ingredient: string | { amount: number; unit: string; name: string; notes?: string }) => {
    if (typeof ingredient === 'string') {
      return ingredient;
    }
    return `${ingredient.amount} ${ingredient.unit} ${ingredient.name}${ingredient.notes ? ` (${ingredient.notes})` : ''}`;
  };

  const randomQuote = () => foodQuotes[Math.floor(Math.random() * foodQuotes.length)];

  const downloadAsPNG = async () => {
    if (!recipeRef.current) return;
    
    try {
      const canvas = await html2canvas(recipeRef.current, {
        scale: 2,
        useCORS: true,
        logging: false,
        allowTaint: true
      });
      
      const dataUrl = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = `${recipe?.title || 'recipe'}.png`;
      link.click();
    } catch (error) {
      console.error('Error downloading PNG:', error);
      alert('Failed to download PNG. Please try again.');
    }
  };

  const downloadAsPDF = async () => {
    if (!recipeRef.current) return;
    
    try {
      const canvas = await html2canvas(recipeRef.current, {
        scale: 2,
        useCORS: true,
        logging: false,
        allowTaint: true
      });
      
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'px',
        format: [canvas.width, canvas.height]
      });
      
      pdf.addImage(imgData, 'PNG', 0, 0, canvas.width, canvas.height);
      pdf.save(`${recipe?.title || 'recipe'}.pdf`);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      alert('Failed to download PDF. Please try again.');
    }
  };

  return (
    <>
      <main className="flex-1 bg-gradient-to-b from-rose-50 via-orange-50 to-amber-50 min-h-screen">
        <section className="py-8 bg-gradient-to-br from-rose-50 via-orange-50 to-amber-50 dark:from-gray-900 dark:via-amber-900 dark:to-gray-800 rounded-lg mb-6">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-rose-600 to-orange-600 bg-clip-text text-transparent">
              Recipe Generator
            </h1>
            <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
              Create delicious recipes with AI assistance
            </p>
            
            {/* Feature highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                <div className="w-10 h-10 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mb-2">
                  <svg className="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-sm font-semibold mb-1">AI Powered</h3>
                <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Smart recipe suggestions</p>
              </div>
              <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                <div className="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center mb-2">
                  <svg className="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                  </svg>
                </div>
                <h3 className="text-sm font-semibold mb-1">Save Recipes</h3>
                <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Store your favorite recipes</p>
              </div>
              <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                <div className="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center mb-2">
                  <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                  </svg>
                </div>
                <h3 className="text-sm font-semibold mb-1">Customizable</h3>
                <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Adjust ingredients and servings</p>
              </div>
            </div>
          </div>
        </section>
        <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          {!user && (
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-6 mb-8 border border-orange-100">
              <div className="flex items-start gap-4">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <ChefHat className="w-6 h-6 text-orange-600" />
                </div>
                <div className="flex-1">
                  <h2 className="text-lg font-semibold mb-1">Welcome to Recipe Generator!</h2>
                  <p className="text-gray-600 mb-4">
                    Sign in to save your favorite recipes, build your collection, and access them anytime.
                  </p>
                  <div className="flex gap-3">
                    <Link href={`/auth/signin?redirect=${encodeURIComponent('/recipe-generator')}`}>
                      <Button variant="default">
                        <BookOpen className="w-4 h-4 mr-2" />
                        Sign In
                      </Button>
                    </Link>
                    <Link href="/saved-recipes">
                      <Button variant="outline">
                        <BookOpen className="w-4 h-4 mr-2" />
                        View Saved Recipes
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-rose-900 mb-4 flex items-center justify-center gap-2">
              <Utensils className="h-8 w-8 text-rose-600" />
              Kitchen AI Assistant
            </h1>
            <p className="text-lg text-rose-700 max-w-2xl mx-auto">
              Your personal chef that transforms ingredients into delicious recipes
            </p>
          </div>

          <div className="relative">
            <RecipeGeneratorToolClient
              ingredients={ingredients}
              setIngredients={setIngredients}
              servings={servings}
              setServings={setServings}
              filters={filters}
              setFilters={setFilters}
              generateImage={generateImage}
              setGenerateImage={setGenerateImage}
              onSubmit={handleSubmit}
              isLoading={isLoading}
            />
            
            {isLoading && (
              <Button
                variant="destructive"
                size="sm"
                className="absolute top-0 right-0"
                onClick={handleCancel}
              >
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
            )}
          </div>
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <p className="text-red-700">{error}</p>
            </div>
          )}
          
          {(recipe || isLoading) && (
            <div className="w-full bg-white rounded-xl shadow-lg overflow-hidden mb-12">
              <div className="p-8">
                {/* Recipe Status Display */}
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-6 mb-6">
                  <div className="flex items-center space-x-2">
                    {isLoading ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin text-amber-600" />
                        <span className="text-sm sm:text-base text-amber-600 font-medium">Generating your recipe...</span>
                      </>
                    ) : recipe && recipe.status.recipe === 'loading' ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin text-amber-600" />
                        <span className="text-sm sm:text-base text-amber-600 font-medium">Generating recipe...</span>
                      </>
                    ) : recipe && recipe.status.recipe === 'failed' ? (
                      <>
                        <XCircle className="w-5 h-5 text-red-500" />
                        <span className="text-sm sm:text-base text-red-500 font-medium">Error generating recipe</span>
                      </>
                    ) : recipe && recipe.status.recipe === 'completed' ? (
                      <>
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <span className="text-sm sm:text-base text-green-500 font-medium">Recipe generated!</span>
                      </>
                    ) : null}
                  </div>
                  
                  {/* Image Status */}
                  {recipe && recipe.status.recipe === 'completed' && (
                    <div className="flex items-center space-x-2">
                      {recipe.status.image === 'loading' && (
                        <div className="flex items-center space-x-2">
                          <Loader2 className="w-4 h-4 animate-spin text-amber-600" />
                          <span className="text-xs sm:text-sm text-amber-600">Generating image...</span>
                        </div>
                      )}
                      {recipe.status.image === 'success' && (
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-xs sm:text-sm text-green-500">Image generated!</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3 mb-6">
                  {user ? (
                    <Button
                      onClick={handleSaveRecipe}
                      disabled={isSaving || recipe?.status.recipe !== 'completed'}
                      className="flex-1 sm:flex-none bg-rose-600 hover:bg-rose-700 text-white"
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4 mr-2" />
                          Save Recipe
                        </>
                      )}
                    </Button>
                  ) : (
                    <Button
                      onClick={() => router.push(`/auth/signin?redirect=${encodeURIComponent('/recipe-generator')}`)}
                      disabled={recipe?.status.recipe !== 'completed'}
                      className="flex-1 sm:flex-none bg-rose-600 hover:bg-rose-700 text-white"
                    >
                      <BookmarkPlus className="w-4 h-4 mr-2" />
                      Sign In to Save
                    </Button>
                  )}
                  
                  <Button
                    onClick={downloadAsPDF}
                    disabled={recipe?.status.recipe !== 'completed'}
                    variant="outline"
                    className="flex-1 sm:flex-none"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download PDF
                  </Button>
                  
                  <Button
                    onClick={downloadAsPNG}
                    disabled={recipe?.status.recipe !== 'completed'}
                    variant="outline"
                    className="flex-1 sm:flex-none"
                  >
                    <ImageIcon className="w-4 h-4 mr-2" />
                    Download Image
                  </Button>
                  
                  <Button
                    onClick={handleClearRecipe}
                    disabled={!recipe}
                    variant="ghost"
                    className="flex-1 sm:flex-none text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Clear Recipe
                  </Button>
                </div>

                {/* Recipe Display */}
                {recipe && recipe.status.recipe === 'completed' && (
                  <div ref={recipeRef} className="recipe-card bg-white p-6 rounded-lg">
                    <h2 className="text-2xl font-bold text-gray-800 mb-4">{recipe.title}</h2>
                    
                    {recipe.imageUrl && (
                      <div className="mb-6 rounded-lg overflow-hidden">
                        <img 
                          src={recipe.imageUrl} 
                          alt={recipe.title} 
                          className="w-full h-auto object-cover"
                          style={{ maxHeight: '400px' }}
                        />
                      </div>
                    )}
                    
                    <div className="flex flex-wrap gap-4 mb-6">
                      <div className="flex items-center">
                        <Clock className="w-5 h-5 text-amber-600 mr-2" />
                        <span className="text-gray-700">{recipe.cookingTime} mins</span>
                      </div>
                      <div className="flex items-center">
                        <Users className="w-5 h-5 text-amber-600 mr-2" />
                        <span className="text-gray-700">{recipe.servings} servings</span>
                      </div>
                      <div className="flex items-center">
                        <UtensilsCrossed className="w-5 h-5 text-amber-600 mr-2" />
                        <span className="text-gray-700">{recipe.cuisine} cuisine</span>
                      </div>
                    </div>
                    
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">Description</h3>
                      <p className="text-gray-700">{recipe.description}</p>
                    </div>
                    
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">Ingredients</h3>
                      <ul className="list-disc pl-5 space-y-1">
                        {recipe.ingredients.map((ingredient, index) => (
                          <li key={index} className="text-gray-700">
                            {renderIngredient(ingredient)}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">Instructions</h3>
                      <ol className="list-decimal pl-5 space-y-2">
                        {recipe.instructions.map((step, index) => (
                          <li key={index} className="text-gray-700">{step}</li>
                        ))}
                      </ol>
                    </div>
                    
                    {recipe.tips && recipe.tips.length > 0 && (
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">Tips</h3>
                        <ul className="list-disc pl-5 space-y-1">
                          {recipe.tips.map((tip, index) => (
                            <li key={index} className="text-gray-700">{tip}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    <div className="text-center mt-8 text-gray-500 italic">
                      "{randomQuote()}"
                    </div>
                  </div>
                )}
                
                {/* Loading State */}
                {isLoading && (
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="w-16 h-16 mb-4 relative">
                      <div className="absolute inset-0 rounded-full border-4 border-amber-200 opacity-25"></div>
                      <div className="absolute inset-0 rounded-full border-4 border-amber-600 opacity-75 animate-spin border-t-transparent"></div>
                    </div>
                    <h3 className="text-xl font-semibold text-amber-600 mb-2">Cooking up your recipe...</h3>
                    <p className="text-gray-600 max-w-md text-center">
                      Our AI chef is preparing a delicious recipe based on your ingredients. This might take a moment.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {user && (
            <div className="mb-12">
              <SavedRecipes ref={savedRecipesRef} />
            </div>
          )}
          
          <ConsentDialog open={showConsentDialog} onOpenChange={setShowConsentDialog} />
          
          <SupportSection />
        </div>
      </main>
      <Footer />
    </>
  );
}
