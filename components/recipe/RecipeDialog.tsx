'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { SavedRecipe } from '@/types/recipe';
import Image from 'next/image';
import { Download, Trash2, ChefHat, Clock, Users } from 'lucide-react';
import { generatePDF } from '../../lib/pdf-utils';

interface RecipeDialogProps {
  recipe: SavedRecipe;
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
  onDownload: () => void;
}

export function RecipeDialog({ recipe, isOpen, onClose, onDelete, onDownload }: RecipeDialogProps) {
  // Prioritize the original AI-generated image, then fall back to Backblaze URL
  const recipeImage = recipe.imageUrl || recipe.backblazeImageUrl;
  const hasImage = Boolean(recipeImage);

  const handleDownload = async () => {
    await generatePDF(recipe);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-white">
        <DialogHeader className="sticky top-0 bg-white z-10 pb-4 border-b">
          <DialogTitle className="text-2xl font-bold text-rose-800">{recipe.title}</DialogTitle>
          <DialogDescription className="text-gray-600 italic">
            {recipe.description}
          </DialogDescription>
          
          {/* Recipe Meta Info */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mt-4">
            <div className="flex items-center gap-2 bg-rose-50 p-2 rounded-lg">
              <Clock className="h-4 w-4 text-rose-600" />
              <span className="text-sm text-rose-800">{recipe.cookingTime} mins</span>
            </div>
            <div className="flex items-center gap-2 bg-amber-50 p-2 rounded-lg">
              <Users className="h-4 w-4 text-amber-600" />
              <span className="text-sm text-amber-800">{recipe.servings} servings</span>
            </div>
            <div className="flex items-center gap-2 bg-emerald-50 p-2 rounded-lg">
              <ChefHat className="h-4 w-4 text-emerald-600" />
              <span className="text-sm text-emerald-800 capitalize">{recipe.cuisine}</span>
            </div>
            <div className="flex items-center gap-2 bg-blue-50 p-2 rounded-lg">
              <ChefHat className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-blue-800 capitalize">{recipe.difficulty}</span>
            </div>
          </div>
        </DialogHeader>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 py-6">
          {/* Left Column - Image and Actions */}
          <div className="lg:col-span-1 space-y-4">
            {hasImage && (
              <img
                src={recipeImage}
                alt={recipe.title}
                className="w-full h-64 object-cover rounded-lg"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />
            )}
            
            <div className="flex flex-col gap-3">
              <Button onClick={handleDownload} className="w-full bg-rose-600 hover:bg-rose-700 text-white">
                <Download className="w-4 h-4 mr-2" />
                Download PDF
              </Button>
              <Button onClick={onDownload} variant="outline" className="w-full border-amber-500 text-amber-700 hover:bg-amber-50">
                <Download className="w-4 h-4 mr-2" />
                Download PNG
              </Button>
              <Button variant="outline" onClick={onDelete} className="w-full border-red-500 text-red-700 hover:bg-red-50">
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Recipe
              </Button>
            </div>
          </div>

          {/* Right Column - Recipe Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Ingredients Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-rose-100">
              <h3 className="text-lg font-semibold mb-4 text-rose-800 flex items-center gap-2">
                <div className="h-6 w-1 bg-rose-500 rounded-full"></div>
                Ingredients
              </h3>
              <ul className="space-y-2">
                {recipe.ingredients.map((ingredient, index) => (
                  <li key={index} className="flex items-start gap-2 text-gray-700">
                    <span className="text-rose-500">•</span>
                    <span>
                      {typeof ingredient === 'string' 
                        ? ingredient 
                        : `${ingredient.amount} ${ingredient.unit} ${ingredient.name}${ingredient.notes ? ` (${ingredient.notes})` : ''}`
                      }
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Instructions Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-rose-100">
              <h3 className="text-lg font-semibold mb-4 text-rose-800 flex items-center gap-2">
                <div className="h-6 w-1 bg-rose-500 rounded-full"></div>
                Instructions
              </h3>
              <ol className="space-y-4">
                {recipe.instructions.steps.map((step, index) => (
                  <li key={index} className="flex gap-3">
                    <span className="flex-shrink-0 font-semibold text-rose-600">{index + 1}.</span>
                    <p className="text-gray-700">{step}</p>
                  </li>
                ))}
              </ol>
            </div>

            {/* Tips Section */}
            {recipe.instructions.tips.length > 0 && (
              <div className="bg-amber-50 rounded-xl p-6 shadow-sm border border-amber-100">
                <h3 className="text-lg font-semibold mb-4 text-amber-800">Chef's Tips</h3>
                <ul className="space-y-2">
                  {recipe.instructions.tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-amber-500">💡</span>
                      <p className="text-amber-800">{tip}</p>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Nutritional Information */}
            {recipe.nutritionalInfo && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-rose-100">
                <h3 className="text-lg font-semibold mb-4 text-rose-800 flex items-center gap-2">
                  <div className="h-6 w-1 bg-rose-500 rounded-full"></div>
                  Nutritional Information
                </h3>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  {Object.entries(recipe.nutritionalInfo).map(([key, value]) => (
                    value && (
                      <div key={key} className="bg-rose-50 p-3 rounded-lg">
                        <p className="text-sm text-rose-600 capitalize">{key}</p>
                        <p className="font-semibold text-rose-900">
                          {typeof value === 'number' ? `${value}g` : value}
                        </p>
                      </div>
                    )
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}