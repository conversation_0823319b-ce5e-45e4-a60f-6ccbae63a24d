'use client';

import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Recipe, SavedRecipe } from '@/types/recipe';
import { auth, db } from '@/lib/firebase';
import { collection, query, orderBy, onSnapshot, deleteDoc, doc , } from 'firebase/firestore';
import { RecipeDialog } from './RecipeDialog';
import { Button } from '@/components/ui/button';
import { Eye, Download, Trash2, ChefHat, Clock, Users } from 'lucide-react';
import Image from 'next/image';
import { toast } from 'sonner';
import Link from 'next/link';

export interface SavedRecipesRef {
  refreshRecipes: () => Promise<void>;
}

const SavedRecipes = forwardRef<SavedRecipesRef>((_, ref) => {
  const [recipes, setRecipes] = useState<SavedRecipe[]>([]);
  const [selectedRecipe, setSelectedRecipe] = useState<SavedRecipe | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const recipesPerPage = 5;

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      setUser(user);
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (!user) {
      setRecipes([]);
      return;
    }

    const recipesRef = collection(db, 'users', user.uid, 'recipes');
    const q = query(recipesRef, orderBy('createdAt', 'desc'));
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const fetchedRecipes = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          userId: user.uid,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          savedAt: data.savedAt?.toDate() || new Date(),
        } as SavedRecipe;
      });
      
      setRecipes(fetchedRecipes);
      setIsLoading(false);
    }, (error) => {
      console.error('Error fetching recipes:', error);
      toast.error('Failed to load saved recipes');
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, [user]);

  useImperativeHandle(ref, () => ({
    refreshRecipes: async () => {
      return Promise.resolve();
    }
  }));

  const handleDelete = async (recipeId: string) => {
    if (!user) {
      return;
    }
    
    try {
      await deleteDoc(doc(db, 'users', user.uid, 'recipes', recipeId));
      setRecipes(prev => prev.filter(r => r.id !== recipeId));
      toast.success('Recipe deleted successfully');
      setSelectedRecipe(null);
    } catch (error) {
      console.error('Error deleting recipe:', error);
      toast.error('Failed to delete recipe');
    }
  };

  const handleDownload = async (recipe: SavedRecipe) => {
    try {
      const response = await fetch(recipe.backblazeImageUrl || recipe.imageUrl || '');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${recipe.title.toLowerCase().replace(/\s+/g, '-')}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error('Failed to download recipe image');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin mr-2">
          <ChefHat className="w-5 h-5" />
        </div>
        <span>Loading your recipes...</span>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="max-w-2xl mx-auto text-center py-16 px-4">
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-8 border border-orange-100">
          <ChefHat className="w-16 h-16 mx-auto mb-6 text-orange-500" />
          <h2 className="text-3xl font-semibold mb-4">Your Recipe Collection Awaits</h2>
          <p className="text-gray-600 text-lg mb-8">
            Sign in to access your saved recipes, create your personal collection, and never lose a great recipe again!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signin">
              <Button 
                size="lg"
                className="bg-orange-600 hover:bg-orange-700"
              >
                Sign In to View Recipes
              </Button>
            </Link>
            <Link href="/recipe-generator">
              <Button variant="outline" size="lg">
                <ChefHat className="w-5 h-5 mr-2" />
                Create New Recipe
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {recipes.length === 0 ? (
        <div className="text-center py-8">
          {user ? (
            <div className="bg-amber-50/90 backdrop-blur-sm rounded-xl p-8 border border-amber-200 space-y-4">
              <ChefHat className="mx-auto h-12 w-12 text-amber-600" />
              <h3 className="text-xl font-semibold text-amber-900">No recipes saved yet</h3>
              <p className="text-amber-700">Generate some recipes to get started!</p>
            </div>
          ) : (
            <div className="bg-amber-50/90 backdrop-blur-sm rounded-xl p-8 border border-amber-200 space-y-4">
              <ChefHat className="mx-auto h-12 w-12 text-amber-600" />
              <h3 className="text-xl font-semibold text-amber-900">Sign in to save recipes</h3>
              <Link href="/auth/signin">
                <Button className="bg-amber-600 hover:bg-amber-700 text-white">Sign In</Button>
              </Link>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-amber-900">Your Recipe Collection</h2>
            <span className="text-amber-700 font-medium">{recipes.length} {recipes.length === 1 ? 'Recipe' : 'Recipes'}</span>
          </div>
          
          <div className="grid gap-4">
            {recipes
              .slice((currentPage - 1) * recipesPerPage, currentPage * recipesPerPage)
              .map((recipe) => (
                <div 
                  key={recipe.id} 
                  className="bg-amber-50/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border border-amber-200 overflow-hidden"
                >
                  <div className="p-6 flex items-center gap-6">
                    {(recipe.imageUrl || recipe.backblazeImageUrl) ? (
                      <div className="flex-shrink-0">
                        <img
                          src={recipe.imageUrl || recipe.backblazeImageUrl}
                          alt={recipe.title}
                          className="w-full h-48 object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                      </div>
                    ) : (
                      <div className="flex-shrink-0">
                        <div className="w-28 h-28 bg-amber-100 rounded-lg shadow-sm border border-amber-100 flex items-center justify-center">
                          <ChefHat className="w-12 h-12 text-amber-400" />
                        </div>
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                        <h3 className="text-xl font-semibold text-amber-900 truncate">
                          {recipe.title}
                        </h3>
                        <div className="flex items-center gap-2 text-xs text-amber-600">
                          <Clock className="w-3 h-3" />
                          {recipe.createdAt?.toLocaleString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </div>
                      </div>
                      <p className="text-amber-700 text-sm mt-1 line-clamp-2">
                        {recipe.description}
                      </p>
                      <div className="flex items-center gap-4 mt-4">
                        <div className="flex items-center gap-1 text-sm text-amber-700">
                          <Clock className="w-4 h-4" />
                          <span>{recipe.cookingTime} mins</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-amber-700">
                          <Users className="w-4 h-4" />
                          <span>{recipe.servings} servings</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-amber-700">
                          <ChefHat className="w-4 h-4" />
                          <span className="capitalize">{recipe.difficulty}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-amber-700 hover:text-amber-800 hover:bg-amber-100/50"
                        onClick={() => setSelectedRecipe(recipe)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-amber-700 hover:text-amber-800 hover:bg-amber-100/50"
                        onClick={() => handleDownload(recipe)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-amber-700 hover:text-amber-800 hover:bg-amber-100/50"
                        onClick={() => handleDelete(recipe.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
            ))}
          </div>
          
          {/* Pagination Controls */}
          {recipes.length > recipesPerPage && (
            <div className="flex justify-center items-center gap-4 mt-8">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="border-amber-200 text-amber-700 hover:bg-amber-50"
              >
                Previous
              </Button>
              <span className="text-amber-800 font-medium">
                Page {currentPage} of {Math.ceil(recipes.length / recipesPerPage)}
              </span>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => 
                  Math.min(Math.ceil(recipes.length / recipesPerPage), prev + 1)
                )}
                disabled={currentPage >= Math.ceil(recipes.length / recipesPerPage)}
                className="border-amber-200 text-amber-700 hover:bg-amber-50"
              >
                Next
              </Button>
            </div>
          )}
        </div>
      )}

      {selectedRecipe && (
        <RecipeDialog
          recipe={selectedRecipe}
          isOpen={!!selectedRecipe}
          onClose={() => setSelectedRecipe(null)}
          onDelete={() => {
            handleDelete(selectedRecipe.id);
            setSelectedRecipe(null);
          }}
          onDownload={() => handleDownload(selectedRecipe)}
        />
      )}
    </div>
  );
});

SavedRecipes.displayName = 'SavedRecipes';

export default SavedRecipes;