'use client';

import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// Use dynamic import with SSR disabled
const RecipeGeneratorTool = dynamic(
  () => import('@/components/RecipeGeneratorTool'),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center mb-4">
          <Loader2 className="h-8 w-8 text-amber-600 dark:text-amber-400 animate-spin" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">Loading Recipe Generator...</h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Please wait while we initialize the recipe generator tool.
        </p>
      </div>
    )
  }
);

interface RecipeGeneratorToolClientProps {
  ingredients: string[];
  setIngredients: (ingredients: string[]) => void;
  servings: number;
  setServings: (servings: number) => void;
  filters: any;
  setFilters: (filters: any) => void;
  generateImage: boolean;
  setGenerateImage: (generateImage: boolean) => void;
  onSubmit: () => void;
  isLoading: boolean;
}

export function RecipeGeneratorToolClient(props: RecipeGeneratorToolClientProps) {
  return <RecipeGeneratorTool {...props} />;
}
