"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Copy, Download, Loader2, <PERSON>, <PERSON><PERSON><PERSON>2, Smile, Wand2, CaseSensitive, <PERSON><PERSON>, Bo<PERSON> } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import dynamic from "next/dynamic";
import { Theme, EmojiClickData } from "emoji-picker-react";
import { useTheme } from "next-themes";
import { convertText } from "@/lib/text-conversion";
import { StyleButton } from "@/types/text-conversion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { trackConversion, trackToolUsage, trackEngagement } from "@/lib/newrelic-utils";

const EmojiPicker = dynamic(() => import("emoji-picker-react").then(mod => mod.default), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center p-4">
      <Loader2 className="h-6 w-6 animate-spin" />
    </div>
  )
});

const CASE_BUTTONS: StyleButton[] = [
  // Basic Cases
  { type: "sentence", label: "Sentence case", color: "bg-gray-700 hover:bg-gray-800", category: "basic" },
  { type: "lower", label: "lowercase", color: "bg-gray-700 hover:bg-gray-800", category: "basic" },
  { type: "upper", label: "UPPER CASE", color: "bg-gray-700 hover:bg-gray-800", category: "basic" },
  { type: "capitalized", label: "Capitalized Case", color: "bg-gray-700 hover:bg-gray-800", category: "basic" },
  { type: "alternating", label: "aLtErNaTiNg CaSe", color: "bg-gray-700 hover:bg-gray-800", category: "basic" },
  { type: "title", label: "Title Case", color: "bg-gray-700 hover:bg-gray-800", category: "basic" },

  // Fancy Fonts - Mathematical Unicode
  { type: "bold", label: "𝗕𝗼𝗹𝗱", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },
  { type: "italic", label: "𝐼𝑡𝑎𝑙𝑖𝑐", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },
  { type: "bolditalic", label: "𝑩𝒐𝒍𝒅 𝑰𝒕𝒂𝒍𝒊𝒄", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },
  { type: "script", label: "𝓢𝓬𝓻𝓲𝓹𝓽", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },
  { type: "gothic", label: "𝔊𝔬𝔱𝔥𝔦𝔠", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },
  { type: "doublestruck", label: "𝔻𝕠𝕦𝕓𝕝𝕖", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },
  { type: "monospace", label: "𝙼𝚘𝚗𝚘", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },
  { type: "sansserif", label: "𝖲𝖺𝗇𝗌", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },
  { type: "handwriting", label: "𝒽𝒶𝓃𝒹𝓌𝓇𝒾𝓉𝒾𝓃ℊ", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },
  { type: "neon", label: "₦ɇø₦", color: "bg-gray-700 hover:bg-gray-800", category: "fancy" },

  // Advanced Mathematical Fonts
  { type: "boldsansserif", label: "𝗕𝗼𝗹𝗱 𝗦𝗮𝗻𝘀", color: "bg-gray-700 hover:bg-gray-800", category: "mathematical" },
  { type: "italicsansserif", label: "𝘐𝘵𝘢𝘭𝘪𝘤 𝘚𝘢𝘯𝘴", color: "bg-gray-700 hover:bg-gray-800", category: "mathematical" },
  { type: "bolditalicsansserif", label: "𝙄𝙩𝙖𝙡𝙞𝙘 𝘽𝙤𝙡𝙙", color: "bg-gray-700 hover:bg-gray-800", category: "mathematical" },
  { type: "smallcaps", label: "Sᴍᴀʟʟ Cᴀᴘs", color: "bg-gray-700 hover:bg-gray-800", category: "mathematical" },
  { type: "fullwidth", label: "Ｆｕｌｌｗｉｄｔｈ", color: "bg-gray-700 hover:bg-gray-800", category: "mathematical" },

  // Decorative Styles
  { type: "circlenegative", label: "🅣🅔🅧🅣", color: "bg-gray-700 hover:bg-gray-800", category: "decorative" },
  { type: "circled", label: "Ⓒⓘⓡⓒⓛⓔⓓ", color: "bg-gray-700 hover:bg-gray-800", category: "decorative" },
  { type: "bubble", label: "ⓑⓤⓑⓑⓛⓔ", color: "bg-gray-700 hover:bg-gray-800", category: "decorative" },
  { type: "parenthesized", label: "⒫⒜⒭⒠⒩⒯⒣⒠⒮⒤⒵⒠Ⓓ", color: "bg-gray-700 hover:bg-gray-800", category: "decorative" },
  { type: "regional", label: "🇷🇪🇬🇮🇴🇳🇦🇱", color: "bg-gray-700 hover:bg-gray-800", category: "decorative" },
  { type: "bent", label: "ᗩᕮᔕTᕼETIᑕ", color: "bg-gray-700 hover:bg-gray-800", category: "decorative" },
  { type: "squared", label: "🅂🆀🆄🅰🆁🅴🅳", color: "bg-gray-700 hover:bg-gray-800", category: "decorative" },
  { type: "aesthetic", label: "ａｅｓｔｈｅｔｉｃ", color: "bg-gray-700 hover:bg-gray-800", category: "decorative" },

  // Symbol & Effects
  { type: "superscript", label: "ˢᵘᵖᵉʳˢᶜʳⁱᵖᵗ", color: "bg-gray-700 hover:bg-gray-800", category: "symbols" },
  { type: "subscript", label: "ₛᵤᵦₛcᵣᵢₚₜ", color: "bg-gray-700 hover:bg-gray-800", category: "symbols" },
  { type: "overline", label: "O̅v̅e̅r̅l̅i̅n̅e̅", color: "bg-gray-700 hover:bg-gray-800", category: "symbols" },
  { type: "doubleunderline", label: "D̳o̳u̳b̳l̳e̳ ̳U̳n̳d̳e̳r̳", color: "bg-gray-700 hover:bg-gray-800", category: "symbols" },
  { type: "widetext", label: "Ｗｉｄｅ　Ｔｅｘｔ", color: "bg-gray-700 hover:bg-gray-800", category: "symbols" },
  { type: "squaredtext", label: "🅂🅀🅄🄰🅁🄴🄳", color: "bg-gray-700 hover:bg-gray-800", category: "symbols" },

  // Special Effects
  { type: "mirror", label: "Mirrored Text", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "backwards", label: "Backwards Text", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "reverse", label: "Reverse", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "shuffle", label: "Shuffle", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "mini", label: "ᴍɪɴɪ", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "tiny", label: "ᴛɪɴʏ", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "strikethrough", label: "s̶t̶r̶i̶k̶e̶", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "underline", label: "u̲n̲d̲e̲r̲l̲i̲n̲e̲", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "distorted", label: "D̷i̷s̷t̷o̷r̷t̷e̷d̷", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "upsidedown", label: "ʇxǝʇ uʍop-ǝpᴉsd∩", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "zalgo", label: "Z̴a̴l̴g̴o̴", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "leetspeak", label: "1337 5p34k", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "mocking", label: "MoCkInG", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "binary", label: "Binary", color: "bg-gray-700 hover:bg-gray-800", category: "special" },
  { type: "morse", label: "Morse Code", color: "bg-gray-700 hover:bg-gray-800", category: "special" }
];

export function Converter() {
  const { theme: currentTheme } = useTheme();
  const [text, setText] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('lastText') || "";
    }
    return "";
  });
  const [convertedText, setConvertedText] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('lastConvertedText') || "";
    }
    return "";
  });

  const [sentimentResult, setSentimentResult] = useState<{
    score: number;
    explanation: string;
    emotions: {
      love: number;
      joy: number;
      sadness: number;
      anger: number;
      romantic: number;
      fear: number;
    };
  } | null>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('lastSentimentResult');
      return saved ? JSON.parse(saved) : null;
    }
    return null;
  });

  const [wordFrequency, setWordFrequency] = useState<{ word: string; count: number }[]>([]);
  const [showWordFrequency, setShowWordFrequency] = useState(false);

  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  const emojiPickerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('lastText', text);
    }
  }, [text]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('lastConvertedText', convertedText);
    }
  }, [convertedText]);

  useEffect(() => {
    if (typeof window !== 'undefined' && sentimentResult) {
      localStorage.setItem('lastSentimentResult', JSON.stringify(sentimentResult));
    }
  }, [sentimentResult]);

  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [activeCase, setActiveCase] = useState<string | null>(null);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setText(newText);
    setConvertedText(convertText(newText, activeCase));
  };

  const countStats = (text: string) => {
    return {
      characters: text.length,
      words: text.trim() === "" ? 0 : text.trim().split(/\s+/).length,
      sentences: text.trim() === "" ? 0 : text.trim().split(/[.!?]+/).filter(Boolean).length,
    };
  };

  const handleButtonClick = (type: string) => {
    setActiveCase(type);
    const newText = convertText(text, type);
    setConvertedText(newText);
    
    // Track text conversion with New Relic
    trackConversion(type, text.length, newText.length);
    trackToolUsage('text-converter', { conversionType: type });
  };

  const handleSentimentAnalysis = async () => {
    if (!text.trim()) {
      toast.error("Please enter some text to analyze");
      return;
    }

    const wordCount = text.trim().split(/\s+/).length;
    if (wordCount > 100) {
      toast.error("Text exceeds 100 words limit. Please shorten your text.");
      return;
    }

    setIsAnalyzing(true);
    setSentimentResult(null); // Clear previous results

    try {
      const response = await fetch('/api/sentiment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to analyze sentiment');
      }

      const result = await response.json();
      setSentimentResult(result);
      toast.success("Sentiment analysis has been generated.");
      
      // Track sentiment analysis with New Relic
      trackToolUsage('sentiment-analysis', { 
        textLength: text.length,
        wordCount,
        sentiment: result.sentiment 
      });
    } catch (error: any) {
      console.error('Sentiment analysis error:', error);
      toast.error(error?.message || "Failed to analyze sentiment. Please try again later.");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const analyzeWordFrequency = () => {
    if (!text.trim()) {
      toast.error("Please enter some text to analyze");
      return;
    }

    const words = text.toLowerCase().match(/\b[\w']+\b/g) || [];
    const frequency: { [key: string]: number } = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });

    const sortedFrequency = Object.entries(frequency)
      .map(([word, count]) => ({ word, count }))
      .sort((a, b) => b.count - a.count || a.word.localeCompare(b.word));

    setWordFrequency(sortedFrequency);
  };

  const copyToClipboard = (text: string) => {
    if (!text) {
      toast.error("No text to copy");
      return;
    }
    navigator.clipboard.writeText(text).then(
      () => {
        toast.success("Text copied to clipboard");
        // Track copy action with New Relic
        trackEngagement('copy-text', { 
          textLength: text.length,
          conversionType: activeCase 
        });
      },
      (err) => {
        toast.error("Could not copy text");
        console.error("Could not copy text: ", err);
      }
    );
  };

  const downloadText = () => {
    const element = document.createElement("a");
    const file = new Blob([convertedText], { type: "text/plain" });
    element.href = URL.createObjectURL(file);
    element.download = "converted-text.txt";
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    
    // Track download action with New Relic
    trackEngagement('download-text', { 
      textLength: convertedText.length,
      conversionType: activeCase 
    });
  };

  const handleEmojiClick = useCallback((emojiData: EmojiClickData) => {
    if (textareaRef.current) {
      const start = textareaRef.current.selectionStart;
      const end = textareaRef.current.selectionEnd;
      const newText = text.substring(0, start) + emojiData.emoji + text.substring(end);
      setText(newText);
      setShowEmojiPicker(false);

      requestAnimationFrame(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
          textareaRef.current.setSelectionRange(
            start + emojiData.emoji.length,
            start + emojiData.emoji.length
          );
        }
      });
    }
  }, [text]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current &&
        emojiButtonRef.current &&
        !emojiPickerRef.current.contains(event.target as Node) &&
        !emojiButtonRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    setConvertedText(convertText(text, activeCase));
  }, [text, activeCase]);

  const renderStyleButtons = (category: StyleButton['category'], colorClass: string) => (
    <div className="flex flex-wrap gap-2">
      {CASE_BUTTONS.filter(btn => btn.category === category).map((btn) => (
        <Button
          key={btn.type}
          onClick={() => handleButtonClick(btn.type)}
          variant={activeCase === btn.type ? "default" : "outline"}
          size="sm"
          className={cn(
            "rounded-full transition-all transform hover:scale-105",
            activeCase === btn.type
              ? `${colorClass} text-white shadow-md`
              : `bg-white hover:bg-blue-50 text-blue-500 border-blue-200 hover:border-blue-500`
          )}
        >
          {btn.label}
        </Button>
      ))}
    </div>
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 min-h-[600px]">
      {/* Left Column - Input and Controls */}
      <div className="p-6 border-b lg:border-b-0 lg:border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="space-y-4">
          {/* Input Area */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Input Text</h3>
              <Button
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                ref={emojiButtonRef}
                size="sm"
                variant="ghost"
                className="text-pink-600 hover:text-pink-700 hover:bg-pink-50 dark:text-pink-400 dark:hover:bg-pink-900/50"
              >
                <Smile className="h-5 w-5" />
              </Button>
            </div>
            <div className="relative">
              <Textarea
                ref={textareaRef}
                placeholder="Enter your text here..."
                value={text}
                onChange={(e) => handleTextChange(e)}
                className="min-h-[200px] font-mono bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:ring-blue-500 dark:focus:ring-blue-400 rounded-lg shadow-sm"
              />
            </div>
            <div className="mt-2 flex items-center justify-between text-sm text-gray-600 dark:text-gray-300">
              <div className="flex space-x-4">
                <span>Characters: {countStats(text).characters}</span>
                <span>Words: {countStats(text).words}</span>
                <span>Sentences: {countStats(text).sentences}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setText('');
                  setConvertedText('');
                  setSentimentResult(null);
                }}
                className="text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
              >
                Clear
              </Button>
            </div>
          </div>

          <Tabs defaultValue="transform" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="transform"><CaseSensitive className="w-4 h-4 mr-2" />Transform</TabsTrigger>
              <TabsTrigger value="decorate"><Palette className="w-4 h-4 mr-2" />Decorate</TabsTrigger>
              <TabsTrigger value="analyze"><Bot className="w-4 h-4 mr-2" />Analyze</TabsTrigger>
            </TabsList>
            <TabsContent value="transform" className="mt-4 space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Basic</h4>
                {renderStyleButtons('basic', 'bg-blue-500 hover:bg-blue-600')}
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Special Effects</h4>
                {renderStyleButtons('special', 'bg-indigo-500 hover:bg-indigo-600')}
              </div>
            </TabsContent>
            <TabsContent value="decorate" className="mt-4 space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Fancy Fonts</h4>
                {renderStyleButtons('fancy', 'bg-purple-500 hover:bg-purple-600')}
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Decorative</h4>
                {renderStyleButtons('decorative', 'bg-pink-500 hover:bg-pink-600')}
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Mathematical</h4>
                {renderStyleButtons('mathematical', 'bg-emerald-500 hover:bg-emerald-600')}
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Symbols</h4>
                {renderStyleButtons('symbols', 'bg-orange-500 hover:bg-orange-600')}
              </div>
            </TabsContent>
            <TabsContent value="analyze" className="mt-4">
              <div className="flex flex-wrap gap-2 pt-2">
                <Button
                  onClick={() => handleSentimentAnalysis()}
                  disabled={!text || isAnalyzing}
                  className={cn(
                    "rounded-full transition-all transform hover:scale-105",
                    "bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white shadow-md gap-2",
                    "disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  )}
                >
                  {isAnalyzing ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Brain className="h-4 w-4" />
                  )}
                  Analyze Sentiment
                </Button>

                <Button
                  onClick={() => {
                    analyzeWordFrequency();
                    setShowWordFrequency(!showWordFrequency);
                  }}
                  disabled={!text}
                  className={cn(
                    "rounded-full transition-all transform hover:scale-105",
                    "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-md gap-2",
                    "disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  )}
                >
                  <BarChart2 className="h-4 w-4" />
                  Word Frequency
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Right Column - Output */}
      <div className="p-6 bg-gray-50 dark:bg-gray-900">
        <div className="space-y-4">
          {/* Output Area */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Converted Text</h3>
              {convertedText && (
                <div className="flex gap-2">
                  <Button
                    onClick={() => copyToClipboard(convertedText)}
                    size="sm"
                    variant="outline"
                    className="rounded-full transition-all transform hover:scale-105 hover:bg-blue-50 dark:hover:bg-blue-900 border-blue-200 dark:border-blue-800 gap-2"
                  >
                    <Copy className="h-4 w-4" />
                    Copy
                  </Button>
                  <Button
                    onClick={() => downloadText()}
                    size="sm"
                    variant="outline"
                    className="rounded-full transition-all transform hover:scale-105 hover:bg-blue-50 dark:hover:bg-blue-900 border-blue-200 dark:border-blue-800 gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download
                  </Button>
                </div>
              )}
            </div>
            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg min-h-[200px] font-mono whitespace-pre-wrap border border-gray-200 dark:border-gray-700 shadow-sm">
              {convertedText || (
                <span className="text-gray-400 dark:text-gray-500">
                  Transformed text will appear here...
                </span>
              )}
            </div>
          </div>

          {/* Sentiment Analysis Results */}
          {sentimentResult && (
            <Card className="border-gray-200 dark:border-gray-700">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/50 dark:to-purple-900/50">
                <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-gray-100">
                  <Brain className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  Sentiment Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-700 dark:text-gray-300">Overall Sentiment: {sentimentResult.score >= 0 ? 'Positive' : 'Negative'}</span>
                    <span className="text-gray-700 dark:text-gray-300">{Math.abs(sentimentResult.score * 100)}%</span>
                  </div>
                  <Progress value={Math.abs(sentimentResult.score * 100)} className={cn(
                    sentimentResult.score >= 0 ? "bg-green-100 dark:bg-green-900" : "bg-red-100 dark:bg-red-900",
                    "[&>div]:bg-green-500 dark:[&>div]:bg-green-400"
                  )} />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(sentimentResult.emotions).map(([emotion, value]) => (
                    <div key={emotion}>
                      <div className="flex justify-between mb-1">
                        <span className="capitalize text-gray-700 dark:text-gray-300">{emotion}</span>
                        <span className="text-gray-700 dark:text-gray-300">{Math.round(value * 100)}%</span>
                      </div>
                      <Progress
                        value={value * 100}
                        className="bg-blue-100 dark:bg-blue-900 [&>div]:bg-blue-500 dark:[&>div]:bg-blue-400"
                      />
                    </div>
                  ))}
                </div>

                {sentimentResult.explanation && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-4">
                    {sentimentResult.explanation}
                  </p>
                )}
              </CardContent>
            </Card>
          )}

          {/* Word Frequency Analysis */}
          {showWordFrequency && text && (
            <Card className="border-gray-200 dark:border-gray-700">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/50 dark:to-pink-900/50">
                <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-gray-100">
                  <BarChart2 className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  Word Frequency Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="space-y-2">
                  {wordFrequency
                    .slice(0, 10)
                    .map(({ word, count }) => (
                      <div key={word} className="flex justify-between items-center">
                        <span className="font-mono text-gray-700 dark:text-gray-300">{word}</span>
                        <Badge variant="secondary" className="bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300">
                          {count}
                        </Badge>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <div
          ref={emojiPickerRef}
          className="absolute z-50 shadow-lg rounded-lg"
          style={{
            top: emojiButtonRef.current
              ? emojiButtonRef.current.getBoundingClientRect().bottom + window.scrollY + 5
              : 0,
            left: emojiButtonRef.current
              ? emojiButtonRef.current.getBoundingClientRect().left
              : 0,
          }}
        >
          <EmojiPicker
            theme={currentTheme as Theme}
            onEmojiClick={handleEmojiClick}
          />
        </div>
      )}
    </div>
  );
}
