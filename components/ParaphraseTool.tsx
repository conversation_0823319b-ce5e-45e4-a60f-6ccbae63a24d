'use client';

import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

const WORD_LIMIT = 1000; // Increased from 100 to 1000 words
const COOLDOWN_TIME = 10000; // 10 seconds in milliseconds

interface ParaphraseResponse {
  paraphrasedText: string;
}

async function paraphraseText(text: string, tone: string): Promise<ParaphraseResponse> {
  const response = await fetch('/api/paraphrase', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ text, tone }),
  });

  if (!response.ok) {
    throw new Error('Failed to paraphrase text');
  }

  return response.json();
}

export function ParaphraseTool() {
  const [input, setInput] = useState('');
  const [tone, setTone] = useState('formal');
  const [output, setOutput] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const [outputWordCount, setOutputWordCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [lastRequestTime, setLastRequestTime] = useState(0);
  const [cooldownRemaining, setCooldownRemaining] = useState(0);
  const [lastParaphrasedTime, setLastParaphrasedTime] = useState<Date | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const words = input.trim().split(/\s+/);
    setWordCount(input.trim() ? words.length : 0);
  }, [input]);

  useEffect(() => {
    const words = output.trim().split(/\s+/);
    setOutputWordCount(output.trim() ? words.length : 0);
  }, [output]);

  // Cooldown timer
  useEffect(() => {
    if (cooldownRemaining > 0) {
      intervalRef.current = setInterval(() => {
        const remaining = Math.max(0, COOLDOWN_TIME - (Date.now() - lastRequestTime));
        setCooldownRemaining(remaining);
        if (remaining === 0) {
          clearInterval(intervalRef.current);
        }
      }, 100);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [lastRequestTime, cooldownRemaining]);

  const handleSubmit = async () => {
    if (!input.trim()) {
      toast.error('Please enter some text to paraphrase');
      return;
    }

    if (wordCount > WORD_LIMIT) {
      toast.error(`Text exceeds ${WORD_LIMIT} words limit`);
      return;
    }

    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < COOLDOWN_TIME) {
      const remainingTime = Math.ceil((COOLDOWN_TIME - timeSinceLastRequest) / 1000);
      toast.error(`Please wait ${remainingTime} seconds before making another request`);
      setCooldownRemaining(COOLDOWN_TIME - timeSinceLastRequest);
      return;
    }

    try {
      setIsLoading(true);
      const result = await paraphraseText(input, tone);
      setOutput(result.paraphrasedText);
      setLastRequestTime(now);
      setLastParaphrasedTime(new Date());
      setCooldownRemaining(COOLDOWN_TIME);
    } catch (error) {
      toast.error('Failed to paraphrase text. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  return (
    <div className="grid gap-6">
      {/* Input Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <label htmlFor="input" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Your Text
          </label>
          <span className={`text-sm ${wordCount > WORD_LIMIT ? 'text-red-500' : 'text-gray-500'}`}>
            {wordCount}/{WORD_LIMIT} words
          </span>
        </div>
        <div className="relative">
          <Textarea
            id="input"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Enter your text here (max 1000 words)"
            className="min-h-[150px] transition-all duration-200 focus:shadow-lg resize-y"
          />
          <div className="absolute bottom-2 right-2 flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCopy(input)}
              className="text-gray-500 hover:text-gray-700"
            >
              Copy
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setInput('')}
              className="text-gray-500 hover:text-gray-700"
            >
              Clear
            </Button>
          </div>
        </div>
      </div>

      {/* Tone Selection and Status */}
      <div className="flex flex-col sm:flex-row gap-4 items-center">
        <div className="w-full sm:w-64">
          <Select value={tone} onValueChange={setTone}>
            <SelectTrigger>
              <SelectValue placeholder="Select tone" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="formal">Formal</SelectItem>
              <SelectItem value="informal">Informal</SelectItem>
              <SelectItem value="creative">Creative</SelectItem>
              <SelectItem value="professional">Professional</SelectItem>
              <SelectItem value="simple">Simple & Clear</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex-1 flex items-center gap-4">
          <Button 
            onClick={handleSubmit} 
            disabled={isLoading || !input.trim() || wordCount > WORD_LIMIT || cooldownRemaining > 0}
            className="w-full sm:w-auto min-w-[120px] bg-gradient-to-r from-[#125C88] via-[#1a7ab8] to-[#125C88] hover:from-[#0d4666] hover:via-[#125C88] hover:to-[#0d4666] text-white transition-all duration-300 shadow-md hover:shadow-lg"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>Processing...</span>
              </div>
            ) : cooldownRemaining > 0 ? (
              `Wait ${Math.ceil(cooldownRemaining / 1000)}s`
            ) : (
              'Paraphrase'
            )}
          </Button>
        </div>
      </div>

      {/* Output Section */}
      {output && (
        <div className="space-y-4 animate-fadeIn">
          <div className="flex items-center justify-between">
            <label htmlFor="output" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Paraphrased Text
            </label>
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-500">{outputWordCount} words</span>
              {lastParaphrasedTime && (
                <span className="text-sm text-gray-500">
                  Paraphrased at: {lastParaphrasedTime.toLocaleTimeString()}
                </span>
              )}
            </div>
          </div>
          <div className="relative">
            <Textarea
              id="output"
              value={output}
              readOnly
              className="min-h-[150px] bg-gray-50 dark:bg-gray-700 resize-y"
            />
            <div className="absolute bottom-2 right-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleCopy(output)}
                className="text-gray-500 hover:text-gray-700"
              >
                Copy
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}