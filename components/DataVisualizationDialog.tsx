'use client';

import React, { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PieController,
  ArcElement,
  LineElement,
  PointElement,
} from 'chart.js';
import { Bar, Pie, Line } from 'react-chartjs-2';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, BarChart } from "lucide-react";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PieController,
  ArcElement,
  LineElement,
  PointElement
);

interface DataVisualizationDialogProps {
  data: any[];
  columns: string[];
}

export const DataVisualizationDialog: React.FC<DataVisualizationDialogProps> = ({ data, columns }) => {
  const [selectedChart, setSelectedChart] = useState<'bar' | 'pie' | 'line'>('bar');
  const [selectedColumn, setSelectedColumn] = useState<string>(columns[0] || '');
  const [selectedValueColumn, setSelectedValueColumn] = useState<string>('');
  const [aggregationType, setAggregationType] = useState<'count' | 'sum' | 'average'>('count');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [maxItems, setMaxItems] = useState<number>(15);
  const [open, setOpen] = useState(false);

  const isNumeric = (value: any) => !isNaN(value) && !isNaN(parseFloat(value));

  const processDataForVisualization = () => {
    if (!data || !selectedColumn) return null;

    // Initialize data structure based on aggregation type
    const aggregation: { [key: string]: { count: number; sum: number } } = {};
    data.forEach(row => {
      const key = String(row[selectedColumn]).trim();
      const value = selectedValueColumn ? parseFloat(row[selectedValueColumn]) : 1;
      
      if (key && (!selectedValueColumn || !isNaN(value))) {
        if (!aggregation[key]) {
          aggregation[key] = { count: 0, sum: 0 };
        }
        aggregation[key].count += 1;
        aggregation[key].sum += selectedValueColumn ? value : 0;
      }
    });

    // Calculate final values based on aggregation type
    const processedData = Object.entries(aggregation).map(([key, values]) => ({
      key,
      value: aggregationType === 'count' ? values.count :
             aggregationType === 'sum' ? values.sum :
             values.sum / values.count // average
    }));

    // Sort data
    processedData.sort((a, b) => {
      const comparison = sortOrder === 'asc' ? a.value - b.value : b.value - a.value;
      return comparison;
    });

    // Limit to maxItems
    const limitedData = processedData.slice(0, maxItems);

    const colors = [
      'rgba(54, 162, 235, 0.5)',
      'rgba(255, 99, 132, 0.5)',
      'rgba(75, 192, 192, 0.5)',
      'rgba(255, 206, 86, 0.5)',
      'rgba(153, 102, 255, 0.5)',
      'rgba(255, 159, 64, 0.5)',
      'rgba(201, 203, 207, 0.5)',
      'rgba(255, 99, 132, 0.5)',
    ];

    return {
      labels: limitedData.map(item => item.key),
      datasets: [{
        label: `${selectedColumn} ${aggregationType} ${selectedValueColumn ? `of ${selectedValueColumn}` : ''}`,
        data: limitedData.map(item => item.value),
        backgroundColor: limitedData.map((_, i) => colors[i % colors.length]),
        borderColor: limitedData.map((_, i) => colors[i % colors.length].replace('0.5', '1')),
        borderWidth: 1,
      }]
    };
  };

  const getColumnStats = () => {
    if (!data || !selectedColumn) return null;

    const values = data.map(row => row[selectedColumn]).filter(Boolean);
    const uniqueValues = new Set(values);
    const numericValues = values.filter(isNumeric).map(Number);

    return {
      totalValues: values.length,
      uniqueValues: uniqueValues.size,
      mostCommon: Object.entries(
        values.reduce((acc: any, val) => {
          acc[val] = (acc[val] || 0) + 1;
          return acc;
        }, {})
      )
        .sort(([, a], [, b]) => (b as number) - (a as number))
        .slice(0, 3)
        .map(([val, count]) => `${val} (${count})`),
      ...(numericValues.length > 0 && {
        average: (numericValues.reduce((a, b) => a + b, 0) / numericValues.length).toFixed(2),
        min: Math.min(...numericValues).toFixed(2),
        max: Math.max(...numericValues).toFixed(2),
      }),
    };
  };

  // Get numeric columns for value selection
  const numericColumns = columns.filter(col => 
    data.some(row => isNumeric(row[col]))
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <BarChart className="h-4 w-4" />
          Visualize
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="sticky top-0 bg-white dark:bg-gray-900 z-10 pb-4 border-b">
          <DialogTitle>Data Visualization Dashboard</DialogTitle>
          <DialogDescription>
            Analyze and visualize your data with different chart types
          </DialogDescription>
        </DialogHeader>

        {/* User Guide */}
        <div className="space-y-4 mt-4">
          <Card className="bg-blue-50 dark:bg-blue-950 p-4">
            <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">📊 Quick Guide</h3>
            <ul className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
              <li>1. Select a <span className="font-medium">Category</span> column to group your data</li>
              <li>2. Choose a <span className="font-medium">Value</span> column for numeric analysis (optional)</li>
              <li>3. Pick an <span className="font-medium">Aggregation</span> method:
                <ul className="ml-4 mt-1 space-y-1">
                  <li>• Count: Number of occurrences</li>
                  <li>• Sum: Total of numeric values</li>
                  <li>• Average: Mean of numeric values</li>
                </ul>
              </li>
              <li>4. Select your preferred <span className="font-medium">Chart Type</span></li>
              <li>5. Use <span className="font-medium">Sort</span> and <span className="font-medium">Max items</span> to refine the display</li>
            </ul>
          </Card>

          <div className="grid grid-cols-[2fr,1fr] gap-4">
            <Card className="p-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800">
              <div className="flex flex-wrap gap-4 mb-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-[200px] justify-between bg-white dark:bg-gray-900">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-indigo-500"></div>
                        Category: {selectedColumn}
                      </div>
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="max-h-[300px] overflow-y-auto">
                    {columns.map((column) => (
                      <DropdownMenuItem
                        key={column}
                        onClick={() => setSelectedColumn(column)}
                        className="flex items-center gap-2"
                      >
                        <div className="w-2 h-2 rounded-full bg-indigo-500"></div>
                        {column}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {numericColumns.length > 0 && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-[200px] justify-between bg-white dark:bg-gray-900">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-emerald-500"></div>
                          Value: {selectedValueColumn || 'Count'}
                        </div>
                        <ChevronDown className="h-4 w-4 opacity-50" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="max-h-[300px] overflow-y-auto">
                      <DropdownMenuItem onClick={() => setSelectedValueColumn('')} className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                        Count
                      </DropdownMenuItem>
                      {numericColumns.map((column) => (
                        <DropdownMenuItem
                          key={column}
                          onClick={() => setSelectedValueColumn(column)}
                          className="flex items-center gap-2"
                        >
                          <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                          {column}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-[150px] justify-between bg-white dark:bg-gray-900">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                        {aggregationType}
                      </div>
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setAggregationType('count')} className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-amber-500"></div>
                      count
                    </DropdownMenuItem>
                    {selectedValueColumn && (
                      <>
                        <DropdownMenuItem onClick={() => setAggregationType('sum')} className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-amber-500"></div>
                          sum
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setAggregationType('average')} className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-amber-500"></div>
                          average
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>

                <div className="flex gap-2">
                  {['bar', 'pie', 'line'].map((chartType) => (
                    <Button
                      key={chartType}
                      variant={selectedChart === chartType ? 'default' : 'outline'}
                      onClick={() => setSelectedChart(chartType as 'bar' | 'pie' | 'line')}
                      className={`capitalize ${
                        selectedChart === chartType 
                          ? 'bg-indigo-500 text-white hover:bg-indigo-600'
                          : 'bg-white dark:bg-gray-900'
                      }`}
                    >
                      {chartType}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="flex gap-4 mb-4 items-center">
                <Button
                  variant="outline"
                  onClick={() => setSortOrder(order => order === 'asc' ? 'desc' : 'asc')}
                  className="bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  Sort {sortOrder === 'asc' ? '↑' : '↓'}
                </Button>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Max items:</span>
                  <input
                    type="number"
                    value={maxItems}
                    onChange={(e) => setMaxItems(Math.max(1, parseInt(e.target.value) || 1))}
                    className="w-20 px-2 py-1 border rounded bg-white dark:bg-gray-900 dark:border-gray-700"
                    min="1"
                    max="100"
                  />
                </div>
              </div>

              <div className="h-[400px] relative bg-white dark:bg-gray-900 rounded-lg p-4 border border-gray-100 dark:border-gray-800">
                {processDataForVisualization() ? (
                  <>
                    {selectedChart === 'bar' && (
                      <Bar
                        data={processDataForVisualization()!}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false,
                            },
                            tooltip: {
                              backgroundColor: 'rgba(0, 0, 0, 0.8)',
                              titleColor: 'white',
                              bodyColor: 'white',
                              padding: 12,
                              cornerRadius: 8,
                            },
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              grid: {
                                color: 'rgba(0, 0, 0, 0.1)',
                              },
                            },
                            x: {
                              grid: {
                                display: false,
                              },
                            },
                          },
                        }}
                      />
                    )}
                    {selectedChart === 'pie' && (
                      <Pie
                        data={processDataForVisualization()!}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            tooltip: {
                              backgroundColor: 'rgba(0, 0, 0, 0.8)',
                              titleColor: 'white',
                              bodyColor: 'white',
                              padding: 12,
                              cornerRadius: 8,
                            },
                          },
                        }}
                      />
                    )}
                    {selectedChart === 'line' && (
                      <Line
                        data={processDataForVisualization()!}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false,
                            },
                            tooltip: {
                              backgroundColor: 'rgba(0, 0, 0, 0.8)',
                              titleColor: 'white',
                              bodyColor: 'white',
                              padding: 12,
                              cornerRadius: 8,
                            },
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              grid: {
                                color: 'rgba(0, 0, 0, 0.1)',
                              },
                            },
                            x: {
                              grid: {
                                display: false,
                              },
                            },
                          },
                        }}
                      />
                    )}
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    Select columns and options to visualize your data
                  </div>
                )}
              </div>
            </Card>

            <Card className="p-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800">
              <h3 className="font-medium mb-4 flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-indigo-500"></div>
                Statistics
              </h3>
              {getColumnStats() && (
                <div className="space-y-4">
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Values</p>
                    <p className="font-medium text-lg">{getColumnStats()?.totalValues}</p>
                  </div>
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Unique Values</p>
                    <p className="font-medium text-lg">{getColumnStats()?.uniqueValues}</p>
                  </div>
                  {getColumnStats()?.average !== undefined && (
                    <>
                      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <p className="text-sm text-gray-500 dark:text-gray-400">Average</p>
                        <p className="font-medium text-lg">{getColumnStats()?.average}</p>
                      </div>
                      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <p className="text-sm text-gray-500 dark:text-gray-400">Min / Max</p>
                        <p className="font-medium text-lg">{getColumnStats()?.min} / {getColumnStats()?.max}</p>
                      </div>
                    </>
                  )}
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Most Common</p>
                    <ul className="mt-2 space-y-1">
                      {getColumnStats()?.mostCommon.map((item, index) => (
                        <li key={index} className="font-medium flex items-center gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-indigo-500"></div>
                          {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DataVisualizationDialog;
