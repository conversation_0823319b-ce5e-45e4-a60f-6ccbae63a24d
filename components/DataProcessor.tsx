'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import * as XLSX from 'xlsx';
import Papa from 'papaparse';
import debounce from 'lodash/debounce';
import { 
  FileSpreadsheet,
  FileDown,
  FileJson,
  Search,
  Save,
  Pencil,
  ChevronLeft,
  ChevronRight,
  Database,
  Columns,
  Rows,
  FileType,
  File as FileIcon,
  ChevronDown,
  Trash2,
  BarChart,
  Undo2,
  X,
  Table2,
  Copy,
  Download,
  Upload,
  LucideLock,
  Check,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { DataVisualizationDialog } from './DataVisualizationDialog';
import { QueryBuilder } from "./QueryBuilder";
import { Query, QueryCondition, QueryGroup } from "./QueryBuilder/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog } from "@/components/ui/dialog";

interface TableData {
  headers: string[];
  rows: string[][]; // Changed to string[][]
}

interface SelectedCell {
  rowIndex: number;
  colIndex: number;
  columnName: string;
  value: string;  // Changed to just string since we'll convert numbers to strings for display
}

interface EditCell {
  row: number;
  col: number;
}

interface FileDetails {
  name: string;
  size: string;
  type: string;
  lastModified: string;
  columns: number;
}

export const DataProcessor: React.FC = () => {
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [undoHistory, setUndoHistory] = useState<TableData[]>([]);
  const [outputFormat, setOutputFormat] = useState<'csv' | 'json' | 'xlsx' | 'pdf'>('csv');
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Array<{
    rowIndex: number;
    matches: Array<{ colIndex: number; value: string }>;
  }>>([]);
  const [editingCell, setEditingCell] = useState<{ rowIndex: number; colIndex: number } | null>(null);
  const [editValue, setEditValue] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [fontSize, setFontSize] = useState<'xs' | 'sm' | 'base'>('sm');
  const [fileName, setFileName] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('fileName') || '';
    }
    return '';
  });
  const [isEditingFileName, setIsEditingFileName] = useState(false);
  const [editedFileName, setEditedFileName] = useState('');
  const [fileDetails, setFileDetails] = useState<FileDetails | null>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('fileDetails');
      return saved ? JSON.parse(saved) : null;
    }
    return null;
  });
  const [showSaveMessage, setShowSaveMessage] = useState(false);
  const [lastModified, setLastModified] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('lastModified') || new Date().toLocaleString();
    }
    return new Date().toLocaleString();
  });
  const [previousState, setPreviousState] = useState<TableData | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedCell | null>(null);
  const [activeQuery, setActiveQuery] = useState<Query | null>(null);
  const [filteredData, setFilteredData] = useState<string[][]>([]);
  const [filterColumn, setFilterColumn] = useState<string>('');
  const [filterType, setFilterType] = useState<'contains' | 'exact' | 'greater' | 'less'>('contains');
  const [filterValue, setFilterValue] = useState<string>('');
  const [sortColumns, setSortColumns] = useState<Array<{ column: string; direction: 'asc' | 'desc' }>>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isSortOpen, setIsSortOpen] = useState(false);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [editCell, setEditCell] = useState<EditCell | null>(null);
  const rowsPerPage = 20;

  const fontSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base'
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    const kb = bytes / 1024;
    if (kb < 1024) return kb.toFixed(2) + ' KB';
    const mb = kb / 1024;
    return mb.toFixed(2) + ' MB';
  };

  // Add Australian date formatter
  const formatDateAU = (date: Date): string => {
    return date.toLocaleString('en-AU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    // Set file details immediately when file is dropped
    const details: FileDetails = {
      name: file.name,
      size: formatFileSize(file.size),
      type: file.type || 'text/csv',
      lastModified: formatDateAU(new Date(file.lastModified)),
      columns: 0
    };
    setFileDetails(details);
    setFileName(file.name);
    
    // Save to localStorage
    localStorage.setItem('fileDetails', JSON.stringify(details));
    localStorage.setItem('fileName', file.name);

    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const binaryStr = event.target?.result;
        const workbook = XLSX.read(binaryStr, { type: 'binary' });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });

        if (data.length > 0) {
          const headers = (data[0] as any[]).map(header => header?.toString() || '');
          const rows = data.slice(1).map(row => 
            (row as any[]).map(cell => cell?.toString() || '')
          );

          const newTableData = {
            headers,
            rows
          };

          setTableData(newTableData);
          setFilteredData(rows);
          localStorage.setItem('tableData', JSON.stringify(newTableData));
        }
      } catch (error) {
        console.error('Error processing file:', error);
      }
    };

    reader.readAsBinaryString(file);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
    },
  });

  const handleExport = async (format: 'csv' | 'json' | 'xlsx' | 'pdf') => {
    if (!tableData) return;

    try {
      let content: string | Blob;
      const exportFileName = `${fileName || 'export'}_${new Date().toISOString().split('T')[0]}`;

      switch (format) {
        case 'csv':
          content = Papa.unparse({
            fields: tableData.headers,
            data: tableData.rows
          });
          downloadFile(content, `${exportFileName}.csv`);
          break;

        case 'json':
          const jsonData = tableData.rows.map(row =>
            Object.fromEntries(tableData.headers.map((header, i) => [header, row[i]]))
          );
          content = JSON.stringify(jsonData, null, 2);
          downloadFile(content, `${exportFileName}.json`);
          break;

        case 'xlsx':
          const ws = XLSX.utils.aoa_to_sheet([tableData.headers, ...tableData.rows]);
          const wb = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
          const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
          content = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          downloadFile(content, `${exportFileName}.xlsx`);
          break;

        case 'pdf':
          // Create a temporary table element
          const table = document.createElement('table');
          const headerRow = document.createElement('tr');
          tableData.headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header;
            headerRow.appendChild(th);
          });
          table.appendChild(headerRow);

          tableData.rows.forEach(row => {
            const tr = document.createElement('tr');
            row.forEach(cell => {
              const td = document.createElement('td');
              td.textContent = cell?.toString() ?? '';  // Convert to string safely
              tr.appendChild(td);
            });
            table.appendChild(tr);
          });

          const style = `
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; }
          `;

          const html = `
            <html>
              <head>
                <style>${style}</style>
              </head>
              <body>
                ${table.outerHTML}
              </body>
            </html>
          `;

          const printWindow = window.open('', '_blank');
          if (printWindow) {
            printWindow.document.write(html);
            printWindow.document.close();
            printWindow.print();
          }
          return;
      }
    } catch (error) {
      console.error('Error exporting file:', error);
    }
  };

  const downloadFile = (content: string | Blob, filename: string) => {
    const blob = content instanceof Blob ? content : new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      if (!tableData) return;
      
      const searchValue = value.toLowerCase();
      if (!searchValue) {
        setSearchResults([]);
        return;
      }

      const results: { rowIndex: number; matches: { colIndex: number; value: string }[] }[] = [];

      tableData.rows.forEach((row, rowIndex) => {
        const matches: { colIndex: number; value: string }[] = [];
        row.forEach((cell, colIndex) => {
          if (cell.toString().toLowerCase().includes(searchValue)) {
            matches.push({ colIndex, value: cell.toString() });
          }
        });
        if (matches.length > 0) {
          results.push({ rowIndex, matches });
        }
      });

      setSearchResults(results);
    }, 300),
    [tableData]
  );

  // Evaluate query condition
  const evaluateCondition = (row: string[], condition: QueryCondition | QueryGroup, headers: string[]): boolean => {
    if ('operator' in condition && 'conditions' in condition) {
      // It's a group
      const results = condition.conditions.map(c => evaluateCondition(row, c, headers));
      return condition.operator === 'AND' 
        ? results.every(Boolean)
        : results.some(Boolean);
    }

    // It's a single condition
    const value = row[headers.indexOf(condition.column)];
    const compareValue = condition.value;

    switch (condition.operator) {
      case 'equals':
        return String(value).toLowerCase() === String(compareValue).toLowerCase();
      case 'contains':
        return String(value).toLowerCase().includes(String(compareValue).toLowerCase());
      case 'greater':
        return Number(value) > Number(compareValue);
      case 'less':
        return Number(value) < Number(compareValue);
      default:
        return false;
    }
  };

  useEffect(() => {
    const savedData = localStorage.getItem('tableData');
    if (savedData) {
      const parsedData = JSON.parse(savedData);
      setTableData(parsedData);
      setFilteredData(parsedData.rows);
    }
  }, []);

  useEffect(() => {
    if (tableData) {
      localStorage.setItem('tableData', JSON.stringify(tableData));
    }
  }, [tableData]);

  useEffect(() => {
    if (!tableData) return;
    
    let filtered = [...tableData.rows];

    // Apply global search
    if (globalFilter) {
      const searchValue = globalFilter.toLowerCase();
      filtered = filtered.filter(row =>
        row.some(cell => 
          cell?.toString().toLowerCase().includes(searchValue)
        )
      );
    }

    // Apply column filter
    if (filterColumn && filterType && filterValue) {
      const columnIndex = tableData.headers.indexOf(filterColumn);
      if (columnIndex !== -1) {
        filtered = filtered.filter(row => {
          const cellValue = row[columnIndex]?.toString().toLowerCase() ?? '';
          const searchValue = filterValue.toLowerCase();
          
          switch (filterType) {
            case 'contains':
              return cellValue.includes(searchValue);
            case 'exact':
              return cellValue === searchValue;
            case 'greater': {
              const numCell = parseFloat(cellValue);
              const numSearch = parseFloat(searchValue);
              return !isNaN(numCell) && !isNaN(numSearch) && numCell > numSearch;
            }
            case 'less': {
              const numCell = parseFloat(cellValue);
              const numSearch = parseFloat(searchValue);
              return !isNaN(numCell) && !isNaN(numSearch) && numCell < numSearch;
            }
            default:
              return true;
          }
        });
      }
    }

    // Apply query filter if exists
    if (activeQuery && activeQuery.conditions.length > 0) {
      filtered = filtered.filter(row => 
        evaluateCondition(row, activeQuery, tableData.headers)
      );
    }

    setFilteredData(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [tableData, filterColumn, filterType, filterValue, globalFilter, activeQuery]);

  const filteredAndSortedRows = useMemo(() => {
    if (!tableData) return [];
    
    let result = [...filteredData];

    // Apply sorting
    if (sortColumns.length > 0) {
      result.sort((a, b) => {
        for (const sort of sortColumns) {
          const columnIndex = tableData.headers.indexOf(sort.column);
          if (columnIndex === -1) continue;
          
          const aVal = a[columnIndex].toString();
          const bVal = b[columnIndex].toString();
          
          // Try numeric comparison first
          const aNum = Number(aVal);
          const bNum = Number(bVal);
          
          if (!isNaN(aNum) && !isNaN(bNum)) {
            if (aNum !== bNum) {
              return sort.direction === 'asc' ? aNum - bNum : bNum - aNum;
            }
          } else {
            // Fall back to string comparison
            const comp = aVal.localeCompare(bVal);
            if (comp !== 0) {
              return sort.direction === 'asc' ? comp : -comp;
            }
          }
        }
        return 0;
      });
    }

    return result;
  }, [filteredData, tableData, sortColumns]);

  const currentPageRows = useMemo(() => {
    if (!filteredAndSortedRows) return [];
    const startIndex = (currentPage - 1) * rowsPerPage;
    return filteredAndSortedRows.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredAndSortedRows, currentPage, rowsPerPage]);

  // Enhanced search function with highlighting
  const handleSearch = useCallback((searchTerm: string) => {
    setGlobalFilter(searchTerm);
    if (!tableData) return;

    let filtered = [...tableData.rows];
    const results: Array<{
      rowIndex: number;
      matches: Array<{ colIndex: number; value: string }>;
    }> = [];
    
    if (searchTerm) {
      const searchValue = searchTerm.toLowerCase();
      filtered = filtered.filter((row, rowIndex) => {
        const matches = row.map((cell, colIndex) => {
          const cellValue = cell?.toString().toLowerCase() || '';
          return cellValue.includes(searchValue) 
            ? { colIndex, value: cell?.toString() || '' }
            : null;
        }).filter((match): match is { colIndex: number; value: string } => match !== null);

        if (matches.length > 0) {
          results.push({ rowIndex, matches });
          return true;
        }
        return false;
      });
    }

    setSearchResults(results);
    setFilteredData(filtered);
    setCurrentPage(1);
  }, [tableData]);

  // Highlight matches function
  const highlightMatches = useCallback((cell: string, rowIndex: number, colIndex: number): React.ReactNode => {
    if (!globalFilter) return cell;

    const searchValue = globalFilter.toLowerCase();
    const result = searchResults.find(r => r.rowIndex === rowIndex);
    const isMatch = result?.matches.some(m => m.colIndex === colIndex);

    if (!isMatch) return cell;

    const parts = cell.split(new RegExp(`(${searchValue})`, 'gi'));
    return (
      <>
        {parts.map((part, i) => (
          part.toLowerCase() === searchValue ? (
            <mark key={i} className="bg-yellow-200 dark:bg-yellow-800/50">{part}</mark>
          ) : (
            <span key={i}>{part}</span>
          )
        ))}
      </>
    );
  }, [globalFilter, searchResults]);

  const handleCellEdit = useCallback((rowIndex: number, columnIndex: number, value: string) => {
    if (!tableData) return;

    const newRows = [...tableData.rows];
    newRows[rowIndex] = [...newRows[rowIndex]];
    newRows[rowIndex][columnIndex] = value;

    const newTableData = {
      ...tableData,
      rows: newRows
    };

    setTableData(newTableData);
    setFilteredData(newRows);
    setHasUnsavedChanges(true);
  }, [tableData]);

  const handleCellDoubleClick = useCallback((rowIndex: number, columnIndex: number) => {
    setEditCell({ row: rowIndex, col: columnIndex });
  }, []);

  const handleCellEditComplete = useCallback((value: string) => {
    if (editCell) {
      handleCellEdit(editCell.row, editCell.col, value);
      setEditCell(null);
    }
  }, [editCell, handleCellEdit]);

  const handleCellSave = (globalRowIndex: number, colIndex: number): void => {
    if (!tableData || editValue === undefined) return;

    const newRows = [...tableData.rows];
    newRows[globalRowIndex] = [...newRows[globalRowIndex]];
    newRows[globalRowIndex][colIndex] = editValue;

    setTableData({
      ...tableData,
      rows: newRows
    });
    setEditingCell(null);
    setEditValue('');
    setHasUnsavedChanges(true);
  };

  const handleCellEditCancel = (): void => {
    setEditingCell(null);
    setEditValue('');
  };

  const handleCellKeyPress = (e: React.KeyboardEvent, globalRowIndex: number, colIndex: number): void => {
    if (e.key === 'Enter') {
      handleCellSave(globalRowIndex, colIndex);
    } else if (e.key === 'Escape') {
      handleCellEditCancel();
    }
  };

  const handleToggleEditMode = (): void => {
    setIsEditMode(!isEditMode);
    if (editingCell) {
      handleCellEditCancel();
    }
  };

  const handleSaveChanges = useCallback(() => {
    if (!tableData || !fileDetails) return;

    // Create CSV content to calculate size
    const csvContent = Papa.unparse({
      fields: tableData.headers,
      data: tableData.rows
    });

    // Calculate size in bytes and format it
    const sizeInBytes = new Blob([csvContent]).size;
    const formattedSize = formatFileSize(sizeInBytes);

    // Update file details with new timestamp and formatted size
    const newFileDetails: FileDetails = {
      ...fileDetails,
      lastModified: formatDateAU(new Date()),
      size: formattedSize
    };

    // Update state and localStorage
    setFileDetails(newFileDetails);
    setHasUnsavedChanges(false);
    localStorage.setItem('fileDetails', JSON.stringify(newFileDetails));
    localStorage.setItem('tableData', JSON.stringify(tableData));
  }, [tableData, fileDetails]);

  const handleClearData = useCallback(() => {
    setTableData(null);
    setFileName('');
    setFileDetails(null);
    setCurrentPage(1);
    setSearchTerm('');
    setSearchResults([]);
    setEditingCell(null);
    setEditValue('');
    setIsEditMode(false);
    setHasUnsavedChanges(false);
    localStorage.removeItem('tableData');
    localStorage.removeItem('fileName');
    localStorage.removeItem('fileDetails');
    localStorage.removeItem('searchState');
  }, []);

  const handleDeleteRow = (rowIndex: number): void => {
    if (!tableData || !isEditMode) return;
    
    const newRows = [...tableData.rows];
    newRows.splice(rowIndex, 1);
    
    setTableData({
      ...tableData,
      rows: newRows
    });
    setHasUnsavedChanges(true);
  };

  const setRowAsHeader = (rowIndex: number): void => {
    if (!tableData || !isEditMode) return;
    
    const newHeaders = [...tableData.rows[rowIndex]];
    const newRows = [...tableData.rows];
    
    // Remove the selected row
    newRows.splice(rowIndex, 1);
    
    // Insert current headers as a row at the position where new header was taken from
    if (tableData.headers.length > 0) {
      newRows.splice(rowIndex, 0, tableData.headers);
    }
    
    setTableData({
      headers: newHeaders,
      rows: newRows
    });
    setHasUnsavedChanges(true);
  };

  const unsetHeader = (): void => {
    if (!tableData || !isEditMode || !tableData.headers.length) return;
    
    // Add current headers as the first row
    const newRows = [tableData.headers, ...tableData.rows];
    
    setTableData({
      headers: [],
      rows: newRows
    });
    setHasUnsavedChanges(true);
  };

  const handleHeaderEdit = (colIndex: number, value: string): void => {
    if (!tableData || !isEditMode) return;
    
    const newHeaders = [...tableData.headers];
    newHeaders[colIndex] = value;
    
    setTableData({
      ...tableData,
      headers: newHeaders
    });
    setHasUnsavedChanges(true);
  };

  const handleFileNameEdit = (): void => {
    if (isEditingFileName) {
      setFileName(editedFileName);
      setIsEditingFileName(false);
    } else {
      setEditedFileName(fileName);
      setIsEditingFileName(true);
    }
  };

  const handleUndo = (): void => {
    if (undoHistory.length === 0) return;
    
    const previousState = undoHistory[undoHistory.length - 1];
    setTableData(previousState);
    setUndoHistory(prev => prev.slice(0, -1));
    setHasUnsavedChanges(true);
    setEditingCell(null);
    setEditValue('');
  };

  useEffect(() => {
    if (tableData) {
      localStorage.setItem('tableData', JSON.stringify(tableData));
    }
  }, [tableData]);

  useEffect(() => {
    if (fileName) {
      localStorage.setItem('fileName', fileName);
    }
  }, [fileName]);

  useEffect(() => {
    if (fileDetails) {
      localStorage.setItem('fileDetails', JSON.stringify(fileDetails));
    }
  }, [fileDetails]);

  useEffect(() => {
    const searchState = {
      globalFilter,
      filterColumn,
      filterType,
      filterValue,
      activeQuery,
      currentPage,
      sortColumns
    };
    localStorage.setItem('searchState', JSON.stringify(searchState));
  }, [globalFilter, filterColumn, filterType, filterValue, activeQuery, currentPage, sortColumns]);

  useEffect(() => {
    const savedSearchState = localStorage.getItem('searchState');
    if (savedSearchState) {
      const state = JSON.parse(savedSearchState);
      setGlobalFilter(state.globalFilter || '');
      setFilterColumn(state.filterColumn || '');
      setFilterType(state.filterType || 'contains');
      setFilterValue(state.filterValue || '');
      setActiveQuery(state.activeQuery || null);
      setCurrentPage(state.currentPage || 1);
      setSortColumns(state.sortColumns || []);
    }
  }, []);

  const columns = useMemo(() => {
    if (!tableData) return [];
    return tableData.headers;
  }, [tableData]);

  const totalPages = Math.ceil(filteredAndSortedRows.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;

  const handlePageChange = (page: number): void => {
    setCurrentPage(page);
  };

  const handleSave = useCallback(() => {
    if (!tableData || !hasUnsavedChanges || !fileDetails) return;

    // Update last modified timestamp while preserving all required properties
    const newFileDetails: FileDetails = {
      ...fileDetails,
      lastModified: formatDateAU(new Date())
    };
    
    setFileDetails(newFileDetails);
    localStorage.setItem('tableData', JSON.stringify(tableData));
    localStorage.setItem('fileDetails', JSON.stringify(newFileDetails));
    setHasUnsavedChanges(false);
  }, [tableData, hasUnsavedChanges, fileDetails]);

  return (
    <div className="flex flex-col items-center w-full">
      <div className="w-full px-4 max-w-[95vw] mx-auto">
        {/* File Upload Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">CSV & Excel File Processor</h2>
            <p className="text-gray-600 dark:text-gray-400">
              Upload your CSV or Excel files to view, edit, and convert them to different formats
            </p>
            
            {/* Data Format Guidelines */}
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg text-left max-w-2xl mx-auto">
              <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">📋 Data Format Guidelines</h3>
              <ul className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <li>• Make sure your data has proper column headers in the first row</li>
                <li>• Each column should contain consistent data types (e.g., all numbers or all text)</li>
                <li>• Avoid empty cells or inconsistent formatting</li>
                <li>• For dates, use a consistent format (e.g., YYYY-MM-DD)</li>
                <li>• For numbers, use consistent decimal places</li>
                <li>• Recommended maximum: 1000 rows for optimal performance</li>
              </ul>
              <div className="mt-3 text-xs text-blue-600 dark:text-blue-400">
                Example format: Clean, consistent data will display and analyze better
              </div>
            </div>
          </div>
          
          <div
            {...getRootProps()}
            className={`mt-6 border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300 dark:border-gray-600'}
              hover:border-primary hover:bg-primary/5`}
          >
            <input {...getInputProps()} />
            <div className="space-y-4">
              <div className="flex justify-center">
                <FileSpreadsheet className="h-12 w-12 text-gray-400" />
              </div>
              <div>
                <p className="text-lg font-medium">Click to upload or drag and drop</p>
                <p className="text-sm text-gray-500">CSV, Excel files (CSV, XLSX, XLS)</p>
              </div>
            </div>
          </div>
        </div>

        {/* Table Section */}
        {tableData && (
          <div className="mt-8">
            {/* File Details Section */}
            <div className="bg-gradient-to-r from-blue-300 to-indigo-100 dark:from-blue-950/50 dark:to-indigo-950/50 rounded-lg shadow-sm mb-6">
              <div className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                      <FileSpreadsheet className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div className="flex items-center gap-2">
                      {isEditingFileName ? (
                        <div className="flex items-center gap-2">
                          <input
                            type="text"
                            value={editedFileName}
                            onChange={(e) => setEditedFileName(e.target.value)}
                            className="text-lg font-semibold text-gray-900 dark:text-gray-100 bg-transparent border-b border-gray-300 dark:border-gray-700 focus:outline-none focus:border-indigo-500 dark:focus:border-indigo-400"
                            autoFocus
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') handleFileNameEdit();
                              if (e.key === 'Escape') setIsEditingFileName(false);
                            }}
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleFileNameEdit}
                            className="p-1"
                          >
                            <Check className="h-4 w-4 text-green-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsEditingFileName(false)}
                            className="p-1"
                          >
                            <X className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{fileName}</h3>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleFileNameEdit}
                            className="p-1"
                          >
                            <Pencil className="h-4 w-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                          </Button>
                        </div>
                      )}
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Last modified: {fileDetails?.lastModified}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-6">
                    <div className="text-sm">
                      <div className="flex items-center space-x-2">
                        <Database className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-700 dark:text-gray-300">{fileDetails?.size}</span>
                      </div>
                    </div>
                    <div className="text-sm">
                      <div className="flex items-center space-x-2">
                        <Columns className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-700 dark:text-gray-300">
                          {tableData.headers.length} columns
                        </span>
                      </div>
                    </div>
                    <div className="text-sm">
                      <div className="flex items-center space-x-2">
                        <Rows className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-700 dark:text-gray-300">
                          {filteredData.length} rows
                        </span>
                      </div>
                    </div>
                    <div className="text-sm">
                      <div className="flex items-center space-x-2">
                        <FileType className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-700 dark:text-gray-300">
                          {fileDetails?.type.split('/')[1].toUpperCase()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Cell Information Display Bar */}
            <div className="sticky top-0 z-10 w-full bg-purple-200 dark:bg-purple-900/20 border border-gray-300 dark:border-purple-900 rounded-lg shadow-sm mb-4 overflow-visible">
              <div className="p-3">
                <div className="flex items-center gap-4">
                  {/* Cell Reference */}
                  <div className="flex items-center gap-2 min-w-[80px]">
                    <Table2 className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                    <span className="text-sm font-medium text-purple-700 dark:text-purple-300">
                      {selectedCell ? `${String.fromCharCode(65 + selectedCell.colIndex)}${selectedCell.rowIndex + 1}` : 'No cell selected'}
                    </span>
                  </div>

                  {selectedCell && (
                    <>
                      <div className="h-4 w-px bg-purple-200 dark:bg-purple-700"></div>
                      {/* Column Info */}
                      <div className="flex items-center gap-2">
                        <Columns className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                        <span className="text-sm text-purple-500 dark:text-purple-400">Col:</span>
                        <span className="text-sm font-medium text-purple-700 dark:text-purple-300">
                          {selectedCell.columnName} ({selectedCell.colIndex + 1})
                        </span>
                      </div>

                      <div className="h-4 w-px bg-purple-200 dark:bg-purple-700"></div>
                      {/* Row Info */}
                      <div className="flex items-center gap-2">
                        <Rows className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                        <span className="text-sm text-purple-500 dark:text-purple-400">Row:</span>
                        <span className="text-sm font-medium text-purple-700 dark:text-purple-300">
                          {selectedCell.rowIndex + 1}
                        </span>
                      </div>

                      <div className="h-4 w-px bg-purple-200 dark:bg-purple-700"></div>
                      {/* Cell Content */}
                      <div className="flex-1">
                        <div className="relative">
                          <input
                            type="text"
                            value={selectedCell.value}
                            onChange={(e) => {
                              if (isEditMode && selectedCell) {
                                const newValue = e.target.value;
                                setSelectedCell({
                                  ...selectedCell,
                                  value: newValue
                                });
                                handleCellEdit(selectedCell.rowIndex, selectedCell.colIndex, newValue);
                              }
                            }}
                            readOnly={!isEditMode}
                            className={`w-full px-3 py-1.5 bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-700 rounded text-sm ${
                              !isEditMode ? 'text-purple-600 dark:text-purple-400' : 'text-purple-900 dark:text-purple-100'
                            } focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-purple-500 dark:focus:border-purple-500`}
                            placeholder="Cell content"
                          />
                          {!isEditMode && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                              <X className="h-3.5 w-3.5 text-purple-400" />
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Controls Section */}
            <div className="flex flex-wrap items-center justify-between gap-4 mb-4">
              {/* Left side buttons */}
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleToggleEditMode}
                  className="flex items-center gap-1 px-2 h-8"
                >
                  {isEditMode ? (
                    <>
                      <X className="h-4 w-4" />
                      Cancel Edit
                    </>
                  ) : (
                    <>
                      <Pencil className="h-4 w-4" />
                      Edit
                    </>
                  )}
                </Button>

                {isEditMode && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleUndo}
                    disabled={undoHistory.length === 0}
                    className="flex items-center gap-1 px-2 h-8 text-gray-600 hover:text-gray-700"
                  >
                    <Undo2 className="h-4 w-4" />
                    Undo
                  </Button>
                )}

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1 px-2 h-8"
                    >
                      <FileType className="h-4 w-4" />
                      {fontSize === 'xs' ? 'S' : fontSize === 'sm' ? 'M' : 'L'}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setFontSize('xs')}>Small</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFontSize('sm')}>Medium</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFontSize('base')}>Large</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSave}
                  className="flex items-center gap-1 px-2 h-8 text-green-600 hover:text-green-700"
                  disabled={!hasUnsavedChanges}
                >
                  <Save className="h-4 w-4" />
                  Save
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearData}
                  className="flex items-center gap-1 px-2 h-8 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear
                </Button>
              </div>

              {/* Right side buttons */}
              <div className="flex items-center gap-2">
                {/* Export Button */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1 px-2 h-8 bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800"
                    >
                      <FileDown className="h-4 w-4" />
                      Export
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleExport('csv')}>
                      <FileDown className="mr-2 h-4 w-4" />
                      <span>Export as CSV</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExport('json')}>
                      <FileJson className="mr-2 h-4 w-4" />
                      <span>Export as JSON</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExport('xlsx')}>
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      <span>Export as Excel</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExport('pdf')}>
                      <FileIcon className="mr-2 h-4 w-4" />
                      <span>Export as PDF</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Visualization Dialog */}
                <DataVisualizationDialog
                  data={filteredData.map((row, index) => {
                    const rowData: { [key: string]: string } = {};
                    tableData.headers.forEach((header, i) => {
                      rowData[header] = row[i];
                    });
                    return rowData;
                  })}
                  columns={tableData.headers}
                />
              </div>
            </div>

            {/* Search, Filter, and Sort Controls */}
            <div className="flex flex-wrap items-center gap-4 bg-white dark:bg-gray-950 p-4 rounded-lg shadow-sm mb-4">
              {/* Search Bar */}
              <div className="flex flex-wrap items-center gap-4">
                <div className="relative w-full sm:w-auto max-w-sm">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="text"
                    placeholder="Search across all columns..."
                    value={globalFilter ?? ''}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-8 border-gray-200 dark:border-gray-800 focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400"
                  />
                </div>
              </div>

              {/* Filter and Sort Controls */}
              <div className="flex items-center gap-4">
                <Select 
                  value={filterColumn} 
                  onValueChange={(value) => {
                    setFilterColumn(value);
                    // Reset filter value when column changes
                    setFilterValue('');
                  }}
                >
                  <SelectTrigger style={{ width: '150px' }}>
                    <SelectValue placeholder="Filter by column" />
                  </SelectTrigger>
                  <SelectContent>
                    {tableData?.headers.map((header, index) => (
                      <SelectItem key={index} value={header || `column-${index}`}>
                        {header || `Column ${index + 1}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select 
                  value={filterType} 
                  onValueChange={(value: any) => {
                    setFilterType(value);
                    // Reset filter value when type changes
                    setFilterValue('');
                  }}
                >
                  <SelectTrigger style={{ width: '120px' }}>
                    <SelectValue placeholder="Contains" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="contains">Contains</SelectItem>
                    <SelectItem value="exact">Exact match</SelectItem>
                    <SelectItem value="greater">Greater than</SelectItem>
                    <SelectItem value="less">Less than</SelectItem>
                  </SelectContent>
                </Select>

                <Input
                  type="text"
                  placeholder="Filter value..."
                  value={filterValue}
                  onChange={(e) => setFilterValue(e.target.value)}
                  className="w-[120px]"
                />

                {/* Advanced Query Builder */}
                <div className="flex-1 flex justify-end gap-2">
                  <QueryBuilder 
                    columns={tableData?.headers.map(col => ({
                      id: col,
                      value: col
                    })) || []}
                    onQueryChange={(query) => {
                      setActiveQuery(query);
                    }}
                    activeRows={filteredData?.length || 0}
                    totalRows={tableData?.rows.length || 0}
                    className="w-[140px]"
                  />

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="flex items-center gap-2 min-w-[100px] bg-white dark:bg-gray-900"
                      >
                        <ArrowUpDown className="h-4 w-4" />
                        <span>{sortColumns.length ? `Sorted (${sortColumns.length})` : 'Sort'}</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[200px]">
                      <DropdownMenuLabel className="font-semibold">Sort by columns</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {tableData?.headers.map((header, index) => {
                        const sortInfo = sortColumns.find(s => s.column === header);
                        return (
                          <DropdownMenuItem
                            key={index}
                            onClick={() => {
                              setSortColumns(prev => {
                                const existing = prev.find(s => s.column === header);
                                if (existing) {
                                  if (existing.direction === 'desc') {
                                    // Remove if already desc
                                    return prev.filter(s => s.column !== header);
                                  }
                                  // Toggle direction
                                  return prev.map(s => 
                                    s.column === header 
                                      ? { ...s, direction: 'desc' }
                                      : s
                                  );
                                }
                                // Add new sort column
                                return [...prev, { column: header, direction: 'asc' }];
                              });
                            }}
                            className="flex items-center justify-between py-2"
                          >
                            <span className="flex items-center gap-2">
                              {sortInfo && (
                                <Badge variant="secondary" className="h-5 w-5 p-0 flex items-center justify-center">
                                  {sortColumns.findIndex(s => s.column === header) + 1}
                                </Badge>
                              )}
                              {header}
                            </span>
                            <span className="flex items-center">
                              {sortInfo?.direction === 'asc' && (
                                <ArrowUp className="h-4 w-4 text-green-500" />
                              )}
                              {sortInfo?.direction === 'desc' && (
                                <ArrowDown className="h-4 w-4 text-red-500" />
                              )}
                            </span>
                          </DropdownMenuItem>
                        );
                      })}
                      {sortColumns.length > 0 && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => setSortColumns([])}
                            className="text-red-500 focus:text-red-500"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Clear sorting
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            {/* Table Container */}
            <div className="overflow-x-auto">
              <table className={`w-full border-collapse bg-white dark:bg-gray-900 ${fontSizeClasses[fontSize]}`}>
                <thead>
                  <tr className="bg-blue-50 dark:bg-blue-900/20">
                    {isEditMode && (
                      <th className="w-24 border border-gray-200 dark:border-gray-700 px-4 py-2 text-gray-600 dark:text-gray-300 text-left">
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={unsetHeader}
                            className="h-8 px-2 text-xs whitespace-nowrap bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/40"
                          >
                            Unset Header
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteRow(0)}
                            className="h-8 w-8 p-0"
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </th>
                    )}
                    {tableData.headers.map((header, index) => (
                      <th
                        key={index}
                        className="border border-gray-200 dark:border-gray-700 px-4 py-2 text-gray-600 dark:text-gray-300 text-left"
                      >
                        {isEditMode ? (
                          <input
                            type="text"
                            value={header}
                            onChange={(e) => handleHeaderEdit(index, e.target.value)}
                            className="w-full bg-blue-50 dark:bg-blue-900/20 border rounded px-2 py-1"
                          />
                        ) : (
                          header
                        )}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {currentPageRows.map((row, rowIndex) => {
                    const actualRowIndex = (currentPage - 1) * rowsPerPage + rowIndex;
                    return (
                      <tr
                        key={rowIndex}
                        className={`
                          ${rowIndex % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800/50'}
                          hover:bg-blue-50 dark:hover:bg-blue-900/20
                        `}
                      >
                        {isEditMode && (
                          <td className="border border-gray-200 dark:border-gray-700 px-4 py-2">
                            <div className="flex gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteRow(actualRowIndex)}
                                className="h-8 w-8 p-0"
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setRowAsHeader(actualRowIndex)}
                                className="h-8 px-2 text-xs whitespace-nowrap bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/40"
                              >
                                Set as Header
                              </Button>
                            </div>
                          </td>
                        )}
                        {row.map((cell: string, colIndex: number) => (
                          <td
                            key={colIndex}
                            onClick={() => {
                              setSelectedCell({
                                rowIndex: actualRowIndex,
                                colIndex,
                                columnName: tableData?.headers[colIndex] || '',
                                value: cell
                              });
                            }}
                            className={`
                              border border-gray-200 dark:border-gray-700 px-4 py-2 text-gray-600 dark:text-gray-300
                              cursor-pointer
                              ${selectedCell?.rowIndex === actualRowIndex && selectedCell?.colIndex === colIndex ? 'bg-purple-50 dark:bg-purple-900/30' : ''}
                              ${isEditMode ? 'p-0' : ''}
                            `}
                          >
                            {isEditMode ? (
                              <input
                                type="text"
                                value={cell}
                                onChange={(e) => {
                                  const newValue = e.target.value;
                                  handleCellEdit(actualRowIndex, colIndex, newValue);
                                  if (selectedCell?.rowIndex === actualRowIndex && selectedCell?.colIndex === colIndex) {
                                    setSelectedCell(prev => ({
                                      ...prev!,
                                      value: newValue
                                    }));
                                  }
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedCell({
                                    rowIndex: actualRowIndex,
                                    colIndex,
                                    columnName: tableData?.headers[colIndex] || '',
                                    value: cell
                                  });
                                }}
                                className="w-full h-full px-4 py-2 bg-transparent focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400"
                              />
                            ) : (
                              <span className="block w-full truncate">
                                {highlightMatches(
                                  cell,
                                  actualRowIndex,
                                  colIndex
                                )}
                              </span>
                            )}
                          </td>
                        ))}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {filteredAndSortedRows.length > rowsPerPage && (
              <div className="flex flex-col items-center gap-2 bg-white dark:bg-gray-950 p-4 rounded-lg shadow-sm">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                        disabled={currentPage === 1}
                        className="border-gray-200 dark:border-gray-800"
                      >
                        <ChevronLeft className="h-4 w-4" />
                        <span>Previous</span>
                      </Button>
                    </PaginationItem>
                    <PaginationItem>
                      <div className="px-4 font-medium text-gray-700 dark:text-gray-300">
                        Page {currentPage} of {Math.ceil(filteredAndSortedRows.length / rowsPerPage)}
                      </div>
                    </PaginationItem>
                    <PaginationItem>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(p => Math.min(Math.ceil(filteredAndSortedRows.length / rowsPerPage), p + 1))}
                        disabled={currentPage >= Math.ceil(filteredAndSortedRows.length / rowsPerPage)}
                        className="border-gray-200 dark:border-gray-800"
                      >
                        <span>Next</span>
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Showing rows {(currentPage - 1) * rowsPerPage + 1}-{Math.min(currentPage * rowsPerPage, filteredAndSortedRows.length)} of {filteredAndSortedRows.length}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      <div className="flex justify-end mt-4">
        <Button
          variant="outline"
          className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
          onClick={handleSave}
        >
          <FileDown className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>
    </div>
  );
}

export default DataProcessor;
