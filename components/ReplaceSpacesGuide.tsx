'use client';

import {
  ArrowDownToLine,
  Clipboard,
  FileText,
  Settings2,
  Replace,
  Link2,
  Code,
  FileCode,
  Keyboard,
  CaseSensitive,
  ArrowRight
} from "lucide-react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export function ReplaceSpacesGuide() {
  return (
    <section className="container mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-[#1C6997] dark:text-[#1C6997] mb-2">
            Replace Spaces Guide
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Learn how to use our space replacement tool effectively
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Replace className="h-5 w-5 text-[#1C6997]" />
              <h3 className="font-semibold">Basic Usage</h3>
            </div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Paste your text with spaces</li>
              <li>Choose replacement character</li>
              <li>Preview changes instantly</li>
              <li>Copy result with one click</li>
            </ul>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Link2 className="h-5 w-5 text-[#1C6997]" />
              <h3 className="font-semibold">Common Uses</h3>
            </div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Create URL-friendly strings</li>
              <li>Format file names</li>
              <li>Generate slugs</li>
              <li>Clean text data</li>
            </ul>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <FileCode className="h-5 w-5 text-[#1C6997]" />
              <h3 className="font-semibold">Pro Tips</h3>
            </div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Use underscore for variables</li>
              <li>Use dash for URLs</li>
              <li>Remove spaces for hashtags</li>
              <li>Track character count</li>
            </ul>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Replace className="h-5 w-5 text-[#1C6997]" />
              <h3 className="font-semibold">Advanced Features</h3>
            </div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Multiple replacement options</li>
              <li>Case preservation</li>
              <li>Batch processing</li>
              <li>Real-time preview</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export function NewReplaceSpacesGuide() {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          How to Use Replace Spaces Tool
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Transform your text by replacing spaces with any character of your choice
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
          <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-4">
            <Replace className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2 text-blue-600 dark:text-blue-400">Basic Usage</h3>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
            <li>Paste your text in the input box</li>
            <li>Choose a replacement character</li>
            <li>See results instantly</li>
            <li>Copy with one click</li>
          </ul>
        </div>

        <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
          <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-4">
            <Link2 className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2 text-purple-600 dark:text-purple-400">Common Uses</h3>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
            <li>Create URL-friendly strings</li>
            <li>Format file names</li>
            <li>Generate slugs</li>
            <li>Clean text data</li>
          </ul>
        </div>

        <div className="flex flex-col items-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
          <div className="w-12 h-12 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-4">
            <FileCode className="w-6 h-6 text-pink-600 dark:text-pink-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2 text-pink-600 dark:text-pink-400">Pro Tips</h3>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300 text-center">
            <li>Use underscore for variables</li>
            <li>Use dash for URLs</li>
            <li>Remove spaces for hashtags</li>
            <li>Track stats in real-time</li>
          </ul>
        </div>
      </div>
    </div>
  );
}