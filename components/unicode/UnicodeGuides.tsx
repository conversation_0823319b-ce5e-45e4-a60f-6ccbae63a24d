'use client';

import React from 'react';
import { ArrowRight, HelpCircle, Info, Search, AlertTriangle, CheckCircle, Sparkles, Zap } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export function UnicodeGuides() {
  return (
    <div className="space-y-10">
      {/* How to Use Guide Section */}
      <Card className="shadow-lg border-indigo-200 dark:border-indigo-800 overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950/70 dark:to-purple-950/70 border-b border-indigo-100 dark:border-indigo-800/50 pb-6">
          <CardTitle className="text-xl text-indigo-700 dark:text-indigo-300 flex items-center gap-2">
            <Info className="h-5 w-5 text-indigo-500" />
            How to Use the Unicode Character Finder
          </CardTitle>
          <CardDescription className="text-indigo-600/80 dark:text-indigo-400/80 mt-1">
            Follow these simple steps to find and manage hidden characters in your text
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-0 divide-y md:divide-y-0 md:divide-x divide-indigo-100 dark:divide-indigo-800/30">
            {/* Step 1 */}
            <div className="p-6 relative">
              <div className="absolute top-5 left-5 flex items-center justify-center w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-800/50 text-indigo-600 dark:text-indigo-300 font-semibold text-lg">
                1
              </div>
              <div className="ml-12">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Paste Your Text</h3>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  Paste the text you want to analyze into the input field. This can be text from emails, documents, or AI-generated content from tools like ChatGPT.
                </p>
              </div>
            </div>
            
            {/* Step 2 */}
            <div className="p-6 relative">
              <div className="absolute top-5 left-5 flex items-center justify-center w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-800/50 text-indigo-600 dark:text-indigo-300 font-semibold text-lg">
                2
              </div>
              <div className="ml-12">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Find Hidden Characters</h3>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  Click the "Find Characters" button to detect and highlight all non-printable and special Unicode characters.
                </p>
              </div>
            </div>
            
            {/* Step 3 */}
            <div className="p-6 relative">
              <div className="absolute top-5 left-5 flex items-center justify-center w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-800/50 text-indigo-600 dark:text-indigo-300 font-semibold text-lg">
                3
              </div>
              <div className="ml-12">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Review the Results</h3>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  Examine the highlighted characters and their Unicode information, including code points and character names.
                </p>
              </div>
            </div>
            
            {/* Step 4 */}
            <div className="p-6 relative">
              <div className="absolute top-5 left-5 flex items-center justify-center w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-800/50 text-indigo-600 dark:text-indigo-300 font-semibold text-lg">
                4
              </div>
              <div className="ml-12">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Replace or Remove Characters</h3>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  Choose to replace specific characters or remove all non-printable characters from your text with a single click.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* FAQ Section */}
      <Card className="shadow-lg border-indigo-200 dark:border-indigo-800 overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950/70 dark:to-purple-950/70 border-b border-indigo-100 dark:border-indigo-800/50 pb-6">
          <CardTitle className="text-xl text-indigo-700 dark:text-indigo-300 flex items-center gap-2">
            <HelpCircle className="h-5 w-5 text-indigo-500" />
            Frequently Asked Questions
          </CardTitle>
          <CardDescription className="text-indigo-600/80 dark:text-indigo-400/80 mt-1">
            Common questions about Unicode characters and how to handle them
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <Accordion type="multiple" className="space-y-4">
            {/* FAQ Item 1 */}
            <AccordionItem value="item-1" className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
              <AccordionTrigger className="px-5 py-4 hover:no-underline bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 data-[state=open]:bg-indigo-50 dark:data-[state=open]:bg-indigo-900/30 data-[state=open]:text-indigo-700 dark:data-[state=open]:text-indigo-300 font-medium text-gray-800 dark:text-gray-200">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-amber-500" />
                  What are non-printable Unicode characters?
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-5 py-4 bg-white dark:bg-gray-800/20 text-gray-600 dark:text-gray-400 leading-relaxed">
                <p>
                  Non-printable Unicode characters are special characters that don't have a visible glyph but can affect text formatting, layout, or behavior. These include zero-width spaces, control characters, and various whitespace characters.
                </p>
                <div className="mt-3 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-100 dark:border-amber-800/30 rounded-md text-amber-700 dark:text-amber-300 text-sm flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
                  <span>These characters can cause unexpected behavior in text processing, formatting issues in documents, and even security vulnerabilities.</span>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* FAQ Item 2 */}
            <AccordionItem value="item-2" className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
              <AccordionTrigger className="px-5 py-4 hover:no-underline bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 data-[state=open]:bg-indigo-50 dark:data-[state=open]:bg-indigo-900/30 data-[state=open]:text-indigo-700 dark:data-[state=open]:text-indigo-300 font-medium text-gray-800 dark:text-gray-200">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-indigo-500" />
                  Why do AI tools like ChatGPT leave hidden characters?
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-5 py-4 bg-white dark:bg-gray-800/20 text-gray-600 dark:text-gray-400 leading-relaxed">
                <p>
                  AI tools like ChatGPT sometimes insert invisible Unicode characters (such as zero-width spaces or special whitespace) to format text or mark content. These characters can cause issues when copying text to other applications or when processing the text further.
                </p>
                <div className="mt-3 p-3 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-100 dark:border-indigo-800/30 rounded-md text-indigo-700 dark:text-indigo-300 text-sm flex items-start gap-2">
                  <Info className="h-4 w-4 text-indigo-500 mt-0.5 flex-shrink-0" />
                  <span>These characters are often used for formatting purposes but can interfere with code, data validation, or text processing systems.</span>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* FAQ Item 3 */}
            <AccordionItem value="item-3" className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
              <AccordionTrigger className="px-5 py-4 hover:no-underline bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 data-[state=open]:bg-indigo-50 dark:data-[state=open]:bg-indigo-900/30 data-[state=open]:text-indigo-700 dark:data-[state=open]:text-indigo-300 font-medium text-gray-800 dark:text-gray-200">
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4 text-green-500" />
                  Why would I need to find hidden characters?
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-5 py-4 bg-white dark:bg-gray-800/20 text-gray-600 dark:text-gray-400 leading-relaxed">
                <p>
                  Hidden characters can cause unexpected behavior in text processing, formatting issues in documents, problems with data validation, or even security vulnerabilities. Finding and removing them helps ensure your text behaves as expected.
                </p>
                <ul className="mt-3 space-y-2 list-disc pl-5">
                  <li>Prevents formatting issues when copying text between applications</li>
                  <li>Ensures data validation works correctly in forms and databases</li>
                  <li>Avoids potential security issues in code or command processing</li>
                  <li>Helps debug mysterious text behavior in documents or websites</li>
                </ul>
              </AccordionContent>
            </AccordionItem>

            {/* FAQ Item 4 */}
            <AccordionItem value="item-4" className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
              <AccordionTrigger className="px-5 py-4 hover:no-underline bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 data-[state=open]:bg-indigo-50 dark:data-[state=open]:bg-indigo-900/30 data-[state=open]:text-indigo-700 dark:data-[state=open]:text-indigo-300 font-medium text-gray-800 dark:text-gray-200">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-amber-500" />
                  What types of characters can this tool detect?
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-5 py-4 bg-white dark:bg-gray-800/20 text-gray-600 dark:text-gray-400 leading-relaxed">
                <p>
                  This tool can detect a wide range of special characters, including:
                </p>
                <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-md">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Zero-width characters</h4>
                    <ul className="text-sm space-y-1 list-disc pl-5">
                      <li>Zero Width Space (U+200B)</li>
                      <li>Zero Width Non-Joiner (U+200C)</li>
                      <li>Zero Width Joiner (U+200D)</li>
                      <li>Zero Width No-Break Space (U+FEFF)</li>
                    </ul>
                  </div>
                  <div className="p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-md">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Whitespace characters</h4>
                    <ul className="text-sm space-y-1 list-disc pl-5">
                      <li>Non-Breaking Space (U+00A0)</li>
                      <li>En Space (U+2002)</li>
                      <li>Em Space (U+2003)</li>
                      <li>And many more specialized spaces</li>
                    </ul>
                  </div>
                  <div className="p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-md">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Control characters</h4>
                    <ul className="text-sm space-y-1 list-disc pl-5">
                      <li>ASCII control characters (0x00-0x1F)</li>
                      <li>Delete character (0x7F)</li>
                      <li>C1 control characters (0x80-0x9F)</li>
                    </ul>
                  </div>
                  <div className="p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-md">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Bidirectional text markers</h4>
                    <ul className="text-sm space-y-1 list-disc pl-5">
                      <li>Left-to-Right Mark (U+200E)</li>
                      <li>Right-to-Left Mark (U+200F)</li>
                      <li>Various directional formatting codes</li>
                    </ul>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* FAQ Item 5 */}
            <AccordionItem value="item-5" className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
              <AccordionTrigger className="px-5 py-4 hover:no-underline bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 data-[state=open]:bg-indigo-50 dark:data-[state=open]:bg-indigo-900/30 data-[state=open]:text-indigo-700 dark:data-[state=open]:text-indigo-300 font-medium text-gray-800 dark:text-gray-200">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  How do I remove these characters from my text?
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-5 py-4 bg-white dark:bg-gray-800/20 text-gray-600 dark:text-gray-400 leading-relaxed">
                <p>
                  After analyzing your text, you can:
                </p>
                <ol className="mt-3 space-y-3 list-decimal pl-5">
                  <li className="pl-1">
                    <span className="font-medium text-gray-800 dark:text-gray-200">Replace all special characters:</span>
                    <p className="mt-1">Use the "Replace All Special Characters" button to remove or replace all detected special characters at once.</p>
                  </li>
                  <li className="pl-1">
                    <span className="font-medium text-gray-800 dark:text-gray-200">Replace selected characters:</span>
                    <p className="mt-1">Select specific character types you want to replace, then use the "Replace Selected" button.</p>
                  </li>
                  <li className="pl-1">
                    <span className="font-medium text-gray-800 dark:text-gray-200">Choose replacement options:</span>
                    <p className="mt-1">You can replace characters with spaces, dashes, underscores, or completely remove them.</p>
                  </li>
                </ol>
                <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800/30 rounded-md text-green-700 dark:text-green-300 text-sm flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>For quick cleaning of common invisible characters, use the "Quick Clean Common Invisibles" link below the text input area.</span>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </div>
  );
}
