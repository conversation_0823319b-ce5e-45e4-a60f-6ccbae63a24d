'use client';

import dynamic from 'next/dynamic';
import { Search } from 'lucide-react';
import React from 'react';

// Simple error boundary for debugging
class UnicodeFinderErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('[UnicodeFinder] Error boundary caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[UnicodeFinder] Error boundary details:', { error, errorInfo });

    // Log to your error tracking service if needed
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('unicode-finder-error', {
        detail: { error: error.message, stack: error.stack, errorInfo }
      }));
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center border border-red-300 rounded-lg bg-red-50 dark:bg-red-900/20">
          <div className="w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mb-4">
            <Search className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
          <h2 className="text-xl font-semibold text-red-800 dark:text-red-200 mb-2">Unicode Finder Error</h2>
          <p className="text-red-600 dark:text-red-300 max-w-md mb-4">
            An error occurred while loading the Unicode finder. Please refresh the page or contact support.
          </p>
          <details className="text-sm text-red-500 dark:text-red-400">
            <summary className="cursor-pointer">Error Details</summary>
            <pre className="mt-2 p-2 bg-red-100 dark:bg-red-900/50 rounded text-xs overflow-auto max-w-md">
              {this.state.error?.message}
            </pre>
          </details>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Use dynamic import with SSR disabled
const UnicodeFinder = dynamic(
  () => import('./UnicodeFinder').then(mod => mod.UnicodeFinder),
  {
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
          <Search className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">Loading Unicode Finder...</h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Please wait while we initialize the Unicode character finder tool.
        </p>
      </div>
    )
  }
);

export function UnicodeFinderClient() {
  return (
    <UnicodeFinderErrorBoundary>
      <UnicodeFinder />
    </UnicodeFinderErrorBoundary>
  );
}
