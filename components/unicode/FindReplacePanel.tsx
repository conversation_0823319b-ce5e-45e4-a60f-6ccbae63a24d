'use client';

import { useState, useEffect } from 'react';
import { Search, ArrowRight, X, ChevronDown, ChevronUp, RotateCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface FindReplacePanelProps {
  text: string;
  onReplace: (newText: string) => void;
  onHighlight: (matches: MatchInfo[]) => void;
  disabled?: boolean;
}

export interface MatchInfo {
  start: number;
  end: number;
  text: string;
  replacement?: string;
}

export function FindReplacePanel({ text, onReplace, onHighlight, disabled = false }: FindReplacePanelProps) {
  const [findText, setFindText] = useState('');
  const [replaceText, setReplaceText] = useState('');
  const [matches, setMatches] = useState<MatchInfo[]>([]);
  const [currentMatchIndex, setCurrentMatchIndex] = useState(-1);
  const [isExpanded, setIsExpanded] = useState(true);
  const [matchCase, setMatchCase] = useState(false);
  const [wholeWord, setWholeWord] = useState(false);
  const [useRegex, setUseRegex] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // Find all matches when search parameters change
  useEffect(() => {
    if (!findText || !text) {
      setMatches([]);
      setCurrentMatchIndex(-1);
      onHighlight([]);
      return;
    }

    setIsSearching(true);
    
    try {
      let pattern: RegExp;
      
      if (useRegex) {
        // Use the provided regex pattern
        try {
          pattern = new RegExp(findText, matchCase ? 'g' : 'gi');
        } catch (e) {
          // If regex is invalid, treat as literal text
          const escaped = findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
          pattern = new RegExp(escaped, matchCase ? 'g' : 'gi');
        }
      } else {
        // Escape special regex characters for literal search
        let escaped = findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        
        // Add word boundary if whole word is selected
        if (wholeWord) {
          escaped = `\\b${escaped}\\b`;
        }
        
        pattern = new RegExp(escaped, matchCase ? 'g' : 'gi');
      }
      
      // Find all matches
      const foundMatches: MatchInfo[] = [];
      let match;
      
      while ((match = pattern.exec(text)) !== null) {
        foundMatches.push({
          start: match.index,
          end: match.index + match[0].length,
          text: match[0],
          replacement: replaceText
        });
        
        // Prevent infinite loops with zero-length matches
        if (match.index === pattern.lastIndex) {
          pattern.lastIndex++;
        }
      }
      
      setMatches(foundMatches);
      
      // Reset current match index if we have matches
      if (foundMatches.length > 0 && currentMatchIndex === -1) {
        setCurrentMatchIndex(0);
      } else if (currentMatchIndex >= foundMatches.length) {
        setCurrentMatchIndex(foundMatches.length > 0 ? 0 : -1);
      }
      
      // Highlight matches in the text
      onHighlight(foundMatches);
      
    } catch (error) {
      console.error('Error finding matches:', error);
      setMatches([]);
      setCurrentMatchIndex(-1);
      onHighlight([]);
    } finally {
      setIsSearching(false);
    }
  }, [findText, text, matchCase, wholeWord, useRegex]);

  // Navigate to next match
  const goToNextMatch = () => {
    if (matches.length === 0) return;
    
    const nextIndex = (currentMatchIndex + 1) % matches.length;
    setCurrentMatchIndex(nextIndex);
    
    // Highlight the current match differently
    const highlightedMatches = matches.map((match, index) => ({
      ...match,
      isCurrent: index === nextIndex
    }));
    
    onHighlight(highlightedMatches);
  };

  // Navigate to previous match
  const goToPrevMatch = () => {
    if (matches.length === 0) return;
    
    const prevIndex = (currentMatchIndex - 1 + matches.length) % matches.length;
    setCurrentMatchIndex(prevIndex);
    
    // Highlight the current match differently
    const highlightedMatches = matches.map((match, index) => ({
      ...match,
      isCurrent: index === prevIndex
    }));
    
    onHighlight(highlightedMatches);
  };

  // Replace current match
  const replaceCurrentMatch = () => {
    if (matches.length === 0 || currentMatchIndex === -1) return;
    
    const match = matches[currentMatchIndex];
    const newText = text.substring(0, match.start) + replaceText + text.substring(match.end);
    
    // Update text
    onReplace(newText);
    
    // Recalculate matches after replacement
    // This will be handled by the useEffect when text changes
  };

  // Replace all matches
  const replaceAllMatches = () => {
    if (matches.length === 0) return;
    
    let newText = text;
    let offset = 0;
    
    // Replace all matches, adjusting for the changing string length
    for (const match of matches) {
      const adjustedStart = match.start + offset;
      const adjustedEnd = match.end + offset;
      
      newText = newText.substring(0, adjustedStart) + replaceText + newText.substring(adjustedEnd);
      
      // Update offset based on replacement length difference
      offset += replaceText.length - match.text.length;
    }
    
    // Update text
    onReplace(newText);
  };

  return (
    <Collapsible
      open={isExpanded}
      onOpenChange={setIsExpanded}
      className="w-full border rounded-lg bg-white dark:bg-gray-800 shadow-sm"
    >
      <div className="flex items-center justify-between px-4 py-2 border-b">
        <div className="flex items-center gap-2">
          <Search className="h-4 w-4 text-gray-500" />
          <span className="font-medium text-sm">Find & Replace</span>
          {matches.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {matches.length} {matches.length === 1 ? 'match' : 'matches'}
            </Badge>
          )}
        </div>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </CollapsibleTrigger>
      </div>
      
      <CollapsibleContent>
        <div className="p-4 space-y-4">
          {/* Find input */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Input
                  placeholder="Find text or pattern..."
                  value={findText}
                  onChange={(e) => setFindText(e.target.value)}
                  className="pr-20"
                  disabled={disabled}
                />
                {isSearching && (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2">
                    <RotateCw className="h-4 w-4 text-gray-400 animate-spin" />
                  </div>
                )}
                {findText && !isSearching && (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFindText('')}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4 text-gray-400" />
                    </Button>
                  </div>
                )}
              </div>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={goToPrevMatch}
                      disabled={matches.length === 0 || disabled}
                      className="h-9 w-9 p-0"
                    >
                      <ChevronUp className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Previous match</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={goToNextMatch}
                      disabled={matches.length === 0 || disabled}
                      className="h-9 w-9 p-0"
                    >
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Next match</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            
            {matches.length > 0 && (
              <div className="text-xs text-gray-500">
                Match {currentMatchIndex + 1} of {matches.length}
              </div>
            )}
          </div>
          
          {/* Replace input */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Input
                  placeholder="Replace with..."
                  value={replaceText}
                  onChange={(e) => setReplaceText(e.target.value)}
                  disabled={disabled}
                />
              </div>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={replaceCurrentMatch}
                      disabled={matches.length === 0 || currentMatchIndex === -1 || disabled}
                      className="h-9"
                    >
                      <ArrowRight className="h-4 w-4 mr-1" />
                      Replace
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Replace current match</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={replaceAllMatches}
                      disabled={matches.length === 0 || disabled}
                      className="h-9"
                    >
                      <ArrowRight className="h-4 w-4 mr-1" />
                      Replace All
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Replace all matches</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          
          {/* Search options */}
          <div className="flex flex-wrap items-center gap-x-4 gap-y-2 pt-1">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="match-case"
                checked={matchCase}
                onCheckedChange={(checked) => setMatchCase(checked as boolean)}
                disabled={disabled}
              />
              <Label htmlFor="match-case" className="text-sm cursor-pointer">Match case</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="whole-word"
                checked={wholeWord}
                onCheckedChange={(checked) => setWholeWord(checked as boolean)}
                disabled={disabled || useRegex}
              />
              <Label htmlFor="whole-word" className="text-sm cursor-pointer">Whole word</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="use-regex"
                checked={useRegex}
                onCheckedChange={(checked) => {
                  setUseRegex(checked as boolean);
                  if (checked) setWholeWord(false);
                }}
                disabled={disabled}
              />
              <Label htmlFor="use-regex" className="text-sm cursor-pointer">Use regex</Label>
            </div>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
