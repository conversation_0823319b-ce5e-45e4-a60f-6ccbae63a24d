'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Search, X, Copy, FileText, Download, AlertTriangle, Info, ArrowRight, RefreshCw, Settings, Wand2, Code, Eye, EyeOff, CheckCircle, Replace, Trash, Braces, Loader2, Plus, BookOpen, Zap, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { toast } from 'sonner';
import { FindReplacePanel, MatchInfo } from './FindReplacePanel';
import { UnicodeGuides } from './UnicodeGuides';
import { FeedbackButtons } from '@/components/ui/FeedbackButtons';

// Define common Unicode character categories to check
const unicodeCategories = {
  zeroWidth: [
    { code: '\u200B', name: 'Zero Width Space', hex: 'U+200B' },
    { code: '\u200C', name: 'Zero Width Non-Joiner', hex: 'U+200C' },
    { code: '\u200D', name: 'Zero Width Joiner', hex: 'U+200D' },
    { code: '\uFEFF', name: 'Zero Width No-Break Space (BOM)', hex: 'U+FEFF' },
    { code: '\u2060', name: 'Word Joiner', hex: 'U+2060' },
    { code: '\u180E', name: 'Mongolian Vowel Separator', hex: 'U+180E' },
  ],
  whitespace: [
    { code: '\u0020', name: 'Space', hex: 'U+0020' },
    { code: '\u00A0', name: 'Non-Breaking Space', hex: 'U+00A0' },
    { code: '\u1680', name: 'Ogham Space Mark', hex: 'U+1680' },
    { code: '\u2000', name: 'En Quad', hex: 'U+2000' },
    { code: '\u2001', name: 'Em Quad', hex: 'U+2001' },
    { code: '\u2002', name: 'En Space', hex: 'U+2002' },
    { code: '\u2003', name: 'Em Space', hex: 'U+2003' },
    { code: '\u2004', name: 'Three-Per-Em Space', hex: 'U+2004' },
    { code: '\u2005', name: 'Four-Per-Em Space', hex: 'U+2005' },
    { code: '\u2006', name: 'Six-Per-Em Space', hex: 'U+2006' },
    { code: '\u2007', name: 'Figure Space', hex: 'U+2007' },
    { code: '\u2008', name: 'Punctuation Space', hex: 'U+2008' },
    { code: '\u2009', name: 'Thin Space', hex: 'U+2009' },
    { code: '\u200A', name: 'Hair Space', hex: 'U+200A' },
    { code: '\u202F', name: 'Narrow No-Break Space', hex: 'U+202F' },
    { code: '\u205F', name: 'Medium Mathematical Space', hex: 'U+205F' },
    { code: '\u3000', name: 'Ideographic Space', hex: 'U+3000' },
    { code: '\u0009', name: 'Tab', hex: 'U+0009' },
    { code: '\u000A', name: 'Line Feed', hex: 'U+000A' },
    { code: '\u000B', name: 'Vertical Tab', hex: 'U+000B' },
    { code: '\u000C', name: 'Form Feed', hex: 'U+000C' },
    { code: '\u000D', name: 'Carriage Return', hex: 'U+000D' },
  ],
  control: [
    // Control characters (C0 and C1)
    ...Array.from({ length: 32 }, (_, i) => ({
      code: String.fromCharCode(i),
      name: `Control Character ${i.toString(16).toUpperCase().padStart(2, '0')}`,
      hex: `U+${i.toString(16).toUpperCase().padStart(4, '0')}`,
    })),
    ...Array.from({ length: 32 }, (_, i) => ({
      code: String.fromCharCode(i + 0x80),
      name: `Control Character ${(i + 0x80).toString(16).toUpperCase().padStart(2, '0')}`,
      hex: `U+${(i + 0x80).toString(16).toUpperCase().padStart(4, '0')}`,
    })),
  ],
  bidirectional: [
    { code: '\u061C', name: 'Arabic Letter Mark', hex: 'U+061C' },
    { code: '\u200E', name: 'Left-to-Right Mark', hex: 'U+200E' },
    { code: '\u200F', name: 'Right-to-Left Mark', hex: 'U+200F' },
    { code: '\u202A', name: 'Left-to-Right Embedding', hex: 'U+202A' },
    { code: '\u202B', name: 'Right-to-Left Embedding', hex: 'U+202B' },
    { code: '\u202C', name: 'Pop Directional Formatting', hex: 'U+202C' },
    { code: '\u202D', name: 'Left-to-Right Override', hex: 'U+202D' },
    { code: '\u202E', name: 'Right-to-Left Override', hex: 'U+202E' },
    { code: '\u2066', name: 'Left-to-Right Isolate', hex: 'U+2066' },
    { code: '\u2067', name: 'Right-to-Left Isolate', hex: 'U+2067' },
    { code: '\u2068', name: 'First Strong Isolate', hex: 'U+2068' },
    { code: '\u2069', name: 'Pop Directional Isolate', hex: 'U+2069' },
  ],
  variationSelectors: [
    { code: '\uFE00', name: 'Variation Selector-1', hex: 'U+FE00' },
    { code: '\uFE01', name: 'Variation Selector-2', hex: 'U+FE01' },
    { code: '\uFE02', name: 'Variation Selector-3', hex: 'U+FE02' },
    { code: '\uFE03', name: 'Variation Selector-4', hex: 'U+FE03' },
    { code: '\uFE04', name: 'Variation Selector-5', hex: 'U+FE04' },
    { code: '\uFE05', name: 'Variation Selector-6', hex: 'U+FE05' },
    { code: '\uFE06', name: 'Variation Selector-7', hex: 'U+FE06' },
    { code: '\uFE07', name: 'Variation Selector-8', hex: 'U+FE07' },
    { code: '\uFE08', name: 'Variation Selector-9', hex: 'U+FE08' },
    { code: '\uFE09', name: 'Variation Selector-10', hex: 'U+FE09' },
    { code: '\uFE0A', name: 'Variation Selector-11', hex: 'U+FE0A' },
    { code: '\uFE0B', name: 'Variation Selector-12', hex: 'U+FE0B' },
    { code: '\uFE0C', name: 'Variation Selector-13', hex: 'U+FE0C' },
    { code: '\uFE0D', name: 'Variation Selector-14', hex: 'U+FE0D' },
    { code: '\uFE0E', name: 'Variation Selector-15 (Text)', hex: 'U+FE0E' },
    { code: '\uFE0F', name: 'Variation Selector-16 (Emoji)', hex: 'U+FE0F' },
  ],
  formatCharacters: [
    { code: '\u00AD', name: 'Soft Hyphen', hex: 'U+00AD' },
    { code: '\u034F', name: 'Combining Grapheme Joiner', hex: 'U+034F' },
    { code: '\u115F', name: 'Hangul Choseong Filler', hex: 'U+115F' },
    { code: '\u1160', name: 'Hangul Jungseong Filler', hex: 'U+1160' },
    { code: '\u17B4', name: 'Khmer Vowel Inherent Aq', hex: 'U+17B4' },
    { code: '\u17B5', name: 'Khmer Vowel Inherent Aa', hex: 'U+17B5' },
    { code: '\u3164', name: 'Hangul Filler', hex: 'U+3164' },
  ],
  lineBreaking: [
    { code: '\u2028', name: 'Line Separator', hex: 'U+2028' },
    { code: '\u2029', name: 'Paragraph Separator', hex: 'U+2029' },
    { code: '\u0085', name: 'Next Line (NEL)', hex: 'U+0085' },
  ],
  replacement: [
    { code: '\uFFFD', name: 'Replacement Character', hex: 'U+FFFD' },
    { code: '\uFFFC', name: 'Object Replacement Character', hex: 'U+FFFC' },
  ],
};

// Enhanced character information database
const characterDatabase: Record<string, {
  code: string;
  name: string;
  hex: string;
  description?: string;
  commonSources?: string[];
  potentialIssues?: string[];
  recommendations?: string[];
  usageExamples?: string[];
}> = {};

// Build the enhanced database
Object.values(unicodeCategories).flat().forEach(char => {
  characterDatabase[char.code] = {
    ...char,
    description: getCharacterDescription(char.code, char.name),
    commonSources: getCommonSources(char.code),
    potentialIssues: getPotentialIssues(char.code),
    recommendations: getRecommendations(char.code),
    usageExamples: getUsageExamples(char.code)
  };
});

function getCharacterDescription(code: string, name: string): string {
  const descriptions: Record<string, string> = {
    '\u200B': 'An invisible character that provides a line break opportunity without displaying any visual content.',
    '\u200C': 'Prevents the joining of adjacent characters in scripts that normally connect letters.',
    '\u200D': 'Forces the joining of adjacent characters, often used in emoji sequences.',
    '\uFEFF': 'Originally used as a byte order mark, now primarily functions as a zero-width non-breaking space.',
    '\u00A0': 'A space character that prevents line breaks, keeping words together on the same line.',
    '\u2028': 'Forces a line break in text, similar to pressing Enter but semantically different from paragraph breaks.',
    '\u2029': 'Separates paragraphs in text, indicating a stronger break than a line separator.',
    '\u200E': 'Forces text direction to be left-to-right, overriding automatic direction detection.',
    '\u200F': 'Forces text direction to be right-to-left, overriding automatic direction detection.',
    '\uFE0F': 'Modifies the preceding character to display as an emoji rather than text.',
    '\uFE0E': 'Modifies the preceding character to display as text rather than emoji.',
  };
  return descriptions[code] || `A special Unicode character (${name}) that may affect text display or processing.`;
}

function getCommonSources(code: string): string[] {
  const sources: Record<string, string[]> = {
    '\u200B': ['AI-generated text (ChatGPT, Claude)', 'Web scraping', 'Copy-paste from websites', 'Word processors'],
    '\u200C': ['Persian/Arabic text', 'Complex script documents', 'Typography software'],
    '\u200D': ['Emoji sequences', 'Complex script rendering', 'Font ligatures'],
    '\uFEFF': ['File encoding markers', 'Text editors', 'Data export tools'],
    '\u00A0': ['HTML entities (&nbsp;)', 'Word processors', 'Web content'],
    '\u2028': ['Rich text editors', 'Document conversion', 'Legacy text formats'],
    '\u2029': ['Document formats', 'Text processing software'],
    '\u200E': ['Multilingual documents', 'RTL language processing'],
    '\u200F': ['Arabic/Hebrew text', 'Bidirectional text editors'],
  };
  return sources[code] || ['Various text processing applications', 'Document conversion tools'];
}

function getPotentialIssues(code: string): string[] {
  const issues: Record<string, string[]> = {
    '\u200B': ['Breaks word selection', 'Interferes with search', 'Causes unexpected line breaks'],
    '\u200C': ['May break text rendering', 'Affects font ligatures', 'Search functionality issues'],
    '\u200D': ['Can create invisible characters', 'May cause rendering problems', 'Affects text measurement'],
    '\uFEFF': ['Causes "invisible" characters', 'May break parsing', 'Encoding detection issues'],
    '\u00A0': ['Prevents text wrapping', 'Creates wider than normal spaces', 'Copy-paste inconsistencies'],
    '\u2028': ['Unexpected line breaks', 'Cross-platform compatibility issues', 'Text processing errors'],
    '\u2029': ['Paragraph formatting issues', 'Text flow problems', 'Rendering inconsistencies'],
    '\u200E': ['Text direction conflicts', 'Layout problems in mixed scripts'],
    '\u200F': ['Text direction conflicts', 'Layout problems in mixed scripts'],
  };
  return issues[code] || ['May cause unexpected text behavior', 'Could interfere with text processing'];
}

function getRecommendations(code: string): string[] {
  const recommendations: Record<string, string[]> = {
    '\u200B': ['Remove unless specifically needed for line breaking', 'Replace with regular spaces if appropriate'],
    '\u200C': ['Keep if working with complex scripts', 'Remove from simple Latin text'],
    '\u200D': ['Preserve in emoji sequences', 'Remove from regular text'],
    '\uFEFF': ['Remove from text content', 'Keep only if used as BOM'],
    '\u00A0': ['Replace with regular spaces unless non-breaking is required', 'Use CSS for layout instead'],
    '\u2028': ['Replace with regular line breaks (\\n)', 'Use proper paragraph formatting'],
    '\u2029': ['Use proper paragraph tags in HTML', 'Replace with double line breaks if needed'],
    '\u200E': ['Remove unless working with bidirectional text', 'Use CSS direction property instead'],
    '\u200F': ['Remove unless working with bidirectional text', 'Use CSS direction property instead'],
  };
  return recommendations[code] || ['Consider removing unless specifically needed', 'Test text behavior after removal'];
}

function getUsageExamples(code: string): string[] {
  const examples: Record<string, string[]> = {
    '\u200B': ['word\u200Bbreak → allows breaking between "word" and "break"'],
    '\u200C': ['fi\u200Cle → prevents "fi" ligature in "file"'],
    '\u200D': ['👨\u200D👩\u200D👧 → creates family emoji sequence'],
    '\uFEFF': ['Used at file beginning as byte order mark'],
    '\u00A0': ['10\u00A0km → keeps "10" and "km" together'],
    '\u2028': ['Line 1\u2028Line 2 → forces line break'],
    '\u2029': ['Para 1\u2029Para 2 → separates paragraphs'],
    '\u200E': ['Hello\u200Eمرحبا → forces LTR direction'],
    '\u200F': ['مرحبا\u200FHello → forces RTL direction'],
  };
  return examples[code] || ['Context-dependent usage'];
}

const allSpecialChars = characterDatabase;

const categoryGroups = {
  "Whitespace": unicodeCategories.whitespace,
  "Zero-Width": unicodeCategories.zeroWidth,
  "Directional": unicodeCategories.bidirectional,
  "Control": unicodeCategories.control,
  "Variation Selectors": unicodeCategories.variationSelectors,
  "Format Characters": unicodeCategories.formatCharacters,
  "Line Breaking": unicodeCategories.lineBreaking,
  "Replacement": unicodeCategories.replacement,
};

// Color mapping for different character categories
const categoryColors = {
  "Whitespace": {
    bg: "bg-blue-100 dark:bg-blue-900/40",
    border: "border-blue-400 dark:border-blue-600",
    text: "text-blue-800 dark:text-blue-200",
    accent: "blue"
  },
  "Zero-Width": {
    bg: "bg-green-100 dark:bg-green-900/40",
    border: "border-green-400 dark:border-green-600",
    text: "text-green-800 dark:text-green-200",
    accent: "green"
  },
  "Directional": {
    bg: "bg-purple-100 dark:bg-purple-900/40",
    border: "border-purple-400 dark:border-purple-600",
    text: "text-purple-800 dark:text-purple-200",
    accent: "purple"
  },
  "Control": {
    bg: "bg-red-100 dark:bg-red-900/40",
    border: "border-red-400 dark:border-red-600",
    text: "text-red-800 dark:text-red-200",
    accent: "red"
  },
  "Variation Selectors": {
    bg: "bg-amber-100 dark:bg-amber-900/40",
    border: "border-amber-400 dark:border-amber-600",
    text: "text-amber-800 dark:text-amber-200",
    accent: "amber"
  },
  "Format Characters": {
    bg: "bg-cyan-100 dark:bg-cyan-900/40",
    border: "border-cyan-400 dark:border-cyan-600",
    text: "text-cyan-800 dark:text-cyan-200",
    accent: "cyan"
  },
  "Line Breaking": {
    bg: "bg-orange-100 dark:bg-orange-900/40",
    border: "border-orange-400 dark:border-orange-600",
    text: "text-orange-800 dark:text-orange-200",
    accent: "orange"
  },
  "Replacement": {
    bg: "bg-pink-100 dark:bg-pink-900/40",
    border: "border-pink-400 dark:border-pink-600",
    text: "text-pink-800 dark:text-pink-200",
    accent: "pink"
  },
  "Other": {
    bg: "bg-gray-100 dark:bg-gray-900/40",
    border: "border-gray-400 dark:border-gray-600",
    text: "text-gray-800 dark:text-gray-200",
    accent: "gray"
  }
};

// Character collections for the explorer mode
const explorerCategories = {
  arrows: {
    name: "Arrows & Directions",
    icon: "→",
    characters: [
      { char: '←', name: 'Leftwards Arrow', hex: 'U+2190' },
      { char: '↑', name: 'Upwards Arrow', hex: 'U+2191' },
      { char: '→', name: 'Rightwards Arrow', hex: 'U+2192' },
      { char: '↓', name: 'Downwards Arrow', hex: 'U+2193' },
      { char: '↔', name: 'Left Right Arrow', hex: 'U+2194' },
      { char: '↕', name: 'Up Down Arrow', hex: 'U+2195' },
      { char: '↖', name: 'North West Arrow', hex: 'U+2196' },
      { char: '↗', name: 'North East Arrow', hex: 'U+2197' },
      { char: '↘', name: 'South East Arrow', hex: 'U+2198' },
      { char: '↙', name: 'South West Arrow', hex: 'U+2199' },
      { char: '⇐', name: 'Leftwards Double Arrow', hex: 'U+21D0' },
      { char: '⇑', name: 'Upwards Double Arrow', hex: 'U+21D1' },
      { char: '⇒', name: 'Rightwards Double Arrow', hex: 'U+21D2' },
      { char: '⇓', name: 'Downwards Double Arrow', hex: 'U+21D3' },
      { char: '⇔', name: 'Left Right Double Arrow', hex: 'U+21D4' },
      { char: '⇕', name: 'Up Down Double Arrow', hex: 'U+21D5' },
    ]
  },
  symbols: {
    name: "Mathematical Symbols",
    icon: "∑",
    characters: [
      { char: '∞', name: 'Infinity', hex: 'U+221E' },
      { char: '∑', name: 'N-Ary Summation', hex: 'U+2211' },
      { char: '∏', name: 'N-Ary Product', hex: 'U+220F' },
      { char: '∫', name: 'Integral', hex: 'U+222B' },
      { char: '∂', name: 'Partial Differential', hex: 'U+2202' },
      { char: '∆', name: 'Increment', hex: 'U+2206' },
      { char: '∇', name: 'Nabla', hex: 'U+2207' },
      { char: '±', name: 'Plus-Minus Sign', hex: 'U+00B1' },
      { char: '×', name: 'Multiplication Sign', hex: 'U+00D7' },
      { char: '÷', name: 'Division Sign', hex: 'U+00F7' },
      { char: '≠', name: 'Not Equal To', hex: 'U+2260' },
      { char: '≤', name: 'Less-Than Or Equal To', hex: 'U+2264' },
      { char: '≥', name: 'Greater-Than Or Equal To', hex: 'U+2265' },
      { char: '≈', name: 'Almost Equal To', hex: 'U+2248' },
      { char: '∈', name: 'Element Of', hex: 'U+2208' },
      { char: '∉', name: 'Not An Element Of', hex: 'U+2209' },
    ]
  },
  punctuation: {
    name: "Special Punctuation",
    icon: "…",
    characters: [
      { char: '…', name: 'Horizontal Ellipsis', hex: 'U+2026' },
      { char: '–', name: 'En Dash', hex: 'U+2013' },
      { char: '—', name: 'Em Dash', hex: 'U+2014' },
      { char: '\u2018', name: 'Left Single Quotation Mark', hex: 'U+2018' },
      { char: '\u2019', name: 'Right Single Quotation Mark', hex: 'U+2019' },
      { char: '\u201C', name: 'Left Double Quotation Mark', hex: 'U+201C' },
      { char: '\u201D', name: 'Right Double Quotation Mark', hex: 'U+201D' },
      { char: '•', name: 'Bullet', hex: 'U+2022' },
      { char: '‰', name: 'Per Mille Sign', hex: 'U+2030' },
      { char: '§', name: 'Section Sign', hex: 'U+00A7' },
      { char: '¶', name: 'Pilcrow Sign', hex: 'U+00B6' },
      { char: '†', name: 'Dagger', hex: 'U+2020' },
      { char: '‡', name: 'Double Dagger', hex: 'U+2021' },
      { char: '\u2039', name: 'Single Left-Pointing Angle Quotation Mark', hex: 'U+2039' },
      { char: '\u203A', name: 'Single Right-Pointing Angle Quotation Mark', hex: 'U+203A' },
      { char: '\u00AB', name: 'Left-Pointing Double Angle Quotation Mark', hex: 'U+00AB' },
    ]
  },
  currency: {
    name: "Currency Symbols",
    icon: "$",
    characters: [
      { char: '$', name: 'Dollar Sign', hex: 'U+0024' },
      { char: '€', name: 'Euro Sign', hex: 'U+20AC' },
      { char: '£', name: 'Pound Sign', hex: 'U+00A3' },
      { char: '¥', name: 'Yen Sign', hex: 'U+00A5' },
      { char: '₹', name: 'Indian Rupee Sign', hex: 'U+20B9' },
      { char: '₽', name: 'Ruble Sign', hex: 'U+20BD' },
      { char: '₩', name: 'Won Sign', hex: 'U+20A9' },
      { char: '₪', name: 'New Sheqel Sign', hex: 'U+20AA' },
      { char: '₫', name: 'Dong Sign', hex: 'U+20AB' },
      { char: '₦', name: 'Naira Sign', hex: 'U+20A6' },
      { char: '₨', name: 'Rupee Sign', hex: 'U+20A8' },
      { char: '¢', name: 'Cent Sign', hex: 'U+00A2' },
      { char: '₡', name: 'Colon Sign', hex: 'U+20A1' },
      { char: '₢', name: 'Cruzeiro Sign', hex: 'U+20A2' },
      { char: '₣', name: 'French Franc Sign', hex: 'U+20A3' },
      { char: '₤', name: 'Lira Sign', hex: 'U+20A4' },
    ]
  },
  shapes: {
    name: "Geometric Shapes",
    icon: "●",
    characters: [
      { char: '●', name: 'Black Circle', hex: 'U+25CF' },
      { char: '○', name: 'White Circle', hex: 'U+25CB' },
      { char: '■', name: 'Black Large Square', hex: 'U+25A0' },
      { char: '□', name: 'White Large Square', hex: 'U+25A1' },
      { char: '▲', name: 'Black Up-Pointing Triangle', hex: 'U+25B2' },
      { char: '△', name: 'White Up-Pointing Triangle', hex: 'U+25B3' },
      { char: '▼', name: 'Black Down-Pointing Triangle', hex: 'U+25BC' },
      { char: '▽', name: 'White Down-Pointing Triangle', hex: 'U+25BD' },
      { char: '◆', name: 'Black Diamond', hex: 'U+25C6' },
      { char: '◇', name: 'White Diamond', hex: 'U+25C7' },
      { char: '★', name: 'Black Star', hex: 'U+2605' },
      { char: '☆', name: 'White Star', hex: 'U+2606' },
      { char: '♠', name: 'Black Spade Suit', hex: 'U+2660' },
      { char: '♣', name: 'Black Club Suit', hex: 'U+2663' },
      { char: '♥', name: 'Black Heart Suit', hex: 'U+2665' },
      { char: '♦', name: 'Black Diamond Suit', hex: 'U+2666' },
    ]
  }
};

const replacementOptions = [
  { value: 'remove', label: 'Remove Characters', description: 'Completely remove all special characters' },
  { value: ' ', label: 'Replace with Space', description: 'Replace each character with a standard space' },
  { value: '-', label: 'Replace with Dash', description: 'Replace with hyphen/dash for better readability' },
  { value: '_', label: 'Replace with Underscore', description: 'Replace with underscore for code-friendly text' },
  { value: '•', label: 'Replace with Bullet', description: 'Replace with bullet points to visualize occurrences' },
  { value: 'custom', label: 'Custom Replacement', description: 'Specify your own character for replacement' },
];

interface FoundCharacter {
  char: string;
  hex: string;
  name: string;
  index: number;
  count: number;
  category?: string;
}

// Add error boundary and logging utilities
const handleComponentError = (error: Error, context: string) => {
  console.error(`[UnicodeFinder Error] ${context}:`, error);
  // You could also send this to your error tracking service
};

export function UnicodeFinder() {
  const [text, setText] = useState('');
  const [processedText, setProcessedText] = useState('');
  const [foundCharacters, setFoundCharacters] = useState<FoundCharacter[]>([]);
  const [activeTab, setActiveTab] = useState('input');
  const [highlightedText, setHighlightedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeCategory, setActiveCategory] = useState<string | null>(null); // Not used with accordion but kept for now
  const [selectedReplacement, setSelectedReplacement] = useState('remove'); // Default to remove
  const [customReplacement, setCustomReplacement] = useState('');
  const [isAutoDetect, setIsAutoDetect] = useState(false);
  const [displayMode, setDisplayMode] = useState<'character'|'code'|'both'>('code');
  const [findReplaceMatches, setFindReplaceMatches] = useState<MatchInfo[]>([]);
  const [showFindReplace, setShowFindReplace] = useState(false);
  const [selectedCharacters, setSelectedCharacters] = useState<Set<string>>(new Set());
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isHydrated, setIsHydrated] = useState(false);
  const [componentError, setComponentError] = useState<string | null>(null);
  const [selectedExplorerCategory, setSelectedExplorerCategory] = useState<string>('arrows');
  const [explorerSearchTerm, setExplorerSearchTerm] = useState('');

  // Add hydration tracking
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  const findSpecialCharacters = React.useCallback(() => {
    if (!text) {
      toast.error('Please enter some text to analyze');
      return;
    }
    setIsProcessing(true);
    try {
      const charCounts: Record<string, number> = {};
      const foundChars: FoundCharacter[] = [];
      for (let i = 0; i < text.length; i++) {
        const char = text[i];
        const charCode = char.charCodeAt(0);
        const isSpecial =
          char in allSpecialChars ||
          (charCode < 32) ||
          (charCode >= 0x80 && charCode <= 0x9F);

        if (isSpecial) {
          charCounts[char] = (charCounts[char] || 0) + 1;
          if (charCounts[char] === 1) {
            let category = "Other";
            for (const [catName, chars] of Object.entries(categoryGroups)) {
              if (chars.some(c => c.code === char)) {
                category = catName;
                break;
              }
            }
            const specialInfo = allSpecialChars[char] || {
              code: char,
              name: `Character ${charCode.toString(16).toUpperCase().padStart(4, '0')}`,
              hex: `U+${charCode.toString(16).toUpperCase().padStart(4, '0')}`,
            };
            foundChars.push({ char, hex: specialInfo.hex, name: specialInfo.name, index: i, count: 1, category });
          } else {
            const existingCharIndex = foundChars.findIndex(c => c.char === char);
            if (existingCharIndex !== -1) {
              foundChars[existingCharIndex].count = charCounts[char];
            }
          }
        }
      }
      foundChars.sort((a, b) => a.index - b.index);
      setFoundCharacters(foundChars);
      setProcessedText(text); // Store original text for preview
      updateHighlightedTextDisplay(text, foundChars, displayMode, selectedCharacters); // Pass necessary states

      if (foundChars.length > 0) {
        setActiveTab('results');
        toast.success(`Found ${foundChars.reduce((acc, curr) => acc + curr.count, 0)} occurrences of ${foundChars.length} special character types`);
      } else {
        toast.info('No special Unicode characters found');
      }
    } catch (error) {
      console.error('Error finding special characters:', error);
      toast.error('Error analyzing text');
    } finally {
      setIsProcessing(false);
    }
  }, [text, displayMode, selectedCharacters]); // Added selectedCharacters

  useEffect(() => {
    if (isAutoDetect && text && text !== processedText) {
      const timer = setTimeout(() => findSpecialCharacters(), 300);
      return () => clearTimeout(timer);
    }
  }, [text, isAutoDetect, processedText, findSpecialCharacters]);

  useEffect(() => {
    if (activeTab === 'results' && text !== processedText) {
      setActiveTab('input');
    }
  }, [text, processedText, activeTab]);

  const toggleCharacterSelection = React.useCallback((char: string) => {
    const newSelection = new Set(selectedCharacters);
    if (newSelection.has(char)) newSelection.delete(char);
    else newSelection.add(char);
    setSelectedCharacters(newSelection);
  }, [selectedCharacters]);

  useEffect(() => {
    // Update highlighting when selected characters change or display mode changes
    if (processedText && foundCharacters.length > 0) {
       updateHighlightedTextDisplay(processedText, foundCharacters, displayMode, selectedCharacters);
    }
  }, [selectedCharacters, displayMode, processedText, foundCharacters]);

    // Wrap the problematic updateHighlightedTextDisplay function with error handling
  const updateHighlightedTextDisplay = React.useCallback((currentText: string, charsFound: FoundCharacter[], currentDisplayMode: typeof displayMode, currentSelectedChars: Set<string>) => {
    try {
      if (charsFound.length === 0 || !currentText) {
        setHighlightedText(currentText);
        return;
      }

      // Check if we're in a hydration-safe environment
      if (!isHydrated) {
        setHighlightedText(currentText);
        return;
      }

      let highlightedOutput = '';
      let lastIdx = 0;

      for (let i = 0; i < currentText.length; i++) {
        const char = currentText[i];
        const charCode = char.charCodeAt(0);
        const isSpecial = charsFound.some(fc => fc.char === char && fc.index <= i && currentText.substring(fc.index, fc.index + fc.char.length) === char);

        if (isSpecial) {
          highlightedOutput += currentText.substring(lastIdx, i);
          const specialInfo = allSpecialChars[char] || { name: `Char ${charCode.toString(16).toUpperCase()}`, hex: `U+${charCode.toString(16).toUpperCase().padStart(4, '0')}`};
          const isSelected = currentSelectedChars.has(char);

          // Find the character's category for color coding
          let category = "Other";
          for (const [catName, chars] of Object.entries(categoryGroups)) {
            if (chars.some(c => c.code === char)) {
              category = catName;
              break;
            }
          }

          const colors = categoryColors[category] || categoryColors["Other"];
          let displayContent = '';

          switch (currentDisplayMode) {
            case 'character': displayContent = char || '␣'; break;
            case 'code': displayContent = specialInfo.hex; break;
            case 'both': displayContent = `<span class="code-part ${colors.bg} px-1 rounded-l border-r ${colors.border}">${specialInfo.hex}</span><span class="char-part px-1 rounded-r">${char || '␣'}</span>`; break;
          }

          // Use color-coded highlighting based on category
          const highlightClasses = isSelected
            ? 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100 border-green-300 dark:border-green-600'
            : `${colors.bg} ${colors.text} ${colors.border}`;

          highlightedOutput += `<span class="inline-flex items-center justify-center ${highlightClasses} px-1 py-0.5 rounded border hover:opacity-80 cursor-pointer transition-colors unicode-char-toggle" title="${specialInfo.hex}: ${specialInfo.name} (${category})" data-char="${char.replace(/"/g, '&quot;').replace(/'/g, '&#39;')}" data-category="${category}">${displayContent}</span>`;
          lastIdx = i + 1;
        }
      }
            highlightedOutput += currentText.substring(lastIdx);
      setHighlightedText(highlightedOutput);
    } catch (error) {
      const err = error as Error;
      handleComponentError(err, 'updateHighlightedTextDisplay');
      setComponentError(`Highlighting error: ${err.message}`);
      // Fallback to plain text
      setHighlightedText(currentText);
    }
  }, [isHydrated]);

  // Safer event handling approach
  useEffect(() => {
    if (!isHydrated) return;

    const handleClickOnHighlightedChars = (event: Event) => {
      try {
        const target = event.target as HTMLElement;
        if (target && target.classList.contains('unicode-char-toggle')) {
          const char = target.getAttribute('data-char');
          if (char) {
            toggleCharacterSelection(char);
          }
        }
      } catch (error) {
        handleComponentError(error as Error, 'character click handler');
      }
    };

    // Use event delegation instead of inline handlers
    document.addEventListener('click', handleClickOnHighlightedChars);

    return () => {
      document.removeEventListener('click', handleClickOnHighlightedChars);
    };
  }, [toggleCharacterSelection, isHydrated]);

  const handleFindReplaceHighlight = (matches: MatchInfo[]) => {
    setFindReplaceMatches(matches);
    if (matches.length > 0) {
      let highlighted = '';
      let lastIndex = 0;
      for (const match of matches) {
        highlighted += text.substring(lastIndex, match.start);
        const isCurrent = 'isCurrent' in match && match.isCurrent;
        highlighted += `<span class="inline-flex items-center justify-center ${
          isCurrent ? 'bg-yellow-200 dark:bg-yellow-800' : 'bg-yellow-100 dark:bg-yellow-900'
        } text-yellow-800 dark:text-yellow-200 px-1 py-0.5 rounded border border-yellow-300 dark:border-yellow-700">${match.text}</span>`;
        lastIndex = match.end;
      }
      highlighted += text.substring(lastIndex);
      setHighlightedText(highlighted);
    } else if (foundCharacters.length > 0) {
      updateHighlightedTextDisplay(processedText, foundCharacters, displayMode, selectedCharacters);
    } else {
      setHighlightedText('');
    }
  };

  const handleTextReplace = (newText: string) => {
    setText(newText);
    setProcessedText(''); // Clear processed text as original has changed
    setFoundCharacters([]);
    setFindReplaceMatches([]);
    setHighlightedText('');
    setSelectedCharacters(new Set()); // Clear selections
    setActiveTab('input'); // Switch to input tab
  };

  const getUniqueCategories = () => {
    const categories = foundCharacters
      .map(char => char.category)
      .filter((value, index, self) => value !== undefined && self.indexOf(value) === index);
    return categories as string[];
  };

  const countByCategory = (category: string) => {
    return foundCharacters.filter(char => char.category === category).reduce((sum, char) => sum + char.count, 0);
  };

  const countTypesByCategory = (category: string) => {
    return foundCharacters.filter(char => char.category === category).length;
  };

  // Wrap the replace function with better error handling
    const replaceSelectedOrAllCharacters = (isReplacingSelected: boolean) => {
    try {
      if (!processedText) {
        toast.error('Please analyze some text first.');
        return;
      }
      if (isReplacingSelected && selectedCharacters.size === 0) {
        toast.error('No characters selected for replacement.');
        return;
      }

      const replacementValue = selectedReplacement === 'custom' ? customReplacement : (selectedReplacement === 'remove' ? '' : selectedReplacement);
      if (selectedReplacement === 'custom' && customReplacement.length > 5) {
          toast.error('Custom replacement cannot exceed 5 characters.');
          return;
      }

      setIsProcessing(true);

      // Add a small delay to ensure DOM is stable
      setTimeout(() => {
        try {
          let newText = processedText;
          const charsToReplace = isReplacingSelected ? Array.from(selectedCharacters) : foundCharacters.map(fc => fc.char);

          if (charsToReplace.length === 0 && !isReplacingSelected && foundCharacters.length > 0) {
            foundCharacters.forEach(charInfo => {
                const regex = new RegExp(charInfo.char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
                newText = newText.replace(regex, replacementValue);
            });
          } else {
             const regexPattern = new RegExp(charsToReplace.map(c => c.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|'), 'g');
             newText = newText.replace(regexPattern, replacementValue);
          }

          setText(newText);
          setProcessedText(newText);
          setFoundCharacters([]);
          setHighlightedText(newText);
          setSelectedCharacters(new Set());
          setActiveTab('input');
          toast.success(`Characters ${isReplacingSelected ? 'selected for replacement' : 'all special'} replaced successfully.`);
        } catch (error) {
          handleComponentError(error as Error, 'character replacement');
          toast.error('Error processing text for replacement.');
        } finally {
          setIsProcessing(false);
        }
      }, 100);
    } catch (error) {
      handleComponentError(error as Error, 'replaceSelectedOrAllCharacters setup');
      toast.error('Error setting up text replacement.');
      setIsProcessing(false);
    }
  };

  const copyToClipboard = (textToCopy: string) => {
    navigator.clipboard.writeText(textToCopy)
      .then(() => toast.success('Copied to clipboard'))
      .catch(() => toast.error('Failed to copy to clipboard'));
  };

  const downloadAsText = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success(`${filename} downloaded.`);
  };

  const selectAllTextInArea = () => {
    if (textareaRef.current) {
      textareaRef.current.select();
      toast.success('All text selected in input area');
    }
  };

  const getCharacterCountText = () => {
    const len = text.length;
    const specialOccurrences = foundCharacters.reduce((acc, char) => acc + char.count, 0);
    if (activeTab === 'results' && specialOccurrences > 0) {
      return `${len} characters (includes ${specialOccurrences} special character occurrences)`;
    }
    return `${len} characters`;
  };

  const removeCommonInvisibleSpaces = () => {
    if (!text) return;
    const newText = text.replace(/[\uFEFF\u200B-\u200D\u2060\u00A0]/g, '');
    if (text !== newText) {
      setText(newText);
      toast.success('Common invisible spaces removed. Analyze again if needed.');
      // Optionally, re-analyze automatically or clear results
      setProcessedText('');
      setFoundCharacters([]);
      setHighlightedText('');
      setSelectedCharacters(new Set());
    } else {
      toast.info('No common invisible spaces found to remove.');
    }
  };

  const generateExampleText = () => {
    const exampleText = `This​ text has hidden unicode characters.\u200B\u200B It may look normal but has zero-width spaces,\u200C non-breaking\u00A0spaces and other\u200D invisible characters that can cause issues when copying text from websites or documents. Check U+FEFF, U+200E, and U+000C.\u00AD\u2028Here's a line separator and soft hyphen.\uFE0F\uFE00 Variation selectors are also detected!`;
    setText(exampleText);
    setProcessedText('');
    setFoundCharacters([]);
    setHighlightedText('');
    setSelectedCharacters(new Set());
    setActiveTab('input');
    toast.info('Example text loaded with expanded character types. Click "Find Special Characters".');
  };

  const insertCharacterIntoText = (character: string) => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newText = text.substring(0, start) + character + text.substring(end);
      setText(newText);

      // Reset analysis results since text changed
      setProcessedText('');
      setFoundCharacters([]);
      setHighlightedText('');
      setSelectedCharacters(new Set());

      // Focus back to textarea and set cursor position
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + character.length, start + character.length);
      }, 0);

      toast.success(`Inserted "${character}" into text`);
    }
  };



  const getCharacterStatistics = () => {
    if (foundCharacters.length === 0) return null;

    const totalChars = text.length;
    const totalSpecialChars = foundCharacters.reduce((acc, char) => acc + char.count, 0);
    const specialCharPercentage = ((totalSpecialChars / totalChars) * 100).toFixed(1);

    const categoryStats = Object.entries(categoryGroups).map(([category, chars]) => {
      const categoryChars = foundCharacters.filter(fc => fc.category === category);
      const categoryCount = categoryChars.reduce((acc, char) => acc + char.count, 0);
      const categoryPercentage = totalSpecialChars > 0 ? ((categoryCount / totalSpecialChars) * 100).toFixed(1) : '0';

      return {
        category,
        count: categoryCount,
        percentage: categoryPercentage,
        types: categoryChars.length,
        color: categoryColors[category]?.accent || 'gray'
      };
    }).filter(stat => stat.count > 0).sort((a, b) => b.count - a.count);

    const mostCommonChar = foundCharacters.reduce((prev, current) =>
      (prev.count > current.count) ? prev : current
    );

    const potentialIssues = [];
    if (foundCharacters.some(fc => fc.category === 'Control')) {
      potentialIssues.push('Control characters detected - may cause display issues');
    }
    if (foundCharacters.some(fc => fc.category === 'Zero-Width')) {
      potentialIssues.push('Zero-width characters found - invisible but may affect text processing');
    }
    if (foundCharacters.some(fc => fc.category === 'Directional')) {
      potentialIssues.push('Directional characters present - may affect text direction');
    }
    if (totalSpecialChars > totalChars * 0.1) {
      potentialIssues.push('High concentration of special characters (>10%)');
    }

    return {
      totalChars,
      totalSpecialChars,
      specialCharPercentage,
      categoryStats,
      mostCommonChar,
      potentialIssues
    };
  };

  // Show component error if any
  if (componentError) {
    return (
      <div className="p-4 border border-red-300 rounded-lg bg-red-50 dark:bg-red-900/20">
        <h3 className="text-red-800 dark:text-red-200 font-semibold">Component Error</h3>
        <p className="text-red-600 dark:text-red-300 text-sm mt-1">{componentError}</p>
        <button
          onClick={() => setComponentError(null)}
          className="mt-2 px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
        >
          Dismiss
        </button>
      </div>
    );
  }

  return (
    <TooltipProvider delayDuration={300}>
      <div className="space-y-8"> {/* Increased global spacing */}
        <Card className="border-indigo-200 dark:border-indigo-800 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 dark:from-indigo-950/70 dark:via-purple-950/70 dark:to-pink-950/70 shadow-lg">
          <CardContent className="pt-6 pb-5">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-indigo-700 dark:text-indigo-300 flex items-center gap-2.5">
                  <Search className="h-6 w-6 text-indigo-500 dark:text-indigo-400" />
                  Unicode Character Inspector
                </h1>
                <p className="text-sm text-indigo-600/80 dark:text-indigo-400/80 mt-1.5">
                  Detect, analyze, and manage hidden or problematic Unicode characters in your text.
                </p>
              </div>
              <div className="flex items-center gap-2.5 mt-2 sm:mt-0">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="sm" className="border-indigo-300 dark:border-indigo-700 hover:bg-indigo-50 dark:hover:bg-indigo-900/50" onClick={generateExampleText}>
                      <Code className="h-4 w-4" />
                      <span className="ml-2 hidden sm:inline">Load Example</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Load example text with various hidden characters</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </CardContent>
        </Card>


        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-8 w-full max-w-3xl mx-auto bg-gray-200 dark:bg-gray-700 p-1.5 rounded-xl gap-1.5">
            <TabsTrigger value="input" disabled={isProcessing} className="data-[state=active]:bg-white dark:data-[state=active]:bg-indigo-600 data-[state=active]:text-indigo-700 dark:data-[state=active]:text-white data-[state=active]:shadow-lg rounded-lg py-2.5 text-sm font-semibold transition-all duration-150 ease-in-out">
              <Search className="h-5 w-5 mr-2" /> Input & Analyze
            </TabsTrigger>
            <TabsTrigger value="results" disabled={foundCharacters.length === 0 && !isProcessing} className="data-[state=active]:bg-white dark:data-[state=active]:bg-indigo-600 data-[state=active]:text-indigo-700 dark:data-[state=active]:text-white data-[state=active]:shadow-lg rounded-lg py-2.5 text-sm font-semibold transition-all duration-150 ease-in-out">
              <FileText className="h-5 w-5 mr-2" /> Results & Actions
              {foundCharacters.length > 0 && (
                <Badge variant="default" className="ml-2.5 bg-indigo-600 dark:bg-white text-white dark:text-indigo-700 text-xs px-2 py-1 rounded-full">
                  {foundCharacters.reduce((acc, char) => acc + char.count, 0)}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="explorer" className="data-[state=active]:bg-white dark:data-[state=active]:bg-indigo-600 data-[state=active]:text-indigo-700 dark:data-[state=active]:text-white data-[state=active]:shadow-lg rounded-lg py-2.5 text-sm font-semibold transition-all duration-150 ease-in-out">
              <Search className="h-5 w-5 mr-2" /> Character Explorer
            </TabsTrigger>
            <TabsTrigger value="guides" className="data-[state=active]:bg-white dark:data-[state=active]:bg-indigo-600 data-[state=active]:text-indigo-700 dark:data-[state=active]:text-white data-[state=active]:shadow-lg rounded-lg py-2.5 text-sm font-semibold transition-all duration-150 ease-in-out">
              <BookOpen className="h-5 w-5 mr-2" /> How To & FAQ
            </TabsTrigger>
          </TabsList>

          <TabsContent value="input">
            <Card className="shadow-xl border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
              <CardHeader className="pb-4 pt-6 px-6 bg-gray-50 dark:bg-gray-800/50 border-b dark:border-gray-700/70">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2 text-gray-800 dark:text-gray-200">
                    <Search className="h-5 w-5 text-indigo-500" />
                    Input Text for Analysis
                  </CardTitle>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" onClick={() => setIsAutoDetect(!isAutoDetect)} className="h-8 gap-1.5 text-xs text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700/50">
                        {isAutoDetect ? <CheckCircle className="h-4 w-4 text-green-500" /> : <RefreshCw className="h-4 w-4" />}
                        Auto-Detect
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{isAutoDetect ? 'Disable automatic analysis on paste' : 'Enable automatic analysis on paste'}</TooltipContent>
                  </Tooltip>
                </div>
                <CardDescription className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Paste your text below. Any hidden or special Unicode characters will be identified.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                {showFindReplace && (
                  <div className="mb-4 p-4 border rounded-md bg-gray-50 dark:bg-gray-800/50">
                    <FindReplacePanel text={text} onReplace={handleTextReplace} onHighlight={handleFindReplaceHighlight} disabled={isProcessing} />
                  </div>
                )}
                <div className="relative">
                  <Textarea
                    ref={textareaRef}
                    placeholder="Paste or type your text here to find hidden Unicode characters..."
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    className="min-h-[250px] font-mono text-sm resize-y p-3.5 leading-relaxed bg-gray-50/50 dark:bg-gray-800/30 focus:bg-white dark:focus:bg-gray-800/60"
                    data-gramm="false"
                  />
                  {text && (
                    <div className="absolute top-2.5 right-2.5 flex gap-1.5">
                       <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-7 w-7 bg-white/70 dark:bg-gray-700/70 backdrop-blur-sm shadow-sm hover:bg-gray-100 dark:hover:bg-gray-600" onClick={selectAllTextInArea}>
                            <Braces className="h-3.5 w-3.5" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Select All Text</TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                           <Button variant="ghost" size="icon" className="h-7 w-7 bg-white/70 dark:bg-gray-700/70 backdrop-blur-sm shadow-sm hover:bg-gray-100 dark:hover:bg-gray-600" onClick={() => setText('')}>
                            <X className="h-3.5 w-3.5" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Clear Text Area</TooltipContent>
                      </Tooltip>
                    </div>
                  )}
                </div>
                {text && (
                  <div className="mt-2.5 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <p>{getCharacterCountText()}</p>
                    <Button variant="link" size="sm" onClick={removeCommonInvisibleSpaces} className="text-xs h-auto p-0 text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300">
                      <Wand2 className="h-3.5 w-3.5 mr-1" />
                      Quick Clean Common Invisibles
                    </Button>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex flex-col lg:flex-row gap-3 lg:gap-4 pt-5 pb-6 px-6 bg-gray-50 dark:bg-gray-800/50 border-t dark:border-gray-700/70">
                <Button onClick={findSpecialCharacters} disabled={!text || isProcessing} className="w-full lg:w-auto order-1 lg:order-none gap-2 bg-indigo-600 hover:bg-indigo-700 text-white text-md font-semibold h-11 px-6 rounded-lg shadow-md hover:shadow-lg transition-all">
                  {isProcessing ? <Loader2 className="h-5 w-5 animate-spin" /> : <Search className="h-5 w-5" />}
                  Find Special Characters
                </Button>
                <Button variant={showFindReplace ? "default" : "outline"} onClick={() => setShowFindReplace(!showFindReplace)} className={`w-full lg:w-auto order-2 lg:order-none gap-2 text-md font-medium h-11 px-5 rounded-lg shadow-sm hover:shadow-md transition-all ${showFindReplace ? 'bg-indigo-500 text-white dark:bg-indigo-500' : 'border-gray-300 dark:border-gray-600'}`}>
                  <Replace className="h-5 w-5" />
                  Find & Replace
                </Button>
                <div className="flex gap-2.5 w-full lg:w-auto lg:ml-auto order-3 lg:order-none mt-2 lg:mt-0">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" onClick={() => copyToClipboard(text)} disabled={!text || isProcessing} className="flex-1 gap-2 h-10 rounded-md border-gray-300 dark:border-gray-600">
                        <Copy className="h-4 w-4" /> Copy
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Copy input text to clipboard</TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" onClick={() => downloadAsText(text, 'input-text.txt')} disabled={!text || isProcessing} className="flex-1 gap-2 h-10 rounded-md border-gray-300 dark:border-gray-600">
                        <Download className="h-4 w-4" /> Download
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Download input text as .txt file</TooltipContent>
                  </Tooltip>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="results" className="space-y-8"> {/* Increased spacing for results tab content */}
            {foundCharacters.length > 0 ? (
              <>
                <Card className="shadow-xl border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
                  <CardHeader className="pb-4 pt-6 px-6 bg-gray-50 dark:bg-gray-800/50 border-b dark:border-gray-700/70">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg flex items-center gap-2 text-gray-800 dark:text-gray-200">
                        <AlertTriangle className="h-5 w-5 text-amber-500" />
                        Detected Special Characters
                      </CardTitle>
                      <Badge variant="default" className="ml-2 text-sm py-1 px-2.5 bg-amber-500 text-white">
                        {foundCharacters.reduce((acc, char) => acc + char.count, 0)} Total Occurrences ({foundCharacters.length} Types)
                      </Badge>
                    </div>
                    <CardDescription className="mt-1">
                      Below is a preview of your text with special characters highlighted. Expand categories to see details and manage them.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-2 pb-5">
                    <div className="space-y-4">
                      <div className="space-y-2.5">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Text Preview:</h4>
                          <div className="flex items-center gap-2">
                            <Select value={displayMode} onValueChange={(value) => setDisplayMode(value as 'character' | 'code' | 'both')}>
                              <SelectTrigger className="h-8 text-xs w-[140px] bg-white dark:bg-gray-700/50">
                                <SelectValue placeholder="Display Mode" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="character">Show Characters</SelectItem>
                                <SelectItem value="code">Show Code Points</SelectItem>
                                <SelectItem value="both">Show Both</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div
                          className="p-4 bg-gray-50 dark:bg-gray-900/30 rounded-lg border border-gray-300 dark:border-gray-700 font-mono text-sm overflow-auto max-h-[300px] leading-relaxed shadow-inner selection:bg-indigo-200 dark:selection:bg-indigo-700"
                          dangerouslySetInnerHTML={{ __html: highlightedText }}
                        />

                        {/* Color Legend */}
                        <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800/30 rounded-lg border border-gray-200 dark:border-gray-700">
                          <h5 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">Color Legend:</h5>
                          <div className="flex flex-wrap gap-2 text-xs">
                            {Object.entries(categoryColors).filter(([category]) =>
                              getUniqueCategories().includes(category) || category === "Other"
                            ).map(([category, colors]) => (
                              <div key={category} className="flex items-center gap-1.5">
                                <div className={`w-3 h-3 rounded border ${colors.bg} ${colors.border}`}></div>
                                <span className="text-gray-600 dark:text-gray-400">{category}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Accordion type="multiple" className="w-full space-y-4" defaultValue={getUniqueCategories()}>
                  {getUniqueCategories().map(category => (
                    <AccordionItem value={category} key={category} className="border-0">
                      <Card className="overflow-hidden shadow-lg border-gray-200 dark:border-gray-700 rounded-lg">
                        <AccordionTrigger className="text-md font-semibold hover:no-underline py-4 px-5 bg-gray-100 dark:bg-gray-700/70 hover:bg-gray-200/80 dark:hover:bg-gray-700 transition-colors w-full data-[state=open]:bg-indigo-100 dark:data-[state=open]:bg-indigo-700/40 data-[state=open]:text-indigo-700 dark:data-[state=open]:text-indigo-200 rounded-t-lg data-[state=closed]:rounded-lg">
                          <div className="flex items-center justify-between w-full">
                            <span className="text-gray-800 dark:text-gray-100">{category} Characters</span>
                            <Badge variant="outline" className="text-xs font-semibold bg-white dark:bg-gray-600 border-gray-300 dark:border-gray-500 text-gray-700 dark:text-gray-200 px-2.5 py-1">{countTypesByCategory(category)} types ({countByCategory(category)} occurrences)</Badge>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="border-t border-gray-200 dark:border-gray-700 p-0 bg-white dark:bg-gray-800/40">
                            <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                              <thead className="bg-gray-50 dark:bg-gray-700/50">
                                <tr>
                                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-16 text-center">Select</th>
                                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-20 text-center">Char</th>
                                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Code</th>
                                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-20 text-center">Count</th>
                                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32 text-center">Actions</th>
                                </tr>
                              </thead>
                              <tbody className="bg-white dark:bg-gray-800/30 divide-y divide-gray-200 dark:divide-gray-700/50">
                                {foundCharacters.filter(fc => fc.category === category).map((char, index) => (
                                  <tr
                                    key={`${category}-${index}`}
                                    className={`hover:bg-gray-50/80 dark:hover:bg-gray-700/40 transition-colors ${
                                      selectedCharacters.has(char.char) ? 'bg-green-50/70 dark:bg-green-700/30' : ''
                                    }`}
                                  >
                                    <td className="px-4 py-2.5 whitespace-nowrap text-center">
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Button
                                            variant={selectedCharacters.has(char.char) ? "secondary" : "outline"}
                                            size="icon"
                                            onClick={() => toggleCharacterSelection(char.char)}
                                            className="h-8 w-8 border-gray-300 dark:border-gray-600"
                                          >
                                            {selectedCharacters.has(char.char) ? (
                                              <CheckCircle className="h-4 w-4" />
                                            ) : (
                                              <Plus className="h-4 w-4" />
                                            )}
                                          </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>{selectedCharacters.has(char.char) ? 'Deselect' : 'Select'} this character type</TooltipContent>
                                      </Tooltip>
                                    </td>
                                    <td className="px-4 py-2.5 whitespace-nowrap text-center">
                                      <span
                                        className={`inline-flex items-center justify-center text-center min-w-[32px] h-8 ${
                                          selectedCharacters.has(char.char)
                                            ? 'bg-green-100 dark:bg-green-700 text-green-800 dark:text-green-100 border-green-400 dark:border-green-600'
                                            : `${categoryColors[char.category]?.bg || categoryColors["Other"].bg} ${categoryColors[char.category]?.text || categoryColors["Other"].text} ${categoryColors[char.category]?.border || categoryColors["Other"].border}`
                                        } px-3 py-1 rounded-md border font-mono text-md cursor-pointer hover:opacity-80`}
                                        onClick={() => toggleCharacterSelection(char.char)}
                                      >
                                        {char.char || '␣'}
                                      </span>
                                    </td>
                                    <td className="px-4 py-3 whitespace-nowrap font-mono text-sm text-gray-500 dark:text-gray-400">{char.hex}</td>
                                    <td className="px-4 py-3 whitespace-normal text-sm text-gray-700 dark:text-gray-200 max-w-[240px] leading-relaxed">
                                      <div className="flex items-center gap-2">
                                        {char.name}
                                        {char.name.toLowerCase().includes('control character') && (
                                          <div className="group relative">
                                            <Info className="h-4 w-4 text-blue-500 cursor-help flex-shrink-0" />
                                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 max-w-xs">
                                              Control characters are invisible formatting codes (like line breaks, tabs, etc.) that control text layout but don't display as visible characters.
                                              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </td>
                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-500 dark:text-gray-400 font-medium">{char.count}</td>
                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-center">
                                       <Popover>
                                        <PopoverTrigger asChild>
                                          <Button variant="ghost" size="sm" className="h-9 gap-1.5 text-xs text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700/60 rounded-md">
                                            <Settings className="h-4 w-4" /> Details
                                          </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-96 max-h-96 overflow-y-auto shadow-xl border-gray-300 dark:border-gray-600">
                                          <div className="space-y-4">
                                            <div>
                                              <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100">{char.name}</h3>
                                              {allSpecialChars[char.char]?.description && (
                                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{allSpecialChars[char.char].description}</p>
                                              )}
                                            </div>

                                            <div className="grid grid-cols-[auto,1fr] gap-x-3 gap-y-2 text-sm">
                                              <div className="text-gray-500 dark:text-gray-400 font-medium">Unicode:</div>
                                              <div className="font-mono text-gray-700 dark:text-gray-300">{char.hex}</div>
                                              <div className="text-gray-500 dark:text-gray-400 font-medium">Category:</div>
                                              <div className="text-gray-700 dark:text-gray-300">{char.category}</div>
                                              <div className="text-gray-500 dark:text-gray-400 font-medium">Occurrences:</div>
                                              <div className="text-gray-700 dark:text-gray-300">{char.count}</div>
                                              <div className="text-gray-500 dark:text-gray-400 font-medium">First at:</div>
                                              <div className="text-gray-700 dark:text-gray-300">Index {char.index}</div>
                                            </div>

                                            {allSpecialChars[char.char]?.commonSources && allSpecialChars[char.char].commonSources.length > 0 && (
                                              <div>
                                                <h4 className="font-medium text-sm text-gray-800 dark:text-gray-200 mb-2">Common Sources:</h4>
                                                <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                                                  {allSpecialChars[char.char].commonSources.map((source, i) => (
                                                    <li key={i} className="flex items-center gap-1">
                                                      <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                                                      {source}
                                                    </li>
                                                  ))}
                                                </ul>
                                              </div>
                                            )}

                                            {allSpecialChars[char.char]?.potentialIssues && allSpecialChars[char.char].potentialIssues.length > 0 && (
                                              <div>
                                                <h4 className="font-medium text-sm text-amber-700 dark:text-amber-300 mb-2 flex items-center gap-1">
                                                  <AlertTriangle className="h-3 w-3" />
                                                  Potential Issues:
                                                </h4>
                                                <ul className="text-xs text-amber-600 dark:text-amber-400 space-y-1">
                                                  {allSpecialChars[char.char].potentialIssues.map((issue, i) => (
                                                    <li key={i} className="flex items-center gap-1">
                                                      <span className="w-1 h-1 bg-amber-400 rounded-full"></span>
                                                      {issue}
                                                    </li>
                                                  ))}
                                                </ul>
                                              </div>
                                            )}

                                            {allSpecialChars[char.char]?.recommendations && allSpecialChars[char.char].recommendations.length > 0 && (
                                              <div>
                                                <h4 className="font-medium text-sm text-green-700 dark:text-green-300 mb-2 flex items-center gap-1">
                                                  <CheckCircle className="h-3 w-3" />
                                                  Recommendations:
                                                </h4>
                                                <ul className="text-xs text-green-600 dark:text-green-400 space-y-1">
                                                  {allSpecialChars[char.char].recommendations.map((rec, i) => (
                                                    <li key={i} className="flex items-center gap-1">
                                                      <span className="w-1 h-1 bg-green-400 rounded-full"></span>
                                                      {rec}
                                                    </li>
                                                  ))}
                                                </ul>
                                              </div>
                                            )}

                                            <hr className="border-gray-200 dark:border-gray-700/80" />
                                            <div className="flex justify-between gap-2">
                                              <Button variant="outline" size="sm" className="text-xs flex-1 h-8 border-gray-300 dark:border-gray-600" onClick={() => copyToClipboard(char.char)}>
                                                <Copy className="h-3 w-3 mr-1" /> Copy Char
                                              </Button>
                                              <Button variant="outline" size="sm" className="text-xs flex-1 h-8 border-gray-300 dark:border-gray-600" onClick={() => copyToClipboard(char.hex)}>
                                                <Copy className="h-3 w-3 mr-1" /> Copy Code
                                              </Button>
                                            </div>
                                          </div>
                                        </PopoverContent>
                                      </Popover>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </AccordionContent>
                      </Card>
                    </AccordionItem>
                  ))}
                </Accordion>

                {/* Character Statistics */}
                {(() => {
                  const stats = getCharacterStatistics();
                  return stats ? (
                    <Card className="shadow-xl border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden mt-8">
                      <CardHeader className="pb-4 pt-6 px-6 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/70 dark:to-cyan-950/70 border-b dark:border-gray-700/70">
                        <CardTitle className="text-lg flex items-center gap-2 text-blue-700 dark:text-blue-300">
                          <Info className="h-5 w-5 text-blue-500" />
                          Character Statistics & Analysis
                        </CardTitle>
                        <CardDescription className="mt-1 text-blue-600/80 dark:text-blue-400/80">
                          Detailed breakdown of your text composition and potential issues.
                          {foundCharacters.some(char => char.name.toLowerCase().includes('control character')) && (
                            <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
                              <div className="flex items-start gap-2">
                                <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                <div className="text-sm text-blue-700 dark:text-blue-300">
                                  <strong>Control Characters Found:</strong> These are invisible formatting codes (like line breaks, tabs, carriage returns) that control text layout but don't display as visible characters. They're normal in most text but can sometimes cause formatting issues.
                                </div>
                              </div>
                            </div>
                          )}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                          {/* Overview Stats */}
                          <div className="space-y-4">
                            <h4 className="font-semibold text-gray-800 dark:text-gray-200">Overview</h4>
                            <div className="space-y-3">
                              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                                <span className="text-sm text-gray-600 dark:text-gray-400">Total Characters</span>
                                <span className="font-mono font-semibold text-gray-800 dark:text-gray-200">{stats.totalChars.toLocaleString()}</span>
                              </div>
                              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                                <span className="text-sm text-gray-600 dark:text-gray-400">Special Characters</span>
                                <span className="font-mono font-semibold text-gray-800 dark:text-gray-200">{stats.totalSpecialChars.toLocaleString()}</span>
                              </div>
                              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                                <span className="text-sm text-gray-600 dark:text-gray-400">Special Char %</span>
                                <span className="font-mono font-semibold text-gray-800 dark:text-gray-200">{stats.specialCharPercentage}%</span>
                              </div>
                              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                                <span className="text-sm text-gray-600 dark:text-gray-400">Most Common</span>
                                <span className="font-mono font-semibold text-gray-800 dark:text-gray-200">{stats.mostCommonChar.hex} ({stats.mostCommonChar.count}×)</span>
                              </div>
                            </div>
                          </div>

                          {/* Category Breakdown */}
                          <div className="space-y-4">
                            <h4 className="font-semibold text-gray-800 dark:text-gray-200">Category Breakdown</h4>
                            <div className="space-y-2">
                              {stats.categoryStats.map((stat, index) => (
                                <div key={index} className="space-y-1">
                                  <div className="flex justify-between items-center text-sm">
                                    <span className="text-gray-600 dark:text-gray-400">{stat.category}</span>
                                    <span className="font-mono text-gray-800 dark:text-gray-200">{stat.count} ({stat.percentage}%)</span>
                                  </div>
                                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div
                                      className={`h-2 rounded-full bg-${stat.color}-500`}
                                      style={{ width: `${stat.percentage}%` }}
                                    ></div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Potential Issues */}
                          <div className="space-y-4">
                            <h4 className="font-semibold text-gray-800 dark:text-gray-200">Potential Issues</h4>
                            <div className="space-y-2">
                              {stats.potentialIssues.length > 0 ? (
                                stats.potentialIssues.map((issue, index) => (
                                  <div key={index} className="flex items-start gap-2 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/30 rounded-lg">
                                    <AlertTriangle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
                                    <span className="text-sm text-amber-700 dark:text-amber-300">{issue}</span>
                                  </div>
                                ))
                              ) : (
                                <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-lg">
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                  <span className="text-sm text-green-700 dark:text-green-300">No potential issues detected</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ) : null;
                })()}

                <Card className="shadow-xl border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden mt-8">
                  <CardHeader className="pb-4 pt-6 px-6 bg-gray-50 dark:bg-gray-800/50 border-b dark:border-gray-700/70">
                    <CardTitle className="text-lg flex items-center gap-2 text-gray-800 dark:text-gray-200">
                      <Wand2 className="h-5 w-5 text-indigo-500" />
                      Replacement Actions
                    </CardTitle>
                     <CardDescription className="mt-1">
                      Choose how to replace the detected or selected special characters from the text preview above.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-2 pb-5">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4 items-end">
                      <div>
                        <Label htmlFor="replacement-type" className="text-sm font-medium mb-1.5 block text-gray-700 dark:text-gray-300">
                          Replace With:
                        </Label>
                        <Select value={selectedReplacement} onValueChange={setSelectedReplacement}>
                          <SelectTrigger id="replacement-type" className="w-full h-10 bg-white dark:bg-gray-700/50">
                            <SelectValue placeholder="Select replacement option" />
                          </SelectTrigger>
                          <SelectContent>
                            {replacementOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                <div className="flex flex-col py-0.5">
                                  <span>{option.label}</span>
                                  <span className="text-xs text-gray-500 dark:text-gray-400">
                                    {option.description}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      {selectedReplacement === 'custom' && (
                        <div>
                          <Label htmlFor="custom-replacement-input" className="text-sm font-medium mb-1.5 block text-gray-700 dark:text-gray-300">
                            Custom Character(s):
                          </Label>
                          <Input
                            id="custom-replacement-input"
                            value={customReplacement}
                            onChange={(e) => setCustomReplacement(e.target.value)}
                            placeholder="Max 5 chars"
                            maxLength={5}
                            className="w-full h-10 bg-white dark:bg-gray-700/50"
                          />
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex flex-col md:flex-row gap-3 pt-4 pb-5 border-t dark:border-gray-700/50">
                    <Button
                      onClick={() => replaceSelectedOrAllCharacters(false)}
                      className="w-full md:w-auto gap-2 bg-red-600 hover:bg-red-700 text-base py-2.5 px-5"
                      disabled={isProcessing || foundCharacters.length === 0 || (selectedReplacement === 'custom' && customReplacement.length > 5)}
                    >
                      <Trash className="h-4.5 w-4.5" />
                      Replace All Special Characters
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => replaceSelectedOrAllCharacters(true)}
                      className="w-full md:w-auto gap-2 text-base py-2.5 px-5"
                      disabled={isProcessing || selectedCharacters.size === 0 || (selectedReplacement === 'custom' && customReplacement.length > 5)}
                    >
                      <Replace className="h-4.5 w-4.5" />
                      Replace Selected ({selectedCharacters.size})
                    </Button>
                  </CardFooter>
                </Card>

                <Card className="shadow-lg border-gray-200 dark:border-gray-700 mt-6">
                    <CardHeader className="pb-4 pt-5">
                        <CardTitle className="text-lg flex items-center gap-2 text-gray-800 dark:text-gray-200">
                            <Settings className="h-5 w-5 text-indigo-500" />
                            General Actions
                        </CardTitle>
                        <CardDescription className="mt-1">
                            Additional actions for your text.
                        </CardDescription>
                    </CardHeader>
                    <CardFooter className="flex flex-col sm:flex-row gap-3 pt-4 pb-5 border-t dark:border-gray-700/50">
                        <Button
                            variant="outline"
                            onClick={() => copyToClipboard(processedText)} // Copies the potentially modified text from preview
                            className="w-full sm:w-auto gap-2 text-base py-2.5 px-5"
                            disabled={!processedText}
                        >
                            <Copy className="h-4.5 w-4.5" />
                            Copy Processed Text
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => {
                              const report = [
                                'Unicode Character Analysis Report',
                                '===============================',
                                `Date: ${new Date().toLocaleString()}`,
                                '',
                                `Original text length: ${text.length} characters`,
                                `Processed text length: ${processedText.length} characters`,
                                `Total special character occurrences: ${foundCharacters.reduce((acc, char) => acc + char.count, 0)}`,
                                `Unique special character types: ${foundCharacters.length}`,
                                '',
                                'Detected Character Types:',
                                (getUniqueCategories().map(category => {
                                  const charsInCategory = foundCharacters.filter(fc => fc.category === category);
                                  const categorySummary = `  ${category} (${charsInCategory.length} types, ${charsInCategory.reduce((sum,c) => sum + c.count,0)} occurrences):`;
                                  const characterDetails = charsInCategory.map(char => `    - ${char.hex} (${char.name}): ${char.count} occurrences`).join('\n');
                                  return `${categorySummary}\n${characterDetails}`;
                                })).join('\n\n'), // Removed the || [] as map already returns an array
                                '',
                                '--- Original Text ---',
                                text,
                                '',
                                '--- Processed Text (if different) ---',
                                processedText === text ? "(No changes made or text not processed yet)" : processedText
                              ].join('\n');
                              downloadAsText(report, 'unicode-analysis-report.txt');
                            }}
                            className="w-full sm:w-auto gap-2 text-base py-2.5 px-5"
                            disabled={!text}
                        >
                            <Download className="h-4.5 w-4.5" />
                            Download Analysis Report
                        </Button>
                    </CardFooter>
                </Card>

                {/* Feedback Section */}
                <Card className="shadow-xl border-gray-200 dark:border-gray-700 mt-6 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20">
                  <CardContent className="py-8">
                    <div className="text-center space-y-3">
                      <div className="flex items-center justify-center gap-2 mb-4">
                        <div className="h-1 w-8 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full"></div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-300 px-2">
                          Help us improve
                        </span>
                        <div className="h-1 w-8 bg-gradient-to-r from-indigo-400 to-blue-400 rounded-full"></div>
                      </div>
                      <FeedbackButtons
                        toolName="unicode-finder"
                        className="justify-center"
                        size="md"
                      />
                    </div>
                  </CardContent>
                </Card>

              </>
            ) : (
              <Card className="shadow-lg border-gray-200 dark:border-gray-700">
                <CardContent className="flex flex-col items-center justify-center py-16 text-center">
                  <div className="p-3.5 mb-5 bg-indigo-100 dark:bg-indigo-700/30 rounded-full">
                    <Info className="h-7 w-7 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">No Special Characters Found</h3>
                  <p className="text-gray-600 dark:text-gray-400 max-w-sm">
                    The analyzed text doesn&apos;t contain any hidden or special Unicode characters that match the defined categories.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="explorer" className="space-y-6">
            <Card className="shadow-xl border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
              <CardHeader className="pb-4 pt-6 px-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/70 dark:to-pink-950/70 border-b dark:border-gray-700/70">
                <CardTitle className="text-lg flex items-center gap-2 text-purple-700 dark:text-purple-300">
                  <Search className="h-5 w-5 text-purple-500" />
                  Unicode Character Explorer
                </CardTitle>
                <CardDescription className="mt-1 text-purple-600/80 dark:text-purple-400/80">
                  Browse and insert special Unicode characters directly into your text. Click any character to add it at your cursor position.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Category Selection */}
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(explorerCategories).map(([key, category]) => (
                      <Button
                        key={key}
                        variant={selectedExplorerCategory === key ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedExplorerCategory(key)}
                        className={`gap-2 ${selectedExplorerCategory === key ? 'bg-purple-600 hover:bg-purple-700' : 'border-purple-300 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/50'}`}
                      >
                        <span className="text-lg">{category.icon}</span>
                        {category.name}
                      </Button>
                    ))}
                  </div>

                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search characters..."
                      value={explorerSearchTerm}
                      onChange={(e) => setExplorerSearchTerm(e.target.value)}
                      className="pl-10 bg-white dark:bg-gray-700/50"
                    />
                  </div>

                  {/* Character Grid */}
                  <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 gap-3">
                    {explorerCategories[selectedExplorerCategory]?.characters
                      .filter(char =>
                        explorerSearchTerm === '' ||
                        char.name.toLowerCase().includes(explorerSearchTerm.toLowerCase()) ||
                        char.char.includes(explorerSearchTerm)
                      )
                      .map((char, index) => (
                        <Tooltip key={index}>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="lg"
                              onClick={() => insertCharacterIntoText(char.char)}
                              className="h-16 w-16 text-2xl font-mono border-purple-200 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/30 hover:border-purple-400 dark:hover:border-purple-500 transition-all duration-200 hover:scale-105"
                            >
                              {char.char}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <div className="font-semibold">{char.name}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">{char.hex}</div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      ))}
                  </div>

                  {explorerCategories[selectedExplorerCategory]?.characters
                    .filter(char =>
                      explorerSearchTerm === '' ||
                      char.name.toLowerCase().includes(explorerSearchTerm.toLowerCase()) ||
                      char.char.includes(explorerSearchTerm)
                    ).length === 0 && explorerSearchTerm !== '' && (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <Search className="h-12 w-12 mx-auto mb-3 opacity-50" />
                      <p>No characters found matching "{explorerSearchTerm}"</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="shadow-lg border-gray-200 dark:border-gray-700">
              <CardHeader className="pb-4 pt-5">
                <CardTitle className="text-lg flex items-center gap-2 text-gray-800 dark:text-gray-200">
                  <Zap className="h-5 w-5 text-amber-500" />
                  Quick Actions
                </CardTitle>
                <CardDescription className="mt-1">
                  Useful shortcuts for working with your text.
                </CardDescription>
              </CardHeader>
              <CardFooter className="flex flex-col sm:flex-row gap-3 pt-4 pb-5 border-t dark:border-gray-700/50">
                <Button
                  variant="outline"
                  onClick={() => setActiveTab('input')}
                  className="w-full sm:w-auto gap-2 text-base py-2.5 px-5"
                >
                  <ArrowLeft className="h-4.5 w-4.5" />
                  Back to Input
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    if (text) {
                      findSpecialCharacters();
                      setActiveTab('results');
                    } else {
                      toast.error('Please add some text first');
                    }
                  }}
                  className="w-full sm:w-auto gap-2 text-base py-2.5 px-5"
                  disabled={!text}
                >
                  <Search className="h-4.5 w-4.5" />
                  Analyze Current Text
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="guides">
            <UnicodeGuides />
          </TabsContent>
        </Tabs>
      </div>
    </TooltipProvider>
  );
}
