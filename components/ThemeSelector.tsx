'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Palette, Check, Shuffle } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  useTheme,
  themes,
  themeMetadata,
  themeCategories,
  getRandomTheme,
  getThemeDisplayName,
  getThemeDescription
} from '@/contexts/ThemeContext';

type ThemeType = keyof typeof themes;

export function ThemeSelector() {
  const { currentTheme, setCurrentTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const handleThemeSelect = (theme: ThemeType) => {
    setCurrentTheme(theme);
    setIsOpen(false);
  };

  const handleRandomTheme = () => {
    const randomTheme = getRandomTheme();
    setCurrentTheme(randomTheme);
    setIsOpen(false);
  };

  const ThemePreview = ({ theme }: { theme: ThemeType }) => {
    const themeStyle = themes[theme];
    const metadata = themeMetadata[theme];
    const isSelected = currentTheme === theme;

    return (
      <div
        className={cn(
          "relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:scale-105",
          isSelected
            ? "border-white ring-2 ring-white/50"
            : "border-white/20 hover:border-white/40"
        )}
        onClick={() => handleThemeSelect(theme)}
      >
        {/* Theme Preview */}
        <div className={cn(
          "w-full h-16 rounded-md mb-3 flex items-center justify-center relative overflow-hidden",
          themeStyle.bg
        )}>
          {isSelected && (
            <Check className="h-6 w-6 text-white drop-shadow-lg" />
          )}
          {metadata.isGradient && (
            <Badge
              variant="secondary"
              className="absolute top-1 right-1 text-xs bg-white/20 text-white border-white/30"
            >
              Gradient
            </Badge>
          )}
        </div>

        {/* Theme Info */}
        <div className="space-y-1">
          <h3 className="font-medium text-white text-sm">
            {getThemeDisplayName(theme)}
          </h3>
          <p className="text-white/70 text-xs">
            {getThemeDescription(theme)}
          </p>
          <Badge
            variant="outline"
            className="text-xs bg-white/10 text-white/80 border-white/30"
          >
            {metadata.category}
          </Badge>
        </div>
      </div>
    );
  };

  const CategoryGrid = ({ themes: categoryThemes }: { themes: ThemeType[] }) => (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
      {categoryThemes.map((theme) => (
        <ThemePreview key={theme} theme={theme} />
      ))}
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full hover:bg-white/10"
          title="Choose Theme"
        >
          <Palette className="h-5 w-5" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Choose Your Theme
          </DialogTitle>
          <DialogDescription className="text-gray-300">
            Select from {Object.keys(themes).length} beautiful themes to customize your experience.
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center gap-2 mb-4">
          <Button
            onClick={handleRandomTheme}
            variant="outline"
            size="sm"
            className="bg-white/10 border-white/20 text-white hover:bg-white/20"
          >
            <Shuffle className="h-4 w-4 mr-2" />
            Random Theme
          </Button>
          <Button
            onClick={() => handleThemeSelect('native')}
            variant="outline"
            size="sm"
            className="bg-white/10 border-white/20 text-white hover:bg-white/20"
          >
            Reset to Default
          </Button>
          <Badge variant="secondary" className="bg-white/10 text-white">
            Current: {getThemeDisplayName(currentTheme)}
          </Badge>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8 bg-gray-800 mb-4">
            <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
            <TabsTrigger value="solid" className="text-xs">Solid</TabsTrigger>
            <TabsTrigger value="gradient" className="text-xs">Gradient</TabsTrigger>
            <TabsTrigger value="nature" className="text-xs">Nature</TabsTrigger>
            <TabsTrigger value="professional" className="text-xs">Pro</TabsTrigger>
            <TabsTrigger value="vibrant" className="text-xs">Vibrant</TabsTrigger>
            <TabsTrigger value="dark" className="text-xs">Dark</TabsTrigger>
            <TabsTrigger value="light" className="text-xs">Light</TabsTrigger>
          </TabsList>

          <div className="overflow-y-auto max-h-[50vh] pr-2">
            <TabsContent value="all" className="mt-0">
              <CategoryGrid themes={Object.keys(themes) as ThemeType[]} />
            </TabsContent>

            <TabsContent value="solid" className="mt-0">
              <CategoryGrid themes={themeCategories.solid} />
            </TabsContent>

            <TabsContent value="gradient" className="mt-0">
              <CategoryGrid themes={themeCategories.gradient} />
            </TabsContent>

            <TabsContent value="nature" className="mt-0">
              <CategoryGrid themes={themeCategories.nature} />
            </TabsContent>

            <TabsContent value="professional" className="mt-0">
              <CategoryGrid themes={themeCategories.professional} />
            </TabsContent>

            <TabsContent value="vibrant" className="mt-0">
              <CategoryGrid themes={themeCategories.vibrant} />
            </TabsContent>

            <TabsContent value="dark" className="mt-0">
              <CategoryGrid themes={themeCategories.dark} />
            </TabsContent>

            <TabsContent value="light" className="mt-0">
              <CategoryGrid themes={themeCategories.light} />
            </TabsContent>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
