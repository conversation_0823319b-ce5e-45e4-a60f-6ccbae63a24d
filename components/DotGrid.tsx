'use client';

import { useRef, useEffect, useCallback, useMemo } from "react";
import "./DotGrid.css";

const throttle = (func, limit) => {
  let lastCall = 0;
  return function (...args) {
    const now = performance.now();
    if (now - lastCall >= limit) {
      lastCall = now;
      func.apply(this, args);
    }
  };
};

function hexToRgb(hex) {
  const m = hex.match(/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
  if (!m) return { r: 0, g: 0, b: 0 };
  return {
    r: parseInt(m[1], 16),
    g: parseInt(m[2], 16),
    b: parseInt(m[3], 16),
  };
}

const DotGrid = ({
  dotSize = 16,
  gap = 15,
  baseColor = "#5227FF",
  activeColor = "#5227FF",
  proximity = 120,
  shockRadius = 250,
  shockStrength = 5,
  resistance = 750,
  returnDuration = 1.5,
  maxSpeed = 10,
  speedTrigger = 0.5,
  className = "",
  style = {},
}) => {
  const canvasRef = useRef(null);
  const wrapperRef = useRef(null);
  const dotsRef = useRef([]);

  const baseRgb = useMemo(() => hexToRgb(baseColor), [baseColor]);
  const activeRgb = useMemo(() => hexToRgb(activeColor), [activeColor]);

  const initializeDots = useCallback(() => {
    const canvas = canvasRef.current;
    const wrapper = wrapperRef.current;
    if (!canvas || !wrapper) return;

    const rect = wrapper.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    const ctx = canvas.getContext("2d");
    const dots = [];

    const cols = Math.floor(canvas.width / (dotSize + gap));
    const rows = Math.floor(canvas.height / (dotSize + gap));

    const offsetX = (canvas.width - cols * (dotSize + gap) + gap) / 2;
    const offsetY = (canvas.height - rows * (dotSize + gap) + gap) / 2;

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const x = offsetX + col * (dotSize + gap);
        const y = offsetY + row * (dotSize + gap);
        const dot = {
          x,
          y,
          cx: x + dotSize / 2,
          cy: y + dotSize / 2,
          xOffset: 0,
          yOffset: 0,
          _inertiaApplied: false,
        };
        dots.push(dot);
      }
    }

    dotsRef.current = dots;

    const render = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      dots.forEach((dot) => {
        const x = dot.x + dot.xOffset;
        const y = dot.y + dot.yOffset;

        ctx.fillStyle = `rgb(${baseRgb.r}, ${baseRgb.g}, ${baseRgb.b})`;
        ctx.fillRect(x, y, dotSize, dotSize);
      });
    };

    render();
    dotsRef.current.render = render;
  }, [dotSize, gap, baseRgb]);

  useEffect(() => {
    initializeDots();

    const handleResize = () => {
      initializeDots();
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [initializeDots]);

  useEffect(() => {
    const onMove = (e) => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      const pr = { x, y };
      const vx = e.movementX || 0;
      const vy = e.movementY || 0;

      for (const dot of dotsRef.current) {
        const dist = Math.hypot(dot.cx - pr.x, dot.cy - pr.y);
        if (dist < proximity && !dot._inertiaApplied) {
          dot._inertiaApplied = true;

          const pushX = dot.cx - pr.x + vx * 0.005;
          const pushY = dot.cy - pr.y + vy * 0.005;

          // Simplified animation without GSAP
          const animate = () => {
            dot.xOffset += pushX * 0.1;
            dot.yOffset += pushY * 0.1;

            // Apply resistance
            dot.xOffset *= 0.95;
            dot.yOffset *= 0.95;

            if (Math.abs(dot.xOffset) < 0.1 && Math.abs(dot.yOffset) < 0.1) {
              dot.xOffset = 0;
              dot.yOffset = 0;
              dot._inertiaApplied = false;
            } else {
              requestAnimationFrame(animate);
            }

            if (dotsRef.current.render) {
              dotsRef.current.render();
            }
          };

          animate();
        }
      }
    };

    const onClick = (e) => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const cx = e.clientX - rect.left;
      const cy = e.clientY - rect.top;

      for (const dot of dotsRef.current) {
        const dist = Math.hypot(dot.cx - cx, dot.cy - cy);
        if (dist < shockRadius && !dot._inertiaApplied) {
          dot._inertiaApplied = true;

          const falloff = Math.max(0, 1 - dist / shockRadius);
          const pushX = (dot.cx - cx) * shockStrength * falloff;
          const pushY = (dot.cy - cy) * shockStrength * falloff;

          // Simplified animation without GSAP
          const animate = () => {
            dot.xOffset += pushX * 0.1;
            dot.yOffset += pushY * 0.1;

            // Apply resistance
            dot.xOffset *= 0.95;
            dot.yOffset *= 0.95;

            if (Math.abs(dot.xOffset) < 0.1 && Math.abs(dot.yOffset) < 0.1) {
              dot.xOffset = 0;
              dot.yOffset = 0;
              dot._inertiaApplied = false;
            } else {
              requestAnimationFrame(animate);
            }

            if (dotsRef.current.render) {
              dotsRef.current.render();
            }
          };

          animate();
        }
      }
    };

    const throttledMove = throttle(onMove, 50);
    window.addEventListener("mousemove", throttledMove, { passive: true });
    window.addEventListener("click", onClick);

    return () => {
      window.removeEventListener("mousemove", throttledMove);
      window.removeEventListener("click", onClick);
    };
  }, [
    maxSpeed,
    speedTrigger,
    proximity,
    resistance,
    returnDuration,
    shockRadius,
    shockStrength,
  ]);

  return (
    <section className={`dot-grid ${className}`} style={style}>
      <div ref={wrapperRef} className="dot-grid__wrap">
        <canvas ref={canvasRef} className="dot-grid__canvas" />
      </div>
    </section>
  );
};

export default DotGrid;
