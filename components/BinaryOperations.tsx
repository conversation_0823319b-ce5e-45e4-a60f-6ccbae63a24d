'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"; // Import Tooltip components
import {
  Wand2,
  Eraser,
  Binary, // Assuming Binary icon might be used elsewhere or intended?
  Plus,
  Minus, // Added Minus for consistency if needed later, Plus used for OR/ADD
  X,     // Used for AND/XOR
  CircleOff, // Used for NOT
  ArrowRight,
  GitCommitVertical, // Example alternate for AND
  GitMerge, // Example alternate for OR
  GitPullRequestClosed, // Example alternate for XOR
} from 'lucide-react';
import {
  binaryAND,
  binaryOR,
  binaryXOR,
  binaryNOT,
  binaryAdd,
  isValidBinary,
  formatBinary // Assuming this exists and formats (e.g., groups bits)
} from '@/lib/binaryUtils'; // Assuming this path is correct

type Operation = 'AND' | 'OR' | 'XOR' | 'NOT' | 'ADD';

// Using slightly more distinct icons where possible, keeping text primary
const operationIcons: Record<Operation, React.ReactNode> = {
  AND: <GitCommitVertical className="h-4 w-4" />, // More distinct than X
  OR:  <GitMerge className="h-4 w-4" />, // More distinct than Plus
  XOR: <GitPullRequestClosed className="h-4 w-4" />, // More distinct than X
  NOT: <CircleOff className="h-4 w-4" />,
  ADD: <Plus className="h-4 w-4" /> // Plus is conventional for Add
};

const operationTooltips: Record<Operation, string> = {
    AND: 'Bitwise AND: Result bit is 1 only if both corresponding input bits are 1.',
    OR: 'Bitwise OR: Result bit is 1 if at least one of the corresponding input bits is 1.',
    XOR: 'Bitwise XOR (Exclusive OR): Result bit is 1 if the corresponding input bits are different.',
    NOT: 'Bitwise NOT: Inverts each bit (0 becomes 1, 1 becomes 0). Applied to the first input only.',
    ADD: 'Binary Addition: Adds the two binary numbers, handling carries.'
};


export function BinaryOperations() {
  const [input1, setInput1] = useState('');
  const [input2, setInput2] = useState('');
  const [operation, setOperation] = useState<Operation>('AND');
  const [result, setResult] = useState('');
  const [error, setError] = useState('');

  // Debounce validation/computation? Optional, but good for rapid typing.
  // Consider using a debounce hook if performance becomes an issue.

  const validateAndCompute = () => {
    setError('');
    setResult(''); // Clear previous result immediately

    const cleanInput1 = input1.replace(/\s/g, ''); // Remove spaces for validation/logic
    const cleanInput2 = input2.replace(/\s/g, ''); // Remove spaces for validation/logic

    // Validate inputs
    if (!cleanInput1 || (operation !== 'NOT' && !cleanInput2)) {
      setError('Please provide input binary number(s).');
      return;
    }

    if (!isValidBinary(cleanInput1) || (operation !== 'NOT' && !isValidBinary(cleanInput2))) {
      setError('Invalid binary number(s) detected (only 0 and 1 allowed).');
      return;
    }

    // Perform operation
    try {
      let computedResult = '';
      // Pad inputs for relevant operations - assuming utils might need it
      // If utils handle padding, this isn't strictly necessary but clarifies intent
      let pInput1 = cleanInput1;
      let pInput2 = cleanInput2;
      if (operation !== 'NOT') {
          const maxLen = Math.max(pInput1.length, pInput2.length);
          pInput1 = pInput1.padStart(maxLen, '0');
          pInput2 = pInput2.padStart(maxLen, '0');
      }


      switch (operation) {
        case 'AND':
          computedResult = binaryAND(pInput1, pInput2);
          break;
        case 'OR':
          computedResult = binaryOR(pInput1, pInput2);
          break;
        case 'XOR':
          computedResult = binaryXOR(pInput1, pInput2);
          break;
        case 'NOT':
          // NOT usually doesn't pad, operates on input1 directly
          computedResult = binaryNOT(cleanInput1);
          break;
        case 'ADD':
          computedResult = binaryAdd(pInput1, pInput2);
          break;
      }
      // Format the result using the utility function (e.g., grouping)
      setResult(formatBinary ? formatBinary(computedResult) : computedResult);
    } catch (err) {
       console.error("Binary operation failed:", err); // Log detailed error
      setError(`Calculation error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setResult(''); // Ensure result is cleared on error
    }
  };

  const handleClear = () => {
    setInput1('');
    setInput2('');
    setResult('');
    setError('');
    // Optionally reset operation to default?
    // setOperation('AND');
  };

  // Handle input change, allowing spaces for formatting but removing for logic
  const handleInputChange = (setter: React.Dispatch<React.SetStateAction<string>>) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      // Allow digits, 1s, and spaces for readability
      const formattedValue = e.target.value.replace(/[^01\s]/g, '');
      setter(formattedValue);
      // Clear error on typing
      if (error) {
        setError('');
      }
  };


  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h2 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Binary Operations
        </h2>

        {/* Wrap Select and Inputs in TooltipProvider */}
        <TooltipProvider delayDuration={200}>
            <div className="grid gap-4">
              {/* Operation Selection */}
              <div className="space-y-2">
                <Label htmlFor="operation" className="text-sm font-medium">Operation</Label>
                <Select value={operation} onValueChange={(value) => { setOperation(value as Operation); setResult(''); setError(''); /* Clear result/error on op change */}}>
                  <SelectTrigger className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 border-gray-300 dark:border-gray-700">
                    <SelectValue placeholder="Select operation" />
                  </SelectTrigger>
                  <SelectContent>
                    {(['AND', 'OR', 'XOR', 'NOT', 'ADD'] as Operation[]).map((op) => (
                       <Tooltip key={op}>
                         <TooltipTrigger asChild>
                           <SelectItem value={op}>
                             <span className="flex items-center space-x-2">
                               {operationIcons[op]}
                               <span>{op === 'ADD' ? 'Addition' : op}</span> {/* Use full word for ADD */}
                             </span>
                           </SelectItem>
                         </TooltipTrigger>
                         <TooltipContent side="right" align="center">
                           <p className="max-w-xs">{operationTooltips[op]}</p> {/* Tooltip text */}
                         </TooltipContent>
                       </Tooltip>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Inputs */}
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="input1" className="text-sm font-medium">First Binary Number</Label>
                  <Input
                    id="input1"
                    placeholder="Enter first binary number"
                    value={input1}
                    onChange={handleInputChange(setInput1)}
                    className={cn(
                      "font-mono",
                      error && (error.includes('Invalid') || error.includes('required')) && "border-red-500 focus-visible:ring-red-500" // Highlight on validation error
                    )}
                    spellCheck="false"
                  />
                </div>

                {operation !== 'NOT' && (
                  <div className="space-y-2">
                    <Label htmlFor="input2" className="text-sm font-medium">Second Binary Number</Label>
                    <Input
                      id="input2"
                      placeholder="Enter second binary number"
                      value={input2}
                      onChange={handleInputChange(setInput2)}
                      className={cn(
                        "font-mono",
                         error && (error.includes('Invalid') || error.includes('required')) && "border-red-500 focus-visible:ring-red-500" // Highlight on validation error
                      )}
                      spellCheck="false"
                    />
                  </div>
                )}

                {/* Error Message Display */}
                {error && (
                  <p className="text-red-600 dark:text-red-400 text-sm font-medium px-1">{error}</p>
                )}

                {/* Arrow Separator */}
                 <div className="flex items-center justify-center py-1 text-gray-400 dark:text-gray-600">
                   <ArrowRight className="h-5 w-5" />
                 </div>


                {/* Result Display */}
                <div className="space-y-2">
                   <Label htmlFor="result" className="text-sm font-medium">Result</Label>
                  <Input
                    id="result"
                    readOnly
                    value={result}
                    placeholder="Result appears here"
                    className="font-mono bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 border-gray-300 dark:border-gray-700"
                    aria-live="polite" // Announce changes to screen readers
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 pt-2">
                <Button
                  onClick={handleClear}
                  variant="outline"
                  className="flex-1 space-x-2 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 hover:border-red-200 dark:hover:border-red-700/30 border-gray-300 dark:border-gray-700"
                >
                  <Eraser className="h-4 w-4" />
                  <span>Clear</span>
                </Button>
                <Button
                  onClick={validateAndCompute}
                  className="flex-1 space-x-2 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
                >
                  <Wand2 className="h-4 w-4" />
                  <span>Calculate</span>
                </Button>
              </div>
            </div>
        </TooltipProvider> {/* End TooltipProvider */}
      </div>

      {/* Visual Bit Manipulation */}
      {result && input1 && (operation !== 'NOT' ? input2 : true) && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700/50">
          <h3 className="font-medium mb-4 text-gray-800 dark:text-gray-200">Visual Operation:</h3>
          <div className="space-y-3">
            {/* Input A */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium w-16">Input A:</span>
              <div className="flex gap-1">
                {input1.padStart(8, '0').split('').map((bit, index) => (
                  <div
                    key={index}
                    className={`w-8 h-8 flex items-center justify-center rounded text-sm font-mono ${
                      bit === '1'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                    }`}
                  >
                    {bit}
                  </div>
                ))}
              </div>
            </div>

            {/* Input B (if not NOT operation) */}
            {operation !== 'NOT' && (
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-16">Input B:</span>
                <div className="flex gap-1">
                  {input2.padStart(8, '0').split('').map((bit, index) => (
                    <div
                      key={index}
                      className={`w-8 h-8 flex items-center justify-center rounded text-sm font-mono ${
                        bit === '1'
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                      }`}
                    >
                      {bit}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Operation Symbol */}
            <div className="text-center text-gray-500 dark:text-gray-400 font-mono text-lg">
              {operation === 'AND' && '&'}
              {operation === 'OR' && '|'}
              {operation === 'XOR' && '^'}
              {operation === 'NOT' && '~'}
              {operation === 'ADD' && '+'}
            </div>

            {/* Result */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium w-16">Result:</span>
              <div className="flex gap-1">
                {result.padStart(8, '0').split('').map((bit, index) => (
                  <div
                    key={index}
                    className={`w-8 h-8 flex items-center justify-center rounded text-sm font-mono ${
                      bit === '1'
                        ? 'bg-purple-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                    }`}
                  >
                    {bit}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Operation Guide - enhanced */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700/50">
        <h3 className="font-medium mb-3 text-gray-800 dark:text-gray-200">Operation Guide:</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="p-2 bg-white dark:bg-gray-800 rounded border">
              <div className="font-mono font-semibold text-blue-600">AND (&)</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Both bits must be 1</div>
              <div className="text-xs font-mono">1 & 1 = 1, others = 0</div>
            </div>
            <div className="p-2 bg-white dark:bg-gray-800 rounded border">
              <div className="font-mono font-semibold text-green-600">OR (|)</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Either bit can be 1</div>
              <div className="text-xs font-mono">0 | 0 = 0, others = 1</div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="p-2 bg-white dark:bg-gray-800 rounded border">
              <div className="font-mono font-semibold text-purple-600">XOR (^)</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Bits must be different</div>
              <div className="text-xs font-mono">1 ^ 0 = 1, same = 0</div>
            </div>
            <div className="p-2 bg-white dark:bg-gray-800 rounded border">
              <div className="font-mono font-semibold text-orange-600">NOT (~)</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Inverts all bits</div>
              <div className="text-xs font-mono">~1 = 0, ~0 = 1</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
