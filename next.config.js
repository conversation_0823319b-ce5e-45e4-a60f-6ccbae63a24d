const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "f005.backblazeb2.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "bfldeliverysc.blob.core.windows.net",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "api.flux.host",
        pathname: "/**",
      },
    ],
  },
  compress: true,
  poweredByHeader: false,
  reactStrictMode: true,

  experimental: {
    optimizeCss: true,
  },
  webpack: (config, { isServer }) => {
    config.module.rules.push({
      test: /\.node$/,
      use: "null-loader",
    });

    if (!isServer) {
      config.resolve.alias.canvas = false;
    }

    return config;
  },

  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
};

module.exports = withBundleAnalyzer(nextConfig);
