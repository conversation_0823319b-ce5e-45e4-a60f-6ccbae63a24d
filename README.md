# Free Text Converter

A comprehensive web application that provides a wide range of text manipulation and utility tools. Visit at [https://freetextconverter.com](https://freetextconverter.com)

## 🚀 Features

### Text Tools
- Text Case Conversion
- Replace Spaces
- Find and Replace
- Text Humanizer
- Paraphrase Tool

### Developer Tools
- Binary Calculator
- Password Generator
- QR Code Generator & Scanner
  - QR Code tracking
  - Analytics
  - History

### AI Tools
- Text Humanizer
- Paraphrase Tool
- Pickup Line Generator
- Recipe Generator
- Image Generator

### Multimedia Tools
- Video Downloader
  - Support for multiple platforms (YouTube, TikTok, Instagram, Facebook, Twitter)
  - Quality selection
  - Format options
- Image Processing

### PDF Tools
- Merge PDF
  - Combine multiple PDF files into one document
  - Reorder pages before merging
- Split PDF
  - Extract specific pages or page ranges
  - Split into individual pages
- Compress PDF
  - Reduce file size while maintaining quality
  - Multiple compression levels

## 🛠️ Tech Stack

### Frontend
- **Framework:** Next.js 13+ (App Router)
- **Language:** TypeScript
- **Styling:**
  - Tailwind CSS
  - Shadcn UI
  - Radix UI Components

### Backend & Infrastructure
- **Hosting:** Vercel
- **Database & Auth:** Firebase
  - Firestore for data storage
  - Firebase Authentication
  - Real-time updates
  - User management
- **Storage:** Backblaze B2
  - Media storage
  - Signed URLs
  - Recipe images
- **DNS & Security:** Cloudflare

### Analytics & Monitoring
- **Error Tracking:** Sentry
- **Performance Monitoring:**
  - Vercel Analytics
  - Custom event tracking

## 🔑 Environment Variables

```env
# OpenRouter API Keys
NEXT_PUBLIC_OPENROUTER_API_KEY=
NEXT_PUBLIC_OPENROUTER_HUMANIZER_KEY=
OPENROUTER_PICKUPLINEGENERATOR_KEY=
NEXT_PUBLIC_OPENROUTER_PARAPHRASE_KEY=

# Email Config
EMAIL_USER=
EMAIL_PASS=

# Base URL
NEXT_PUBLIC_BASE_URL=

# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=

# Backblaze
NEXT_PUBLIC_BACKBLAZE_KEY_ID=
NEXT_PUBLIC_BACKBLAZE_APP_KEY=
NEXT_PUBLIC_BACKBLAZE_BUCKET=
NEXT_PUBLIC_BACKBLAZE_PREFIX=

# Flux API
FLUX_API_KEY=

# Redis
UPSTASH_REDIS_REST_URL=
UPSTASH_REDIS_REST_TOKEN=

# Sentry
NEXT_PUBLIC_SENTRY_DSN=
```

## 📁 Project Structure

```
textconverter/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   │   ├── video/        # Video processing
│   │   ├── text/         # Text processing
│   │   ├── qr/           # QR code handling
│   │   └── pdf/          # PDF processing
├── components/            # React components
│   ├── ui/             # UI components
│   ├── text-tools/      # Text manipulation tools
│   ├── pdf-tools/       # PDF processing tools
│   └── shared/          # Shared components
├── contexts/             # React contexts
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions
│   ├── firebase/        # Firebase configuration
│   ├── api/             # API utilities
│   ├── pdf-utils/       # PDF processing utilities
│   └── text-utils/      # Text processing utilities
├── public/              # Static files
└── types/               # TypeScript definitions
```

## 🔐 Security

This project implements security best practices including:
- Cloudflare Security
  - SSL/TLS encryption
  - DDoS protection
- API Security
  - Rate limiting
  - Input validation
- Authentication with Firebase
- Secure file handling

## 🚀 Development

### Prerequisites
- Node.js 18.x
- npm/yarn

### Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Start the development server:
```bash
npm run dev
```

## 📄 License

[MIT License] - See LICENSE file for details

---

*This documentation is actively maintained and updated.*