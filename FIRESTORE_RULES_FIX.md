# Fix for "Missing Permission" Error in LikeButton

## Problem
The `LikeButton` component was showing "Could not fetch like count - missing permission or insufficient" error because:

1. The component tries to read from `archived_likes` collection but there was no security rule for it
2. The component writes to `users/{userId}/likes` but the rule was missing
3. Error handling wasn't graceful for permission-denied errors

## Solution Applied

### 1. Updated Firestore Security Rules

Added the following rules to `components/firebaserules.txt`:

```javascript
// Archived likes collection - for displaying total like count
match /archived_likes/{docId} {
  allow read: if true;
  allow write: if false; // Only allow reads, no writes from client
}

// Under users/{userId}:
// User likes collection
match /likes/{likeId} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}
```

### 2. Improved Error Handling

Updated `LikeButton.tsx` to:
- Not show toast errors for permission-denied (expected when not authenticated)
- Set default like count of 0 when fetch fails
- Handle authentication state more gracefully

## How to Deploy the Fix

### Option 1: Using the Script (Recommended)
```bash
./deploy-firestore-rules.sh
```

### Option 2: Manual Deployment

1. **Install Firebase CLI** (if not already installed):
   ```bash
   npm install -g firebase-tools
   ```

2. **Login to Firebase**:
   ```bash
   firebase login
   ```

3. **Copy the rules file**:
   ```bash
   cp components/firebaserules.txt firestore.rules
   ```

4. **Deploy the rules**:
   ```bash
   firebase deploy --only firestore:rules
   ```

5. **Clean up**:
   ```bash
   rm firestore.rules
   ```

### Option 3: Firebase Console (Manual)

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project
3. Go to Firestore Database → Rules
4. Copy the content from `components/firebaserules.txt`
5. Paste it into the rules editor
6. Click "Publish"

## What This Fixes

✅ **Like count display**: Users can now see the total like count without authentication errors
✅ **Like functionality**: Authenticated users can add likes to their personal collection
✅ **Error handling**: No more annoying permission error toasts for unauthenticated users
✅ **Security**: Maintains proper security while allowing necessary read access

## Testing

After deploying:
1. Refresh your app
2. The like count should display without errors
3. Sign in and try liking - should work without permission errors
4. Sign out and reload - should still show like count without errors

## Database Structure

```
firestore/
├── archived_likes/          # Public read access for total count
│   └── {likeId}
├── users/
│   └── {userId}/
│       └── likes/           # User-specific likes (authenticated access only)
│           └── {likeId}
└── ... (other collections)
```
