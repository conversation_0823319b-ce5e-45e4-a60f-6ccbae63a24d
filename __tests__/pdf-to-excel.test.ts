import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { PDFToExcelTool } from '@/components/pdf-tools/PDFToExcelTool';
import { toast } from 'sonner';
import * as XLSX from 'xlsx';
import { PDFDocument } from 'pdf-lib';
import * as pdfjs from 'pdfjs-dist';

// Mock dependencies
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

jest.mock('xlsx', () => ({
  utils: {
    book_new: jest.fn(() => ({ SheetNames: [], Sheets: {} })),
    aoa_to_sheet: jest.fn(),
    book_append_sheet: jest.fn(),
  },
  write: jest.fn(),
}));

jest.mock('pdf-lib', () => ({
  PDFDocument: {
    load: jest.fn().mockResolvedValue({
      getPageCount: () => 3,
    }),
  },
}));

global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

describe('PDFToExcelTool', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the upload component initially', () => {
    render(<PDFToExcelTool />);
    expect(screen.getByText('Upload a PDF to Convert to Excel')).toBeInTheDocument();
  });

  it('should handle file upload and display preview', async () => {
    render(<PDFToExcelTool />);
    const file = new File(['dummy content'], 'test.pdf', { type: 'application/pdf' });
    const fileInput = screen.getByText('Select PDF File').previousElementSibling as HTMLInputElement;

    fireEvent.change(fileInput, { target: { files: [file] } });

    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument();
      expect(screen.getByText('3 pages')).toBeInTheDocument();
    });
  });

  it('should show an error for non-PDF files', async () => {
    render(<PDFToExcelTool />);
    const file = new File(['dummy content'], 'test.txt', { type: 'text/plain' });
    const fileInput = screen.getByText('Select PDF File').previousElementSibling as HTMLInputElement;

    fireEvent.change(fileInput, { target: { files: [file] } });

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Please select a PDF file');
    });
  });
});
