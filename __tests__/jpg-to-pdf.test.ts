import { jpgToPdf } from "../lib/pdf-utils";

describe("JPG to PDF Conversion", () => {
  it("should convert a JPG image to a PDF", async () => {
    // Create a dummy JPG file
    const jpgBlob = new Blob(
      [
        // A minimal valid JPEG structure
        new Uint8Array([
          0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00,
          0x01, 0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xff, 0xdb,
          0x00, 0x43, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
          0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
          0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
          0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
          0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
          0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xff, 0xc0, 0x00, 0x11, 0x08,
          0x00, 0x01, 0x00, 0x01, 0x03, 0x01, 0x22, 0x00, 0x02, 0x11, 0x01,
          0x03, 0x11, 0x01, 0xff, 0xc4, 0x00, 0x1f, 0x00, 0x00, 0x01, 0x05,
          0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
          0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
          0x09, 0x0a, 0x0b, 0xff, 0xda, 0x00, 0x0c, 0x03, 0x01, 0x00, 0x02,
          0x11, 0x03, 0x11, 0x00, 0x3f, 0x00, 0xfd, 0xc2, 0x20, 0xff, 0xd9,
        ]),
      ],
      { type: "image/jpeg" }
    );
    const jpgFile = new File([jpgBlob], "test.jpg", { type: "image/jpeg" });

    // The File object in JSDOM doesn't have arrayBuffer, so we add it.
    // This is a common workaround for testing file handling in a Node environment.
    Object.defineProperty(jpgFile, "arrayBuffer", {
      value: function () {
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = () => {
            resolve(reader.result);
          };
          reader.readAsArrayBuffer(this);
        });
      },
    });

    // Call the function
    const pdfBlob = await jpgToPdf([jpgFile as any]);

    // Assertions
    expect(pdfBlob).toBeInstanceOf(Blob);
    expect(pdfBlob.type).toBe("application/pdf");
    expect(pdfBlob.size).toBeGreaterThan(0);
  });
});
