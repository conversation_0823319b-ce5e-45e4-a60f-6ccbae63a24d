import { PDFDocument } from 'pdf-lib';
import { mergePDFs } from '../lib/pdf-utils';

describe('PDF Merge Utility', () => {
  // Helper function to create a dummy PDF file for testing
  const createDummyPdf = async (pageCount: number): Promise<File> => {
    const pdfDoc = await PDFDocument.create();
    for (let i = 0; i < pageCount; i++) {
      pdfDoc.addPage();
    }
    const pdfBytes = await pdfDoc.save();
    return new File([pdfBytes], `test-${pageCount}-page.pdf`, { type: 'application/pdf' });
  };

  it('should merge two PDF files into a single document', async () => {
    // 1. Create two dummy PDF files
    const pdf1 = await createDummyPdf(1);
    const pdf2 = await createDummyPdf(2);

    // 2. Merge the files using the utility function
    const mergedPdfBlob = await mergePDFs([pdf1, pdf2]);

    // 3. Verify the merged PDF
    const mergedPdfDoc = await PDFDocument.load(await mergedPdfBlob.arrayBuffer());

    // Check that the merged document has the correct number of pages
    expect(mergedPdfDoc.getPageCount()).toBe(3);
  });

  it('should handle an empty array of files', async () => {
    const mergedPdfBlob = await mergePDFs([]);
    expect(mergedPdfBlob.size).toBe(0);
  });

  it('should correctly report progress', async () => {
    const pdf1 = await createDummyPdf(1);
    const pdf2 = await createDummyPdf(1);
    const progressUpdates: number[] = [];

    await mergePDFs([pdf1, pdf2], (progress) => {
      progressUpdates.push(progress);
    });

    // Check that progress is reported and ends at 100%
    expect(progressUpdates.length).toBeGreaterThan(0);
    expect(progressUpdates[progressUpdates.length - 1]).toBe(100);
  });
});
