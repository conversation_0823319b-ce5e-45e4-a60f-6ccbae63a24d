import { PDFOCRTool } from "@/components/pdf-tools/PDFOCRTool";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { toast } from "sonner";

// Mock the toast and getPDFPageCount functions
jest.mock("sonner", () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));
jest.mock("@/lib/pdf-utils", () => ({
  getPDFPageCount: jest.fn().mockResolvedValue(1),
  formatFileSize: jest.fn((size) => `${(size / 1024).toFixed(2)} KB`),
}));

describe("PDFOCRTool", () => {
  it("should simulate OCR processing and return the original file", async () => {
    render(<PDFOCRTool />);

    // Create a dummy PDF file
    const pdfContent =
      "%PDF-1.5\n1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n3 0 obj<</Type/Page/MediaBox[0 0 612 792]/Parent 2 0 R>>endobj\nxref\n0 4\n0000000000 65535 f \n0000000010 00000 n \n0000000058 00000 n \n0000000111 00000 n \ntrailer<</Size 4/Root 1 0 R>>\nstartxref\n162\n%%EOF";
    const pdfBlob = new Blob([pdfContent], { type: "application/pdf" });
    const pdfFile = new File([pdfBlob], "test.pdf", {
      type: "application/pdf",
    });

    // Simulate file upload
    const fileInput = screen.getByTestId("file-input");
    fireEvent.change(fileInput, { target: { files: [pdfFile] } });

    // Wait for the file to be processed
    await waitFor(() => {
      expect(screen.getByText("test.pdf")).toBeInTheDocument();
    });

    // Click the process button
    const processButton = screen.getByText("Process with OCR");
    fireEvent.click(processButton);

    // Wait for the simulated processing to complete
    await waitFor(
      () => {
        expect(screen.getByText("Download PDF")).toBeInTheDocument();
      },
      { timeout: 5000 }
    );

    // Check if the success toast was called
    expect(toast.success).toHaveBeenCalledWith("OCR Completed", {
      description: "Your PDF has been processed successfully.",
    });

    // Check if the download link points to a blob URL
    const downloadButton = screen.getByText("Download PDF").closest("a");
    expect(downloadButton).toHaveAttribute("href");
  });
});
