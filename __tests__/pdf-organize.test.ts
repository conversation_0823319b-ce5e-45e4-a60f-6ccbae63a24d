import { PDFDocument } from 'pdf-lib';
import { organizePDF } from '../lib/pdf-utils';

describe('PDF Organize Utility', () => {
  it('should rearrange the pages of a PDF document', async () => {
    // 1. Create a dummy PDF file
    const pdfDoc = await PDFDocument.create();
    const page1 = pdfDoc.addPage();
    page1.drawText('Page 1');
    const page2 = pdfDoc.addPage();
    page2.drawText('Page 2');
    const page3 = pdfDoc.addPage();
    page3.drawText('Page 3');
    const pdfBytes = await pdfDoc.save();

    // 2. Rearrange the pages to be in the order 3, 1, 2
    const newOrder = [2, 0, 1];
    const organizedPdfBytes = await organizePDF(pdfBytes, newOrder);

    // 3. Verify the organized PDF
    const organizedPdfDoc = await PDFDocument.load(organizedPdfBytes);
    const organizedPages = organizedPdfDoc.getPages();
    expect(organizedPages.length).toBe(3);
  });
});
