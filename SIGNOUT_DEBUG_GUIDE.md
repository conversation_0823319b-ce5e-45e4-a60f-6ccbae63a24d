# Sign Out Button Debug Guide

## Issue
The sign out button in the image generator page doesn't seem to work when clicked.

## Fixes Applied

### 1. Enhanced Sign Out Function
Added comprehensive error handling and logging:

```typescript
const handleSignOut = async () => {
  try {
    console.log("Image Generator: Starting sign out...");
    await signOut();
    console.log("Image Generator: Sign out completed");
  } catch (error) {
    console.error("Image Generator: Sign out error:", error);
    toast.error("Failed to sign out");
  }
};
```

### 2. Improved AuthContext Sign Out
Made the sign out more reliable with forced redirect:

```typescript
const signOut = async () => {
  console.log("AuthContext: Starting sign out...");
  try {
    // First remove the cookie
    Cookies.remove('firebaseToken', { path: '/' });
    console.log("AuthContext: <PERSON>ie removed");
    
    // Then sign out from Firebase
    await firebaseSignOut(auth);
    console.log("AuthContext: Firebase sign out completed");
    
    // Force redirect using window.location for reliability
    setTimeout(() => {
      console.log("AuthContext: Redirecting to sign in page");
      window.location.href = '/auth/signin';
    }, 100);
    
  } catch (error) {
    console.error('AuthContext: Error signing out:', error);
    // Even if Firebase signOut fails, still redirect to sign in
    Cookies.remove('firebaseToken', { path: '/' });
    window.location.href = '/auth/signin';
  }
};
```

### 3. Enhanced Button Click Handler
Added event prevention and debugging:

```typescript
<Button 
  variant="outline" 
  size="sm" 
  onClick={(e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Sign out button clicked');
    handleSignOut();
  }}
  type="button"
>
  <LogOut className="h-4 w-4 mr-2" />
  Sign Out
</Button>
```

## Testing Steps

### 1. Open Browser Developer Tools
1. Go to your image generator page: `https://yoursite.com/image-generator`
2. Open Developer Tools (F12)
3. Go to the **Console** tab

### 2. Test Sign Out Button
1. Click the "Sign Out" button in the top right
2. Watch the console for these messages:
   ```
   Sign out button clicked
   Image Generator: Starting sign out...
   AuthContext: Starting sign out...
   AuthContext: Cookie removed
   AuthContext: Firebase sign out completed
   AuthContext: Redirecting to sign in page
   Image Generator: Sign out completed
   ```

### 3. Expected Behavior
After clicking sign out:
1. Console shows the debug messages above
2. You should be redirected to `/auth/signin`
3. The Firebase token cookie should be removed
4. You should no longer be authenticated

### 4. If It Still Doesn't Work

**Check for JavaScript Errors:**
1. Look for any red error messages in the console
2. Check if there are any network errors in the Network tab

**Check Cookie Removal:**
1. Go to Developer Tools > Application tab
2. Look under Storage > Cookies
3. Verify the `firebaseToken` cookie is removed after sign out

**Manual Test:**
Try this in the browser console:
```javascript
// Test if the auth context is available
console.log('Auth context available:', !!window.auth);

// Test cookie removal manually
document.cookie = 'firebaseToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
console.log('Cookie manually removed');

// Test redirect manually
window.location.href = '/auth/signin';
```

## Common Issues & Solutions

### Issue 1: Button Not Responding
**Cause:** Event propagation or form submission interference
**Solution:** Added `e.preventDefault()` and `e.stopPropagation()`

### Issue 2: Firebase Sign Out Fails
**Cause:** Network issues or Firebase configuration
**Solution:** Added fallback that removes cookie and redirects anyway

### Issue 3: Redirect Not Working
**Cause:** Next.js router issues in production
**Solution:** Using `window.location.href` instead of `router.push()`

### Issue 4: Cookie Not Removed
**Cause:** Path or domain mismatch
**Solution:** Explicitly set path to '/' when removing cookie

## Additional Debugging

If the issue persists, add this temporary debug button to test:

```typescript
<Button onClick={() => {
  console.log('Manual sign out test');
  document.cookie = 'firebaseToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
  window.location.href = '/auth/signin';
}}>
  Manual Sign Out Test
</Button>
```

This will help isolate whether the issue is with the Firebase auth or the redirect mechanism.
