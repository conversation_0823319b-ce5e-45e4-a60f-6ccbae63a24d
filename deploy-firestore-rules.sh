#!/bin/bash

# Deploy Firestore Rules Script
# This script deploys the updated Firestore security rules to fix the likes permission issue

echo "🔥 Deploying Firestore Security Rules..."
echo "This will update the security rules to fix the 'missing permission' error for likes."
echo ""

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed."
    echo "Please install it first: npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ You are not logged in to Firebase."
    echo "Please run: firebase login"
    exit 1
fi

# Copy the rules file to the correct location
if [ -f "components/firebaserules.txt" ]; then
    cp components/firebaserules.txt firestore.rules
    echo "✅ Copied rules from components/firebaserules.txt to firestore.rules"
else
    echo "❌ Could not find components/firebaserules.txt"
    exit 1
fi

# Deploy the rules
echo "🚀 Deploying Firestore rules..."
firebase deploy --only firestore:rules

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Firestore rules deployed successfully!"
    echo ""
    echo "The following changes were made:"
    echo "• Added read permission for 'archived_likes' collection"
    echo "• Added user-specific likes collection rules under users/{userId}/likes"
    echo "• Improved error handling for permission-denied errors"
    echo ""
    echo "The 'missing permission' error should now be resolved."
else
    echo "❌ Failed to deploy Firestore rules"
    exit 1
fi

# Clean up
rm -f firestore.rules
echo "🧹 Cleaned up temporary files"
