declare module 'backblaze-b2' {
  interface B2Options {
    applicationKeyId: string;
    applicationKey: string;
  }

  interface DownloadAuthorizationOptions {
    bucketId: string;
    fileNamePrefix: string;
    validDurationInSeconds: number;
  }

  interface DownloadAuthorizationResponse {
    data: {
      downloadUrl: string;
      authorizationToken: string;
      bucketId: string;
      fileNamePrefix: string;
      validDurationInSeconds: number;
    };
  }

  export class B2 {
    constructor(options: B2Options);
    authorize(): Promise<any>;
    getDownloadAuthorization(options: DownloadAuthorizationOptions): Promise<DownloadAuthorizationResponse>;
  }
}