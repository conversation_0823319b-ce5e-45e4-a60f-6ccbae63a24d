/**
 * Interface representing a PDF file in the application
 */
export interface PDFFile {
  /** Unique identifier for the file */
  id: string;
  
  /** Name of the file */
  name: string;
  
  /** Size of the file in bytes */
  size: number;
  
  /** Reference to the actual File object */
  file: File;
  
  /** Number of pages in the PDF, optional because it might not be calculated yet */
  pageCount?: number;
  
  /** Selected pages for operations that work on specific pages */
  selectedPages?: number[];
  
  /** Preview URL for displaying the PDF */
  previewUrl?: string;
} 