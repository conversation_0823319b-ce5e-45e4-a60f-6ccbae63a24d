export type RecipeStatus = 'idle' | 'loading' | 'completed' | 'failed';
export type ImageStatus = 'idle' | 'loading' | 'success' | 'failed';

export interface Recipe {
  id: string;
  title: string;
  description: string;
  cookingTime: number;
  servings: number;
  difficulty: 'easy' | 'medium' | 'hard';
  cuisine: string;
  recipeLanguage: string;
  ingredients: (string | Ingredient)[];
  instructions: {
    steps: string[];
    tips: string[];
  };
  nutritionalInfo: NutritionalInfo;
  imageUrl?: string;
  image?: string; // URL of the generated image
  imageError?: string; // Friendly error message for image generation
  tags: string[];
  status: {
    recipe: RecipeStatus;
    image: ImageStatus;
  };
  error?: {
    recipe?: string;
    image?: string;
  };
  backblazeImageUrl?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Ingredient {
  name: string;
  amount: number;
  unit: string;
  notes?: string;
}

export interface NutritionalInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  sugar?: number;
}

export interface RecipeFilters {
  cuisine?: string;
  maxCookingTime?: number;
  difficulty?: string;
  dietary?: string[];
  outputLanguage?: string;
}

export interface RecipeGeneratorRequest {
  ingredients: string[];
  filters: {
    cuisine?: string;
    maxCookingTime?: number;
    dietary?: string[];
    outputLanguage?: string;
  };
  servings?: number;
  generateImage?: boolean;
}

export interface SavedRecipe extends Recipe {
  userId: string;
  savedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  backblazeImageUrl?: string;
  originalImageUrl?: string;
}

export interface SaveRecipeRequest {
  recipe: Recipe;
  imageData?: string;
  userId: string;
}