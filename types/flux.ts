// Flux Image Generation Types

export interface FluxUltraGenerationRequest {
  prompt: string;
  prompt_upsampling?: boolean;
  seed?: number;
  aspect_ratio?: string;
  safety_tolerance?: number;
  output_format?: 'jpeg' | 'png';
  raw?: boolean;
  image_prompt?: string;
  image_prompt_strength?: number;
}

export interface FluxKontextRequest {
  prompt: string;
  input_image: string; // Base64 encoded
  seed?: number;
  aspect_ratio?: string;
  output_format?: 'jpeg' | 'png';
  prompt_upsampling?: boolean;
  safety_tolerance?: number;
}

export interface FluxFillRequest {
  prompt: string;
  image: string; // Base64 encoded
  mask?: string; // Base64 encoded mask
  strength?: number;
  safety_tolerance?: number;
  output_format?: 'jpeg' | 'png';
}

export interface FluxExpandRequest {
  image: string; // Base64 encoded
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
  prompt?: string;
  output_format?: 'jpeg' | 'png';
}

export interface FluxCannyRequest {
  prompt: string;
  control_image: string; // Base64 encoded
  control_strength?: number;
  canny_low_threshold?: number;
  canny_high_threshold?: number;
  safety_tolerance?: number;
  output_format?: 'jpeg' | 'png';
}

export interface FluxDepthRequest {
  prompt: string;
  control_image: string; // Base64 encoded
  control_strength?: number;
  safety_tolerance?: number;
  output_format?: 'jpeg' | 'png';
}

// API Response Types
export interface FluxResponse {
  id: string;
  status?: string;
}

export interface FluxResult {
  status: 'Ready' | 'Pending' | 'Error';
  result?: {
    sample: string; // URL to generated image
    error?: string;
  };
}

// Edit Types
export type FluxEditType = 'kontext-pro' | 'kontext-max' | 'fill' | 'expand' | 'canny' | 'depth';

export interface ImageEditRequest {
  prompt: string;
  inputImage: string;
  editType: FluxEditType;
  aspectRatio?: string;
  mask?: string; // For fill operations
  expandParams?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
  controlStrength?: number; // For canny/depth
  cannyThresholds?: {
    low: number;
    high: number;
  };
}

export interface ImageEditResponse {
  success: boolean;
  imageUrl?: string;
  editType?: FluxEditType;
  taskId?: string;
  error?: string;
}
