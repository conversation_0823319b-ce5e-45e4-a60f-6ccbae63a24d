/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import { onDocumentCreated } from "firebase-functions/v2/firestore";
import * as admin from "firebase-admin";

// Initialize Firebase Admin
admin.initializeApp();

// Function to update QR code scan count
exports.onScanCreated = onDocumentCreated(
  "users/{userId}/qr_codes/{qrCodeId}/scans/{scanId}",
  async (event) => {
    const snapshot = event.data;
    if (!snapshot) {
      console.log("No data associated with the event");
      return;
    }

    const scanData = snapshot.data();
    const userId = event.params.userId;
    const qrCodeId = event.params.qrCodeId;

    console.log(`Processing new scan for QR code: ${qrCodeId}`, {
      scanId: event.params.scanId,
      timestamp: scanData.timestamp,
      deviceType: scanData.deviceType,
    });

    try {
      // Get reference to user's QR code document
      const userQrCodeRef = admin
        .firestore()
        .collection("users")
        .doc(userId)
        .collection("qr_codes")
        .doc(qrCodeId);

      // Update the document in a transaction
      await admin.firestore().runTransaction(async (transaction) => {
        // Get current stats
        const qrCodeSnapshot = await transaction.get(userQrCodeRef);
        const qrCodeStats = qrCodeSnapshot.data() || {};

        // Initialize or update device stats
        const deviceStats = qrCodeStats.deviceStats || {
          mobile: 0,
          desktop: 0,
        };
        deviceStats[scanData.deviceType] =
          (deviceStats[scanData.deviceType] || 0) + 1;

        // Initialize or update hourly stats
        const scanTime = scanData.timestamp.toDate();
        const hourKey = scanTime.getHours().toString();
        const hourlyStats = qrCodeStats.hourlyStats || {};
        hourlyStats[hourKey] = (hourlyStats[hourKey] || 0) + 1;

        // Update user's QR code document with the new stats
        transaction.update(userQrCodeRef, {
          scanCount: admin.firestore.FieldValue.increment(1),
          lastScanned: admin.firestore.FieldValue.serverTimestamp(),
          deviceStats,
          hourlyStats,
          lastDeviceType: scanData.deviceType,
          lastUserAgent: scanData.userAgent,
        });
      });

      console.log(`Successfully updated scan stats for QR code: ${qrCodeId}`);
      return null;
    } catch (error) {
      console.error(
        `Error updating scan stats for QR code ${qrCodeId}:`,
        error
      );
      throw error; // Retries the function on failure
    }
  }
);

// Start writing functions
// https://firebase.google.com/docs/functions/typescript
