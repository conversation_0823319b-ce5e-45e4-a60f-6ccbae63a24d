#!/bin/bash

echo "🚀 Deploying LikeButton Fix to Vercel"
echo "======================================"

# Step 1: Deploy Firestore Rules
echo ""
echo "📋 Step 1: Deploying Firestore Rules..."
if [ -f "components/firebaserules.txt" ]; then
    cp components/firebaserules.txt firestore.rules
    echo "✅ Copied rules to firestore.rules"
    
    if command -v firebase &> /dev/null; then
        echo "🔥 Deploying to Firebase..."
        firebase deploy --only firestore:rules
        if [ $? -eq 0 ]; then
            echo "✅ Firestore rules deployed successfully!"
        else
            echo "❌ Failed to deploy Firestore rules"
            exit 1
        fi
    else
        echo "⚠️  Firebase CLI not found. Please deploy rules manually:"
        echo "   firebase deploy --only firestore:rules"
    fi
    
    rm -f firestore.rules
else
    echo "❌ Could not find components/firebaserules.txt"
    exit 1
fi

# Step 2: Git Operations
echo ""
echo "📋 Step 2: Pushing code changes..."
git add .
git status

echo ""
read -p "Do you want to commit and push these changes? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git commit -m "Fix LikeButton permission error and update Firestore rules

- Added archived_likes collection rule for public read access
- Added user-specific likes subcollection rule
- Improved error handling in LikeButton component
- Fixed 'missing permission' error on page reload"
    
    echo "📤 Pushing to repository..."
    git push origin main
    
    if [ $? -eq 0 ]; then
        echo "✅ Code pushed successfully!"
    else
        echo "❌ Failed to push code"
        exit 1
    fi
else
    echo "⏭️  Skipping git push"
fi

# Step 3: Environment Variables Reminder
echo ""
echo "📋 Step 3: Environment Variables"
echo "================================"
echo "🔧 Make sure your Vercel environment variables are set from .env.local:"
echo ""
echo "Required variables:"
echo "- NEXT_PUBLIC_FIREBASE_* (all Firebase config)"
echo "- DEEPSEEK_API_KEY"
echo "- GOOGLE_API_KEY"
echo "- FLUX_API_KEY"
echo "- BACKBLAZE_* (all Backblaze config)"
echo "- UPSTASH_REDIS_* (Redis config)"
echo "- EMAIL_* (email config)"
echo ""
echo "📝 To set them in Vercel:"
echo "1. Go to https://vercel.com/dashboard"
echo "2. Select your project"
echo "3. Go to Settings → Environment Variables"
echo "4. Copy values from .env.local"
echo ""

# Step 4: Deployment Status
echo "📋 Step 4: Deployment"
echo "===================="
echo "🎯 Your changes should now be deployed automatically to Vercel"
echo "🔍 Check your Vercel dashboard for deployment status"
echo ""
echo "✅ Deployment checklist:"
echo "  ✓ Firestore rules updated"
echo "  ✓ Code changes pushed"
echo "  ⚠️  Environment variables (check manually)"
echo "  ⚠️  Vercel deployment (check dashboard)"
echo ""
echo "🎉 Once deployed, the 'missing permission' error should be resolved!"
